#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A2A多智能体系统 ADK基础Runner类

封装Google ADK Runner功能，集成用户权限验证
"""

import logging
import asyncio
from typing import Dict, List, Optional, Any, Union, Callable, Awaitable, Type, TypeVar, AsyncGenerator
from datetime import datetime
import uuid

from google.ai.generativelanguage import Content
from google.genai import types
from google.adk.agents.llm_agent import Agent
from google.adk.runners import Runner
from google.adk.events.event import Event
from google.adk.sessions.session import Session

from app.models.user import User
from app.models.agent import Agent as AgentModel
from app.models.task import Task, TaskExecution
from app.core.logging import get_logger
from app.auth.permissions import check_user_permission

from ..services.database_session_service import DatabaseSessionService
from ..converters.event_converter import EventConverter
from ..converters.response_converter import ResponseConverter

# 创建类型变量用于子类继承
T = TypeVar('T', bound='BaseRunner')

class BaseRunner:
    """
    基础Runner类，封装ADK Runner功能，集成用户权限验证
    
    提供以下功能：
    1. 用户权限验证
    2. 事件处理和转换
    3. 流式输出支持
    4. 错误处理和日志记录
    5. 运行状态管理
    """
    
    def __init__(
        self,
        user_id: int,
        owner_id: int,
        agent_instance: Agent,
        session: Optional[DatabaseSessionService] = None,
        task_id: Optional[int] = None,
        execution_id: Optional[str] = None,
        config: Optional[Dict[str, Any]] = None,
        event_handlers: Optional[Dict[str, List[Callable[[Event], Awaitable[None]]]]] = None,
        logger: Optional[logging.Logger] = None
    ):
        """
        初始化基础Runner
        
        Args:
            user_id: 用户ID
            owner_id: 拥有者ID
            agent_instance: ADK Agent实例
            session: 数据库会话服务，如果为None则创建新的会话
            task_id: 关联的任务ID
            execution_id: 执行ID，如果为None则自动生成
            config: Runner配置
            event_handlers: 事件处理器字典
            logger: 日志记录器，如果为None则创建新的记录器
        """
        self.user_id = user_id
        self.owner_id = owner_id
        self.agent_instance = agent_instance
        self.task_id = task_id
        self.execution_id = execution_id or str(uuid.uuid4())
        self.config = config or {}
        self.event_handlers = event_handlers or {}
        self.logger = logger or get_logger(f"runner_{self.execution_id}")
        
        # 创建会话服务
        self.session = session or DatabaseSessionService(user_id=user_id, owner_id=owner_id)
        
        # 创建ADK Runner
        runner_options = RunnerOptions(
            session=self.session.get_adk_session()
        )
        self.runner = Runner(agent=agent_instance, options=runner_options)
        
        # 运行状态
        self.state = RunnerState.IDLE
        self.start_time = None
        self.end_time = None
        self.error = None
        
        # 转换器
        self.event_converter = EventConverter()
        self.response_converter = ResponseConverter()
        
        # 注册默认事件处理器
        self._register_default_event_handlers()
        
        self.logger.info(f"Runner {self.execution_id} 已初始化，用户ID: {user_id}，拥有者ID: {owner_id}")
    
    def _register_default_event_handlers(self) -> None:
        """
        注册默认事件处理器
        """
        # 为每种事件类型注册日志处理器
        for event_type in EventType:
            if event_type not in self.event_handlers:
                self.event_handlers[event_type] = []
            
            # 添加日志处理器
            self.event_handlers[event_type].append(self._log_event)
    
    async def _log_event(self, event: Event) -> None:
        """
        记录事件日志
        
        Args:
            event: ADK事件
        """
        self.logger.debug(f"事件: {event.type.name}, 内容: {event.data}")
    
    async def _handle_event(self, event: Event) -> None:
        """
        处理ADK事件
        
        Args:
            event: ADK事件
        """
        # 获取事件类型的处理器列表
        handlers = self.event_handlers.get(event.type, [])
        
        # 调用所有处理器
        for handler in handlers:
            try:
                await handler(event)
            except Exception as e:
                self.logger.error(f"事件处理器错误: {str(e)}")
    
    async def _check_permission(self) -> bool:
        """
        检查用户权限
        
        Returns:
            bool: 是否有权限
        """
        try:
            # 检查用户是否存在
            user = await User.get_by_id(self.user_id)
            if not user:
                self.logger.error(f"用户不存在: {self.user_id}")
                return False
            
            # 检查用户是否有权限运行此Runner
            has_permission = await check_user_permission(
                user_id=self.user_id,
                owner_id=self.owner_id,
                resource_type="runner",
                action="execute",
                resource_id=self.execution_id
            )
            
            if not has_permission:
                self.logger.error(f"用户 {self.user_id} 没有权限执行 Runner {self.execution_id}")
            
            return has_permission
        except Exception as e:
            self.logger.error(f"权限检查错误: {str(e)}")
            return False
    
    async def run(
        self, 
        input_content: Union[str, Content, types.Content],
        stream: bool = False
    ) -> Any:
        """
        运行Runner
        
        Args:
            input_content: 输入内容，可以是字符串或ADK Content对象
            stream: 是否使用流式输出
            
        Returns:
            Any: 运行结果
            
        Raises:
            PermissionError: 用户没有权限
            RuntimeError: 运行时错误
        """
        # 检查权限
        has_permission = await self._check_permission()
        if not has_permission:
            raise PermissionError(f"用户 {self.user_id} 没有权限执行 Runner {self.execution_id}")
        
        # 设置开始时间和状态
        self.start_time = datetime.now()
        self.state = RunnerState.RUNNING
        self.logger.info(f"Runner {self.execution_id} 开始运行")
        
        try:
            # 转换输入内容为ADK Content
            if isinstance(input_content, str):
                content = Content(parts=[{"text": input_content}])
            else:
                content = input_content
            
            # 根据是否流式输出选择不同的运行方式
            if stream:
                return await self._run_stream(content)
            else:
                return await self._run_sync(content)
        except Exception as e:
            self.state = RunnerState.ERROR
            self.error = str(e)
            self.logger.error(f"Runner运行错误: {str(e)}")
            raise RuntimeError(f"Runner运行错误: {str(e)}")
        finally:
            # 设置结束时间
            self.end_time = datetime.now()
            if self.state == RunnerState.RUNNING:
                self.state = RunnerState.COMPLETED
            
            # 记录运行时间
            duration = (self.end_time - self.start_time).total_seconds()
            self.logger.info(f"Runner {self.execution_id} 运行完成，耗时: {duration}秒，状态: {self.state.name}")
    
    async def _run_sync(self, content: Content) -> Any:
        """
        同步运行Runner
        
        Args:
            content: ADK Content对象
            
        Returns:
            Any: 运行结果
        """
        # 运行ADK Runner
        response = await self.runner.run_async(content)
        
        # 转换响应
        result = self.response_converter.adk_to_api(response)
        
        return result
    
    async def _run_stream(self, content: Content) -> AsyncGenerator[Any, None]:
        """
        流式运行Runner
        
        Args:
            content: ADK Content对象
            
        Yields:
            Any: 流式运行结果
        """
        # 运行ADK Runner并获取流式响应
        async for response in self.runner.run_stream_async(content):
            # 转换响应
            result = self.response_converter.adk_to_api(response)
            
            # 产生结果
            yield result
    
    async def stop(self) -> None:
        """
        停止Runner
        """
        if self.state == RunnerState.RUNNING:
            try:
                # 停止ADK Runner
                await self.runner.stop_async()
                
                # 更新状态
                self.state = RunnerState.STOPPED
                self.end_time = datetime.now()
                
                self.logger.info(f"Runner {self.execution_id} 已停止")
            except Exception as e:
                self.logger.error(f"停止Runner错误: {str(e)}")
    
    async def pause(self) -> None:
        """
        暂停Runner
        """
        if self.state == RunnerState.RUNNING:
            try:
                # 暂停ADK Runner (如果ADK支持的话)
                # 目前ADK可能不直接支持暂停，这里是一个占位实现
                self.state = RunnerState.PAUSED
                self.logger.info(f"Runner {self.execution_id} 已暂停")
            except Exception as e:
                self.logger.error(f"暂停Runner错误: {str(e)}")
    
    async def resume(self) -> None:
        """
        恢复Runner
        """
        if self.state == RunnerState.PAUSED:
            try:
                # 恢复ADK Runner (如果ADK支持的话)
                # 目前ADK可能不直接支持恢复，这里是一个占位实现
                self.state = RunnerState.RUNNING
                self.logger.info(f"Runner {self.execution_id} 已恢复")
            except Exception as e:
                self.logger.error(f"恢复Runner错误: {str(e)}")
    
    def get_state(self) -> Dict[str, Any]:
        """
        获取Runner状态
        
        Returns:
            Dict[str, Any]: Runner状态信息
        """
        return {
            "execution_id": self.execution_id,
            "task_id": self.task_id,
            "user_id": self.user_id,
            "owner_id": self.owner_id,
            "state": self.state.name,
            "start_time": self.start_time.isoformat() if self.start_time else None,
            "end_time": self.end_time.isoformat() if self.end_time else None,
            "duration": (self.end_time - self.start_time).total_seconds() if (self.start_time and self.end_time) else None,
            "error": self.error
        }
    
    @classmethod
    async def create(cls: Type[T], 
                    user_id: int, 
                    owner_id: int, 
                    agent_instance: Agent, 
                    **kwargs) -> T:
        """
        创建Runner实例的工厂方法
        
        Args:
            user_id: 用户ID
            owner_id: 拥有者ID
            agent_instance: ADK Agent实例
            **kwargs: 其他参数
            
        Returns:
            BaseRunner: Runner实例
        """
        # 创建实例
        instance = cls(user_id=user_id, owner_id=owner_id, agent_instance=agent_instance, **kwargs)
        
        # 检查权限
        has_permission = await instance._check_permission()
        if not has_permission:
            raise PermissionError(f"用户 {user_id} 没有权限创建 Runner")
        
        return instance