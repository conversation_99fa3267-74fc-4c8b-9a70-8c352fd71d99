#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A2A多智能体系统 MCP管理器

统一管理MCP客户端和服务器的生命周期，提供高级API
"""

import asyncio
import logging
import json
from typing import Dict, List, Any, Optional, Callable, Union, Set
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime, timed<PERSON>ta
from pathlib import Path
import yaml

# Google ADK imports
from google.ai.generativelanguage import FunctionDeclaration, Schema, Type as SchemaType

from .base_tool import (
    BaseTool, ToolConfig, ToolResult, ToolError, ToolStatus,
    ToolExecutionContext, ToolPermission
)
from .mcp_client import MCPClient, MCPClientConfig, MCPClientState
from .mcp_server import MCPServer, MCPServerConfig, MCPServerState
from .mcp_protocol import (
    MCPCapabilities, MCPClientInfo, MCPServerInfo, MCPTool,
    MCPResourceTemplate, MCPPrompt, MCP<PERSON>rror, MCPMessageValidator
)


class MCPManagerState(Enum):
    """MCP管理器状态枚举"""
    STOPPED = "stopped"
    STARTING = "starting"
    RUNNING = "running"
    STOPPING = "stopping"
    ERROR = "error"


class MCPConnectionType(Enum):
    """MCP连接类型枚举"""
    CLIENT = "client"
    SERVER = "server"


@dataclass
class MCPEndpointConfig:
    """MCP端点配置"""
    name: str
    type: MCPConnectionType
    enabled: bool = True
    auto_start: bool = True
    retry_attempts: int = 3
    retry_delay: float = 1.0
    health_check_interval: int = 60
    
    # 客户端配置
    client_config: Optional[MCPClientConfig] = None
    
    # 服务器配置
    server_config: Optional[MCPServerConfig] = None
    
    # 元数据
    description: str = ""
    tags: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class MCPManagerConfig:
    """MCP管理器配置"""
    # 基础配置
    name: str = "A2A-MCP-Manager"
    version: str = "1.0.0"
    description: str = "A2A多智能体系统MCP管理器"
    
    # 端点配置
    endpoints: List[MCPEndpointConfig] = field(default_factory=list)
    
    # 全局配置
    global_timeout: int = 30
    max_concurrent_connections: int = 50
    enable_health_checks: bool = True
    health_check_interval: int = 60
    
    # 日志配置
    log_level: str = "INFO"
    log_file: Optional[str] = None
    enable_metrics: bool = True
    
    # 安全配置
    enable_auth: bool = True
    auth_tokens: Set[str] = field(default_factory=set)
    
    # 配置文件路径
    config_file: Optional[str] = None
    
    @classmethod
    def from_file(cls, config_file: str) -> 'MCPManagerConfig':
        """
        从配置文件加载配置
        
        Args:
            config_file: 配置文件路径
        
        Returns:
            MCPManagerConfig: 配置对象
        """
        config_path = Path(config_file)
        if not config_path.exists():
            raise FileNotFoundError(f"配置文件不存在: {config_file}")
        
        with open(config_path, 'r', encoding='utf-8') as f:
            if config_path.suffix.lower() in ['.yaml', '.yml']:
                data = yaml.safe_load(f)
            else:
                data = json.load(f)
        
        # 解析端点配置
        endpoints = []
        for endpoint_data in data.get('endpoints', []):
            endpoint_config = MCPEndpointConfig(
                name=endpoint_data['name'],
                type=MCPConnectionType(endpoint_data['type']),
                enabled=endpoint_data.get('enabled', True),
                auto_start=endpoint_data.get('auto_start', True),
                retry_attempts=endpoint_data.get('retry_attempts', 3),
                retry_delay=endpoint_data.get('retry_delay', 1.0),
                health_check_interval=endpoint_data.get('health_check_interval', 60),
                description=endpoint_data.get('description', ''),
                tags=endpoint_data.get('tags', []),
                metadata=endpoint_data.get('metadata', {})
            )
            
            # 解析客户端配置
            if endpoint_config.type == MCPConnectionType.CLIENT and 'client' in endpoint_data:
                client_data = endpoint_data['client']
                endpoint_config.client_config = MCPClientConfig(
                    server_url=client_data['server_url'],
                    transport=client_data.get('transport', 'websocket'),
                    auth_token=client_data.get('auth_token'),
                    auth_method=client_data.get('auth_method', 'bearer'),
                    timeout=client_data.get('timeout', 30),
                    retry_attempts=client_data.get('retry_attempts', 3),
                    retry_delay=client_data.get('retry_delay', 1.0),
                    heartbeat_interval=client_data.get('heartbeat_interval', 30),
                    max_message_size=client_data.get('max_message_size', 1024 * 1024),
                    enable_compression=client_data.get('enable_compression', True),
                    custom_headers=client_data.get('custom_headers', {}),
                    client_info=client_data.get('client_info', {
                        'name': 'A2A-MCP-Client',
                        'version': '1.0.0'
                    }),
                    capabilities=client_data.get('capabilities', {
                        'tools': True,
                        'resources': True,
                        'prompts': True,
                        'notifications': True
                    })
                )
            
            # 解析服务器配置
            if endpoint_config.type == MCPConnectionType.SERVER and 'server' in endpoint_data:
                server_data = endpoint_data['server']
                endpoint_config.server_config = MCPServerConfig(
                    name=server_data.get('name', 'A2A-MCP-Server'),
                    version=server_data.get('version', '1.0.0'),
                    description=server_data.get('description', 'A2A多智能体系统MCP服务器'),
                    host=server_data.get('host', 'localhost'),
                    port=server_data.get('port', 8080),
                    transport=server_data.get('transport', 'websocket'),
                    enable_auth=server_data.get('enable_auth', True),
                    auth_tokens=set(server_data.get('auth_tokens', [])),
                    allowed_origins=server_data.get('allowed_origins', ['*']),
                    max_connections=server_data.get('max_connections', 100),
                    protocol_version=server_data.get('protocol_version', '2024-11-05'),
                    capabilities=server_data.get('capabilities', {
                        'tools': True,
                        'resources': True,
                        'prompts': True,
                        'notifications': True
                    }),
                    max_message_size=server_data.get('max_message_size', 1024 * 1024),
                    heartbeat_interval=server_data.get('heartbeat_interval', 30),
                    request_timeout=server_data.get('request_timeout', 30),
                    enable_compression=server_data.get('enable_compression', True),
                    log_level=server_data.get('log_level', 'INFO'),
                    log_requests=server_data.get('log_requests', True),
                    log_responses=server_data.get('log_responses', False)
                )
            
            endpoints.append(endpoint_config)
        
        return cls(
            name=data.get('name', 'A2A-MCP-Manager'),
            version=data.get('version', '1.0.0'),
            description=data.get('description', 'A2A多智能体系统MCP管理器'),
            endpoints=endpoints,
            global_timeout=data.get('global_timeout', 30),
            max_concurrent_connections=data.get('max_concurrent_connections', 50),
            enable_health_checks=data.get('enable_health_checks', True),
            health_check_interval=data.get('health_check_interval', 60),
            log_level=data.get('log_level', 'INFO'),
            log_file=data.get('log_file'),
            enable_metrics=data.get('enable_metrics', True),
            enable_auth=data.get('enable_auth', True),
            auth_tokens=set(data.get('auth_tokens', [])),
            config_file=config_file
        )


@dataclass
class MCPEndpointStatus:
    """MCP端点状态"""
    name: str
    type: MCPConnectionType
    state: Union[MCPClientState, MCPServerState]
    enabled: bool
    connected_at: Optional[datetime] = None
    last_activity: Optional[datetime] = None
    error_count: int = 0
    last_error: Optional[str] = None
    stats: Dict[str, Any] = field(default_factory=dict)


class MCPManagerError(Exception):
    """MCP管理器异常"""
    pass


class MCPManager:
    """MCP管理器
    
    统一管理MCP客户端和服务器的生命周期，提供高级API
    """
    
    def __init__(self, config: MCPManagerConfig):
        """
        初始化MCP管理器
        
        Args:
            config: 管理器配置
        """
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
        self.logger.setLevel(getattr(logging, config.log_level.upper()))
        
        # 配置日志文件
        if config.log_file:
            handler = logging.FileHandler(config.log_file)
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
        
        # 管理器状态
        self.state = MCPManagerState.STOPPED
        
        # 端点管理
        self.clients: Dict[str, MCPClient] = {}
        self.servers: Dict[str, MCPServer] = {}
        self.endpoint_status: Dict[str, MCPEndpointStatus] = {}
        
        # 任务管理
        self._health_check_task: Optional[asyncio.Task] = None
        self._endpoint_tasks: Dict[str, asyncio.Task] = {}
        
        # 消息验证器
        self.validator = MCPMessageValidator()
        
        # 统计信息
        self.stats = {
            'start_time': None,
            'clients_count': 0,
            'servers_count': 0,
            'total_connections': 0,
            'active_connections': 0,
            'messages_processed': 0,
            'errors_count': 0,
            'tools_called': 0,
            'resources_read': 0,
            'prompts_used': 0
        }
        
        # 事件处理器
        self.event_handlers: Dict[str, List[Callable]] = {
            'endpoint_connected': [],
            'endpoint_disconnected': [],
            'endpoint_error': [],
            'tool_called': [],
            'resource_read': [],
            'prompt_used': [],
            'message_received': [],
            'message_sent': []
        }
    
    async def start(self) -> None:
        """
        启动MCP管理器
        """
        if self.state != MCPManagerState.STOPPED:
            raise MCPManagerError(f"管理器状态错误: {self.state}")
        
        try:
            self.state = MCPManagerState.STARTING
            self.stats['start_time'] = datetime.now()
            
            # 初始化端点状态
            for endpoint_config in self.config.endpoints:
                if endpoint_config.enabled:
                    self.endpoint_status[endpoint_config.name] = MCPEndpointStatus(
                        name=endpoint_config.name,
                        type=endpoint_config.type,
                        state=(
                            MCPClientState.DISCONNECTED
                            if endpoint_config.type == MCPConnectionType.CLIENT
                            else MCPServerState.STOPPED
                        ),
                        enabled=True
                    )
            
            # 启动自动启动的端点
            for endpoint_config in self.config.endpoints:
                if endpoint_config.enabled and endpoint_config.auto_start:
                    await self._start_endpoint(endpoint_config)
            
            # 启动健康检查
            if self.config.enable_health_checks:
                self._health_check_task = asyncio.create_task(self._health_check_loop())
            
            self.state = MCPManagerState.RUNNING
            self.logger.info(f"MCP管理器已启动，管理 {len(self.endpoint_status)} 个端点")
            
        except Exception as e:
            self.state = MCPManagerState.ERROR
            self.logger.error(f"启动MCP管理器失败: {e}")
            raise MCPManagerError(f"启动失败: {str(e)}")
    
    async def stop(self) -> None:
        """
        停止MCP管理器
        """
        if self.state == MCPManagerState.STOPPED:
            return
        
        try:
            self.state = MCPManagerState.STOPPING
            
            # 停止健康检查
            if self._health_check_task:
                self._health_check_task.cancel()
                try:
                    await self._health_check_task
                except asyncio.CancelledError:
                    pass
                self._health_check_task = None
            
            # 停止所有端点任务
            for task in self._endpoint_tasks.values():
                task.cancel()
            
            if self._endpoint_tasks:
                await asyncio.gather(
                    *self._endpoint_tasks.values(),
                    return_exceptions=True
                )
            self._endpoint_tasks.clear()
            
            # 停止所有客户端
            for client in self.clients.values():
                try:
                    await client.disconnect()
                except Exception as e:
                    self.logger.error(f"停止客户端失败: {e}")
            self.clients.clear()
            
            # 停止所有服务器
            for server in self.servers.values():
                try:
                    await server.stop()
                except Exception as e:
                    self.logger.error(f"停止服务器失败: {e}")
            self.servers.clear()
            
            # 清理状态
            self.endpoint_status.clear()
            
            self.state = MCPManagerState.STOPPED
            self.logger.info("MCP管理器已停止")
            
        except Exception as e:
            self.state = MCPManagerState.ERROR
            self.logger.error(f"停止MCP管理器失败: {e}")
            raise MCPManagerError(f"停止失败: {str(e)}")
    
    async def start_endpoint(self, name: str) -> bool:
        """
        启动指定端点
        
        Args:
            name: 端点名称
        
        Returns:
            bool: 启动是否成功
        """
        endpoint_config = self._get_endpoint_config(name)
        if not endpoint_config:
            raise MCPManagerError(f"端点配置不存在: {name}")
        
        return await self._start_endpoint(endpoint_config)
    
    async def stop_endpoint(self, name: str) -> bool:
        """
        停止指定端点
        
        Args:
            name: 端点名称
        
        Returns:
            bool: 停止是否成功
        """
        try:
            # 停止端点任务
            if name in self._endpoint_tasks:
                self._endpoint_tasks[name].cancel()
                try:
                    await self._endpoint_tasks[name]
                except asyncio.CancelledError:
                    pass
                del self._endpoint_tasks[name]
            
            # 停止客户端
            if name in self.clients:
                await self.clients[name].disconnect()
                del self.clients[name]
                self.stats['clients_count'] -= 1
            
            # 停止服务器
            if name in self.servers:
                await self.servers[name].stop()
                del self.servers[name]
                self.stats['servers_count'] -= 1
            
            # 更新状态
            if name in self.endpoint_status:
                status = self.endpoint_status[name]
                if status.type == MCPConnectionType.CLIENT:
                    status.state = MCPClientState.DISCONNECTED
                else:
                    status.state = MCPServerState.STOPPED
                status.connected_at = None
                status.last_activity = None
            
            # 触发事件
            await self._emit_event('endpoint_disconnected', {'name': name})
            
            self.logger.info(f"端点已停止: {name}")
            return True
            
        except Exception as e:
            self.logger.error(f"停止端点失败 {name}: {e}")
            await self._emit_event('endpoint_error', {
                'name': name,
                'error': str(e),
                'operation': 'stop'
            })
            return False
    
    async def restart_endpoint(self, name: str) -> bool:
        """
        重启指定端点
        
        Args:
            name: 端点名称
        
        Returns:
            bool: 重启是否成功
        """
        await self.stop_endpoint(name)
        await asyncio.sleep(1)  # 等待清理完成
        return await self.start_endpoint(name)
    
    def get_client(self, name: str) -> Optional[MCPClient]:
        """
        获取MCP客户端
        
        Args:
            name: 客户端名称
        
        Returns:
            Optional[MCPClient]: 客户端实例
        """
        return self.clients.get(name)
    
    def get_server(self, name: str) -> Optional[MCPServer]:
        """
        获取MCP服务器
        
        Args:
            name: 服务器名称
        
        Returns:
            Optional[MCPServer]: 服务器实例
        """
        return self.servers.get(name)
    
    def get_endpoint_status(self, name: str) -> Optional[MCPEndpointStatus]:
        """
        获取端点状态
        
        Args:
            name: 端点名称
        
        Returns:
            Optional[MCPEndpointStatus]: 端点状态
        """
        return self.endpoint_status.get(name)
    
    def list_endpoints(self) -> List[MCPEndpointStatus]:
        """
        列出所有端点状态
        
        Returns:
            List[MCPEndpointStatus]: 端点状态列表
        """
        return list(self.endpoint_status.values())
    
    def list_clients(self) -> List[str]:
        """
        列出所有客户端名称
        
        Returns:
            List[str]: 客户端名称列表
        """
        return list(self.clients.keys())
    
    def list_servers(self) -> List[str]:
        """
        列出所有服务器名称
        
        Returns:
            List[str]: 服务器名称列表
        """
        return list(self.servers.keys())
    
    async def call_tool(
        self,
        client_name: str,
        tool_name: str,
        arguments: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        通过指定客户端调用工具
        
        Args:
            client_name: 客户端名称
            tool_name: 工具名称
            arguments: 工具参数
        
        Returns:
            Dict[str, Any]: 工具执行结果
        """
        client = self.get_client(client_name)
        if not client:
            raise MCPManagerError(f"客户端不存在: {client_name}")
        
        try:
            result = await client.call_tool(tool_name, arguments)
            
            # 更新统计
            self.stats['tools_called'] += 1
            
            # 触发事件
            await self._emit_event('tool_called', {
                'client': client_name,
                'tool': tool_name,
                'arguments': arguments,
                'result': result
            })
            
            return result
            
        except Exception as e:
            self.stats['errors_count'] += 1
            self.logger.error(f"工具调用失败 {client_name}.{tool_name}: {e}")
            raise
    
    async def read_resource(
        self,
        client_name: str,
        uri: str
    ) -> Dict[str, Any]:
        """
        通过指定客户端读取资源
        
        Args:
            client_name: 客户端名称
            uri: 资源URI
        
        Returns:
            Dict[str, Any]: 资源内容
        """
        client = self.get_client(client_name)
        if not client:
            raise MCPManagerError(f"客户端不存在: {client_name}")
        
        try:
            result = await client.read_resource(uri)
            
            # 更新统计
            self.stats['resources_read'] += 1
            
            # 触发事件
            await self._emit_event('resource_read', {
                'client': client_name,
                'uri': uri,
                'result': result
            })
            
            return result
            
        except Exception as e:
            self.stats['errors_count'] += 1
            self.logger.error(f"资源读取失败 {client_name}.{uri}: {e}")
            raise
    
    async def get_prompt(
        self,
        client_name: str,
        prompt_name: str,
        arguments: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        通过指定客户端获取提示词
        
        Args:
            client_name: 客户端名称
            prompt_name: 提示词名称
            arguments: 提示词参数
        
        Returns:
            Dict[str, Any]: 提示词内容
        """
        client = self.get_client(client_name)
        if not client:
            raise MCPManagerError(f"客户端不存在: {client_name}")
        
        try:
            result = await client.get_prompt(prompt_name, arguments)
            
            # 更新统计
            self.stats['prompts_used'] += 1
            
            # 触发事件
            await self._emit_event('prompt_used', {
                'client': client_name,
                'prompt': prompt_name,
                'arguments': arguments,
                'result': result
            })
            
            return result
            
        except Exception as e:
            self.stats['errors_count'] += 1
            self.logger.error(f"提示词获取失败 {client_name}.{prompt_name}: {e}")
            raise
    
    def register_event_handler(
        self,
        event_type: str,
        handler: Callable[[Dict[str, Any]], None]
    ) -> None:
        """
        注册事件处理器
        
        Args:
            event_type: 事件类型
            handler: 事件处理函数
        """
        if event_type not in self.event_handlers:
            self.event_handlers[event_type] = []
        
        self.event_handlers[event_type].append(handler)
    
    def unregister_event_handler(
        self,
        event_type: str,
        handler: Callable[[Dict[str, Any]], None]
    ) -> None:
        """
        注销事件处理器
        
        Args:
            event_type: 事件类型
            handler: 事件处理函数
        """
        if event_type in self.event_handlers:
            if handler in self.event_handlers[event_type]:
                self.event_handlers[event_type].remove(handler)
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取管理器统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        # 更新活跃连接数
        active_connections = 0
        for status in self.endpoint_status.values():
            if status.type == MCPConnectionType.CLIENT:
                if status.state in [MCPClientState.CONNECTED, MCPClientState.AUTHENTICATED]:
                    active_connections += 1
            else:
                if status.state == MCPServerState.RUNNING:
                    active_connections += 1
        
        self.stats['active_connections'] = active_connections
        
        return {
            **self.stats,
            'manager_state': self.state.value,
            'endpoints_count': len(self.endpoint_status),
            'uptime': (
                (datetime.now() - self.stats['start_time']).total_seconds()
                if self.stats['start_time'] else 0
            )
        }
    
    async def _start_endpoint(self, endpoint_config: MCPEndpointConfig) -> bool:
        """
        启动端点
        
        Args:
            endpoint_config: 端点配置
        
        Returns:
            bool: 启动是否成功
        """
        name = endpoint_config.name
        
        try:
            if endpoint_config.type == MCPConnectionType.CLIENT:
                # 启动客户端
                if not endpoint_config.client_config:
                    raise MCPManagerError(f"客户端配置缺失: {name}")
                
                client = MCPClient(endpoint_config.client_config)
                success = await client.connect()
                
                if success:
                    self.clients[name] = client
                    self.stats['clients_count'] += 1
                    
                    # 更新状态
                    if name in self.endpoint_status:
                        status = self.endpoint_status[name]
                        status.state = MCPClientState.AUTHENTICATED
                        status.connected_at = datetime.now()
                        status.last_activity = datetime.now()
                        status.error_count = 0
                        status.last_error = None
                    
                    # 启动监控任务
                    self._endpoint_tasks[name] = asyncio.create_task(
                        self._monitor_client(name, client)
                    )
                    
                    # 触发事件
                    await self._emit_event('endpoint_connected', {'name': name, 'type': 'client'})
                    
                    self.logger.info(f"客户端已启动: {name}")
                    return True
                else:
                    self.logger.error(f"客户端启动失败: {name}")
                    return False
            
            else:
                # 启动服务器
                if not endpoint_config.server_config:
                    raise MCPManagerError(f"服务器配置缺失: {name}")
                
                server = MCPServer(endpoint_config.server_config)
                await server.start()
                
                self.servers[name] = server
                self.stats['servers_count'] += 1
                
                # 更新状态
                if name in self.endpoint_status:
                    status = self.endpoint_status[name]
                    status.state = MCPServerState.RUNNING
                    status.connected_at = datetime.now()
                    status.last_activity = datetime.now()
                    status.error_count = 0
                    status.last_error = None
                
                # 启动监控任务
                self._endpoint_tasks[name] = asyncio.create_task(
                    self._monitor_server(name, server)
                )
                
                # 触发事件
                await self._emit_event('endpoint_connected', {'name': name, 'type': 'server'})
                
                self.logger.info(f"服务器已启动: {name}")
                return True
                
        except Exception as e:
            self.logger.error(f"启动端点失败 {name}: {e}")
            
            # 更新错误状态
            if name in self.endpoint_status:
                status = self.endpoint_status[name]
                status.error_count += 1
                status.last_error = str(e)
            
            # 触发事件
            await self._emit_event('endpoint_error', {
                'name': name,
                'error': str(e),
                'operation': 'start'
            })
            
            return False
    
    async def _monitor_client(self, name: str, client: MCPClient) -> None:
        """
        监控客户端状态
        
        Args:
            name: 客户端名称
            client: 客户端实例
        """
        try:
            while name in self.clients:
                await asyncio.sleep(30)  # 每30秒检查一次
                
                # 更新状态
                if name in self.endpoint_status:
                    status = self.endpoint_status[name]
                    status.state = client.state
                    status.last_activity = datetime.now()
                    status.stats = client.get_stats()
                
                # 检查连接状态
                if client.state == MCPClientState.ERROR:
                    self.logger.warning(f"客户端连接异常: {name}")
                    break
                    
        except asyncio.CancelledError:
            pass
        except Exception as e:
            self.logger.error(f"客户端监控异常 {name}: {e}")
    
    async def _monitor_server(self, name: str, server: MCPServer) -> None:
        """
        监控服务器状态
        
        Args:
            name: 服务器名称
            server: 服务器实例
        """
        try:
            while name in self.servers:
                await asyncio.sleep(30)  # 每30秒检查一次
                
                # 更新状态
                if name in self.endpoint_status:
                    status = self.endpoint_status[name]
                    status.state = server.state
                    status.last_activity = datetime.now()
                    status.stats = server.get_stats()
                
                # 检查服务器状态
                if server.state == MCPServerState.ERROR:
                    self.logger.warning(f"服务器状态异常: {name}")
                    break
                    
        except asyncio.CancelledError:
            pass
        except Exception as e:
            self.logger.error(f"服务器监控异常 {name}: {e}")
    
    async def _health_check_loop(self) -> None:
        """
        健康检查循环
        """
        try:
            while self.state == MCPManagerState.RUNNING:
                await asyncio.sleep(self.config.health_check_interval)
                
                # 检查所有端点健康状态
                for name, status in self.endpoint_status.items():
                    if not status.enabled:
                        continue
                    
                    endpoint_config = self._get_endpoint_config(name)
                    if not endpoint_config:
                        continue
                    
                    # 检查是否需要重启
                    should_restart = False
                    
                    if status.type == MCPConnectionType.CLIENT:
                        if name in self.clients:
                            client = self.clients[name]
                            if client.state == MCPClientState.ERROR:
                                should_restart = True
                        else:
                            should_restart = True
                    else:
                        if name in self.servers:
                            server = self.servers[name]
                            if server.state == MCPServerState.ERROR:
                                should_restart = True
                        else:
                            should_restart = True
                    
                    # 执行重启
                    if should_restart and status.error_count < endpoint_config.retry_attempts:
                        self.logger.info(f"尝试重启端点: {name}")
                        await self.restart_endpoint(name)
                        
        except asyncio.CancelledError:
            pass
        except Exception as e:
            self.logger.error(f"健康检查循环异常: {e}")
    
    def _get_endpoint_config(self, name: str) -> Optional[MCPEndpointConfig]:
        """
        获取端点配置
        
        Args:
            name: 端点名称
        
        Returns:
            Optional[MCPEndpointConfig]: 端点配置
        """
        for config in self.config.endpoints:
            if config.name == name:
                return config
        return None
    
    async def _emit_event(self, event_type: str, data: Dict[str, Any]) -> None:
        """
        触发事件
        
        Args:
            event_type: 事件类型
            data: 事件数据
        """
        if event_type in self.event_handlers:
            for handler in self.event_handlers[event_type]:
                try:
                    if asyncio.iscoroutinefunction(handler):
                        await handler(data)
                    else:
                        handler(data)
                except Exception as e:
                    self.logger.error(f"事件处理器异常 {event_type}: {e}")
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.start()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.stop()