#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A2A多智能体系统 ADK工作流Runner

支持多智能体协作，包含用户权限控制
"""

import logging
import asyncio
from typing import Dict, List, Optional, Any, Union, Callable, Awaitable, Tuple
from datetime import datetime
import uuid

from google.ai.generativelanguage import Content
from google.genai import types
from google.adk.agents.llm_agent import Agent
from google.adk.events.event import Event

from app.models.workflow import Workflow, WorkflowExecution, WorkflowStep
from app.models.agent import Agent as AgentModel
from app.models.task import Task, TaskExecution
from app.core.logging import get_logger

from .base_runner import BaseRunner
from .agent_runner import AgentRunner
from ..services.database_session_service import DatabaseSessionService
from ..services.database_memory_service import DatabaseMemoryService
from ..services.database_artifact_service import DatabaseArtifactService

class WorkflowRunner(BaseRunner):
    """
    工作流Runner，支持多智能体协作，包含用户权限控制
    
    提供以下功能：
    1. 多智能体协作执行
    2. 工作流状态管理
    3. 节点执行控制
    4. 工作流执行历史记录
    5. 工作流执行指标收集
    """
    
    def __init__(
        self,
        user_id: int,
        owner_id: int,
        workflow_id: int,
        workflow_instance: Optional[Agent] = None,
        workflow_config: Optional[Dict[str, Any]] = None,
        session: Optional[DatabaseSessionService] = None,
        memory_service: Optional[DatabaseMemoryService] = None,
        artifact_service: Optional[DatabaseArtifactService] = None,
        task_id: Optional[int] = None,
        execution_id: Optional[str] = None,
        config: Optional[Dict[str, Any]] = None,
        event_handlers: Optional[Dict[str, List[Callable[[Any], Awaitable[None]]]]] = None,
        logger: Optional[logging.Logger] = None
    ):
        """
        初始化工作流Runner
        
        Args:
            user_id: 用户ID
            owner_id: 拥有者ID
            workflow_id: 工作流ID
            workflow_instance: ADK工作流实例
            workflow_config: 工作流配置
            session: 数据库会话服务
            memory_service: 数据库内存服务
            artifact_service: 数据库工件服务
            task_id: 关联的任务ID
            execution_id: 执行ID
            config: Runner配置
            event_handlers: 事件处理器字典
            logger: 日志记录器
        """
        # 如果没有提供workflow_instance，创建一个占位符
        if workflow_instance is None:
            # 这里应该根据workflow_id创建实际的ADK Agent实例
            # 暂时使用None，在实际使用时需要实现
            workflow_instance = None
        
        super().__init__(
            user_id=user_id,
            owner_id=owner_id,
            agent_instance=workflow_instance,
            session=session,
            task_id=task_id,
            execution_id=execution_id,
            config=config,
            event_handlers=event_handlers,
            logger=logger
        )
        
        self.workflow_id = workflow_id
        self.workflow_config = workflow_config or {}
        
        # 创建内存服务和工件服务
        self.memory_service = memory_service or DatabaseMemoryService(user_id=user_id, owner_id=owner_id)
        self.artifact_service = artifact_service or DatabaseArtifactService(user_id=user_id, owner_id=owner_id)
        
        # 工作流执行状态
        self.workflow_execution = None
        self.current_node = None
        self.completed_nodes = []
        self.failed_nodes = []
        
        # 智能体Runner池
        self.agent_runners: Dict[int, AgentRunner] = {}
        
        # 执行指标
        self.metrics = {
            "total_nodes": 0,
            "completed_nodes": 0,
            "failed_nodes": 0,
            "total_tokens": 0,
            "total_latency": 0,
            "total_tool_calls": 0,
            "parallel_executions": 0
        }
        
        self.logger.info(f"WorkflowRunner {self.execution_id} 已初始化，工作流ID: {workflow_id}")
    
    async def _load_workflow_model(self) -> Optional[Workflow]:
        """
        加载工作流模型
        
        Returns:
            Optional[Workflow]: 工作流模型
        """
        try:
            workflow_model = await Workflow.get_by_id(self.workflow_id)
            if not workflow_model:
                self.logger.warning(f"工作流不存在: {self.workflow_id}")
                return None
            
            # 检查用户是否有权限访问此工作流
            if workflow_model.user_id != self.user_id and workflow_model.owner_id != self.owner_id and not workflow_model.is_public:
                self.logger.warning(f"用户 {self.user_id} 没有权限访问工作流 {self.workflow_id}")
                return None
            
            return workflow_model
        except Exception as e:
            self.logger.error(f"加载工作流模型错误: {str(e)}")
            return None
    
    async def _load_workflow_nodes(self) -> List[WorkflowStep]:
        """
        加载工作流节点
        
        Returns:
            List[WorkflowStep]: 工作流节点列表
        """
        try:
            nodes = await WorkflowStep.get_by_workflow_id(self.workflow_id)
            return nodes or []
        except Exception as e:
            self.logger.error(f"加载工作流节点错误: {str(e)}")
            return []
    
    async def _create_workflow_execution(self) -> Optional[WorkflowExecution]:
        """
        创建工作流执行记录
        
        Returns:
            Optional[WorkflowExecution]: 工作流执行记录
        """
        try:
            # 检查工作流是否存在
            workflow = await self._load_workflow_model()
            if not workflow:
                return None
            
            # 创建工作流执行记录
            execution = WorkflowExecution(
                workflow_id=self.workflow_id,
                user_id=self.user_id,
                owner_id=self.owner_id,
                execution_id=self.execution_id,
                status="running",
                config=self.config,
                metrics={}
            )
            
            await execution.save()
            self.workflow_execution = execution
            return execution
        except Exception as e:
            self.logger.error(f"创建工作流执行记录错误: {str(e)}")
            return None
    
    async def _update_workflow_execution(self, status: str) -> None:
        """
        更新工作流执行记录
        
        Args:
            status: 执行状态
        """
        if not self.workflow_execution:
            return
        
        try:
            # 更新状态和指标
            self.workflow_execution.status = status
            self.workflow_execution.metrics = self.metrics
            self.workflow_execution.end_time = datetime.now() if status in ["completed", "failed", "stopped"] else None
            
            await self.workflow_execution.save()
        except Exception as e:
            self.logger.error(f"更新工作流执行记录错误: {str(e)}")
    
    async def _create_agent_runner(self, agent_id: int, node_config: Dict[str, Any]) -> Optional[AgentRunner]:
        """
        创建智能体Runner
        
        Args:
            agent_id: 智能体ID
            node_config: 节点配置
            
        Returns:
            Optional[AgentRunner]: 智能体Runner
        """
        try:
            # 检查智能体是否存在
            agent_model = await AgentModel.get_by_id(agent_id)
            if not agent_model:
                self.logger.warning(f"智能体不存在: {agent_id}")
                return None
            
            # 检查用户是否有权限访问此智能体
            if agent_model.user_id != self.user_id and agent_model.owner_id != self.owner_id and not agent_model.is_public:
                self.logger.warning(f"用户 {self.user_id} 没有权限访问智能体 {agent_id}")
                return None
            
            # 创建ADK Agent实例（这里需要根据实际情况实现）
            # 暂时使用None，在实际使用时需要实现
            agent_instance = None
            
            # 创建智能体Runner
            agent_runner = AgentRunner(
                user_id=self.user_id,
                owner_id=self.owner_id,
                agent_instance=agent_instance,
                agent_id=agent_id,
                agent_config=node_config,
                session=self.session,
                memory_service=self.memory_service,
                artifact_service=self.artifact_service,
                task_id=self.task_id,
                execution_id=f"{self.execution_id}_agent_{agent_id}",
                config=node_config
            )
            
            return agent_runner
        except Exception as e:
            self.logger.error(f"创建智能体Runner错误: {str(e)}")
            return None
    
    async def _execute_node(self, node: WorkflowStep, input_data: Any) -> Tuple[bool, Any]:
        """
        执行工作流节点
        
        Args:
            node: 工作流节点
            input_data: 输入数据
            
        Returns:
            Tuple[bool, Any]: (是否成功, 输出数据)
        """
        try:
            self.current_node = node
            self.logger.info(f"开始执行节点: {node.name} (ID: {node.id})")
            
            # 获取或创建智能体Runner
            if node.agent_id not in self.agent_runners:
                agent_runner = await self._create_agent_runner(node.agent_id, node.config or {})
                if not agent_runner:
                    self.logger.error(f"无法创建智能体Runner，节点: {node.name}")
                    return False, None
                self.agent_runners[node.agent_id] = agent_runner
            
            agent_runner = self.agent_runners[node.agent_id]
            
            # 执行智能体
            result = await agent_runner.run(input_data)
            
            # 更新指标
            agent_state = agent_runner.get_state()
            if "metrics" in agent_state:
                agent_metrics = agent_state["metrics"]
                self.metrics["total_tokens"] += agent_metrics.get("total_tokens", 0)
                self.metrics["total_latency"] += agent_metrics.get("latency", 0)
                self.metrics["total_tool_calls"] += agent_metrics.get("tool_calls", 0)
            
            self.completed_nodes.append(node.id)
            self.metrics["completed_nodes"] += 1
            
            self.logger.info(f"节点执行成功: {node.name}")
            return True, result
        except Exception as e:
            self.logger.error(f"节点执行失败: {node.name}, 错误: {str(e)}")
            self.failed_nodes.append(node.id)
            self.metrics["failed_nodes"] += 1
            return False, None
    
    async def _execute_parallel_nodes(self, nodes: List[WorkflowStep], input_data: Any) -> Dict[int, Any]:
        """
        并行执行多个节点
        
        Args:
            nodes: 节点列表
            input_data: 输入数据
            
        Returns:
            Dict[int, Any]: 节点ID到输出数据的映射
        """
        tasks = []
        for node in nodes:
            task = asyncio.create_task(self._execute_node(node, input_data))
            tasks.append((node.id, task))
        
        results = {}
        self.metrics["parallel_executions"] += 1
        
        # 等待所有任务完成
        for node_id, task in tasks:
            try:
                success, result = await task
                if success:
                    results[node_id] = result
            except Exception as e:
                self.logger.error(f"并行节点执行错误，节点ID: {node_id}, 错误: {str(e)}")
        
        return results
    
    async def _get_next_nodes(self, current_node_id: Optional[int], nodes: List[WorkflowStep]) -> List[WorkflowStep]:
        """
        获取下一个要执行的节点
        
        Args:
            current_node_id: 当前节点ID
            nodes: 所有节点列表
            
        Returns:
            List[WorkflowNode]: 下一个要执行的节点列表
        """
        if current_node_id is None:
            # 返回起始节点（没有依赖的节点）
            return [node for node in nodes if not node.dependencies]
        
        # 查找依赖当前节点的节点
        next_nodes = []
        for node in nodes:
            if node.id in self.completed_nodes:
                continue
            
            # 检查节点的所有依赖是否都已完成
            if node.dependencies:
                dependencies = node.dependencies if isinstance(node.dependencies, list) else [node.dependencies]
                if all(dep_id in self.completed_nodes for dep_id in dependencies):
                    next_nodes.append(node)
            elif current_node_id and current_node_id in (node.dependencies or []):
                next_nodes.append(node)
        
        return next_nodes
    
    async def run(
        self, 
        input_content: Union[str, Content, types.Content],
        stream: bool = False
    ) -> Any:
        """
        运行工作流Runner
        
        Args:
            input_content: 输入内容
            stream: 是否使用流式输出
            
        Returns:
            Any: 运行结果
        """
        # 加载工作流模型
        workflow_model = await self._load_workflow_model()
        if not workflow_model:
            raise ValueError(f"无法加载工作流: {self.workflow_id}")
        
        # 加载工作流节点
        nodes = await self._load_workflow_nodes()
        if not nodes:
            raise ValueError(f"工作流没有节点: {self.workflow_id}")
        
        # 创建工作流执行记录
        execution = await self._create_workflow_execution()
        
        # 初始化指标
        self.metrics["total_nodes"] = len(nodes)
        
        try:
            # 检查权限
            has_permission = await self._check_permission()
            if not has_permission:
                raise PermissionError(f"用户 {self.user_id} 没有权限执行工作流 {self.workflow_id}")
            
            # 设置开始时间和状态
            self.start_time = datetime.now()
            self.state = "running"
            
            current_data = input_content
            current_node_id = None
            
            # 执行工作流
            while True:
                # 获取下一个要执行的节点
                next_nodes = await self._get_next_nodes(current_node_id, nodes)
                
                if not next_nodes:
                    # 没有更多节点要执行
                    break
                
                # 检查是否可以并行执行
                if len(next_nodes) == 1:
                    # 单个节点执行
                    node = next_nodes[0]
                    success, result = await self._execute_node(node, current_data)
                    if not success:
                        raise RuntimeError(f"节点执行失败: {node.name}")
                    current_data = result
                    current_node_id = node.id
                else:
                    # 并行执行多个节点
                    results = await self._execute_parallel_nodes(next_nodes, current_data)
                    if not results:
                        raise RuntimeError("所有并行节点执行失败")
                    
                    # 合并结果（这里可以根据实际需求实现不同的合并策略）
                    current_data = results
                    current_node_id = list(results.keys())[-1]  # 使用最后一个节点的ID
            
            # 更新工作流执行记录
            await self._update_workflow_execution("completed")
            
            self.logger.info(f"工作流执行完成: {self.workflow_id}")
            return current_data
        except Exception as e:
            # 更新工作流执行记录为失败
            await self._update_workflow_execution("failed")
            self.logger.error(f"工作流执行失败: {str(e)}")
            raise e
        finally:
            # 设置结束时间
            self.end_time = datetime.now()
            if self.state == "running":
                self.state = "completed"
    
    async def stop(self) -> None:
        """
        停止工作流Runner
        """
        # 停止所有智能体Runner
        for agent_runner in self.agent_runners.values():
            await agent_runner.stop()
        
        await super().stop()
        
        # 更新工作流执行记录
        await self._update_workflow_execution("stopped")
    
    def get_state(self) -> Dict[str, Any]:
        """
        获取工作流Runner状态
        
        Returns:
            Dict[str, Any]: Runner状态信息
        """
        state = super().get_state()
        
        # 添加工作流特定信息
        state.update({
            "workflow_id": self.workflow_id,
            "current_node": self.current_node.id if self.current_node else None,
            "completed_nodes": self.completed_nodes,
            "failed_nodes": self.failed_nodes,
            "metrics": self.metrics,
            "agent_runners": {agent_id: runner.get_state() for agent_id, runner in self.agent_runners.items()}
        })
        
        return state