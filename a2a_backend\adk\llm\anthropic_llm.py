#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A2A多智能体系统 Anthropic Claude LLM集成

基于Google ADK LiteLLM的扩展实现
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any, AsyncIterator, Union
from datetime import datetime

from google.adk.models.lite_llm import LiteLlm as ADKLiteLLM
from google.genai.types import Content, Part, GenerateContentResponse
import anthropic
from anthropic import AsyncAnthropic

from app.services.llm_service import LLMConfig, LLMProvider
from .base_llm import BaseLLM, StreamingResponse


class AnthropicLLM(BaseLLM):
    """
    Anthropic Claude LLM集成
    
    基于Google ADK LiteLLM的扩展实现，支持流式输出和自定义配置
    """
    
    def __init__(self, config: LLMConfig, user_id: int):
        """
        初始化Anthropic LLM
        
        Args:
            config: LLM配置
            user_id: 用户ID
        """
        super().__init__(config, user_id)
        
        self.logger = logging.getLogger(__name__)
        self._adk_llm: Optional[ADKLiteLLM] = None
        self._client: Optional[AsyncAnthropic] = None
        
        # 初始化Anthropic客户端
        self._initialize_client()
        
        self.logger.info(f"Anthropic LLM初始化完成，用户: {user_id}, 模型: {config.model_name}")
    
    def _initialize_client(self) -> None:
        """
        初始化Anthropic客户端
        """
        try:
            # 创建异步Anthropic客户端
            client_kwargs = {
                "api_key": self.config.api_key,
                "timeout": self.config.timeout,
            }
            
            # 如果有自定义端点，使用自定义端点
            if self.config.api_endpoint:
                client_kwargs["base_url"] = self.config.api_endpoint
            
            self._client = AsyncAnthropic(**client_kwargs)
            
            # 创建ADK LiteLLM实例
            self._adk_llm = ADKLiteLLM(
                model_name=f"anthropic/{self.config.model_name}",
                api_key=self.config.api_key
            )
            
        except Exception as e:
            self.logger.error(f"初始化Anthropic客户端失败: {e}")
            raise
    
    async def generate_content(
        self,
        messages: List[Content],
        stream: bool = False,
        **kwargs
    ) -> Union[GenerateContentResponse, AsyncIterator[GenerateContentResponse]]:
        """
        生成内容
        
        Args:
            messages: 消息列表
            stream: 是否流式输出
            **kwargs: 其他参数
        
        Returns:
            Union[GenerateContentResponse, AsyncIterator[GenerateContentResponse]]: 生成的内容
        """
        try:
            # 记录请求开始
            start_time = datetime.now()
            self.logger.info(f"开始生成内容，用户: {self.user_id}, 流式: {stream}")
            
            # 检查速率限制
            if not await self._check_rate_limit():
                raise Exception("超过速率限制")
            
            # 转换消息格式
            anthropic_messages = self._convert_messages_to_anthropic_format(messages)
            
            if stream:
                return self._generate_content_stream(anthropic_messages, **kwargs)
            else:
                return await self._generate_content_sync(anthropic_messages, **kwargs)
                
        except Exception as e:
            self.logger.error(f"生成内容失败: {e}")
            # 记录失败统计
            await self._record_usage(0, 0.0, False)
            raise
    
    async def _generate_content_sync(
        self,
        messages: List[Dict[str, Any]],
        **kwargs
    ) -> GenerateContentResponse:
        """
        同步生成内容
        
        Args:
            messages: Anthropic格式的消息列表
            **kwargs: 其他参数
        
        Returns:
            GenerateContentResponse: 生成的内容
        """
        try:
            # 构建请求参数
            request_params = {
                "model": self.config.model_name,
                "messages": messages,
                "max_tokens": self.config.max_tokens,
                "temperature": self.config.temperature,
                "top_p": self.config.extra_params.get("top_p", 1.0),
            }
            
            # 添加系统提示
            if "system" in kwargs:
                request_params["system"] = kwargs["system"]
            
            # 添加工具调用支持
            if "tools" in kwargs:
                request_params["tools"] = kwargs["tools"]
            if "tool_choice" in kwargs:
                request_params["tool_choice"] = kwargs["tool_choice"]
            
            # 调用Anthropic API
            response = await self._client.messages.create(**request_params)
            
            # 转换为ADK格式
            adk_response = self._convert_to_adk_response(response)
            
            # 记录使用统计
            tokens_used = self._count_tokens_from_anthropic_response(response)
            cost = self._calculate_cost(tokens_used)
            await self._record_usage(tokens_used, cost, True)
            
            self.logger.info(f"内容生成完成，用户: {self.user_id}, tokens: {tokens_used}")
            return adk_response
            
        except Exception as e:
            self.logger.error(f"同步生成内容失败: {e}")
            raise
    
    async def _generate_content_stream(
        self,
        messages: List[Dict[str, Any]],
        **kwargs
    ) -> AsyncIterator[GenerateContentResponse]:
        """
        流式生成内容
        
        Args:
            messages: Anthropic格式的消息列表
            **kwargs: 其他参数
        
        Yields:
            GenerateContentResponse: 生成的内容片段
        """
        try:
            total_tokens = 0
            accumulated_content = ""
            
            # 构建请求参数
            request_params = {
                "model": self.config.model_name,
                "messages": messages,
                "max_tokens": self.config.max_tokens,
                "temperature": self.config.temperature,
                "top_p": self.config.extra_params.get("top_p", 1.0),
                "stream": True,
            }
            
            # 添加系统提示
            if "system" in kwargs:
                request_params["system"] = kwargs["system"]
            
            # 添加工具调用支持
            if "tools" in kwargs:
                request_params["tools"] = kwargs["tools"]
            if "tool_choice" in kwargs:
                request_params["tool_choice"] = kwargs["tool_choice"]
            
            # 调用Anthropic流式API
            async with self._client.messages.stream(**request_params) as stream:
                async for event in stream:
                    if event.type == "content_block_delta":
                        if hasattr(event.delta, 'text') and event.delta.text:
                            content_delta = event.delta.text
                            accumulated_content += content_delta
                            
                            # 转换为ADK格式
                            adk_chunk = self._create_adk_chunk(content_delta, False)
                            
                            # 估算tokens
                            chunk_tokens = await self.estimate_tokens(content_delta)
                            total_tokens += chunk_tokens
                            
                            yield adk_chunk
                    
                    elif event.type == "content_block_stop":
                        # 发送完成标记
                        final_chunk = self._create_adk_chunk("", True)
                        yield final_chunk
                        break
                    
                    elif event.type == "message_stop":
                        # 消息结束
                        break
            
            # 记录使用统计
            cost = self._calculate_cost(total_tokens)
            await self._record_usage(total_tokens, cost, True)
            
            self.logger.info(f"流式内容生成完成，用户: {self.user_id}, tokens: {total_tokens}")
            
        except Exception as e:
            self.logger.error(f"流式生成内容失败: {e}")
            raise
    
    def _convert_messages_to_anthropic_format(self, messages: List[Content]) -> List[Dict[str, Any]]:
        """
        转换消息为Anthropic格式
        
        Args:
            messages: ADK消息列表
        
        Returns:
            List[Dict[str, Any]]: Anthropic格式的消息列表
        """
        try:
            anthropic_messages = []
            
            for message in messages:
                # 确定角色
                role = "user"  # 默认为用户消息
                
                # 处理消息内容
                content_parts = []
                for part in message.parts:
                    if hasattr(part, 'text') and part.text:
                        content_parts.append({
                            "type": "text",
                            "text": part.text
                        })
                    elif hasattr(part, 'inline_data') and part.inline_data:
                        # 处理图片等内联数据
                        if part.inline_data.mime_type.startswith("image/"):
                            content_parts.append({
                                "type": "image",
                                "source": {
                                    "type": "base64",
                                    "media_type": part.inline_data.mime_type,
                                    "data": part.inline_data.data
                                }
                            })
                
                # 如果只有一个文本部分，简化格式
                if len(content_parts) == 1 and content_parts[0]["type"] == "text":
                    content = content_parts[0]["text"]
                else:
                    content = content_parts
                
                anthropic_messages.append({
                    "role": role,
                    "content": content
                })
            
            return anthropic_messages
            
        except Exception as e:
            self.logger.error(f"转换消息为Anthropic格式失败: {e}")
            return []
    
    def _convert_to_adk_response(self, response: Any) -> GenerateContentResponse:
        """
        转换为ADK响应格式
        
        Args:
            response: Anthropic响应
        
        Returns:
            GenerateContentResponse: ADK格式响应
        """
        try:
            # 提取响应内容
            text_content = ""
            tool_calls = None
            
            if hasattr(response, 'content') and response.content:
                for content_block in response.content:
                    if hasattr(content_block, 'text') and content_block.text:
                        text_content += content_block.text
                    elif hasattr(content_block, 'tool_use') and content_block.tool_use:
                        # 处理工具调用
                        if tool_calls is None:
                            tool_calls = []
                        tool_calls.append(content_block.tool_use)
            
            # 创建ADK内容
            parts = []
            if text_content:
                parts.append(Part(text=text_content))
            
            # 处理工具调用
            if tool_calls:
                for tool_call in tool_calls:
                    tool_info = {
                        "tool_call": {
                            "id": tool_call.id,
                            "name": tool_call.name,
                            "input": tool_call.input
                        }
                    }
                    parts.append(Part(text=f"[Tool Call: {tool_info}]"))
            
            content = Content(parts=parts)
            
            # 创建ADK响应
            adk_response = type('GenerateContentResponse', (), {
                'candidates': [type('Candidate', (), {
                    'content': content,
                    'finish_reason': response.stop_reason if hasattr(response, 'stop_reason') else None,
                    'safety_ratings': []
                })()],
                'usage_metadata': type('UsageMetadata', (), {
                    'prompt_token_count': response.usage.input_tokens if hasattr(response, 'usage') and response.usage else 0,
                    'candidates_token_count': response.usage.output_tokens if hasattr(response, 'usage') and response.usage else 0,
                    'total_token_count': (response.usage.input_tokens + response.usage.output_tokens) if hasattr(response, 'usage') and response.usage else 0
                })()
            })()
            
            return adk_response
            
        except Exception as e:
            self.logger.error(f"转换ADK响应格式失败: {e}")
            # 返回一个空的响应
            return type('GenerateContentResponse', (), {
                'candidates': [],
                'usage_metadata': None
            })()
    
    def _create_adk_chunk(self, content: str, is_complete: bool) -> GenerateContentResponse:
        """
        创建ADK格式的流式响应片段
        
        Args:
            content: 内容
            is_complete: 是否完成
        
        Returns:
            GenerateContentResponse: ADK格式响应片段
        """
        try:
            parts = [Part(text=content)] if content else []
            adk_content = Content(parts=parts)
            
            # 创建ADK响应片段
            adk_chunk = type('GenerateContentResponse', (), {
                'candidates': [type('Candidate', (), {
                    'content': adk_content,
                    'finish_reason': 'end_turn' if is_complete else None,
                    'safety_ratings': []
                })()],
                'usage_metadata': None
            })()
            
            return adk_chunk
            
        except Exception as e:
            self.logger.error(f"创建ADK响应片段失败: {e}")
            return type('GenerateContentResponse', (), {
                'candidates': [],
                'usage_metadata': None
            })()
    
    def _count_tokens_from_anthropic_response(self, response: Any) -> int:
        """
        从Anthropic响应中统计token数量
        
        Args:
            response: Anthropic响应
        
        Returns:
            int: token数量
        """
        try:
            if hasattr(response, 'usage') and response.usage:
                return response.usage.input_tokens + response.usage.output_tokens
            
            # 如果没有usage信息，估算token数量
            text_content = ""
            if hasattr(response, 'content') and response.content:
                for content_block in response.content:
                    if hasattr(content_block, 'text') and content_block.text:
                        text_content += content_block.text
            
            # 简单估算：1个token约等于4个字符
            return len(text_content) // 4
            
        except Exception as e:
            self.logger.error(f"统计token数量失败: {e}")
            return 0
    
    def _calculate_cost(self, tokens: int) -> float:
        """
        计算成本
        
        Args:
            tokens: token数量
        
        Returns:
            float: 成本
        """
        try:
            # Anthropic的定价（示例，实际价格可能不同）
            # 这里需要根据实际的定价来计算
            cost_per_1k_tokens = self.config.extra_params.get("cost_per_1k_tokens", 0.008)
            return (tokens / 1000) * cost_per_1k_tokens
            
        except Exception as e:
            self.logger.error(f"计算成本失败: {e}")
            return 0.0
    
    async def get_model_info(self) -> Dict[str, Any]:
        """
        获取模型信息
        
        Returns:
            Dict[str, Any]: 模型信息
        """
        try:
            return {
                "provider": LLMProvider.ANTHROPIC,
                "model_name": self.config.model_name,
                "max_tokens": self.config.max_tokens,
                "temperature": self.config.temperature,
                "supports_streaming": True,
                "supports_function_calling": True,
                "supports_vision": "vision" in self.config.model_name.lower() or "claude-3" in self.config.model_name.lower(),
                "context_window": self._get_context_window_for_model()
            }
            
        except Exception as e:
            self.logger.error(f"获取模型信息失败: {e}")
            return {}
    
    def _get_context_window_for_model(self) -> int:
        """
        根据模型名称获取上下文窗口大小
        
        Returns:
            int: 上下文窗口大小
        """
        model_context_windows = {
            "claude-3-opus": 200000,
            "claude-3-sonnet": 200000,
            "claude-3-haiku": 200000,
            "claude-2.1": 200000,
            "claude-2": 100000,
            "claude-instant": 100000,
        }
        
        for model_prefix, context_window in model_context_windows.items():
            if self.config.model_name.startswith(model_prefix):
                return context_window
        
        # 默认值
        return self.config.extra_params.get("context_window", 100000)
    
    def supports_function_calling(self) -> bool:
        """
        是否支持函数调用
        
        Returns:
            bool: 是否支持函数调用
        """
        # Claude-3系列支持工具调用
        return "claude-3" in self.config.model_name
    
    def supports_vision(self) -> bool:
        """
        是否支持视觉输入
        
        Returns:
            bool: 是否支持视觉输入
        """
        # Claude-3系列支持视觉输入
        return "claude-3" in self.config.model_name
    
    async def validate_config(self) -> bool:
        """
        验证配置
        
        Returns:
            bool: 配置是否有效
        """
        try:
            # 检查必需的配置
            if not self.config.api_key:
                self.logger.error("缺少API密钥")
                return False
            
            if not self.config.model_name:
                self.logger.error("缺少模型名称")
                return False
            
            # 尝试进行简单的API调用来验证配置
            test_messages = [{
                "role": "user",
                "content": "Hello"
            }]
            
            response = await self._client.messages.create(
                model=self.config.model_name,
                messages=test_messages,
                max_tokens=10
            )
            
            if response and response.content:
                self.logger.info("配置验证成功")
                return True
            else:
                self.logger.error("配置验证失败：无效响应")
                return False
                
        except Exception as e:
            self.logger.error(f"配置验证失败: {e}")
            return False
    
    async def cleanup(self) -> None:
        """
        清理资源
        """
        try:
            if self._client:
                await self._client.close()
            self._client = None
            self._adk_llm = None
            self.logger.info(f"Anthropic LLM资源清理完成，用户: {self.user_id}")
            
        except Exception as e:
            self.logger.error(f"清理资源失败: {e}")
    
    @classmethod
    async def create(
        cls,
        config: LLMConfig,
        user_id: int,
        **kwargs
    ) -> "AnthropicLLM":
        """
        创建Anthropic LLM实例
        
        Args:
            config: LLM配置
            user_id: 用户ID
            **kwargs: 其他参数
        
        Returns:
            AnthropicLLM: Anthropic LLM实例
        """
        try:
            instance = cls(config, user_id)
            
            # 验证配置
            if not await instance.validate_config():
                raise Exception("Anthropic LLM配置验证失败")
            
            return instance
            
        except Exception as e:
            logging.getLogger(__name__).error(f"创建Anthropic LLM实例失败: {e}")
            raise