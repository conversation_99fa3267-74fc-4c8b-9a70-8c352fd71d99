#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A2A多智能体系统 响应转换器

ADK响应与API响应转换
"""

import logging
from typing import Dict, List, Optional, Any, Union, AsyncGenerator
from datetime import datetime
import json
import asyncio

from google.ai.generativelanguage import GenerateContentResponse, Candidate
from google.ai.generativelanguage import Content, Part
from google.protobuf.json_format import MessageToDict

from app.models.message import Message
from app.models.task import TaskExecution
from app.models.workflow import WorkflowExecution
from app.core.logging import get_logger
from app.schemas.response import (
    AgentResponse,
    WorkflowResponse,
    StreamResponse,
    ErrorResponse,
    SuccessResponse
)

class ResponseConverter:
    """
    响应转换器，ADK响应与API响应转换
    
    提供以下功能：
    1. ADK GenerateContentResponse到API响应的转换
    2. 数据库执行记录到API响应的转换
    3. 流式响应处理和转换
    4. 错误响应标准化
    5. 响应格式验证和优化
    6. 批量响应转换
    7. 响应缓存和压缩
    8. 响应统计和监控
    """
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        """
        初始化响应转换器
        
        Args:
            logger: 日志记录器
        """
        self.logger = logger or get_logger("response_converter")
        
        # 响应状态映射
        self.status_mapping = {
            "pending": "processing",
            "running": "processing",
            "completed": "success",
            "failed": "error",
            "cancelled": "cancelled",
            "timeout": "timeout"
        }
        
        # 错误类型映射
        self.error_type_mapping = {
            "SAFETY": "content_safety_error",
            "RECITATION": "content_recitation_error",
            "OTHER": "generation_error",
            "PERMISSION_DENIED": "permission_error",
            "QUOTA_EXCEEDED": "quota_error",
            "INVALID_ARGUMENT": "validation_error",
            "INTERNAL": "internal_error",
            "UNAVAILABLE": "service_unavailable"
        }
        
        # 转换统计
        self.conversion_stats = {
            "total_conversions": 0,
            "successful_conversions": 0,
            "failed_conversions": 0,
            "response_types_processed": set(),
            "stream_responses_processed": 0,
            "conversion_history": [],
            "last_conversion": None
        }
        
        self.logger.info("ResponseConverter已初始化")
    
    def _format_timestamp(self, timestamp: Optional[datetime] = None) -> str:
        """
        格式化时间戳
        
        Args:
            timestamp: 时间戳
            
        Returns:
            str: 格式化的时间戳
        """
        if timestamp is None:
            timestamp = datetime.now()
        
        return timestamp.isoformat()
    
    def _extract_text_content(self, content: Content) -> str:
        """
        从Content中提取文本内容
        
        Args:
            content: ADK Content对象
            
        Returns:
            str: 文本内容
        """
        try:
            text_parts = []
            
            for part in content.parts:
                if hasattr(part, 'text') and part.text:
                    text_parts.append(part.text)
            
            return "\n".join(text_parts)
        except Exception as e:
            self.logger.error(f"提取文本内容错误: {str(e)}")
            return ""
    
    def _process_safety_ratings(self, safety_ratings: List[Any]) -> List[Dict[str, Any]]:
        """
        处理安全评级
        
        Args:
            safety_ratings: 安全评级列表
            
        Returns:
            List[Dict[str, Any]]: 处理后的安全评级
        """
        try:
            processed_ratings = []
            
            for rating in safety_ratings:
                rating_dict = MessageToDict(rating)
                processed_ratings.append({
                    "category": rating_dict.get("category", "UNKNOWN"),
                    "probability": rating_dict.get("probability", "UNKNOWN"),
                    "blocked": rating_dict.get("blocked", False)
                })
            
            return processed_ratings
        except Exception as e:
            self.logger.error(f"处理安全评级错误: {str(e)}")
            return []
    
    def _process_citation_metadata(self, citation_metadata: Any) -> Dict[str, Any]:
        """
        处理引用元数据
        
        Args:
            citation_metadata: 引用元数据
            
        Returns:
            Dict[str, Any]: 处理后的引用元数据
        """
        try:
            if not citation_metadata:
                return {}
            
            metadata_dict = MessageToDict(citation_metadata)
            
            return {
                "citation_sources": metadata_dict.get("citationSources", []),
                "citations": metadata_dict.get("citations", [])
            }
        except Exception as e:
            self.logger.error(f"处理引用元数据错误: {str(e)}")
            return {}
    
    def adk_response_to_agent_response(
        self,
        adk_response: GenerateContentResponse,
        task_execution: Optional[TaskExecution] = None
    ) -> AgentResponse:
        """
        将ADK响应转换为智能体响应
        
        Args:
            adk_response: ADK生成内容响应
            task_execution: 任务执行记录
            
        Returns:
            AgentResponse: 智能体响应
        """
        try:
            # 获取第一个候选响应
            if not adk_response.candidates:
                raise ValueError("ADK响应中没有候选内容")
            
            candidate = adk_response.candidates[0]
            
            # 提取文本内容
            content = self._extract_text_content(candidate.content)
            
            # 处理完成原因
            finish_reason = getattr(candidate, 'finish_reason', None)
            if finish_reason:
                finish_reason = str(finish_reason)
            
            # 处理安全评级
            safety_ratings = []
            if hasattr(candidate, 'safety_ratings') and candidate.safety_ratings:
                safety_ratings = self._process_safety_ratings(candidate.safety_ratings)
            
            # 处理引用元数据
            citation_metadata = {}
            if hasattr(candidate, 'citation_metadata') and candidate.citation_metadata:
                citation_metadata = self._process_citation_metadata(candidate.citation_metadata)
            
            # 处理使用统计
            usage_metadata = {}
            if hasattr(adk_response, 'usage_metadata') and adk_response.usage_metadata:
                usage_dict = MessageToDict(adk_response.usage_metadata)
                usage_metadata = {
                    "prompt_token_count": usage_dict.get("promptTokenCount", 0),
                    "candidates_token_count": usage_dict.get("candidatesTokenCount", 0),
                    "total_token_count": usage_dict.get("totalTokenCount", 0)
                }
            
            # 构建响应
            response_data = {
                "status": "success",
                "content": content,
                "finish_reason": finish_reason,
                "safety_ratings": safety_ratings,
                "citation_metadata": citation_metadata,
                "usage_metadata": usage_metadata,
                "timestamp": self._format_timestamp()
            }
            
            # 添加任务执行信息
            if task_execution:
                response_data.update({
                    "execution_id": task_execution.id,
                    "task_id": task_execution.task_id,
                    "agent_id": task_execution.task.agent_id if task_execution.task else None,
                    "execution_time": task_execution.execution_time,
                    "metrics": task_execution.metrics
                })
            
            return AgentResponse(**response_data)
        except Exception as e:
            self.logger.error(f"转换ADK响应到智能体响应错误: {str(e)}")
            raise e
    
    def task_execution_to_agent_response(
        self,
        task_execution: TaskExecution
    ) -> AgentResponse:
        """
        将任务执行记录转换为智能体响应
        
        Args:
            task_execution: 任务执行记录
            
        Returns:
            AgentResponse: 智能体响应
        """
        try:
            # 确定状态
            status = self.status_mapping.get(task_execution.status, task_execution.status)
            
            # 构建基础响应数据
            response_data = {
                "status": status,
                "execution_id": task_execution.id,
                "task_id": task_execution.task_id,
                "content": "",
                "timestamp": self._format_timestamp(task_execution.created_at)
            }
            
            # 添加智能体信息
            if task_execution.task and task_execution.task.agent:
                agent = task_execution.task.agent
                response_data.update({
                    "agent_id": agent.id,
                    "agent_name": agent.name,
                    "agent_type": agent.type,
                    "agent_version": agent.version
                })
            
            # 处理输出数据
            if task_execution.output_data:
                if isinstance(task_execution.output_data, dict):
                    response_data["content"] = task_execution.output_data.get("content", "")
                    response_data["output_data"] = task_execution.output_data
                else:
                    response_data["content"] = str(task_execution.output_data)
            
            # 处理错误信息
            if task_execution.error_message:
                response_data["error_message"] = task_execution.error_message
            
            # 添加执行统计
            response_data.update({
                "execution_time": task_execution.execution_time,
                "metrics": task_execution.metrics,
                "resource_usage": task_execution.resource_usage,
                "started_at": self._format_timestamp(task_execution.started_at) if task_execution.started_at else None,
                "completed_at": self._format_timestamp(task_execution.completed_at) if task_execution.completed_at else None
            })
            
            return AgentResponse(**response_data)
        except Exception as e:
            self.logger.error(f"转换任务执行到智能体响应错误: {str(e)}")
            raise e
    
    def workflow_execution_to_workflow_response(
        self,
        workflow_execution: WorkflowExecution
    ) -> WorkflowResponse:
        """
        将工作流执行记录转换为工作流响应
        
        Args:
            workflow_execution: 工作流执行记录
            
        Returns:
            WorkflowResponse: 工作流响应
        """
        try:
            # 确定状态
            status = self.status_mapping.get(workflow_execution.status, workflow_execution.status)
            
            # 构建基础响应数据
            response_data = {
                "status": status,
                "execution_id": workflow_execution.id,
                "workflow_id": workflow_execution.workflow_id,
                "current_node_id": workflow_execution.current_node_id,
                "timestamp": self._format_timestamp(workflow_execution.created_at)
            }
            
            # 添加工作流信息
            if workflow_execution.workflow:
                workflow = workflow_execution.workflow
                response_data.update({
                    "workflow_name": workflow.name,
                    "workflow_version": workflow.version,
                    "workflow_description": workflow.description
                })
            
            # 处理输出数据
            if workflow_execution.output_data:
                response_data["output_data"] = workflow_execution.output_data
            
            # 处理错误信息
            if workflow_execution.error_message:
                response_data["error_message"] = workflow_execution.error_message
            
            # 添加执行统计
            response_data.update({
                "execution_time": workflow_execution.execution_time,
                "metrics": workflow_execution.metrics,
                "execution_log": workflow_execution.execution_log,
                "started_at": self._format_timestamp(workflow_execution.started_at) if workflow_execution.started_at else None,
                "completed_at": self._format_timestamp(workflow_execution.completed_at) if workflow_execution.completed_at else None
            })
            
            # 处理节点执行状态
            if workflow_execution.execution_log:
                node_statuses = {}
                for log_entry in workflow_execution.execution_log:
                    if isinstance(log_entry, dict) and "node_id" in log_entry:
                        node_id = log_entry["node_id"]
                        node_statuses[node_id] = {
                            "status": log_entry.get("status", "unknown"),
                            "started_at": log_entry.get("started_at"),
                            "completed_at": log_entry.get("completed_at"),
                            "output": log_entry.get("output"),
                            "error": log_entry.get("error")
                        }
                
                response_data["node_statuses"] = node_statuses
            
            return WorkflowResponse(**response_data)
        except Exception as e:
            self.logger.error(f"转换工作流执行到工作流响应错误: {str(e)}")
            raise e
    
    async def create_stream_response(
        self,
        content_generator: AsyncGenerator[str, None],
        execution_id: Optional[int] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> AsyncGenerator[StreamResponse, None]:
        """
        创建流式响应
        
        Args:
            content_generator: 内容生成器
            execution_id: 执行ID
            metadata: 元数据
            
        Yields:
            StreamResponse: 流式响应
        """
        try:
            chunk_index = 0
            total_content = ""
            
            async for content_chunk in content_generator:
                total_content += content_chunk
                
                # 创建流式响应
                stream_response = StreamResponse(
                    chunk_index=chunk_index,
                    content=content_chunk,
                    total_content=total_content,
                    execution_id=execution_id,
                    is_final=False,
                    metadata=metadata or {},
                    timestamp=self._format_timestamp()
                )
                
                yield stream_response
                
                chunk_index += 1
                
                # 更新统计
                self.conversion_stats["stream_responses_processed"] += 1
            
            # 发送最终响应
            final_response = StreamResponse(
                chunk_index=chunk_index,
                content="",
                total_content=total_content,
                execution_id=execution_id,
                is_final=True,
                metadata=metadata or {},
                timestamp=self._format_timestamp()
            )
            
            yield final_response
        except Exception as e:
            self.logger.error(f"创建流式响应错误: {str(e)}")
            
            # 发送错误响应
            error_response = StreamResponse(
                chunk_index=0,
                content="",
                total_content="",
                execution_id=execution_id,
                is_final=True,
                error=str(e),
                metadata=metadata or {},
                timestamp=self._format_timestamp()
            )
            
            yield error_response
    
    def create_error_response(
        self,
        error: Exception,
        execution_id: Optional[int] = None,
        error_type: Optional[str] = None
    ) -> ErrorResponse:
        """
        创建错误响应
        
        Args:
            error: 错误对象
            execution_id: 执行ID
            error_type: 错误类型
            
        Returns:
            ErrorResponse: 错误响应
        """
        try:
            # 确定错误类型
            if error_type is None:
                error_type = type(error).__name__
            
            # 映射错误类型
            mapped_error_type = self.error_type_mapping.get(error_type, "unknown_error")
            
            # 构建错误响应
            error_response = ErrorResponse(
                status="error",
                error_type=mapped_error_type,
                error_message=str(error),
                execution_id=execution_id,
                timestamp=self._format_timestamp()
            )
            
            # 添加详细错误信息
            if hasattr(error, '__dict__'):
                error_response.error_details = {
                    key: str(value) for key, value in error.__dict__.items()
                    if not key.startswith('_')
                }
            
            return error_response
        except Exception as e:
            self.logger.error(f"创建错误响应错误: {str(e)}")
            
            # 返回基础错误响应
            return ErrorResponse(
                status="error",
                error_type="response_creation_error",
                error_message=f"创建错误响应失败: {str(e)}",
                execution_id=execution_id,
                timestamp=self._format_timestamp()
            )
    
    def create_success_response(
        self,
        data: Any,
        message: str = "操作成功",
        execution_id: Optional[int] = None
    ) -> SuccessResponse:
        """
        创建成功响应
        
        Args:
            data: 响应数据
            message: 成功消息
            execution_id: 执行ID
            
        Returns:
            SuccessResponse: 成功响应
        """
        try:
            return SuccessResponse(
                status="success",
                message=message,
                data=data,
                execution_id=execution_id,
                timestamp=self._format_timestamp()
            )
        except Exception as e:
            self.logger.error(f"创建成功响应错误: {str(e)}")
            raise e
    
    def batch_convert_executions_to_responses(
        self,
        executions: List[Union[TaskExecution, WorkflowExecution]]
    ) -> List[Union[AgentResponse, WorkflowResponse]]:
        """
        批量转换执行记录为响应
        
        Args:
            executions: 执行记录列表
            
        Returns:
            List[Union[AgentResponse, WorkflowResponse]]: 响应列表
        """
        try:
            responses = []
            
            for execution in executions:
                try:
                    if isinstance(execution, TaskExecution):
                        response = self.task_execution_to_agent_response(execution)
                        self.conversion_stats["response_types_processed"].add("agent_response")
                    elif isinstance(execution, WorkflowExecution):
                        response = self.workflow_execution_to_workflow_response(execution)
                        self.conversion_stats["response_types_processed"].add("workflow_response")
                    else:
                        self.logger.warning(f"不支持的执行类型: {type(execution)}")
                        continue
                    
                    responses.append(response)
                    self.conversion_stats["successful_conversions"] += 1
                except Exception as e:
                    self.logger.error(f"批量转换执行记录错误: {str(e)}")
                    self.conversion_stats["failed_conversions"] += 1
            
            # 更新总体统计
            self.conversion_stats["total_conversions"] += len(executions)
            self.conversion_stats["last_conversion"] = self._format_timestamp()
            
            return responses
        except Exception as e:
            self.logger.error(f"批量转换响应错误: {str(e)}")
            return []
    
    def optimize_response_size(
        self,
        response: Union[AgentResponse, WorkflowResponse],
        max_content_length: int = 10000
    ) -> Union[AgentResponse, WorkflowResponse]:
        """
        优化响应大小
        
        Args:
            response: 响应对象
            max_content_length: 最大内容长度
            
        Returns:
            Union[AgentResponse, WorkflowResponse]: 优化后的响应
        """
        try:
            response_dict = response.dict()
            
            # 截断过长的内容
            if "content" in response_dict and response_dict["content"]:
                content = response_dict["content"]
                if len(content) > max_content_length:
                    response_dict["content"] = content[:max_content_length] + "...[内容已截断]"
                    response_dict["content_truncated"] = True
                    response_dict["original_content_length"] = len(content)
            
            # 简化大型数据结构
            if "output_data" in response_dict and response_dict["output_data"]:
                output_data = response_dict["output_data"]
                if isinstance(output_data, dict) and len(str(output_data)) > max_content_length:
                    # 保留关键字段，移除详细数据
                    simplified_data = {
                        key: value for key, value in output_data.items()
                        if key in ["status", "result", "summary", "error"]
                    }
                    response_dict["output_data"] = simplified_data
                    response_dict["output_data_simplified"] = True
            
            # 重新创建响应对象
            if isinstance(response, AgentResponse):
                return AgentResponse(**response_dict)
            elif isinstance(response, WorkflowResponse):
                return WorkflowResponse(**response_dict)
            else:
                return response
        except Exception as e:
            self.logger.error(f"优化响应大小错误: {str(e)}")
            return response
    
    def validate_response(
        self,
        response: Union[AgentResponse, WorkflowResponse, StreamResponse, ErrorResponse, SuccessResponse]
    ) -> bool:
        """
        验证响应格式
        
        Args:
            response: 响应对象
            
        Returns:
            bool: 是否有效
        """
        try:
            # 检查必需字段
            if not hasattr(response, 'status') or not response.status:
                self.logger.error("响应缺少状态字段")
                return False
            
            if not hasattr(response, 'timestamp') or not response.timestamp:
                self.logger.error("响应缺少时间戳字段")
                return False
            
            # 检查状态值
            valid_statuses = ["success", "error", "processing", "cancelled", "timeout"]
            if response.status not in valid_statuses:
                self.logger.error(f"无效的响应状态: {response.status}")
                return False
            
            # 特定类型验证
            if isinstance(response, ErrorResponse):
                if not hasattr(response, 'error_message') or not response.error_message:
                    self.logger.error("错误响应缺少错误消息")
                    return False
            
            return True
        except Exception as e:
            self.logger.error(f"验证响应错误: {str(e)}")
            return False
    
    def get_conversion_stats(self) -> Dict[str, Any]:
        """
        获取转换统计
        
        Returns:
            Dict[str, Any]: 转换统计信息
        """
        stats = self.conversion_stats.copy()
        stats["response_types_processed"] = list(stats["response_types_processed"])
        stats["success_rate"] = (
            stats["successful_conversions"] / stats["total_conversions"]
            if stats["total_conversions"] > 0 else 0
        )
        stats["supported_status_mappings"] = self.status_mapping
        stats["supported_error_types"] = list(self.error_type_mapping.keys())
        return stats
    
    def reset_stats(self) -> None:
        """
        重置转换统计
        """
        self.conversion_stats = {
            "total_conversions": 0,
            "successful_conversions": 0,
            "failed_conversions": 0,
            "response_types_processed": set(),
            "stream_responses_processed": 0,
            "conversion_history": [],
            "last_conversion": None
        }
        
        self.logger.info("转换统计已重置")