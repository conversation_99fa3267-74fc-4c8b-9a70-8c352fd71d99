#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A2A多智能体系统 计算工具

支持数学计算、数据分析和统计计算，记录用户使用情况
"""

import asyncio
import math
import statistics
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Union, Callable
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime
import json
import re
import ast
import operator
from decimal import Decimal, getcontext
import sympy as sp
from scipy import stats
import matplotlib.pyplot as plt
import io
import base64

# Google ADK imports
from google.ai.generativelanguage import FunctionDeclaration, Schema, Type

from .base_tool import (
    BaseTool, ToolConfig, ToolResult, ToolError, ToolStatus,
    ToolExecutionContext, ToolPermission
)


class CalculationType(Enum):
    """计算类型枚举"""
    BASIC = "basic"  # 基础数学运算
    ADVANCED = "advanced"  # 高级数学函数
    STATISTICAL = "statistical"  # 统计计算
    ALGEBRAIC = "algebraic"  # 代数计算
    CALCULUS = "calculus"  # 微积分
    LINEAR_ALGEBRA = "linear_algebra"  # 线性代数
    DATA_ANALYSIS = "data_analysis"  # 数据分析
    PLOTTING = "plotting"  # 绘图
    CUSTOM = "custom"  # 自定义计算


class OutputFormat(Enum):
    """输出格式枚举"""
    NUMBER = "number"  # 数值
    TEXT = "text"  # 文本
    JSON = "json"  # JSON格式
    TABLE = "table"  # 表格
    PLOT = "plot"  # 图表
    LATEX = "latex"  # LaTeX格式


@dataclass
class CalculationConfig(ToolConfig):
    """计算工具配置"""
    max_computation_time: float = 30.0  # 最大计算时间（秒）
    max_memory_usage: int = 100 * 1024 * 1024  # 最大内存使用（100MB）
    decimal_precision: int = 28  # 小数精度
    enable_symbolic: bool = True  # 启用符号计算
    enable_plotting: bool = True  # 启用绘图
    enable_data_analysis: bool = True  # 启用数据分析
    max_array_size: int = 10000  # 最大数组大小
    allowed_functions: List[str] = field(default_factory=lambda: [
        # 基础数学函数
        'abs', 'round', 'min', 'max', 'sum', 'pow',
        # 三角函数
        'sin', 'cos', 'tan', 'asin', 'acos', 'atan', 'atan2',
        'sinh', 'cosh', 'tanh', 'asinh', 'acosh', 'atanh',
        # 指数和对数
        'exp', 'log', 'log10', 'log2', 'sqrt', 'cbrt',
        # 统计函数
        'mean', 'median', 'mode', 'stdev', 'variance',
        # 特殊函数
        'factorial', 'gamma', 'lgamma', 'erf', 'erfc'
    ])
    blocked_functions: List[str] = field(default_factory=lambda: [
        'exec', 'eval', 'compile', 'open', 'input', 'raw_input',
        '__import__', 'globals', 'locals', 'vars', 'dir'
    ])
    rate_limit_calculations: int = 1000  # 每小时计算次数限制
    enable_caching: bool = True  # 启用结果缓存


@dataclass
class CalculationRequest:
    """计算请求"""
    expression: str
    calculation_type: CalculationType = CalculationType.BASIC
    variables: Dict[str, Any] = field(default_factory=dict)
    output_format: OutputFormat = OutputFormat.NUMBER
    precision: Optional[int] = None
    plot_config: Dict[str, Any] = field(default_factory=dict)
    data: Optional[Union[List, Dict]] = None
    options: Dict[str, Any] = field(default_factory=dict)


@dataclass
class CalculationResult:
    """计算结果"""
    result: Any
    calculation_type: CalculationType
    output_format: OutputFormat
    execution_time: float
    memory_used: int
    steps: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class UsageStats:
    """使用统计"""
    user_id: str
    total_calculations: int = 0
    calculation_types: Dict[str, int] = field(default_factory=dict)
    total_execution_time: float = 0.0
    average_execution_time: float = 0.0
    last_calculation: Optional[datetime] = None
    error_count: int = 0
    most_used_functions: Dict[str, int] = field(default_factory=dict)


class CalculationTool(BaseTool):
    """计算工具"""
    
    def __init__(self, config: CalculationConfig):
        """
        初始化计算工具
        
        Args:
            config: 计算工具配置
        """
        super().__init__(config)
        self._usage_stats: Dict[str, UsageStats] = {}
        self._safe_functions = self._build_safe_functions()
        
        # 设置小数精度
        getcontext().prec = config.decimal_precision
    
    def get_function_declaration(self) -> FunctionDeclaration:
        """
        获取工具的函数声明
        
        Returns:
            FunctionDeclaration: ADK函数声明
        """
        parameters_schema = Schema(
            type=Type.OBJECT,
            properties={
                "expression": Schema(
                    type=Type.STRING,
                    description="要计算的数学表达式或代码"
                ),
                "calculation_type": Schema(
                    type=Type.STRING,
                    description="计算类型 (basic, advanced, statistical, algebraic, calculus, linear_algebra, data_analysis, plotting, custom)"
                ),
                "variables": Schema(
                    type=Type.OBJECT,
                    description="变量字典，用于表达式中的变量替换"
                ),
                "output_format": Schema(
                    type=Type.STRING,
                    description="输出格式 (number, text, json, table, plot, latex)"
                ),
                "precision": Schema(
                    type=Type.INTEGER,
                    description="计算精度（小数位数）"
                ),
                "plot_config": Schema(
                    type=Type.OBJECT,
                    description="绘图配置（标题、标签、样式等）"
                ),
                "data": Schema(
                    type=Type.ARRAY,
                    description="用于数据分析的数据集"
                ),
                "options": Schema(
                    type=Type.OBJECT,
                    description="额外选项配置"
                )
            },
            required=["expression"]
        )
        
        return FunctionDeclaration(
            name=self.name,
            description=self.description,
            parameters=parameters_schema
        )
    
    async def execute(
        self,
        context: ToolExecutionContext,
        **kwargs
    ) -> ToolResult:
        """
        执行计算
        
        Args:
            context: 执行上下文
            **kwargs: 额外参数
        
        Returns:
            ToolResult: 执行结果
        """
        async with self._execution_context(context):
            try:
                # 解析计算请求
                calc_request = self._parse_calculation_request(context.parameters)
                
                # 检查速率限制
                rate_limit_error = await self._check_rate_limit(context.user_id)
                if rate_limit_error:
                    return ToolResult(
                        tool_name=self.name,
                        execution_id=context.execution_id,
                        status=ToolStatus.FAILED,
                        error=rate_limit_error,
                        user_id=context.user_id
                    )
                
                # 验证计算安全性
                security_error = await self._validate_calculation_security(calc_request)
                if security_error:
                    return ToolResult(
                        tool_name=self.name,
                        execution_id=context.execution_id,
                        status=ToolStatus.FAILED,
                        error=security_error,
                        user_id=context.user_id
                    )
                
                # 执行计算
                start_time = datetime.now()
                calc_result = await self._execute_calculation(calc_request, context.user_id)
                execution_time = (datetime.now() - start_time).total_seconds()
                
                # 更新使用统计
                await self._update_usage_stats(
                    context.user_id, calc_request.calculation_type, execution_time
                )
                
                return ToolResult(
                    tool_name=self.name,
                    execution_id=context.execution_id,
                    status=ToolStatus.SUCCESS,
                    result=calc_result.__dict__,
                    user_id=context.user_id,
                    metadata={
                        "calculation_type": calc_request.calculation_type.value,
                        "output_format": calc_request.output_format.value,
                        "execution_time": execution_time,
                        "memory_used": calc_result.memory_used
                    }
                )
                
            except Exception as e:
                self.logger.error(f"计算执行失败: {e}")
                
                # 更新错误统计
                await self._update_error_stats(context.user_id)
                
                return ToolResult(
                    tool_name=self.name,
                    execution_id=context.execution_id,
                    status=ToolStatus.FAILED,
                    error=ToolError(
                        code="CALCULATION_ERROR",
                        message=f"计算执行失败: {str(e)}"
                    ),
                    user_id=context.user_id
                )
    
    def _parse_calculation_request(self, parameters: Dict[str, Any]) -> CalculationRequest:
        """
        解析计算请求
        
        Args:
            parameters: 原始参数
        
        Returns:
            CalculationRequest: 解析后的计算请求
        """
        return CalculationRequest(
            expression=parameters["expression"],
            calculation_type=CalculationType(parameters.get("calculation_type", "basic")),
            variables=parameters.get("variables", {}),
            output_format=OutputFormat(parameters.get("output_format", "number")),
            precision=parameters.get("precision"),
            plot_config=parameters.get("plot_config", {}),
            data=parameters.get("data"),
            options=parameters.get("options", {})
        )
    
    async def _check_rate_limit(self, user_id: str) -> Optional[ToolError]:
        """
        检查速率限制
        
        Args:
            user_id: 用户ID
        
        Returns:
            Optional[ToolError]: 速率限制错误
        """
        if user_id not in self._usage_stats:
            return None
        
        stats = self._usage_stats[user_id]
        
        # 检查每小时计算次数
        if stats.last_calculation:
            time_diff = datetime.now() - stats.last_calculation
            if time_diff.total_seconds() < 3600:  # 1小时内
                # 简化的速率限制检查
                if stats.total_calculations >= self.config.rate_limit_calculations:
                    return ToolError(
                        code="RATE_LIMIT_EXCEEDED",
                        message=f"超过计算次数限制: {self.config.rate_limit_calculations}/小时"
                    )
        
        return None
    
    async def _validate_calculation_security(self, request: CalculationRequest) -> Optional[ToolError]:
        """
        验证计算安全性
        
        Args:
            request: 计算请求
        
        Returns:
            Optional[ToolError]: 安全验证错误
        """
        try:
            # 检查表达式长度
            if len(request.expression) > 10000:
                return ToolError(
                    code="EXPRESSION_TOO_LONG",
                    message="表达式过长"
                )
            
            # 检查危险函数
            for blocked_func in self.config.blocked_functions:
                if blocked_func in request.expression:
                    return ToolError(
                        code="DANGEROUS_FUNCTION",
                        message=f"表达式包含危险函数: {blocked_func}"
                    )
            
            # 检查数据大小
            if request.data:
                if isinstance(request.data, list) and len(request.data) > self.config.max_array_size:
                    return ToolError(
                        code="DATA_TOO_LARGE",
                        message=f"数据集过大: {len(request.data)} > {self.config.max_array_size}"
                    )
            
            # 检查变量值
            for var_name, var_value in request.variables.items():
                if isinstance(var_value, (list, tuple)) and len(var_value) > self.config.max_array_size:
                    return ToolError(
                        code="VARIABLE_TOO_LARGE",
                        message=f"变量 {var_name} 过大"
                    )
            
            return None
            
        except Exception as e:
            return ToolError(
                code="SECURITY_VALIDATION_ERROR",
                message=f"安全验证异常: {str(e)}"
            )
    
    async def _execute_calculation(
        self,
        request: CalculationRequest,
        user_id: str
    ) -> CalculationResult:
        """
        执行计算
        
        Args:
            request: 计算请求
            user_id: 用户ID
        
        Returns:
            CalculationResult: 计算结果
        """
        start_time = datetime.now()
        steps = []
        warnings = []
        
        try:
            # 根据计算类型执行不同的计算
            if request.calculation_type == CalculationType.BASIC:
                result = await self._execute_basic_calculation(request, steps)
            elif request.calculation_type == CalculationType.ADVANCED:
                result = await self._execute_advanced_calculation(request, steps)
            elif request.calculation_type == CalculationType.STATISTICAL:
                result = await self._execute_statistical_calculation(request, steps)
            elif request.calculation_type == CalculationType.ALGEBRAIC:
                result = await self._execute_algebraic_calculation(request, steps)
            elif request.calculation_type == CalculationType.CALCULUS:
                result = await self._execute_calculus_calculation(request, steps)
            elif request.calculation_type == CalculationType.LINEAR_ALGEBRA:
                result = await self._execute_linear_algebra_calculation(request, steps)
            elif request.calculation_type == CalculationType.DATA_ANALYSIS:
                result = await self._execute_data_analysis(request, steps)
            elif request.calculation_type == CalculationType.PLOTTING:
                result = await self._execute_plotting(request, steps)
            else:
                result = await self._execute_custom_calculation(request, steps)
            
            # 格式化输出
            formatted_result = await self._format_output(result, request.output_format, request.precision)
            
            execution_time = (datetime.now() - start_time).total_seconds()
            
            return CalculationResult(
                result=formatted_result,
                calculation_type=request.calculation_type,
                output_format=request.output_format,
                execution_time=execution_time,
                memory_used=0,  # 简化实现
                steps=steps,
                warnings=warnings
            )
            
        except Exception as e:
            raise Exception(f"计算执行失败: {str(e)}")
    
    async def _execute_basic_calculation(self, request: CalculationRequest, steps: List[str]) -> Any:
        """
        执行基础数学计算
        
        Args:
            request: 计算请求
            steps: 计算步骤
        
        Returns:
            Any: 计算结果
        """
        steps.append(f"执行基础计算: {request.expression}")
        
        # 创建安全的计算环境
        safe_dict = self._safe_functions.copy()
        safe_dict.update(request.variables)
        
        try:
            # 解析并计算表达式
            parsed = ast.parse(request.expression, mode='eval')
            result = eval(compile(parsed, '<string>', 'eval'), {"__builtins__": {}}, safe_dict)
            
            steps.append(f"计算结果: {result}")
            return result
            
        except Exception as e:
            raise Exception(f"基础计算失败: {str(e)}")
    
    async def _execute_advanced_calculation(self, request: CalculationRequest, steps: List[str]) -> Any:
        """
        执行高级数学计算
        
        Args:
            request: 计算请求
            steps: 计算步骤
        
        Returns:
            Any: 计算结果
        """
        steps.append(f"执行高级计算: {request.expression}")
        
        # 添加高级数学函数
        advanced_functions = {
            'factorial': math.factorial,
            'gamma': math.gamma,
            'lgamma': math.lgamma,
            'erf': math.erf,
            'erfc': math.erfc,
            'gcd': math.gcd,
            'lcm': lambda a, b: abs(a * b) // math.gcd(a, b),
            'degrees': math.degrees,
            'radians': math.radians,
            'hypot': math.hypot,
            'isclose': math.isclose,
            'isfinite': math.isfinite,
            'isinf': math.isinf,
            'isnan': math.isnan
        }
        
        safe_dict = self._safe_functions.copy()
        safe_dict.update(advanced_functions)
        safe_dict.update(request.variables)
        
        try:
            parsed = ast.parse(request.expression, mode='eval')
            result = eval(compile(parsed, '<string>', 'eval'), {"__builtins__": {}}, safe_dict)
            
            steps.append(f"高级计算结果: {result}")
            return result
            
        except Exception as e:
            raise Exception(f"高级计算失败: {str(e)}")
    
    async def _execute_statistical_calculation(self, request: CalculationRequest, steps: List[str]) -> Any:
        """
        执行统计计算
        
        Args:
            request: 计算请求
            steps: 计算步骤
        
        Returns:
            Any: 计算结果
        """
        steps.append(f"执行统计计算: {request.expression}")
        
        # 准备数据
        data = request.data or []
        if not data and 'data' in request.variables:
            data = request.variables['data']
        
        # 统计函数
        stat_functions = {
            'mean': statistics.mean,
            'median': statistics.median,
            'mode': statistics.mode,
            'stdev': statistics.stdev,
            'variance': statistics.variance,
            'harmonic_mean': statistics.harmonic_mean,
            'geometric_mean': statistics.geometric_mean,
            'quantiles': statistics.quantiles,
            'correlation': lambda x, y: np.corrcoef(x, y)[0, 1],
            'covariance': lambda x, y: np.cov(x, y)[0, 1],
            'zscore': lambda x, data: (x - np.mean(data)) / np.std(data),
            'percentile': lambda data, p: np.percentile(data, p),
            'histogram': lambda data, bins=10: np.histogram(data, bins=bins)
        }
        
        safe_dict = self._safe_functions.copy()
        safe_dict.update(stat_functions)
        safe_dict.update(request.variables)
        safe_dict['data'] = data
        
        try:
            parsed = ast.parse(request.expression, mode='eval')
            result = eval(compile(parsed, '<string>', 'eval'), {"__builtins__": {}}, safe_dict)
            
            steps.append(f"统计计算结果: {result}")
            return result
            
        except Exception as e:
            raise Exception(f"统计计算失败: {str(e)}")
    
    async def _execute_algebraic_calculation(self, request: CalculationRequest, steps: List[str]) -> Any:
        """
        执行代数计算
        
        Args:
            request: 计算请求
            steps: 计算步骤
        
        Returns:
            Any: 计算结果
        """
        if not self.config.enable_symbolic:
            raise Exception("符号计算未启用")
        
        steps.append(f"执行代数计算: {request.expression}")
        
        try:
            # 使用sympy进行符号计算
            expr = sp.sympify(request.expression)
            
            # 替换变量
            for var_name, var_value in request.variables.items():
                if isinstance(var_value, (int, float)):
                    expr = expr.subs(var_name, var_value)
            
            # 简化表达式
            simplified = sp.simplify(expr)
            
            steps.append(f"简化后的表达式: {simplified}")
            
            # 尝试数值计算
            try:
                result = float(simplified.evalf())
                steps.append(f"数值结果: {result}")
                return result
            except:
                # 返回符号结果
                return str(simplified)
            
        except Exception as e:
            raise Exception(f"代数计算失败: {str(e)}")
    
    async def _execute_calculus_calculation(self, request: CalculationRequest, steps: List[str]) -> Any:
        """
        执行微积分计算
        
        Args:
            request: 计算请求
            steps: 计算步骤
        
        Returns:
            Any: 计算结果
        """
        if not self.config.enable_symbolic:
            raise Exception("符号计算未启用")
        
        steps.append(f"执行微积分计算: {request.expression}")
        
        try:
            # 解析微积分操作
            if 'diff(' in request.expression:
                # 求导
                result = self._execute_differentiation(request, steps)
            elif 'integrate(' in request.expression:
                # 积分
                result = self._execute_integration(request, steps)
            elif 'limit(' in request.expression:
                # 极限
                result = self._execute_limit(request, steps)
            else:
                # 通用符号计算
                expr = sp.sympify(request.expression)
                result = str(sp.simplify(expr))
            
            steps.append(f"微积分计算结果: {result}")
            return result
            
        except Exception as e:
            raise Exception(f"微积分计算失败: {str(e)}")
    
    async def _execute_linear_algebra_calculation(self, request: CalculationRequest, steps: List[str]) -> Any:
        """
        执行线性代数计算
        
        Args:
            request: 计算请求
            steps: 计算步骤
        
        Returns:
            Any: 计算结果
        """
        steps.append(f"执行线性代数计算: {request.expression}")
        
        # 线性代数函数
        linalg_functions = {
            'matrix': np.array,
            'det': np.linalg.det,
            'inv': np.linalg.inv,
            'rank': np.linalg.matrix_rank,
            'trace': np.trace,
            'transpose': np.transpose,
            'eigenvals': lambda m: np.linalg.eigvals(m).tolist(),
            'eigenvecs': lambda m: np.linalg.eig(m)[1].tolist(),
            'svd': lambda m: [u.tolist() for u in np.linalg.svd(m)],
            'norm': np.linalg.norm,
            'dot': np.dot,
            'cross': np.cross,
            'solve': np.linalg.solve
        }
        
        safe_dict = self._safe_functions.copy()
        safe_dict.update(linalg_functions)
        safe_dict.update(request.variables)
        
        try:
            parsed = ast.parse(request.expression, mode='eval')
            result = eval(compile(parsed, '<string>', 'eval'), {"__builtins__": {}}, safe_dict)
            
            # 转换numpy数组为列表
            if isinstance(result, np.ndarray):
                result = result.tolist()
            
            steps.append(f"线性代数计算结果: {result}")
            return result
            
        except Exception as e:
            raise Exception(f"线性代数计算失败: {str(e)}")
    
    async def _execute_data_analysis(self, request: CalculationRequest, steps: List[str]) -> Any:
        """
        执行数据分析
        
        Args:
            request: 计算请求
            steps: 计算步骤
        
        Returns:
            Any: 计算结果
        """
        if not self.config.enable_data_analysis:
            raise Exception("数据分析未启用")
        
        steps.append(f"执行数据分析: {request.expression}")
        
        try:
            # 准备数据
            if request.data:
                df = pd.DataFrame(request.data)
            elif 'data' in request.variables:
                df = pd.DataFrame(request.variables['data'])
            else:
                df = pd.DataFrame()
            
            # 数据分析函数
            analysis_functions = {
                'dataframe': lambda data: pd.DataFrame(data),
                'describe': lambda df: df.describe().to_dict(),
                'corr': lambda df: df.corr().to_dict(),
                'groupby': lambda df, col: df.groupby(col),
                'pivot': lambda df, index, columns, values: df.pivot(index, columns, values).to_dict(),
                'value_counts': lambda series: series.value_counts().to_dict(),
                'unique': lambda series: series.unique().tolist(),
                'nunique': lambda series: series.nunique(),
                'isnull': lambda df: df.isnull().sum().to_dict(),
                'fillna': lambda df, value: df.fillna(value),
                'dropna': lambda df: df.dropna()
            }
            
            safe_dict = self._safe_functions.copy()
            safe_dict.update(analysis_functions)
            safe_dict.update(request.variables)
            safe_dict['df'] = df
            
            parsed = ast.parse(request.expression, mode='eval')
            result = eval(compile(parsed, '<string>', 'eval'), {"__builtins__": {}}, safe_dict)
            
            # 转换pandas对象为字典
            if isinstance(result, pd.DataFrame):
                result = result.to_dict()
            elif isinstance(result, pd.Series):
                result = result.to_dict()
            
            steps.append(f"数据分析结果: {type(result).__name__}")
            return result
            
        except Exception as e:
            raise Exception(f"数据分析失败: {str(e)}")
    
    async def _execute_plotting(self, request: CalculationRequest, steps: List[str]) -> Any:
        """
        执行绘图
        
        Args:
            request: 计算请求
            steps: 计算步骤
        
        Returns:
            Any: 计算结果（base64编码的图片）
        """
        if not self.config.enable_plotting:
            raise Exception("绘图功能未启用")
        
        steps.append(f"执行绘图: {request.expression}")
        
        try:
            # 创建图形
            plt.figure(figsize=(10, 6))
            
            # 准备绘图环境
            plot_functions = {
                'plot': plt.plot,
                'scatter': plt.scatter,
                'bar': plt.bar,
                'hist': plt.hist,
                'pie': plt.pie,
                'boxplot': plt.boxplot,
                'xlabel': plt.xlabel,
                'ylabel': plt.ylabel,
                'title': plt.title,
                'legend': plt.legend,
                'grid': plt.grid,
                'xlim': plt.xlim,
                'ylim': plt.ylim
            }
            
            safe_dict = self._safe_functions.copy()
            safe_dict.update(plot_functions)
            safe_dict.update(request.variables)
            
            if request.data:
                safe_dict['data'] = request.data
            
            # 执行绘图代码
            exec(request.expression, {"__builtins__": {}}, safe_dict)
            
            # 应用绘图配置
            if request.plot_config:
                if 'title' in request.plot_config:
                    plt.title(request.plot_config['title'])
                if 'xlabel' in request.plot_config:
                    plt.xlabel(request.plot_config['xlabel'])
                if 'ylabel' in request.plot_config:
                    plt.ylabel(request.plot_config['ylabel'])
            
            # 保存图片到内存
            buffer = io.BytesIO()
            plt.savefig(buffer, format='png', dpi=150, bbox_inches='tight')
            buffer.seek(0)
            
            # 转换为base64
            image_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')
            
            plt.close()
            
            steps.append("图片生成完成")
            return image_base64
            
        except Exception as e:
            plt.close()  # 确保清理
            raise Exception(f"绘图失败: {str(e)}")
    
    async def _execute_custom_calculation(self, request: CalculationRequest, steps: List[str]) -> Any:
        """
        执行自定义计算
        
        Args:
            request: 计算请求
            steps: 计算步骤
        
        Returns:
            Any: 计算结果
        """
        steps.append(f"执行自定义计算: {request.expression}")
        
        # 组合所有可用函数
        all_functions = self._safe_functions.copy()
        all_functions.update({
            'np': np,
            'pd': pd,
            'sp': sp,
            'plt': plt
        })
        all_functions.update(request.variables)
        
        try:
            # 尝试作为表达式执行
            try:
                parsed = ast.parse(request.expression, mode='eval')
                result = eval(compile(parsed, '<string>', 'eval'), {"__builtins__": {}}, all_functions)
            except SyntaxError:
                # 作为语句执行
                local_vars = all_functions.copy()
                exec(request.expression, {"__builtins__": {}}, local_vars)
                
                # 查找结果变量
                if 'result' in local_vars:
                    result = local_vars['result']
                else:
                    # 返回所有新变量
                    new_vars = {k: v for k, v in local_vars.items() 
                               if k not in all_functions and not k.startswith('_')}
                    result = new_vars if new_vars else "执行完成"
            
            steps.append(f"自定义计算结果: {type(result).__name__}")
            return result
            
        except Exception as e:
            raise Exception(f"自定义计算失败: {str(e)}")
    
    def _execute_differentiation(self, request: CalculationRequest, steps: List[str]) -> str:
        """
        执行求导计算
        
        Args:
            request: 计算请求
            steps: 计算步骤
        
        Returns:
            str: 求导结果
        """
        # 简化的求导实现
        # 实际应该解析diff(expression, variable)格式
        expr_str = request.expression.replace('diff(', '').replace(')', '')
        parts = expr_str.split(',')
        
        if len(parts) >= 2:
            expr = sp.sympify(parts[0].strip())
            var = sp.Symbol(parts[1].strip())
            result = sp.diff(expr, var)
        else:
            expr = sp.sympify(parts[0].strip())
            # 自动检测变量
            symbols = expr.free_symbols
            if symbols:
                var = list(symbols)[0]
                result = sp.diff(expr, var)
            else:
                result = 0
        
        steps.append(f"求导: d/d{var}({expr}) = {result}")
        return str(result)
    
    def _execute_integration(self, request: CalculationRequest, steps: List[str]) -> str:
        """
        执行积分计算
        
        Args:
            request: 计算请求
            steps: 计算步骤
        
        Returns:
            str: 积分结果
        """
        # 简化的积分实现
        expr_str = request.expression.replace('integrate(', '').replace(')', '')
        parts = expr_str.split(',')
        
        if len(parts) >= 2:
            expr = sp.sympify(parts[0].strip())
            var = sp.Symbol(parts[1].strip())
            result = sp.integrate(expr, var)
        else:
            expr = sp.sympify(parts[0].strip())
            symbols = expr.free_symbols
            if symbols:
                var = list(symbols)[0]
                result = sp.integrate(expr, var)
            else:
                result = expr
        
        steps.append(f"积分: ∫{expr} d{var} = {result}")
        return str(result)
    
    def _execute_limit(self, request: CalculationRequest, steps: List[str]) -> str:
        """
        执行极限计算
        
        Args:
            request: 计算请求
            steps: 计算步骤
        
        Returns:
            str: 极限结果
        """
        # 简化的极限实现
        expr_str = request.expression.replace('limit(', '').replace(')', '')
        parts = expr_str.split(',')
        
        if len(parts) >= 3:
            expr = sp.sympify(parts[0].strip())
            var = sp.Symbol(parts[1].strip())
            point = sp.sympify(parts[2].strip())
            result = sp.limit(expr, var, point)
        else:
            raise Exception("极限计算需要表达式、变量和趋近点")
        
        steps.append(f"极限: lim({var}→{point}) {expr} = {result}")
        return str(result)
    
    async def _format_output(self, result: Any, output_format: OutputFormat, precision: Optional[int]) -> Any:
        """
        格式化输出结果
        
        Args:
            result: 原始结果
            output_format: 输出格式
            precision: 精度
        
        Returns:
            Any: 格式化后的结果
        """
        if output_format == OutputFormat.NUMBER:
            if isinstance(result, (int, float, complex)):
                if precision is not None and isinstance(result, float):
                    return round(result, precision)
                return result
            else:
                return str(result)
        
        elif output_format == OutputFormat.TEXT:
            return str(result)
        
        elif output_format == OutputFormat.JSON:
            if isinstance(result, (dict, list)):
                return result
            else:
                return {"result": result}
        
        elif output_format == OutputFormat.TABLE:
            if isinstance(result, dict):
                return {"headers": list(result.keys()), "data": [list(result.values())]}
            elif isinstance(result, list):
                return {"headers": ["Value"], "data": [[item] for item in result]}
            else:
                return {"headers": ["Result"], "data": [[result]]}
        
        elif output_format == OutputFormat.LATEX:
            if hasattr(result, '_latex'):
                return result._latex()
            else:
                return f"${result}$"
        
        else:  # PLOT format
            return result
    
    async def _update_usage_stats(
        self,
        user_id: str,
        calculation_type: CalculationType,
        execution_time: float
    ) -> None:
        """
        更新使用统计
        
        Args:
            user_id: 用户ID
            calculation_type: 计算类型
            execution_time: 执行时间
        """
        if user_id not in self._usage_stats:
            self._usage_stats[user_id] = UsageStats(user_id=user_id)
        
        stats = self._usage_stats[user_id]
        stats.total_calculations += 1
        stats.total_execution_time += execution_time
        stats.average_execution_time = stats.total_execution_time / stats.total_calculations
        stats.last_calculation = datetime.now()
        
        # 更新计算类型统计
        calc_type_str = calculation_type.value
        if calc_type_str not in stats.calculation_types:
            stats.calculation_types[calc_type_str] = 0
        stats.calculation_types[calc_type_str] += 1
    
    async def _update_error_stats(self, user_id: str) -> None:
        """
        更新错误统计
        
        Args:
            user_id: 用户ID
        """
        if user_id not in self._usage_stats:
            self._usage_stats[user_id] = UsageStats(user_id=user_id)
        
        self._usage_stats[user_id].error_count += 1
    
    def _build_safe_functions(self) -> Dict[str, Callable]:
        """
        构建安全函数字典
        
        Returns:
            Dict[str, Callable]: 安全函数字典
        """
        safe_functions = {
            # 基础数学函数
            'abs': abs,
            'round': round,
            'min': min,
            'max': max,
            'sum': sum,
            'pow': pow,
            'divmod': divmod,
            
            # 数学常数
            'pi': math.pi,
            'e': math.e,
            'tau': math.tau,
            'inf': math.inf,
            'nan': math.nan,
            
            # 三角函数
            'sin': math.sin,
            'cos': math.cos,
            'tan': math.tan,
            'asin': math.asin,
            'acos': math.acos,
            'atan': math.atan,
            'atan2': math.atan2,
            'sinh': math.sinh,
            'cosh': math.cosh,
            'tanh': math.tanh,
            'asinh': math.asinh,
            'acosh': math.acosh,
            'atanh': math.atanh,
            
            # 指数和对数
            'exp': math.exp,
            'log': math.log,
            'log10': math.log10,
            'log2': math.log2,
            'sqrt': math.sqrt,
            
            # 其他数学函数
            'ceil': math.ceil,
            'floor': math.floor,
            'trunc': math.trunc,
            'copysign': math.copysign,
            'fabs': math.fabs,
            'fmod': math.fmod,
            'frexp': math.frexp,
            'ldexp': math.ldexp,
            'modf': math.modf,
            
            # 类型转换
            'int': int,
            'float': float,
            'complex': complex,
            'bool': bool,
            'str': str,
            'list': list,
            'tuple': tuple,
            'dict': dict,
            'set': set,
            
            # 序列函数
            'len': len,
            'range': range,
            'enumerate': enumerate,
            'zip': zip,
            'sorted': sorted,
            'reversed': reversed,
            'all': all,
            'any': any,
        }
        
        # 只包含允许的函数
        filtered_functions = {}
        for name, func in safe_functions.items():
            if name in self.config.allowed_functions or name in ['pi', 'e', 'tau', 'inf', 'nan']:
                filtered_functions[name] = func
        
        return filtered_functions
    
    def get_usage_stats(self, user_id: str) -> Optional[UsageStats]:
        """
        获取用户使用统计
        
        Args:
            user_id: 用户ID
        
        Returns:
            Optional[UsageStats]: 使用统计
        """
        return self._usage_stats.get(user_id)
    
    def get_all_usage_stats(self) -> Dict[str, UsageStats]:
        """
        获取所有用户使用统计
        
        Returns:
            Dict[str, UsageStats]: 所有用户使用统计
        """
        return self._usage_stats.copy()
    
    async def _validate_specific_parameters(self, parameters: Dict[str, Any]) -> Optional[ToolError]:
        """
        验证计算工具参数
        
        Args:
            parameters: 参数字典
        
        Returns:
            Optional[ToolError]: 验证错误
        """
        try:
            # 检查必需参数
            if "expression" not in parameters:
                return ToolError(
                    code="MISSING_REQUIRED_PARAMETER",
                    message="缺少必需参数: expression"
                )
            
            # 验证计算类型
            if "calculation_type" in parameters:
                try:
                    CalculationType(parameters["calculation_type"])
                except ValueError:
                    return ToolError(
                        code="INVALID_CALCULATION_TYPE",
                        message=f"无效的计算类型: {parameters['calculation_type']}"
                    )
            
            # 验证输出格式
            if "output_format" in parameters:
                try:
                    OutputFormat(parameters["output_format"])
                except ValueError:
                    return ToolError(
                        code="INVALID_OUTPUT_FORMAT",
                        message=f"无效的输出格式: {parameters['output_format']}"
                    )
            
            # 验证精度
            if "precision" in parameters:
                precision = parameters["precision"]
                if not isinstance(precision, int) or precision < 0:
                    return ToolError(
                        code="INVALID_PRECISION",
                        message="精度必须是非负整数"
                    )
            
            return None
            
        except Exception as e:
            return ToolError(
                code="PARAMETER_VALIDATION_ERROR",
                message=f"参数验证异常: {str(e)}"
            )