#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A2A多智能体系统 上下文管理器

智能管理token限制和上下文窗口
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional, Tuple, Union
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime, timedelta
import tiktoken
import json
from collections import deque


class CompressionStrategy(Enum):
    """压缩策略枚举"""
    TRUNCATE_OLDEST = "truncate_oldest"  # 截断最旧的消息
    TRUNCATE_MIDDLE = "truncate_middle"  # 截断中间的消息
    SUMMARIZE_OLDEST = "summarize_oldest"  # 总结最旧的消息
    SLIDING_WINDOW = "sliding_window"  # 滑动窗口
    IMPORTANCE_BASED = "importance_based"  # 基于重要性


class MessageType(Enum):
    """消息类型枚举"""
    SYSTEM = "system"
    USER = "user"
    ASSISTANT = "assistant"
    FUNCTION = "function"
    TOOL = "tool"


@dataclass
class Message:
    """消息数据类"""
    type: MessageType
    content: str
    timestamp: datetime = field(default_factory=datetime.now)
    token_count: Optional[int] = None
    importance: float = 1.0  # 重要性评分 (0-1)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "role": self.type.value,
            "content": self.content,
            "timestamp": self.timestamp.isoformat(),
            "token_count": self.token_count,
            "importance": self.importance,
            "metadata": self.metadata
        }


@dataclass
class ContextWindow:
    """上下文窗口配置"""
    max_tokens: int
    reserved_tokens: int = 1000  # 为响应预留的token数
    system_message_tokens: int = 0  # 系统消息占用的token数
    
    @property
    def available_tokens(self) -> int:
        """可用的token数"""
        return self.max_tokens - self.reserved_tokens - self.system_message_tokens


@dataclass
class CompressionResult:
    """压缩结果"""
    original_messages: List[Message]
    compressed_messages: List[Message]
    original_tokens: int
    compressed_tokens: int
    compression_ratio: float
    strategy_used: CompressionStrategy
    metadata: Dict[str, Any] = field(default_factory=dict)


class ContextManager:
    """上下文管理器"""
    
    def __init__(
        self,
        model_name: str = "gpt-3.5-turbo",
        max_tokens: int = 4096,
        reserved_tokens: int = 1000,
        default_strategy: CompressionStrategy = CompressionStrategy.SLIDING_WINDOW
    ):
        """
        初始化上下文管理器
        
        Args:
            model_name: 模型名称
            max_tokens: 最大token数
            reserved_tokens: 预留token数
            default_strategy: 默认压缩策略
        """
        self.model_name = model_name
        self.default_strategy = default_strategy
        self.logger = logging.getLogger(__name__)
        
        # 初始化tokenizer
        try:
            self.tokenizer = tiktoken.encoding_for_model(model_name)
        except KeyError:
            # 如果模型不支持，使用默认编码
            self.tokenizer = tiktoken.get_encoding("cl100k_base")
            self.logger.warning(f"模型 {model_name} 不支持tiktoken，使用默认编码")
        
        # 上下文窗口配置
        self.context_window = ContextWindow(
            max_tokens=max_tokens,
            reserved_tokens=reserved_tokens
        )
        
        # 消息历史
        self.messages: deque[Message] = deque()
        
        # 系统消息
        self.system_message: Optional[Message] = None
        
        # 压缩历史
        self.compression_history: List[CompressionResult] = []
        
        # 重要性评分器（可以扩展为更复杂的算法）
        self.importance_keywords = {
            "error": 0.9,
            "warning": 0.7,
            "important": 0.8,
            "critical": 0.95,
            "urgent": 0.85
        }
        
        self.logger.info(f"上下文管理器初始化完成，模型: {model_name}，最大token: {max_tokens}")
    
    def set_system_message(self, content: str) -> None:
        """
        设置系统消息
        
        Args:
            content: 系统消息内容
        """
        try:
            self.system_message = Message(
                type=MessageType.SYSTEM,
                content=content,
                importance=1.0  # 系统消息重要性最高
            )
            
            # 计算系统消息token数
            self.system_message.token_count = self.count_tokens(content)
            self.context_window.system_message_tokens = self.system_message.token_count
            
            self.logger.info(f"设置系统消息，token数: {self.system_message.token_count}")
            
        except Exception as e:
            self.logger.error(f"设置系统消息失败: {e}")
            raise
    
    def add_message(
        self,
        message_type: MessageType,
        content: str,
        importance: Optional[float] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Message:
        """
        添加消息
        
        Args:
            message_type: 消息类型
            content: 消息内容
            importance: 重要性评分
            metadata: 元数据
        
        Returns:
            Message: 添加的消息
        """
        try:
            # 计算重要性评分
            if importance is None:
                importance = self._calculate_importance(content)
            
            # 创建消息
            message = Message(
                type=message_type,
                content=content,
                importance=importance,
                metadata=metadata or {}
            )
            
            # 计算token数
            message.token_count = self.count_tokens(content)
            
            # 添加到消息历史
            self.messages.append(message)
            
            self.logger.debug(
                f"添加消息: {message_type.value}, token: {message.token_count}, "
                f"重要性: {importance:.2f}"
            )
            
            return message
            
        except Exception as e:
            self.logger.error(f"添加消息失败: {e}")
            raise
    
    def add_user_message(self, content: str, **kwargs) -> Message:
        """添加用户消息"""
        return self.add_message(MessageType.USER, content, **kwargs)
    
    def add_assistant_message(self, content: str, **kwargs) -> Message:
        """添加助手消息"""
        return self.add_message(MessageType.ASSISTANT, content, **kwargs)
    
    def add_function_message(self, content: str, **kwargs) -> Message:
        """添加函数消息"""
        return self.add_message(MessageType.FUNCTION, content, **kwargs)
    
    async def get_context(
        self,
        strategy: Optional[CompressionStrategy] = None,
        target_tokens: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """
        获取上下文消息列表
        
        Args:
            strategy: 压缩策略
            target_tokens: 目标token数
        
        Returns:
            List[Dict[str, Any]]: 格式化的消息列表
        """
        try:
            strategy = strategy or self.default_strategy
            target_tokens = target_tokens or self.context_window.available_tokens
            
            # 获取当前消息列表
            current_messages = list(self.messages)
            
            # 计算当前token总数
            current_tokens = sum(
                msg.token_count or 0 for msg in current_messages
            )
            
            # 如果超出限制，进行压缩
            if current_tokens > target_tokens:
                self.logger.info(
                    f"当前token数 {current_tokens} 超出限制 {target_tokens}，开始压缩"
                )
                
                compressed_messages = await self._compress_messages(
                    current_messages, target_tokens, strategy
                )
            else:
                compressed_messages = current_messages
            
            # 构建最终消息列表
            result_messages = []
            
            # 添加系统消息
            if self.system_message:
                result_messages.append(self.system_message.to_dict())
            
            # 添加压缩后的消息
            for msg in compressed_messages:
                result_messages.append(msg.to_dict())
            
            return result_messages
            
        except Exception as e:
            self.logger.error(f"获取上下文失败: {e}")
            raise
    
    async def _compress_messages(
        self,
        messages: List[Message],
        target_tokens: int,
        strategy: CompressionStrategy
    ) -> List[Message]:
        """
        压缩消息列表
        
        Args:
            messages: 原始消息列表
            target_tokens: 目标token数
            strategy: 压缩策略
        
        Returns:
            List[Message]: 压缩后的消息列表
        """
        try:
            original_tokens = sum(msg.token_count or 0 for msg in messages)
            
            if strategy == CompressionStrategy.TRUNCATE_OLDEST:
                compressed_messages = await self._truncate_oldest(messages, target_tokens)
            elif strategy == CompressionStrategy.TRUNCATE_MIDDLE:
                compressed_messages = await self._truncate_middle(messages, target_tokens)
            elif strategy == CompressionStrategy.SUMMARIZE_OLDEST:
                compressed_messages = await self._summarize_oldest(messages, target_tokens)
            elif strategy == CompressionStrategy.SLIDING_WINDOW:
                compressed_messages = await self._sliding_window(messages, target_tokens)
            elif strategy == CompressionStrategy.IMPORTANCE_BASED:
                compressed_messages = await self._importance_based(messages, target_tokens)
            else:
                raise ValueError(f"不支持的压缩策略: {strategy}")
            
            # 记录压缩结果
            compressed_tokens = sum(msg.token_count or 0 for msg in compressed_messages)
            compression_ratio = compressed_tokens / original_tokens if original_tokens > 0 else 1.0
            
            compression_result = CompressionResult(
                original_messages=messages,
                compressed_messages=compressed_messages,
                original_tokens=original_tokens,
                compressed_tokens=compressed_tokens,
                compression_ratio=compression_ratio,
                strategy_used=strategy
            )
            
            self.compression_history.append(compression_result)
            
            self.logger.info(
                f"消息压缩完成: {original_tokens} -> {compressed_tokens} tokens, "
                f"压缩比: {compression_ratio:.2f}, 策略: {strategy.value}"
            )
            
            return compressed_messages
            
        except Exception as e:
            self.logger.error(f"压缩消息失败: {e}")
            raise
    
    async def _truncate_oldest(self, messages: List[Message], target_tokens: int) -> List[Message]:
        """截断最旧的消息"""
        result = []
        current_tokens = 0
        
        # 从最新的消息开始添加
        for message in reversed(messages):
            if current_tokens + (message.token_count or 0) <= target_tokens:
                result.insert(0, message)
                current_tokens += message.token_count or 0
            else:
                break
        
        return result
    
    async def _truncate_middle(self, messages: List[Message], target_tokens: int) -> List[Message]:
        """截断中间的消息，保留开头和结尾"""
        if len(messages) <= 2:
            return messages
        
        # 保留的开头和结尾消息数量
        keep_start = min(2, len(messages) // 4)
        keep_end = min(2, len(messages) // 4)
        
        start_messages = messages[:keep_start]
        end_messages = messages[-keep_end:]
        
        # 计算开头和结尾消息的token数
        start_tokens = sum(msg.token_count or 0 for msg in start_messages)
        end_tokens = sum(msg.token_count or 0 for msg in end_messages)
        
        remaining_tokens = target_tokens - start_tokens - end_tokens
        
        if remaining_tokens <= 0:
            return start_messages + end_messages
        
        # 从中间部分选择消息
        middle_messages = messages[keep_start:-keep_end]
        selected_middle = await self._select_messages_by_tokens(
            middle_messages, remaining_tokens
        )
        
        return start_messages + selected_middle + end_messages
    
    async def _summarize_oldest(self, messages: List[Message], target_tokens: int) -> List[Message]:
        """总结最旧的消息（简化实现）"""
        # 这里是简化实现，实际可以调用LLM进行总结
        result = []
        current_tokens = 0
        summarized_count = 0
        
        for i, message in enumerate(reversed(messages)):
            if current_tokens + (message.token_count or 0) <= target_tokens:
                result.insert(0, message)
                current_tokens += message.token_count or 0
            else:
                summarized_count += 1
        
        # 如果有被总结的消息，添加总结消息
        if summarized_count > 0:
            summary_content = f"[总结了 {summarized_count} 条较早的消息]"
            summary_message = Message(
                type=MessageType.SYSTEM,
                content=summary_content,
                importance=0.5
            )
            summary_message.token_count = self.count_tokens(summary_content)
            result.insert(0, summary_message)
        
        return result
    
    async def _sliding_window(self, messages: List[Message], target_tokens: int) -> List[Message]:
        """滑动窗口策略"""
        return await self._truncate_oldest(messages, target_tokens)
    
    async def _importance_based(self, messages: List[Message], target_tokens: int) -> List[Message]:
        """基于重要性的压缩"""
        # 按重要性排序
        sorted_messages = sorted(messages, key=lambda x: x.importance, reverse=True)
        
        result = []
        current_tokens = 0
        
        for message in sorted_messages:
            if current_tokens + (message.token_count or 0) <= target_tokens:
                result.append(message)
                current_tokens += message.token_count or 0
        
        # 按时间顺序重新排列
        result.sort(key=lambda x: x.timestamp)
        
        return result
    
    async def _select_messages_by_tokens(
        self,
        messages: List[Message],
        target_tokens: int
    ) -> List[Message]:
        """根据token限制选择消息"""
        result = []
        current_tokens = 0
        
        for message in messages:
            if current_tokens + (message.token_count or 0) <= target_tokens:
                result.append(message)
                current_tokens += message.token_count or 0
            else:
                break
        
        return result
    
    def count_tokens(self, text: str) -> int:
        """
        计算文本的token数
        
        Args:
            text: 文本内容
        
        Returns:
            int: token数
        """
        try:
            return len(self.tokenizer.encode(text))
        except Exception as e:
            self.logger.error(f"计算token数失败: {e}")
            # 简单估算：平均每个token约4个字符
            return len(text) // 4
    
    def _calculate_importance(self, content: str) -> float:
        """
        计算消息重要性
        
        Args:
            content: 消息内容
        
        Returns:
            float: 重要性评分 (0-1)
        """
        try:
            content_lower = content.lower()
            importance = 0.5  # 基础重要性
            
            # 根据关键词调整重要性
            for keyword, score in self.importance_keywords.items():
                if keyword in content_lower:
                    importance = max(importance, score)
            
            # 根据消息长度调整（较长的消息可能更重要）
            if len(content) > 500:
                importance += 0.1
            elif len(content) < 50:
                importance -= 0.1
            
            # 确保在有效范围内
            return max(0.0, min(1.0, importance))
            
        except Exception as e:
            self.logger.error(f"计算重要性失败: {e}")
            return 0.5
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            total_messages = len(self.messages)
            total_tokens = sum(msg.token_count or 0 for msg in self.messages)
            
            # 按类型统计
            type_stats = {}
            for msg in self.messages:
                msg_type = msg.type.value
                if msg_type not in type_stats:
                    type_stats[msg_type] = {"count": 0, "tokens": 0}
                type_stats[msg_type]["count"] += 1
                type_stats[msg_type]["tokens"] += msg.token_count or 0
            
            # 压缩统计
            compression_stats = {
                "total_compressions": len(self.compression_history),
                "avg_compression_ratio": 0.0
            }
            
            if self.compression_history:
                avg_ratio = sum(
                    result.compression_ratio for result in self.compression_history
                ) / len(self.compression_history)
                compression_stats["avg_compression_ratio"] = avg_ratio
            
            return {
                "total_messages": total_messages,
                "total_tokens": total_tokens,
                "avg_tokens_per_message": total_tokens / total_messages if total_messages > 0 else 0,
                "context_window": {
                    "max_tokens": self.context_window.max_tokens,
                    "available_tokens": self.context_window.available_tokens,
                    "system_tokens": self.context_window.system_message_tokens
                },
                "type_stats": type_stats,
                "compression_stats": compression_stats
            }
            
        except Exception as e:
            self.logger.error(f"获取统计信息失败: {e}")
            return {}
    
    def clear_messages(self, keep_system: bool = True) -> None:
        """
        清空消息历史
        
        Args:
            keep_system: 是否保留系统消息
        """
        try:
            self.messages.clear()
            
            if not keep_system:
                self.system_message = None
                self.context_window.system_message_tokens = 0
            
            self.logger.info("消息历史已清空")
            
        except Exception as e:
            self.logger.error(f"清空消息失败: {e}")
    
    def export_messages(self, format_type: str = "json") -> str:
        """
        导出消息历史
        
        Args:
            format_type: 导出格式（json, text）
        
        Returns:
            str: 导出的内容
        """
        try:
            if format_type == "json":
                messages_data = []
                
                if self.system_message:
                    messages_data.append(self.system_message.to_dict())
                
                for msg in self.messages:
                    messages_data.append(msg.to_dict())
                
                return json.dumps(messages_data, ensure_ascii=False, indent=2)
            
            elif format_type == "text":
                lines = []
                
                if self.system_message:
                    lines.append(f"[SYSTEM] {self.system_message.content}")
                    lines.append("")
                
                for msg in self.messages:
                    timestamp = msg.timestamp.strftime("%Y-%m-%d %H:%M:%S")
                    lines.append(f"[{msg.type.value.upper()}] {timestamp}")
                    lines.append(msg.content)
                    lines.append("")
                
                return "\n".join(lines)
            
            else:
                raise ValueError(f"不支持的导出格式: {format_type}")
                
        except Exception as e:
            self.logger.error(f"导出消息失败: {e}")
            return ""
    
    def update_context_window(self, max_tokens: int, reserved_tokens: Optional[int] = None) -> None:
        """
        更新上下文窗口配置
        
        Args:
            max_tokens: 最大token数
            reserved_tokens: 预留token数
        """
        try:
            self.context_window.max_tokens = max_tokens
            
            if reserved_tokens is not None:
                self.context_window.reserved_tokens = reserved_tokens
            
            self.logger.info(
                f"上下文窗口已更新: 最大token {max_tokens}, "
                f"预留token {self.context_window.reserved_tokens}"
            )
            
        except Exception as e:
            self.logger.error(f"更新上下文窗口失败: {e}")