#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A2A多智能体系统主入口文件

基于Google ADK和FastAPI的多智能体协作系统
作者: A2A开发团队
创建时间: 2024
"""

import asyncio
import uvicorn
from contextlib import asynccontextmanager
from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from loguru import logger

from app.core.config import get_settings
from app.core.database import init_database, close_database
from app.core.logging import setup_logging
from app.core.storage import init_storage_managers, cleanup_storage_managers
from app.core.cache import init_cache_manager, cleanup_cache_manager
from app.middleware.auth import AuthMiddleware
from app.middleware.logging import LoggingMiddleware
from app.middleware.exception import ExceptionMiddleware
from app.api.v1 import api_router
from app.core.exceptions import A2AException


@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    应用生命周期管理
    
    Args:
        app: FastAPI应用实例
    """
    # 启动时初始化
    logger.info("🚀 A2A多智能体系统启动中...")
    
    # 初始化数据库
    await init_database()
    logger.info("✅ 数据库连接已建立")
    
    # 初始化日志系统
    await setup_logging()
    logger.info("✅ 日志系统已初始化")
    
    # 初始化存储管理器
    await init_storage_managers()
    logger.info("✅ 存储管理器已初始化")
    
    # 初始化缓存管理器
    await init_cache_manager()
    logger.info("✅ 缓存管理器已初始化")
    
    logger.info("🎉 A2A多智能体系统启动完成")
    
    yield
    
    # 关闭时清理
    logger.info("🔄 A2A多智能体系统关闭中...")
    
    # 清理缓存管理器
    await cleanup_cache_manager()
    logger.info("✅ 缓存管理器已清理")
    
    # 清理存储管理器
    await cleanup_storage_managers()
    logger.info("✅ 存储管理器已清理")
    
    await close_database()
    logger.info("✅ 数据库连接已关闭")
    logger.info("👋 A2A多智能体系统已关闭")


def create_app() -> FastAPI:
    """
    创建FastAPI应用实例
    
    Returns:
        FastAPI: 配置完成的FastAPI应用实例
    """
    settings = get_settings()
    
    # 创建FastAPI应用
    app = FastAPI(
        title="A2A多智能体系统",
        description="基于Google ADK和FastAPI的多智能体协作系统",
        version="1.0.0",
        docs_url="/docs" if settings.debug else None,
        redoc_url="/redoc" if settings.debug else None,
        lifespan=lifespan
    )
    
    # 配置CORS中间件
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.allowed_origins,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # 添加自定义中间件（按顺序添加）
    app.add_middleware(ExceptionMiddleware)
    app.add_middleware(LoggingMiddleware)
    app.add_middleware(AuthMiddleware)
    
    # 注册路由
    app.include_router(api_router, prefix="/api/v1")
    
    # 全局异常处理器
    @app.exception_handler(A2AException)
    async def a2a_exception_handler(request: Request, exc: A2AException):
        """
        A2A自定义异常处理器
        
        Args:
            request: 请求对象
            exc: A2A异常实例
            
        Returns:
            JSONResponse: 错误响应
        """
        logger.error(f"A2A异常: {exc.message}", extra={
            "error_code": exc.error_code,
            "details": exc.details,
            "path": request.url.path
        })
        
        return JSONResponse(
            status_code=exc.status_code,
            content={
                "error": True,
                "error_code": exc.error_code,
                "message": exc.message,
                "details": exc.details,
                "timestamp": exc.timestamp.isoformat()
            }
        )
    
    # 健康检查端点
    @app.get("/health")
    async def health_check():
        """
        健康检查端点
        
        Returns:
            dict: 健康状态信息
        """
        return {
            "status": "healthy",
            "service": "A2A多智能体系统",
            "version": "1.0.0"
        }
    
    return app


# 创建应用实例
app = create_app()


if __name__ == "__main__":
    settings = get_settings()
    
    # 运行应用
    uvicorn.run(
        "main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
        log_level="info" if not settings.debug else "debug",
        access_log=True
    )