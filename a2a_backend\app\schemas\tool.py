#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A2A多智能体系统工具相关Pydantic模式

定义工具相关的请求和响应模式
"""

from typing import Dict, List, Optional, Any
from datetime import datetime
from pydantic import Field, validator
from enum import Enum

from .base import BaseSchema


class ToolType(str, Enum):
    """工具类型枚举"""
    FUNCTION = "function"
    API = "api"
    WEBHOOK = "webhook"
    DATABASE = "database"
    FILE = "file"
    SYSTEM = "system"
    CUSTOM = "custom"


class ToolStatus(str, Enum):
    """工具状态枚举"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    DEPRECATED = "deprecated"
    MAINTENANCE = "maintenance"
    TESTING = "testing"


class ExecutionStatus(str, Enum):
    """执行状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    TIMEOUT = "timeout"


class SecurityLevel(str, Enum):
    """安全级别枚举"""
    PUBLIC = "public"
    RESTRICTED = "restricted"
    PRIVATE = "private"
    ADMIN_ONLY = "admin_only"


class ToolCreate(BaseSchema):
    """
    工具创建请求模式
    """
    
    name: str = Field(
        min_length=1,
        max_length=100,
        description="工具名称"
    )
    
    description: Optional[str] = Field(
        default=None,
        max_length=1000,
        description="工具描述"
    )
    
    type: ToolType = Field(
        description="工具类型"
    )
    
    category: Optional[str] = Field(
        default=None,
        max_length=50,
        description="工具分类"
    )
    
    schema_definition: Dict[str, Any] = Field(
        description="工具模式定义（JSON Schema）"
    )
    
    implementation: Dict[str, Any] = Field(
        description="工具实现配置"
    )
    
    config: Optional[Dict[str, Any]] = Field(
        default_factory=dict,
        description="工具配置"
    )
    
    version: str = Field(
        default="1.0.0",
        description="工具版本"
    )
    
    security_level: SecurityLevel = Field(
        default=SecurityLevel.RESTRICTED,
        description="安全级别"
    )
    
    required_permissions: List[str] = Field(
        default_factory=list,
        description="所需权限列表"
    )
    
    tags: List[str] = Field(
        default_factory=list,
        description="标签列表"
    )
    
    is_public: bool = Field(
        default=False,
        description="是否公开"
    )
    
    timeout: Optional[int] = Field(
        default=None,
        ge=1,
        le=3600,
        description="超时时间（秒）"
    )
    
    max_retries: int = Field(
        default=3,
        ge=0,
        le=10,
        description="最大重试次数"
    )


class ToolUpdate(BaseSchema):
    """
    工具更新请求模式
    """
    
    name: Optional[str] = Field(
        default=None,
        min_length=1,
        max_length=100,
        description="工具名称"
    )
    
    description: Optional[str] = Field(
        default=None,
        max_length=1000,
        description="工具描述"
    )
    
    schema_definition: Optional[Dict[str, Any]] = Field(
        default=None,
        description="工具模式定义（JSON Schema）"
    )
    
    implementation: Optional[Dict[str, Any]] = Field(
        default=None,
        description="工具实现配置"
    )
    
    config: Optional[Dict[str, Any]] = Field(
        default=None,
        description="工具配置"
    )
    
    status: Optional[ToolStatus] = Field(
        default=None,
        description="工具状态"
    )
    
    security_level: Optional[SecurityLevel] = Field(
        default=None,
        description="安全级别"
    )
    
    required_permissions: Optional[List[str]] = Field(
        default=None,
        description="所需权限列表"
    )
    
    tags: Optional[List[str]] = Field(
        default=None,
        description="标签列表"
    )
    
    is_public: Optional[bool] = Field(
        default=None,
        description="是否公开"
    )
    
    timeout: Optional[int] = Field(
        default=None,
        ge=1,
        le=3600,
        description="超时时间（秒）"
    )
    
    max_retries: Optional[int] = Field(
        default=None,
        ge=0,
        le=10,
        description="最大重试次数"
    )


class ToolResponse(BaseSchema):
    """
    工具响应模式
    """
    
    id: str = Field(
        description="工具ID"
    )
    
    name: str = Field(
        description="工具名称"
    )
    
    description: Optional[str] = Field(
        description="工具描述"
    )
    
    type: ToolType = Field(
        description="工具类型"
    )
    
    category: Optional[str] = Field(
        description="工具分类"
    )
    
    schema_definition: Dict[str, Any] = Field(
        description="工具模式定义（JSON Schema）"
    )
    
    implementation: Dict[str, Any] = Field(
        description="工具实现配置"
    )
    
    config: Dict[str, Any] = Field(
        description="工具配置"
    )
    
    version: str = Field(
        description="工具版本"
    )
    
    status: ToolStatus = Field(
        description="工具状态"
    )
    
    security_level: SecurityLevel = Field(
        description="安全级别"
    )
    
    required_permissions: List[str] = Field(
        description="所需权限列表"
    )
    
    tags: List[str] = Field(
        description="标签列表"
    )
    
    is_public: bool = Field(
        description="是否公开"
    )
    
    creator_id: str = Field(
        description="创建者ID"
    )
    
    timeout: Optional[int] = Field(
        description="超时时间（秒）"
    )
    
    max_retries: int = Field(
        description="最大重试次数"
    )
    
    usage_count: int = Field(
        description="使用次数"
    )
    
    success_count: int = Field(
        description="成功次数"
    )
    
    failure_count: int = Field(
        description="失败次数"
    )
    
    avg_execution_time: Optional[float] = Field(
        description="平均执行时间（秒）"
    )
    
    last_used_at: Optional[datetime] = Field(
        description="最后使用时间"
    )
    
    created_at: datetime = Field(
        description="创建时间"
    )
    
    updated_at: datetime = Field(
        description="更新时间"
    )


class ToolExecutionCreate(BaseSchema):
    """
    工具执行创建请求模式
    """
    
    input_data: Dict[str, Any] = Field(
        description="输入数据"
    )
    
    context: Optional[Dict[str, Any]] = Field(
        default_factory=dict,
        description="执行上下文"
    )
    
    timeout_override: Optional[int] = Field(
        default=None,
        ge=1,
        le=3600,
        description="超时时间覆盖（秒）"
    )
    
    config_override: Optional[Dict[str, Any]] = Field(
        default=None,
        description="配置覆盖"
    )
    
    async_execution: bool = Field(
        default=False,
        description="是否异步执行"
    )


class ToolExecutionResponse(BaseSchema):
    """
    工具执行响应模式
    """
    
    id: str = Field(
        description="执行ID"
    )
    
    tool_id: str = Field(
        description="工具ID"
    )
    
    execution_id: str = Field(
        description="执行标识"
    )
    
    status: ExecutionStatus = Field(
        description="执行状态"
    )
    
    input_data: Dict[str, Any] = Field(
        description="输入数据"
    )
    
    output_data: Optional[Dict[str, Any]] = Field(
        description="输出数据"
    )
    
    error_message: Optional[str] = Field(
        description="错误信息"
    )
    
    error_details: Optional[Dict[str, Any]] = Field(
        description="错误详情"
    )
    
    context: Dict[str, Any] = Field(
        description="执行上下文"
    )
    
    user_id: Optional[str] = Field(
        description="用户ID"
    )
    
    agent_id: Optional[str] = Field(
        description="智能体ID"
    )
    
    session_id: Optional[str] = Field(
        description="会话ID"
    )
    
    task_id: Optional[str] = Field(
        description="任务ID"
    )
    
    started_at: Optional[datetime] = Field(
        description="开始时间"
    )
    
    completed_at: Optional[datetime] = Field(
        description="完成时间"
    )
    
    duration: Optional[float] = Field(
        description="执行时长（秒）"
    )
    
    cpu_usage: Optional[float] = Field(
        description="CPU使用率"
    )
    
    memory_usage: Optional[int] = Field(
        description="内存使用量（字节）"
    )
    
    created_at: datetime = Field(
        description="创建时间"
    )
    
    updated_at: datetime = Field(
        description="更新时间"
    )


class ToolCategoryResponse(BaseSchema):
    """
    工具分类响应模式
    """
    
    id: str = Field(
        description="分类ID"
    )
    
    name: str = Field(
        description="分类名称"
    )
    
    description: Optional[str] = Field(
        description="分类描述"
    )
    
    parent_id: Optional[str] = Field(
        description="父分类ID"
    )
    
    level: int = Field(
        description="层级"
    )
    
    sort_order: int = Field(
        description="排序"
    )
    
    icon: Optional[str] = Field(
        description="图标"
    )
    
    tool_count: int = Field(
        description="工具数量"
    )
    
    created_at: datetime = Field(
        description="创建时间"
    )
    
    updated_at: datetime = Field(
        description="更新时间"
    )


class AgentToolResponse(BaseSchema):
    """
    智能体工具关联响应模式
    """
    
    id: str = Field(
        description="关联ID"
    )
    
    agent_id: str = Field(
        description="智能体ID"
    )
    
    tool_id: str = Field(
        description="工具ID"
    )
    
    config: Dict[str, Any] = Field(
        description="工具配置"
    )
    
    permissions: List[str] = Field(
        description="权限列表"
    )
    
    priority: int = Field(
        description="优先级"
    )
    
    is_enabled: bool = Field(
        description="是否启用"
    )
    
    usage_count: int = Field(
        description="使用次数"
    )
    
    last_used_at: Optional[datetime] = Field(
        description="最后使用时间"
    )
    
    created_at: datetime = Field(
        description="创建时间"
    )
    
    updated_at: datetime = Field(
        description="更新时间"
    )


class ToolStatsResponse(BaseSchema):
    """
    工具统计信息响应模式
    """
    
    total_executions: int = Field(
        description="总执行次数"
    )
    
    successful_executions: int = Field(
        description="成功执行次数"
    )
    
    failed_executions: int = Field(
        description="失败执行次数"
    )
    
    success_rate: float = Field(
        description="成功率"
    )
    
    avg_execution_time: float = Field(
        description="平均执行时间（秒）"
    )
    
    total_execution_time: float = Field(
        description="总执行时间（秒）"
    )
    
    avg_cpu_usage: float = Field(
        description="平均CPU使用率"
    )
    
    avg_memory_usage: int = Field(
        description="平均内存使用量（字节）"
    )
    
    unique_users: int = Field(
        description="独立用户数"
    )
    
    unique_agents: int = Field(
        description="独立智能体数"
    )


class ToolSearchRequest(BaseSchema):
    """
    工具搜索请求模式
    """
    
    query: Optional[str] = Field(
        default=None,
        max_length=200,
        description="搜索查询"
    )
    
    type: Optional[ToolType] = Field(
        default=None,
        description="工具类型过滤"
    )
    
    category: Optional[str] = Field(
        default=None,
        description="分类过滤"
    )
    
    tags: List[str] = Field(
        default_factory=list,
        description="标签过滤"
    )
    
    security_level: Optional[SecurityLevel] = Field(
        default=None,
        description="安全级别过滤"
    )
    
    is_public: Optional[bool] = Field(
        default=None,
        description="是否公开过滤"
    )
    
    creator_id: Optional[str] = Field(
        default=None,
        description="创建者过滤"
    )
    
    sort_by: str = Field(
        default="created_at",
        description="排序字段"
    )
    
    sort_order: str = Field(
        default="desc",
        description="排序方向（asc/desc）"
    )


class ToolValidationRequest(BaseSchema):
    """
    工具验证请求模式
    """
    
    schema_definition: Dict[str, Any] = Field(
        description="工具模式定义"
    )
    
    implementation: Dict[str, Any] = Field(
        description="工具实现配置"
    )
    
    test_data: Optional[Dict[str, Any]] = Field(
        default=None,
        description="测试数据"
    )
    
    validate_security: bool = Field(
        default=True,
        description="是否验证安全性"
    )