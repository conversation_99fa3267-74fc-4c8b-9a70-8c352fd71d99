# A2A多智能体系统 Conda环境配置文件
# 使用方法: conda env create -f a2a_environment.yml
# 激活环境: conda activate a2a

name: a2a_backend
channels:
  - conda-forge
  - defaults
dependencies:
  # Python版本
  - python=3.12
  
  # 核心框架
  - pip
  
  # 数据库相关
  - postgresql
  - mysql
  
  # 异步支持（asyncio是Python内置模块，无需安装）
  
  # Web相关
  - requests
  - urllib3
  
  # 安全认证
  - cryptography
  
  # 监控日志
  - prometheus_client
  
  # 测试框架
  - pytest
  - pytest-asyncio
  - pytest-cov
  
  # 通用工具
  - click
  - rich
  - python-dateutil
  - pytz
  
  # 数据处理
  - orjson
  
  # 开发工具
  - black
  - isort
  
  # pip安装的包（conda中不可用或版本不匹配）
  - pip:
    # Google Agent Development Kit (ADK) - 核心框架
    - google-adk>=1.4.2
    
    # LLM客户端
    - openai==1.3.7
    - anthropic==0.7.8
    - google-generativeai==0.3.2
    - dashscope==1.17.0  # 阿里云千问
    - zhipuai==2.0.1     # 智谱AI
    
    # MCP支持
    - mcp==1.0.0
    
    # 其他专用库
    - tiktoken           # OpenAI token计算
    - tenacity          # 重试机制
    - cachetools        # 缓存工具
    - python-slugify    # 字符串处理
    - email-validator   # 邮箱验证
    - phonenumbers      # 电话号码验证
    - pillow           # 图像处理
    - python-magic     # 文件类型检测
    - cryptography     # 加密库
    - jwt              # JWT处理
    - redis            # Redis客户端
    - celery           # 任务队列
    - flower           # Celery监控
    - sentry-sdk       # 错误追踪
    - jaeger-client    # 分布式追踪
    - opentelemetry-api # 可观测性
    - opentelemetry-sdk
    - opentelemetry-instrumentation-fastapi
    - opentelemetry-instrumentation-sqlalchemy
    - opentelemetry-instrumentation-requests
    - opentelemetry-exporter-jaeger
    - python-multipart
    - slowapi          # 限流
    - python-socketio # Socket.IO支持
    - eventlet         # 异步网络库
    - gevent          # 协程库
    - gunicorn        # WSGI服务器
    - supervisor      # 进程管理
    - psutil          # 系统监控
    - memory-profiler # 内存分析
    - line-profiler   # 性能分析
    - py-spy          # Python性能分析
    - locust          # 负载测试
    - faker           # 测试数据生成
    - freezegun       # 时间模拟
    - responses       # HTTP模拟
    - pytest-mock    # Mock测试
    - pytest-cov     # 覆盖率测试
    - pytest-xdist   # 并行测试
    - pytest-benchmark # 性能测试
    - coverage        # 代码覆盖率
    - bandit          # 安全检查
    - safety          # 依赖安全检查
    - vulture         # 死代码检测
    - radon           # 代码复杂度
    - xenon           # 代码质量
    - autopep8        # 代码格式化
    - yapf            # 代码格式化
    - docformatter    # 文档格式化
    - pydocstyle      # 文档风格检查
    - sphinx          # 文档生成
    - sphinx-rtd-theme # 文档主题
    - mkdocs          # 文档生成
    - mkdocs-material # 文档主题

# 环境变量配置
variables:
  PYTHONPATH: "$CONDA_PREFIX/lib/python3.12/site-packages"
  PYTHONUNBUFFERED: "1"
  PYTHONDONTWRITEBYTECODE: "1"
  PYTHONIOENCODING: "utf-8"
  LANG: "zh_CN.UTF-8"
  LC_ALL: "zh_CN.UTF-8"
  TZ: "Asia/Shanghai"
  
# 开发环境特定配置
# 生产环境请移除以下调试相关配置
  PYTHONDEBUG: "1"
  PYTHONASYNCIODEBUG: "1"
  PYTHONDEVMODE: "1"
  PYTHONFAULTHANDLER: "1"
  PYTHONTRACEMALLOC: "1"