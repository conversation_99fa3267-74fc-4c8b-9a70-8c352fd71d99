"""响应模式定义"""

from typing import Any, Dict, List, Optional, Union
from pydantic import BaseModel, Field
from datetime import datetime

from .base import BaseSchema


class BaseResponse(BaseSchema):
    """基础响应模式"""
    success: bool = Field(description="是否成功")
    message: str = Field(description="响应消息")
    timestamp: datetime = Field(default_factory=datetime.now, description="时间戳")
    request_id: Optional[str] = Field(None, description="请求ID")


class SuccessResponse(BaseResponse):
    """成功响应模式"""
    success: bool = Field(True, description="是否成功")
    data: Optional[Any] = Field(None, description="响应数据")


class ErrorResponse(BaseResponse):
    """错误响应模式"""
    success: bool = Field(False, description="是否成功")
    error_code: Optional[str] = Field(None, description="错误代码")
    error_details: Optional[Dict[str, Any]] = Field(None, description="错误详情")


class AgentResponse(BaseResponse):
    """智能体响应模式"""
    agent_id: int = Field(description="智能体ID")
    execution_id: Optional[str] = Field(None, description="执行ID")
    status: str = Field(description="执行状态")
    result: Optional[Any] = Field(None, description="执行结果")
    metadata: Optional[Dict[str, Any]] = Field(None, description="元数据")


class WorkflowResponse(BaseResponse):
    """工作流响应模式"""
    workflow_id: int = Field(description="工作流ID")
    execution_id: Optional[str] = Field(None, description="执行ID")
    status: str = Field(description="执行状态")
    current_step: Optional[str] = Field(None, description="当前步骤")
    result: Optional[Any] = Field(None, description="执行结果")
    metadata: Optional[Dict[str, Any]] = Field(None, description="元数据")


class StreamResponse(BaseResponse):
    """流式响应模式"""
    stream_id: str = Field(description="流ID")
    chunk_id: int = Field(description="块ID")
    data: Any = Field(description="数据块")
    is_final: bool = Field(False, description="是否为最后一块")
    metadata: Optional[Dict[str, Any]] = Field(None, description="元数据")


class PaginatedResponse(SuccessResponse):
    """分页响应模式"""
    total: int = Field(description="总数")
    page: int = Field(description="当前页")
    page_size: int = Field(description="页大小")
    total_pages: int = Field(description="总页数")
    has_next: bool = Field(description="是否有下一页")
    has_prev: bool = Field(description="是否有上一页")
    items: List[Any] = Field(description="数据项")


class ValidationErrorResponse(ErrorResponse):
    """验证错误响应模式"""
    validation_errors: List[Dict[str, Any]] = Field(description="验证错误列表")


class AuthErrorResponse(ErrorResponse):
    """认证错误响应模式"""
    auth_required: bool = Field(True, description="是否需要认证")
    auth_url: Optional[str] = Field(None, description="认证URL")


class RateLimitResponse(ErrorResponse):
    """限流响应模式"""
    retry_after: int = Field(description="重试间隔（秒）")
    limit: int = Field(description="限制数量")
    remaining: int = Field(description="剩余数量")
    reset_time: datetime = Field(description="重置时间")