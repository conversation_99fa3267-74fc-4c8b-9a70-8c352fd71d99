#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A2A多智能体系统 数据库会话服务

替代ADK内存会话，支持用户隔离
"""

import logging
import asyncio
import json
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timedelta
import uuid

from google.adk.sessions.base_session_service import BaseSessionService
from google.adk.sessions.session import Session
from google.ai.generativelanguage import Content, Part

from app.models.user import User
from app.models.session import Session as SessionModel
from app.models.message import Message as SessionMessage
from app.core.logging import get_logger
from app.auth.permissions import check_user_permission
from app.core.database import get_db

class DatabaseSession(Session):
    """
    数据库会话实现
    
    基于数据库存储的会话，支持持久化和用户隔离
    """
    
    def __init__(
        self,
        session_id: str,
        user_id: int,
        owner_id: int,
        session_model: SessionModel,
        logger: Optional[logging.Logger] = None
    ):
        """
        初始化数据库会话
        
        Args:
            session_id: 会话ID
            user_id: 用户ID
            owner_id: 拥有者ID
            session_model: 会话模型
            logger: 日志记录器
        """
        super().__init__(session_id)
        
        self.user_id = user_id
        self.owner_id = owner_id
        self.session_model = session_model
        self.logger = logger or get_logger(f"db_session_{session_id}")
        
        # 消息缓存
        self._message_cache: List[SessionMessage] = []
        self._cache_dirty = False
        
        # 会话状态
        self._is_active = True
        self._last_activity = datetime.now()
        
        self.logger.info(f"DatabaseSession已初始化: {session_id}")
    
    async def _load_messages(self) -> None:
        """
        从数据库加载消息
        """
        try:
            messages = await SessionMessage.get_by_session_id(self.session_model.id)
            self._message_cache = messages or []
            self._cache_dirty = False
            
            self.logger.debug(f"已加载 {len(self._message_cache)} 条消息")
        except Exception as e:
            self.logger.error(f"加载消息错误: {str(e)}")
            self._message_cache = []
    
    async def _save_message(self, message: SessionMessage) -> None:
        """
        保存消息到数据库
        
        Args:
            message: 会话消息
        """
        try:
            await message.save()
            self._message_cache.append(message)
            self._cache_dirty = True
            
            self.logger.debug(f"消息已保存: {message.id}")
        except Exception as e:
            self.logger.error(f"保存消息错误: {str(e)}")
            raise e
    
    async def add_message(self, content: Content, role: str = "user") -> None:
        """
        添加消息到会话
        
        Args:
            content: 消息内容
            role: 消息角色
        """
        try:
            # 更新最后活动时间
            self._last_activity = datetime.now()
            
            # 转换Content为字符串
            content_str = self._content_to_string(content)
            
            # 创建消息记录
            message = SessionMessage(
                session_id=self.session_model.id,
                user_id=self.user_id,
                owner_id=self.owner_id,
                role=role,
                content=content_str,
                metadata={
                    "timestamp": self._last_activity.isoformat(),
                    "content_type": "text"
                }
            )
            
            # 保存消息
            await self._save_message(message)
            
            # 更新会话模型
            self.session_model.message_count = len(self._message_cache)
            self.session_model.last_activity = self._last_activity
            await self.session_model.save()
            
            self.logger.info(f"消息已添加到会话: {role}")
        except Exception as e:
            self.logger.error(f"添加消息错误: {str(e)}")
            raise e
    
    def _content_to_string(self, content: Content) -> str:
        """
        将Content转换为字符串
        
        Args:
            content: ADK Content对象
            
        Returns:
            str: 字符串表示
        """
        try:
            if hasattr(content, 'parts') and content.parts:
                parts_text = []
                for part in content.parts:
                    if hasattr(part, 'text') and part.text:
                        parts_text.append(part.text)
                return "\n".join(parts_text)
            elif hasattr(content, 'text'):
                return content.text
            else:
                return str(content)
        except Exception as e:
            self.logger.error(f"转换Content错误: {str(e)}")
            return str(content)
    
    def _string_to_content(self, text: str) -> Content:
        """
        将字符串转换为Content
        
        Args:
            text: 文本内容
            
        Returns:
            Content: ADK Content对象
        """
        try:
            return Content(parts=[Part(text=text)])
        except Exception as e:
            self.logger.error(f"转换字符串错误: {str(e)}")
            return Content(parts=[Part(text=text)])
    
    async def get_messages(
        self,
        limit: Optional[int] = None,
        offset: int = 0
    ) -> List[Content]:
        """
        获取会话消息
        
        Args:
            limit: 消息数量限制
            offset: 偏移量
            
        Returns:
            List[Content]: 消息列表
        """
        try:
            # 如果缓存为空，从数据库加载
            if not self._message_cache:
                await self._load_messages()
            
            # 应用分页
            messages = self._message_cache[offset:]
            if limit:
                messages = messages[:limit]
            
            # 转换为Content对象
            content_list = []
            for message in messages:
                content = self._string_to_content(message.content)
                content_list.append(content)
            
            return content_list
        except Exception as e:
            self.logger.error(f"获取消息错误: {str(e)}")
            return []
    
    async def get_message_history(
        self,
        include_metadata: bool = False
    ) -> List[Dict[str, Any]]:
        """
        获取消息历史（包含元数据）
        
        Args:
            include_metadata: 是否包含元数据
            
        Returns:
            List[Dict[str, Any]]: 消息历史
        """
        try:
            # 如果缓存为空，从数据库加载
            if not self._message_cache:
                await self._load_messages()
            
            history = []
            for message in self._message_cache:
                msg_dict = {
                    "id": message.id,
                    "role": message.role,
                    "content": message.content,
                    "created_at": message.created_at.isoformat() if message.created_at else None
                }
                
                if include_metadata and message.metadata:
                    msg_dict["metadata"] = message.metadata
                
                history.append(msg_dict)
            
            return history
        except Exception as e:
            self.logger.error(f"获取消息历史错误: {str(e)}")
            return []
    
    async def clear_messages(self) -> None:
        """
        清除会话消息
        """
        try:
            # 删除数据库中的消息
            await SessionMessage.delete_by_session_id(self.session_model.id)
            
            # 清空缓存
            self._message_cache = []
            self._cache_dirty = False
            
            # 更新会话模型
            self.session_model.message_count = 0
            await self.session_model.save()
            
            self.logger.info("会话消息已清除")
        except Exception as e:
            self.logger.error(f"清除消息错误: {str(e)}")
            raise e
    
    async def update_metadata(self, metadata: Dict[str, Any]) -> None:
        """
        更新会话元数据
        
        Args:
            metadata: 元数据
        """
        try:
            # 合并元数据
            current_metadata = self.session_model.metadata or {}
            current_metadata.update(metadata)
            
            self.session_model.metadata = current_metadata
            await self.session_model.save()
            
            self.logger.info("会话元数据已更新")
        except Exception as e:
            self.logger.error(f"更新元数据错误: {str(e)}")
            raise e
    
    def is_active(self) -> bool:
        """
        检查会话是否活跃
        
        Returns:
            bool: 是否活跃
        """
        return self._is_active
    
    async def close(self) -> None:
        """
        关闭会话
        """
        try:
            self._is_active = False
            
            # 更新会话状态
            self.session_model.status = "closed"
            self.session_model.end_time = datetime.now()
            await self.session_model.save()
            
            self.logger.info("会话已关闭")
        except Exception as e:
            self.logger.error(f"关闭会话错误: {str(e)}")
            raise e
    
    def get_session_info(self) -> Dict[str, Any]:
        """
        获取会话信息
        
        Returns:
            Dict[str, Any]: 会话信息
        """
        return {
            "session_id": self.session_id,
            "user_id": self.user_id,
            "owner_id": self.owner_id,
            "message_count": len(self._message_cache),
            "is_active": self._is_active,
            "last_activity": self._last_activity.isoformat(),
            "created_at": self.session_model.created_at.isoformat() if self.session_model.created_at else None,
            "metadata": self.session_model.metadata
        }

class DatabaseSessionService(BaseSessionService):
    """
    数据库会话服务，替代ADK内存会话，支持用户隔离
    
    提供以下功能：
    1. 基于数据库的会话存储
    2. 用户权限验证和隔离
    3. 会话生命周期管理
    4. 消息持久化存储
    5. 会话统计和监控
    6. 自动清理过期会话
    """
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        """
        初始化数据库会话服务
        
        Args:
            logger: 日志记录器
        """
        super().__init__()
        
        self.logger = logger or get_logger("database_session_service")
        
        # 会话缓存
        self._session_cache: Dict[str, DatabaseSession] = {}
        
        # 服务统计
        self.service_stats = {
            "total_sessions_created": 0,
            "active_sessions": 0,
            "total_messages": 0,
            "users_served": set(),
            "last_activity": None
        }
        
        # 启动清理任务
        self._cleanup_task = None
        self._start_cleanup_task()
        
        self.logger.info("DatabaseSessionService已初始化")
    
    def _start_cleanup_task(self) -> None:
        """
        启动清理任务
        """
        async def cleanup_loop():
            while True:
                try:
                    await self._cleanup_expired_sessions()
                    await asyncio.sleep(3600)  # 每小时清理一次
                except Exception as e:
                    self.logger.error(f"清理任务错误: {str(e)}")
                    await asyncio.sleep(300)  # 出错后5分钟重试
        
        self._cleanup_task = asyncio.create_task(cleanup_loop())
    
    async def _check_permission(
        self,
        user_id: int,
        owner_id: int,
        action: str = "create"
    ) -> bool:
        """
        检查用户权限
        
        Args:
            user_id: 用户ID
            owner_id: 拥有者ID
            action: 操作类型
            
        Returns:
            bool: 是否有权限
        """
        try:
            # 检查用户是否存在
            user = await User.get_by_id(user_id)
            if not user:
                self.logger.error(f"用户不存在: {user_id}")
                return False
            
            # 检查用户权限
            has_permission = await check_user_permission(
                user_id=user_id,
                owner_id=owner_id,
                resource_type="session",
                action=action
            )
            
            if not has_permission:
                self.logger.error(f"用户 {user_id} 没有权限{action}会话")
            
            return has_permission
        except Exception as e:
            self.logger.error(f"权限检查错误: {str(e)}")
            return False
    
    async def create_session(
        self,
        user_id: int,
        owner_id: int,
        session_name: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Optional[DatabaseSession]:
        """
        创建新会话
        
        Args:
            user_id: 用户ID
            owner_id: 拥有者ID
            session_name: 会话名称
            metadata: 会话元数据
            
        Returns:
            Optional[DatabaseSession]: 数据库会话实例
        """
        try:
            # 检查权限
            has_permission = await self._check_permission(user_id, owner_id, "create")
            if not has_permission:
                raise PermissionError(f"用户 {user_id} 没有权限创建会话")
            
            # 生成会话ID
            session_id = str(uuid.uuid4())
            
            # 创建会话模型
            session_model = SessionModel(
                session_id=session_id,
                user_id=user_id,
                owner_id=owner_id,
                name=session_name or f"Session_{session_id[:8]}",
                status="active",
                metadata=metadata or {},
                message_count=0
            )
            
            await session_model.save()
            
            # 创建会话实例
            session = DatabaseSession(
                session_id=session_id,
                user_id=user_id,
                owner_id=owner_id,
                session_model=session_model,
                logger=self.logger
            )
            
            # 缓存会话
            self._session_cache[session_id] = session
            
            # 更新统计
            self.service_stats["total_sessions_created"] += 1
            self.service_stats["active_sessions"] += 1
            self.service_stats["users_served"].add(user_id)
            self.service_stats["last_activity"] = datetime.now().isoformat()
            
            self.logger.info(f"会话已创建: {session_id} (用户: {user_id})")
            return session
        except Exception as e:
            self.logger.error(f"创建会话错误: {str(e)}")
            raise e
    
    async def get_session(
        self,
        session_id: str,
        user_id: int
    ) -> Optional[DatabaseSession]:
        """
        获取会话
        
        Args:
            session_id: 会话ID
            user_id: 用户ID
            
        Returns:
            Optional[DatabaseSession]: 数据库会话实例
        """
        try:
            # 检查缓存
            if session_id in self._session_cache:
                session = self._session_cache[session_id]
                # 验证用户权限
                if session.user_id == user_id or await self._check_permission(user_id, session.owner_id, "read"):
                    return session
                else:
                    self.logger.error(f"用户 {user_id} 没有权限访问会话 {session_id}")
                    return None
            
            # 从数据库加载
            session_model = await SessionModel.get_by_session_id(session_id)
            if not session_model:
                self.logger.error(f"会话不存在: {session_id}")
                return None
            
            # 验证用户权限
            if session_model.user_id != user_id and not await self._check_permission(user_id, session_model.owner_id, "read"):
                self.logger.error(f"用户 {user_id} 没有权限访问会话 {session_id}")
                return None
            
            # 创建会话实例
            session = DatabaseSession(
                session_id=session_id,
                user_id=session_model.user_id,
                owner_id=session_model.owner_id,
                session_model=session_model,
                logger=self.logger
            )
            
            # 缓存会话
            self._session_cache[session_id] = session
            
            return session
        except Exception as e:
            self.logger.error(f"获取会话错误: {str(e)}")
            return None
    
    async def list_user_sessions(
        self,
        user_id: int,
        owner_id: int,
        limit: int = 50,
        offset: int = 0
    ) -> List[Dict[str, Any]]:
        """
        列出用户会话
        
        Args:
            user_id: 用户ID
            owner_id: 拥有者ID
            limit: 数量限制
            offset: 偏移量
            
        Returns:
            List[Dict[str, Any]]: 会话列表
        """
        try:
            # 检查权限
            has_permission = await self._check_permission(user_id, owner_id, "list")
            if not has_permission:
                raise PermissionError(f"用户 {user_id} 没有权限列出会话")
            
            # 从数据库获取会话
            sessions = await SessionModel.get_by_user_id(user_id, limit=limit, offset=offset)
            
            session_list = []
            for session_model in sessions:
                session_info = {
                    "session_id": session_model.session_id,
                    "name": session_model.name,
                    "status": session_model.status,
                    "message_count": session_model.message_count,
                    "created_at": session_model.created_at.isoformat() if session_model.created_at else None,
                    "last_activity": session_model.last_activity.isoformat() if session_model.last_activity else None,
                    "metadata": session_model.metadata
                }
                session_list.append(session_info)
            
            return session_list
        except Exception as e:
            self.logger.error(f"列出用户会话错误: {str(e)}")
            return []
    
    async def delete_session(
        self,
        session_id: str,
        user_id: int
    ) -> bool:
        """
        删除会话
        
        Args:
            session_id: 会话ID
            user_id: 用户ID
            
        Returns:
            bool: 是否删除成功
        """
        try:
            # 获取会话
            session = await self.get_session(session_id, user_id)
            if not session:
                return False
            
            # 检查删除权限
            has_permission = await self._check_permission(user_id, session.owner_id, "delete")
            if not has_permission:
                raise PermissionError(f"用户 {user_id} 没有权限删除会话 {session_id}")
            
            # 关闭会话
            await session.close()
            
            # 删除消息
            await SessionMessage.delete_by_session_id(session.session_model.id)
            
            # 删除会话模型
            await session.session_model.delete()
            
            # 从缓存中移除
            if session_id in self._session_cache:
                del self._session_cache[session_id]
            
            # 更新统计
            self.service_stats["active_sessions"] = max(0, self.service_stats["active_sessions"] - 1)
            
            self.logger.info(f"会话已删除: {session_id}")
            return True
        except Exception as e:
            self.logger.error(f"删除会话错误: {str(e)}")
            return False
    
    async def _cleanup_expired_sessions(self) -> None:
        """
        清理过期会话
        """
        try:
            # 定义过期时间（7天）
            expiry_time = datetime.now() - timedelta(days=7)
            
            # 获取过期会话
            expired_sessions = await SessionModel.get_expired_sessions(expiry_time)
            
            for session_model in expired_sessions:
                try:
                    # 删除消息
                    await SessionMessage.delete_by_session_id(session_model.id)
                    
                    # 删除会话
                    await session_model.delete()
                    
                    # 从缓存中移除
                    if session_model.session_id in self._session_cache:
                        del self._session_cache[session_model.session_id]
                    
                    self.logger.info(f"过期会话已清理: {session_model.session_id}")
                except Exception as e:
                    self.logger.error(f"清理会话错误: {str(e)}")
            
            if expired_sessions:
                self.logger.info(f"已清理 {len(expired_sessions)} 个过期会话")
        except Exception as e:
            self.logger.error(f"清理过期会话错误: {str(e)}")
    
    def get_service_stats(self) -> Dict[str, Any]:
        """
        获取服务统计
        
        Returns:
            Dict[str, Any]: 服务统计信息
        """
        stats = self.service_stats.copy()
        stats["users_served"] = len(stats["users_served"])
        stats["cached_sessions"] = len(self._session_cache)
        return stats
    
    async def close(self) -> None:
        """
        关闭服务
        """
        try:
            # 取消清理任务
            if self._cleanup_task:
                self._cleanup_task.cancel()
            
            # 关闭所有活跃会话
            for session in self._session_cache.values():
                await session.close()
            
            # 清空缓存
            self._session_cache.clear()
            
            self.logger.info("DatabaseSessionService已关闭")
        except Exception as e:
            self.logger.error(f"关闭服务错误: {str(e)}")