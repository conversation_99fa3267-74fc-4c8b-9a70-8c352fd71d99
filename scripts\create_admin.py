#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A2A多智能体系统管理员用户创建脚本

功能：
1. 交互式创建管理员用户
2. 验证用户输入
3. 设置用户权限
"""

import asyncio
import sys
import getpass
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from loguru import logger
from app.core.database import init_database
from app.services.user_service import UserService
from app.core.config import get_settings


def validate_email(email: str) -> bool:
    """
    验证邮箱格式
    
    Args:
        email: 邮箱地址
        
    Returns:
        bool: 是否有效
    """
    import re
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None


def validate_password(password: str) -> tuple[bool, str]:
    """
    验证密码强度
    
    Args:
        password: 密码
        
    Returns:
        tuple: (是否有效, 错误信息)
    """
    if len(password) < 8:
        return False, "密码长度至少8位"
    
    if len(password) > 128:
        return False, "密码长度不能超过128位"
    
    if not any(c.isupper() for c in password):
        return False, "密码必须包含至少一个大写字母"
    
    if not any(c.islower() for c in password):
        return False, "密码必须包含至少一个小写字母"
    
    if not any(c.isdigit() for c in password):
        return False, "密码必须包含至少一个数字"
    
    if not any(c in '!@#$%^&*()_+-=[]{}|;:,.<>?' for c in password):
        return False, "密码必须包含至少一个特殊字符"
    
    return True, ""


def get_user_input():
    """
    获取用户输入
    
    Returns:
        dict: 用户数据
    """
    print("\n" + "=" * 50)
    print("创建A2A系统管理员用户")
    print("=" * 50)
    
    # 获取用户名
    while True:
        username = input("请输入用户名 (3-50字符): ").strip()
        if not username:
            print("❌ 用户名不能为空")
            continue
        if len(username) < 3 or len(username) > 50:
            print("❌ 用户名长度必须在3-50字符之间")
            continue
        if not username.replace('_', '').replace('-', '').isalnum():
            print("❌ 用户名只能包含字母、数字、下划线和连字符")
            continue
        break
    
    # 获取邮箱
    while True:
        email = input("请输入邮箱地址: ").strip()
        if not email:
            print("❌ 邮箱地址不能为空")
            continue
        if not validate_email(email):
            print("❌ 邮箱地址格式不正确")
            continue
        break
    
    # 获取密码
    while True:
        password = getpass.getpass("请输入密码 (8-128字符，包含大小写字母、数字和特殊字符): ")
        if not password:
            print("❌ 密码不能为空")
            continue
        
        is_valid, error_msg = validate_password(password)
        if not is_valid:
            print(f"❌ {error_msg}")
            continue
        
        # 确认密码
        confirm_password = getpass.getpass("请再次输入密码确认: ")
        if password != confirm_password:
            print("❌ 两次输入的密码不一致")
            continue
        break
    
    # 获取全名
    full_name = input("请输入全名 (可选): ").strip()
    if not full_name:
        full_name = "系统管理员"
    
    # 获取手机号
    phone = input("请输入手机号 (可选): ").strip()
    
    return {
        "username": username,
        "email": email,
        "password": password,
        "full_name": full_name,
        "phone": phone if phone else None,
        "role": "super_admin",
        "is_verified": True,
        "is_active": True
    }


async def create_admin_user(user_data: dict):
    """
    创建管理员用户
    
    Args:
        user_data: 用户数据
        
    Returns:
        bool: 是否成功
    """
    try:
        user_service = UserService()
        
        # 检查用户名是否已存在
        existing_user = await user_service.get_user_by_username(user_data["username"])
        if existing_user:
            logger.error(f"用户名 '{user_data['username']}' 已存在")
            return False
        
        # 检查邮箱是否已存在
        existing_email = await user_service.get_user_by_email(user_data["email"])
        if existing_email:
            logger.error(f"邮箱 '{user_data['email']}' 已存在")
            return False
        
        # 创建用户
        result = await user_service.create_user(user_data)
        
        if result.get("success"):
            user_id = result["data"]["user_id"]
            logger.success(f"管理员用户创建成功！")
            logger.info(f"用户ID: {user_id}")
            logger.info(f"用户名: {user_data['username']}")
            logger.info(f"邮箱: {user_data['email']}")
            logger.info(f"角色: {user_data['role']}")
            return True
        else:
            logger.error(f"创建用户失败: {result.get('message')}")
            return False
            
    except Exception as e:
        logger.error(f"创建用户时发生错误: {str(e)}")
        return False


async def main():
    """
    主函数
    """
    try:
        # 初始化数据库连接
        await init_database()
        logger.info("数据库连接初始化成功")
        
        # 获取用户输入
        user_data = get_user_input()
        
        # 确认信息
        print("\n" + "-" * 30)
        print("请确认用户信息:")
        print(f"用户名: {user_data['username']}")
        print(f"邮箱: {user_data['email']}")
        print(f"全名: {user_data['full_name']}")
        print(f"手机号: {user_data['phone'] or '未设置'}")
        print(f"角色: {user_data['role']}")
        print("-" * 30)
        
        confirm = input("确认创建用户? (y/N): ").strip().lower()
        if confirm not in ['y', 'yes']:
            print("❌ 用户取消了操作")
            return False
        
        # 创建用户
        success = await create_admin_user(user_data)
        
        if success:
            print("\n" + "=" * 50)
            print("✅ 管理员用户创建成功！")
            print("=" * 50)
            print("下一步:")
            print("1. 启动应用: python main.py")
            print("2. 访问API文档: http://localhost:8000/docs")
            print("3. 使用新创建的管理员账户登录")
            print("4. 建议首次登录后修改密码")
            return True
        else:
            print("\n❌ 管理员用户创建失败")
            return False
            
    except KeyboardInterrupt:
        print("\n❌ 用户中断了操作")
        return False
    except Exception as e:
        logger.error(f"执行过程中发生错误: {str(e)}")
        return False


if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except Exception as e:
        logger.error(f"程序执行失败: {str(e)}")
        sys.exit(1)
