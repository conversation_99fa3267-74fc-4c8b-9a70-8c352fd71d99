#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A2A多智能体系统 工具安全

提供工具执行的安全控制、权限验证和安全审计功能
"""

import asyncio
import hashlib
import hmac
import json
import logging
import re
import time
import threading
from typing import Dict, List, Any, Optional, Union, Callable, Set, Tuple
from dataclasses import dataclass, field, asdict
from enum import Enum
from datetime import datetime, timedelta
from abc import ABC, abstractmethod
import ipaddress
import urllib.parse

# Google ADK imports
from google.ai.generativelanguage import FunctionDeclaration, Schema, Type as SchemaType

from .base_tool import (
    BaseTool, ToolConfig, ToolResult, ToolError, ToolStatus,
    ToolExecutionContext, ToolPermission
)


class SecurityLevel(Enum):
    """安全级别枚举"""
    LOW = "low"          # 低安全级别
    MEDIUM = "medium"    # 中等安全级别
    HIGH = "high"        # 高安全级别
    CRITICAL = "critical"  # 关键安全级别


class ThreatLevel(Enum):
    """威胁级别枚举"""
    INFO = "info"
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class SecurityAction(Enum):
    """安全动作枚举"""
    ALLOW = "allow"      # 允许
    DENY = "deny"        # 拒绝
    WARN = "warn"        # 警告
    AUDIT = "audit"      # 审计
    QUARANTINE = "quarantine"  # 隔离


class AuthenticationMethod(Enum):
    """认证方法枚举"""
    API_KEY = "api_key"
    JWT = "jwt"
    OAUTH2 = "oauth2"
    BASIC = "basic"
    CERTIFICATE = "certificate"
    CUSTOM = "custom"


@dataclass
class SecurityRule:
    """安全规则"""
    id: str
    name: str
    description: str
    pattern: str  # 匹配模式（正则表达式或通配符）
    action: SecurityAction
    threat_level: ThreatLevel
    enabled: bool = True
    priority: int = 0  # 优先级（数字越大优先级越高）
    conditions: Dict[str, Any] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)


@dataclass
class SecurityPolicy:
    """安全策略"""
    id: str
    name: str
    description: str
    security_level: SecurityLevel
    rules: List[SecurityRule] = field(default_factory=list)
    default_action: SecurityAction = SecurityAction.DENY
    enabled: bool = True
    applies_to: List[str] = field(default_factory=list)  # 适用的工具或用户
    metadata: Dict[str, Any] = field(default_factory=dict)
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)


@dataclass
class SecurityIncident:
    """安全事件"""
    id: str
    title: str
    description: str
    threat_level: ThreatLevel
    source: str  # 事件源（工具名称、用户ID等）
    target: str  # 目标（资源、操作等）
    action_taken: SecurityAction
    evidence: Dict[str, Any] = field(default_factory=dict)
    resolved: bool = False
    resolved_at: Optional[datetime] = None
    timestamp: datetime = field(default_factory=datetime.now)
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class SecurityAuditLog:
    """安全审计日志"""
    id: str
    user_id: Optional[str]
    tool_name: str
    operation: str
    resource: Optional[str]
    action: SecurityAction
    result: str  # success, denied, error
    threat_level: ThreatLevel
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    session_id: Optional[str] = None
    request_id: Optional[str] = None
    duration: Optional[float] = None
    details: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.now)


@dataclass
class RateLimitRule:
    """速率限制规则"""
    id: str
    name: str
    pattern: str  # 匹配模式
    max_requests: int  # 最大请求数
    time_window: int  # 时间窗口（秒）
    action: SecurityAction = SecurityAction.DENY
    enabled: bool = True
    applies_to: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class SecurityConfig:
    """安全配置"""
    # 基础配置
    security_level: SecurityLevel = SecurityLevel.MEDIUM
    enable_authentication: bool = True
    enable_authorization: bool = True
    enable_audit_logging: bool = True
    enable_rate_limiting: bool = True
    enable_input_validation: bool = True
    enable_output_filtering: bool = True
    
    # 认证配置
    authentication_methods: List[AuthenticationMethod] = field(
        default_factory=lambda: [AuthenticationMethod.API_KEY]
    )
    token_expiry: int = 3600  # 令牌过期时间（秒）
    max_login_attempts: int = 5
    lockout_duration: int = 300  # 锁定时间（秒）
    
    # 加密配置
    encryption_algorithm: str = "AES-256-GCM"
    hash_algorithm: str = "SHA-256"
    secret_key: Optional[str] = None
    
    # 网络安全
    allowed_ip_ranges: List[str] = field(default_factory=list)
    blocked_ip_ranges: List[str] = field(default_factory=list)
    allowed_domains: List[str] = field(default_factory=list)
    blocked_domains: List[str] = field(default_factory=list)
    
    # 输入验证
    max_input_size: int = 10 * 1024 * 1024  # 10MB
    allowed_file_types: List[str] = field(default_factory=list)
    blocked_file_types: List[str] = field(
        default_factory=lambda: ['.exe', '.bat', '.cmd', '.scr']
    )
    
    # 输出过滤
    sensitive_data_patterns: List[str] = field(
        default_factory=lambda: [
            r'\b\d{4}[\s-]?\d{4}[\s-]?\d{4}[\s-]?\d{4}\b',  # 信用卡号
            r'\b\d{3}-\d{2}-\d{4}\b',  # SSN
            r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'  # 邮箱
        ]
    )
    
    # 审计配置
    audit_log_retention: int = 90  # 审计日志保留天数
    audit_log_level: str = "INFO"
    
    # 事件响应
    enable_incident_response: bool = True
    incident_notification_channels: List[str] = field(default_factory=list)
    
    # 监控配置
    enable_security_monitoring: bool = True
    monitoring_interval: int = 60  # 监控间隔（秒）
    
    # 存储配置
    storage_backend: str = "database"  # memory, file, database
    storage_config: Dict[str, Any] = field(default_factory=dict)


class SecurityError(Exception):
    """安全异常"""
    
    def __init__(self, message: str, threat_level: ThreatLevel = ThreatLevel.MEDIUM,
                 details: Optional[Dict[str, Any]] = None):
        super().__init__(message)
        self.threat_level = threat_level
        self.details = details or {}


class ISecurityProvider(ABC):
    """安全提供者接口"""
    
    @abstractmethod
    async def authenticate(self, credentials: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """认证用户"""
        pass
    
    @abstractmethod
    async def authorize(self, user_info: Dict[str, Any], 
                      resource: str, operation: str) -> bool:
        """授权检查"""
        pass
    
    @abstractmethod
    async def validate_input(self, data: Any, schema: Optional[Dict[str, Any]] = None) -> bool:
        """输入验证"""
        pass
    
    @abstractmethod
    async def filter_output(self, data: Any) -> Any:
        """输出过滤"""
        pass


class DefaultSecurityProvider(ISecurityProvider):
    """默认安全提供者"""
    
    def __init__(self, config: SecurityConfig):
        """
        初始化默认安全提供者
        
        Args:
            config: 安全配置
        """
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # API密钥存储（实际应用中应使用数据库）
        self.api_keys: Dict[str, Dict[str, Any]] = {}
        
        # 登录尝试记录
        self.login_attempts: Dict[str, List[datetime]] = {}
        self.locked_accounts: Dict[str, datetime] = {}
    
    async def authenticate(self, credentials: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """认证用户"""
        auth_method = credentials.get('method')
        
        if auth_method == AuthenticationMethod.API_KEY.value:
            return await self._authenticate_api_key(credentials)
        elif auth_method == AuthenticationMethod.JWT.value:
            return await self._authenticate_jwt(credentials)
        elif auth_method == AuthenticationMethod.BASIC.value:
            return await self._authenticate_basic(credentials)
        else:
            raise SecurityError(
                f"不支持的认证方法: {auth_method}",
                ThreatLevel.MEDIUM
            )
    
    async def _authenticate_api_key(self, credentials: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """API密钥认证"""
        api_key = credentials.get('api_key')
        if not api_key:
            return None
        
        # 查找API密钥
        key_info = self.api_keys.get(api_key)
        if not key_info:
            return None
        
        # 检查密钥是否过期
        if key_info.get('expires_at'):
            expires_at = datetime.fromisoformat(key_info['expires_at'])
            if datetime.now() > expires_at:
                return None
        
        # 检查密钥是否启用
        if not key_info.get('enabled', True):
            return None
        
        return {
            'user_id': key_info.get('user_id'),
            'permissions': key_info.get('permissions', []),
            'metadata': key_info.get('metadata', {})
        }
    
    async def _authenticate_jwt(self, credentials: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """JWT认证"""
        token = credentials.get('token')
        if not token:
            return None
        
        try:
            import jwt
            
            # 解码JWT（需要密钥）
            if not self.config.secret_key:
                raise SecurityError("JWT密钥未配置", ThreatLevel.HIGH)
            
            payload = jwt.decode(
                token,
                self.config.secret_key,
                algorithms=['HS256']
            )
            
            # 检查过期时间
            if 'exp' in payload:
                if datetime.fromtimestamp(payload['exp']) < datetime.now():
                    return None
            
            return {
                'user_id': payload.get('user_id'),
                'permissions': payload.get('permissions', []),
                'metadata': payload.get('metadata', {})
            }
            
        except ImportError:
            raise SecurityError("JWT库未安装", ThreatLevel.HIGH)
        except jwt.InvalidTokenError:
            return None
    
    async def _authenticate_basic(self, credentials: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """基础认证"""
        username = credentials.get('username')
        password = credentials.get('password')
        
        if not username or not password:
            return None
        
        # 检查账户锁定
        if username in self.locked_accounts:
            lock_time = self.locked_accounts[username]
            if datetime.now() < lock_time + timedelta(seconds=self.config.lockout_duration):
                raise SecurityError(
                    f"账户已锁定，请在 {self.config.lockout_duration} 秒后重试",
                    ThreatLevel.MEDIUM
                )
            else:
                del self.locked_accounts[username]
        
        # 记录登录尝试
        now = datetime.now()
        if username not in self.login_attempts:
            self.login_attempts[username] = []
        
        self.login_attempts[username].append(now)
        
        # 清理旧的尝试记录
        cutoff_time = now - timedelta(minutes=15)
        self.login_attempts[username] = [
            attempt for attempt in self.login_attempts[username]
            if attempt > cutoff_time
        ]
        
        # 检查尝试次数
        if len(self.login_attempts[username]) > self.config.max_login_attempts:
            self.locked_accounts[username] = now
            raise SecurityError(
                f"登录尝试次数过多，账户已锁定",
                ThreatLevel.HIGH
            )
        
        # 这里应该验证用户名和密码（连接到用户数据库）
        # 为了演示，我们使用简单的硬编码验证
        if username == "admin" and password == "password":
            return {
                'user_id': username,
                'permissions': ['admin'],
                'metadata': {}
            }
        
        return None
    
    async def authorize(self, user_info: Dict[str, Any], 
                      resource: str, operation: str) -> bool:
        """授权检查"""
        if not self.config.enable_authorization:
            return True
        
        permissions = user_info.get('permissions', [])
        
        # 管理员权限
        if 'admin' in permissions:
            return True
        
        # 检查具体权限
        required_permission = f"{resource}:{operation}"
        if required_permission in permissions:
            return True
        
        # 检查通配符权限
        for permission in permissions:
            if '*' in permission:
                pattern = permission.replace('*', '.*')
                if re.match(pattern, required_permission):
                    return True
        
        return False
    
    async def validate_input(self, data: Any, schema: Optional[Dict[str, Any]] = None) -> bool:
        """输入验证"""
        if not self.config.enable_input_validation:
            return True
        
        try:
            # 检查数据大小
            data_size = len(str(data).encode('utf-8'))
            if data_size > self.config.max_input_size:
                raise SecurityError(
                    f"输入数据过大: {data_size} > {self.config.max_input_size}",
                    ThreatLevel.MEDIUM
                )
            
            # 检查恶意模式
            data_str = str(data).lower()
            malicious_patterns = [
                r'<script[^>]*>.*?</script>',  # XSS
                r'javascript:',  # JavaScript URL
                r'on\w+\s*=',  # 事件处理器
                r'\b(union|select|insert|update|delete|drop)\b.*\b(from|into|table)\b',  # SQL注入
                r'\.\.[\\/]',  # 路径遍历
                r'\b(eval|exec|system|shell_exec)\s*\(',  # 代码执行
            ]
            
            for pattern in malicious_patterns:
                if re.search(pattern, data_str, re.IGNORECASE | re.DOTALL):
                    raise SecurityError(
                        f"检测到恶意输入模式: {pattern}",
                        ThreatLevel.HIGH
                    )
            
            # 如果提供了schema，进行结构验证
            if schema:
                await self._validate_schema(data, schema)
            
            return True
            
        except SecurityError:
            raise
        except Exception as e:
            raise SecurityError(
                f"输入验证失败: {e}",
                ThreatLevel.MEDIUM
            )
    
    async def _validate_schema(self, data: Any, schema: Dict[str, Any]) -> None:
        """模式验证"""
        try:
            import jsonschema
            jsonschema.validate(data, schema)
        except ImportError:
            self.logger.warning("jsonschema库未安装，跳过模式验证")
        except jsonschema.ValidationError as e:
            raise SecurityError(
                f"数据不符合模式: {e.message}",
                ThreatLevel.MEDIUM
            )
    
    async def filter_output(self, data: Any) -> Any:
        """输出过滤"""
        if not self.config.enable_output_filtering:
            return data
        
        try:
            # 转换为字符串进行处理
            if isinstance(data, (dict, list)):
                data_str = json.dumps(data, ensure_ascii=False)
            else:
                data_str = str(data)
            
            # 过滤敏感数据
            filtered_str = data_str
            for pattern in self.config.sensitive_data_patterns:
                filtered_str = re.sub(pattern, '[REDACTED]', filtered_str, flags=re.IGNORECASE)
            
            # 如果原始数据是字典或列表，尝试解析回去
            if isinstance(data, (dict, list)):
                try:
                    return json.loads(filtered_str)
                except json.JSONDecodeError:
                    return filtered_str
            
            return filtered_str
            
        except Exception as e:
            self.logger.error(f"输出过滤失败: {e}")
            return data


class RateLimiter:
    """速率限制器"""
    
    def __init__(self):
        """
        初始化速率限制器
        """
        self.requests: Dict[str, List[datetime]] = {}
        self.lock = threading.RLock()
    
    def is_allowed(self, key: str, rule: RateLimitRule) -> bool:
        """
        检查是否允许请求
        
        Args:
            key: 限制键（通常是用户ID或IP地址）
            rule: 速率限制规则
        
        Returns:
            bool: 是否允许
        """
        if not rule.enabled:
            return True
        
        with self.lock:
            now = datetime.now()
            
            # 初始化请求记录
            if key not in self.requests:
                self.requests[key] = []
            
            # 清理过期请求
            cutoff_time = now - timedelta(seconds=rule.time_window)
            self.requests[key] = [
                req_time for req_time in self.requests[key]
                if req_time > cutoff_time
            ]
            
            # 检查请求数量
            if len(self.requests[key]) >= rule.max_requests:
                return False
            
            # 记录当前请求
            self.requests[key].append(now)
            return True
    
    def get_remaining_requests(self, key: str, rule: RateLimitRule) -> int:
        """
        获取剩余请求数
        
        Args:
            key: 限制键
            rule: 速率限制规则
        
        Returns:
            int: 剩余请求数
        """
        if not rule.enabled:
            return rule.max_requests
        
        with self.lock:
            if key not in self.requests:
                return rule.max_requests
            
            # 清理过期请求
            now = datetime.now()
            cutoff_time = now - timedelta(seconds=rule.time_window)
            self.requests[key] = [
                req_time for req_time in self.requests[key]
                if req_time > cutoff_time
            ]
            
            return max(0, rule.max_requests - len(self.requests[key]))
    
    def reset_limit(self, key: str) -> None:
        """
        重置限制
        
        Args:
            key: 限制键
        """
        with self.lock:
            if key in self.requests:
                del self.requests[key]


class SecurityManager:
    """安全管理器
    
    提供工具执行的安全控制、权限验证和安全审计功能
    """
    
    def __init__(self, config: SecurityConfig, 
                 security_provider: Optional[ISecurityProvider] = None):
        """
        初始化安全管理器
        
        Args:
            config: 安全配置
            security_provider: 安全提供者（可选）
        """
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
        self.logger.setLevel(getattr(logging, config.audit_log_level.upper()))
        
        # 安全提供者
        self.security_provider = security_provider or DefaultSecurityProvider(config)
        
        # 安全策略和规则
        self.policies: Dict[str, SecurityPolicy] = {}
        self.rules: Dict[str, SecurityRule] = {}
        self.rate_limit_rules: Dict[str, RateLimitRule] = {}
        
        # 组件
        self.rate_limiter = RateLimiter()
        
        # 事件和日志
        self.incidents: Dict[str, SecurityIncident] = {}
        self.audit_logs: List[SecurityAuditLog] = []
        
        # IP地址缓存
        self.allowed_ips: Set[ipaddress.IPv4Network] = set()
        self.blocked_ips: Set[ipaddress.IPv4Network] = set()
        
        # 锁
        self.lock = threading.RLock()
        
        # 初始化IP范围
        self._init_ip_ranges()
        
        # 初始化默认策略
        self._init_default_policies()
    
    def _init_ip_ranges(self) -> None:
        """
        初始化IP地址范围
        """
        try:
            for ip_range in self.config.allowed_ip_ranges:
                self.allowed_ips.add(ipaddress.IPv4Network(ip_range, strict=False))
            
            for ip_range in self.config.blocked_ip_ranges:
                self.blocked_ips.add(ipaddress.IPv4Network(ip_range, strict=False))
                
        except Exception as e:
            self.logger.error(f"初始化IP范围失败: {e}")
    
    def _init_default_policies(self) -> None:
        """
        初始化默认安全策略
        """
        # 默认安全策略
        default_policy = SecurityPolicy(
            id="default",
            name="默认安全策略",
            description="系统默认的安全策略",
            security_level=self.config.security_level,
            default_action=SecurityAction.DENY
        )
        
        # 添加默认规则
        default_rules = [
            SecurityRule(
                id="allow_authenticated",
                name="允许已认证用户",
                description="允许已通过认证的用户访问",
                pattern="*",
                action=SecurityAction.ALLOW,
                threat_level=ThreatLevel.LOW,
                conditions={'authenticated': True}
            ),
            SecurityRule(
                id="block_malicious_input",
                name="阻止恶意输入",
                description="阻止包含恶意模式的输入",
                pattern=".*",
                action=SecurityAction.DENY,
                threat_level=ThreatLevel.HIGH,
                conditions={'has_malicious_pattern': True}
            ),
            SecurityRule(
                id="audit_admin_operations",
                name="审计管理员操作",
                description="记录所有管理员操作",
                pattern="admin:*",
                action=SecurityAction.AUDIT,
                threat_level=ThreatLevel.INFO
            )
        ]
        
        default_policy.rules = default_rules
        self.add_policy(default_policy)
        
        # 默认速率限制规则
        default_rate_limit = RateLimitRule(
            id="default_rate_limit",
            name="默认速率限制",
            pattern="*",
            max_requests=100,
            time_window=60,
            action=SecurityAction.DENY
        )
        
        self.add_rate_limit_rule(default_rate_limit)
    
    def add_policy(self, policy: SecurityPolicy) -> None:
        """
        添加安全策略
        
        Args:
            policy: 安全策略
        """
        with self.lock:
            self.policies[policy.id] = policy
            
            # 添加规则到规则字典
            for rule in policy.rules:
                self.rules[rule.id] = rule
            
            self.logger.info(f"安全策略已添加: {policy.name}")
    
    def remove_policy(self, policy_id: str) -> None:
        """
        移除安全策略
        
        Args:
            policy_id: 策略ID
        """
        with self.lock:
            if policy_id in self.policies:
                policy = self.policies[policy_id]
                
                # 移除相关规则
                for rule in policy.rules:
                    if rule.id in self.rules:
                        del self.rules[rule.id]
                
                del self.policies[policy_id]
                self.logger.info(f"安全策略已移除: {policy.name}")
    
    def add_rate_limit_rule(self, rule: RateLimitRule) -> None:
        """
        添加速率限制规则
        
        Args:
            rule: 速率限制规则
        """
        with self.lock:
            self.rate_limit_rules[rule.id] = rule
            self.logger.info(f"速率限制规则已添加: {rule.name}")
    
    def remove_rate_limit_rule(self, rule_id: str) -> None:
        """
        移除速率限制规则
        
        Args:
            rule_id: 规则ID
        """
        with self.lock:
            if rule_id in self.rate_limit_rules:
                rule = self.rate_limit_rules[rule_id]
                del self.rate_limit_rules[rule_id]
                self.logger.info(f"速率限制规则已移除: {rule.name}")
    
    async def authenticate_user(self, credentials: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        认证用户
        
        Args:
            credentials: 认证凭据
        
        Returns:
            Optional[Dict[str, Any]]: 用户信息
        """
        if not self.config.enable_authentication:
            return {'user_id': 'anonymous', 'permissions': []}
        
        try:
            user_info = await self.security_provider.authenticate(credentials)
            
            # 记录认证日志
            await self._log_audit(
                user_id=user_info.get('user_id') if user_info else None,
                tool_name="auth",
                operation="authenticate",
                action=SecurityAction.ALLOW if user_info else SecurityAction.DENY,
                result="success" if user_info else "denied",
                threat_level=ThreatLevel.INFO,
                details={'method': credentials.get('method')}
            )
            
            return user_info
            
        except SecurityError as e:
            await self._log_audit(
                user_id=None,
                tool_name="auth",
                operation="authenticate",
                action=SecurityAction.DENY,
                result="error",
                threat_level=e.threat_level,
                details={'error': str(e), 'method': credentials.get('method')}
            )
            raise
    
    async def authorize_operation(self, user_info: Dict[str, Any],
                                tool_name: str, operation: str,
                                context: Optional[ToolExecutionContext] = None) -> bool:
        """
        授权操作
        
        Args:
            user_info: 用户信息
            tool_name: 工具名称
            operation: 操作名称
            context: 执行上下文
        
        Returns:
            bool: 是否授权
        """
        try:
            # 基础授权检查
            resource = f"{tool_name}:{operation}"
            authorized = await self.security_provider.authorize(
                user_info, tool_name, operation
            )
            
            if not authorized:
                await self._log_audit(
                    user_id=user_info.get('user_id'),
                    tool_name=tool_name,
                    operation=operation,
                    action=SecurityAction.DENY,
                    result="denied",
                    threat_level=ThreatLevel.MEDIUM,
                    details={'reason': 'insufficient_permissions'}
                )
                return False
            
            # 策略检查
            policy_result = await self._check_policies(
                user_info, tool_name, operation, context
            )
            
            if not policy_result:
                await self._log_audit(
                    user_id=user_info.get('user_id'),
                    tool_name=tool_name,
                    operation=operation,
                    action=SecurityAction.DENY,
                    result="denied",
                    threat_level=ThreatLevel.MEDIUM,
                    details={'reason': 'policy_violation'}
                )
                return False
            
            # 速率限制检查
            rate_limit_result = await self._check_rate_limits(
                user_info, tool_name, operation
            )
            
            if not rate_limit_result:
                await self._log_audit(
                    user_id=user_info.get('user_id'),
                    tool_name=tool_name,
                    operation=operation,
                    action=SecurityAction.DENY,
                    result="denied",
                    threat_level=ThreatLevel.MEDIUM,
                    details={'reason': 'rate_limit_exceeded'}
                )
                return False
            
            # IP地址检查
            ip_result = await self._check_ip_address(context)
            
            if not ip_result:
                await self._log_audit(
                    user_id=user_info.get('user_id'),
                    tool_name=tool_name,
                    operation=operation,
                    action=SecurityAction.DENY,
                    result="denied",
                    threat_level=ThreatLevel.HIGH,
                    details={'reason': 'ip_blocked'}
                )
                return False
            
            await self._log_audit(
                user_id=user_info.get('user_id'),
                tool_name=tool_name,
                operation=operation,
                action=SecurityAction.ALLOW,
                result="success",
                threat_level=ThreatLevel.INFO
            )
            
            return True
            
        except Exception as e:
            await self._log_audit(
                user_id=user_info.get('user_id'),
                tool_name=tool_name,
                operation=operation,
                action=SecurityAction.DENY,
                result="error",
                threat_level=ThreatLevel.HIGH,
                details={'error': str(e)}
            )
            raise SecurityError(
                f"授权检查失败: {e}",
                ThreatLevel.HIGH
            )
    
    async def _check_policies(self, user_info: Dict[str, Any],
                            tool_name: str, operation: str,
                            context: Optional[ToolExecutionContext] = None) -> bool:
        """
        检查安全策略
        
        Args:
            user_info: 用户信息
            tool_name: 工具名称
            operation: 操作名称
            context: 执行上下文
        
        Returns:
            bool: 是否通过策略检查
        """
        resource = f"{tool_name}:{operation}"
        
        # 获取适用的策略
        applicable_policies = []
        for policy in self.policies.values():
            if not policy.enabled:
                continue
            
            if not policy.applies_to or tool_name in policy.applies_to:
                applicable_policies.append(policy)
        
        # 如果没有适用的策略，使用默认策略
        if not applicable_policies:
            default_policy = self.policies.get('default')
            if default_policy:
                applicable_policies = [default_policy]
        
        # 检查每个策略
        for policy in applicable_policies:
            # 按优先级排序规则
            sorted_rules = sorted(policy.rules, key=lambda r: r.priority, reverse=True)
            
            for rule in sorted_rules:
                if not rule.enabled:
                    continue
                
                # 检查模式匹配
                if not self._match_pattern(rule.pattern, resource):
                    continue
                
                # 检查条件
                if not self._check_rule_conditions(rule, user_info, context):
                    continue
                
                # 执行动作
                if rule.action == SecurityAction.ALLOW:
                    return True
                elif rule.action == SecurityAction.DENY:
                    return False
                elif rule.action == SecurityAction.WARN:
                    self.logger.warning(f"安全警告: {rule.description}")
                    continue
                elif rule.action == SecurityAction.AUDIT:
                    await self._log_audit(
                        user_id=user_info.get('user_id'),
                        tool_name=tool_name,
                        operation=operation,
                        action=SecurityAction.AUDIT,
                        result="audited",
                        threat_level=rule.threat_level,
                        details={'rule': rule.name}
                    )
                    continue
            
            # 如果没有匹配的规则，使用策略的默认动作
            if policy.default_action == SecurityAction.DENY:
                return False
        
        return True
    
    def _match_pattern(self, pattern: str, text: str) -> bool:
        """
        匹配模式
        
        Args:
            pattern: 模式字符串
            text: 要匹配的文本
        
        Returns:
            bool: 是否匹配
        """
        try:
            # 支持通配符和正则表达式
            if '*' in pattern:
                regex_pattern = pattern.replace('*', '.*')
                return bool(re.match(regex_pattern, text, re.IGNORECASE))
            else:
                return bool(re.match(pattern, text, re.IGNORECASE))
        except re.error:
            return pattern.lower() == text.lower()
    
    def _check_rule_conditions(self, rule: SecurityRule,
                             user_info: Dict[str, Any],
                             context: Optional[ToolExecutionContext] = None) -> bool:
        """
        检查规则条件
        
        Args:
            rule: 安全规则
            user_info: 用户信息
            context: 执行上下文
        
        Returns:
            bool: 是否满足条件
        """
        if not rule.conditions:
            return True
        
        try:
            for condition_key, condition_value in rule.conditions.items():
                if condition_key == 'authenticated':
                    is_authenticated = user_info.get('user_id') is not None
                    if is_authenticated != condition_value:
                        return False
                
                elif condition_key == 'user_id':
                    if user_info.get('user_id') != condition_value:
                        return False
                
                elif condition_key == 'permission':
                    permissions = user_info.get('permissions', [])
                    if condition_value not in permissions:
                        return False
                
                elif condition_key == 'time_range':
                    # 检查时间范围
                    current_hour = datetime.now().hour
                    start_hour, end_hour = condition_value
                    if not (start_hour <= current_hour <= end_hour):
                        return False
                
                elif condition_key == 'ip_range':
                    # 检查IP范围
                    if context and hasattr(context, 'ip_address'):
                        try:
                            client_ip = ipaddress.IPv4Address(context.ip_address)
                            allowed_network = ipaddress.IPv4Network(condition_value, strict=False)
                            if client_ip not in allowed_network:
                                return False
                        except ValueError:
                            return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"检查规则条件失败: {e}")
            return False
    
    async def _check_rate_limits(self, user_info: Dict[str, Any],
                               tool_name: str, operation: str) -> bool:
        """
        检查速率限制
        
        Args:
            user_info: 用户信息
            tool_name: 工具名称
            operation: 操作名称
        
        Returns:
            bool: 是否通过速率限制检查
        """
        if not self.config.enable_rate_limiting:
            return True
        
        resource = f"{tool_name}:{operation}"
        user_id = user_info.get('user_id', 'anonymous')
        
        # 检查适用的速率限制规则
        for rule in self.rate_limit_rules.values():
            if not rule.enabled:
                continue
            
            # 检查模式匹配
            if not self._match_pattern(rule.pattern, resource):
                continue
            
            # 检查适用范围
            if rule.applies_to and user_id not in rule.applies_to:
                continue
            
            # 检查速率限制
            limit_key = f"{user_id}:{resource}"
            if not self.rate_limiter.is_allowed(limit_key, rule):
                return False
        
        return True
    
    async def _check_ip_address(self, context: Optional[ToolExecutionContext] = None) -> bool:
        """
        检查IP地址
        
        Args:
            context: 执行上下文
        
        Returns:
            bool: 是否允许该IP地址
        """
        if not context or not hasattr(context, 'ip_address') or not context.ip_address:
            return True
        
        try:
            client_ip = ipaddress.IPv4Address(context.ip_address)
            
            # 检查黑名单
            for blocked_network in self.blocked_ips:
                if client_ip in blocked_network:
                    return False
            
            # 检查白名单（如果配置了白名单）
            if self.allowed_ips:
                for allowed_network in self.allowed_ips:
                    if client_ip in allowed_network:
                        return True
                return False  # 不在白名单中
            
            return True
            
        except ValueError:
            # 无效的IP地址
            return False
    
    async def validate_input(self, data: Any, 
                           schema: Optional[Dict[str, Any]] = None) -> Any:
        """
        验证输入数据
        
        Args:
            data: 输入数据
            schema: 数据模式（可选）
        
        Returns:
            Any: 验证后的数据
        
        Raises:
            SecurityError: 验证失败
        """
        try:
            # 使用安全提供者验证
            is_valid = await self.security_provider.validate_input(data, schema)
            
            if not is_valid:
                raise SecurityError(
                    "输入验证失败",
                    ThreatLevel.MEDIUM
                )
            
            return data
            
        except SecurityError:
            raise
        except Exception as e:
            raise SecurityError(
                f"输入验证异常: {e}",
                ThreatLevel.MEDIUM
            )
    
    async def filter_output(self, data: Any) -> Any:
        """
        过滤输出数据
        
        Args:
            data: 输出数据
        
        Returns:
            Any: 过滤后的数据
        """
        try:
            return await self.security_provider.filter_output(data)
        except Exception as e:
            self.logger.error(f"输出过滤失败: {e}")
            return data
    
    async def _log_audit(self, user_id: Optional[str], tool_name: str,
                       operation: str, action: SecurityAction,
                       result: str, threat_level: ThreatLevel,
                       resource: Optional[str] = None,
                       ip_address: Optional[str] = None,
                       user_agent: Optional[str] = None,
                       session_id: Optional[str] = None,
                       request_id: Optional[str] = None,
                       duration: Optional[float] = None,
                       details: Optional[Dict[str, Any]] = None) -> None:
        """
        记录审计日志
        
        Args:
            user_id: 用户ID
            tool_name: 工具名称
            operation: 操作名称
            action: 安全动作
            result: 结果
            threat_level: 威胁级别
            resource: 资源
            ip_address: IP地址
            user_agent: 用户代理
            session_id: 会话ID
            request_id: 请求ID
            duration: 持续时间
            details: 详细信息
        """
        if not self.config.enable_audit_logging:
            return
        
        try:
            audit_log = SecurityAuditLog(
                id=f"audit_{int(time.time() * 1000000)}",
                user_id=user_id,
                tool_name=tool_name,
                operation=operation,
                resource=resource,
                action=action,
                result=result,
                threat_level=threat_level,
                ip_address=ip_address,
                user_agent=user_agent,
                session_id=session_id,
                request_id=request_id,
                duration=duration,
                details=details or {}
            )
            
            with self.lock:
                self.audit_logs.append(audit_log)
                
                # 清理过期日志
                cutoff_date = datetime.now() - timedelta(days=self.config.audit_log_retention)
                self.audit_logs = [
                    log for log in self.audit_logs
                    if log.timestamp > cutoff_date
                ]
            
            # 记录到日志文件
            log_level = getattr(logging, threat_level.value.upper(), logging.INFO)
            self.logger.log(
                log_level,
                f"安全审计: {user_id or 'anonymous'} {action.value} {tool_name}:{operation} -> {result}"
            )
            
        except Exception as e:
            self.logger.error(f"记录审计日志失败: {e}")
    
    def create_incident(self, title: str, description: str,
                       threat_level: ThreatLevel, source: str,
                       target: str, action_taken: SecurityAction,
                       evidence: Optional[Dict[str, Any]] = None) -> SecurityIncident:
        """
        创建安全事件
        
        Args:
            title: 事件标题
            description: 事件描述
            threat_level: 威胁级别
            source: 事件源
            target: 目标
            action_taken: 采取的动作
            evidence: 证据
        
        Returns:
            SecurityIncident: 安全事件
        """
        incident = SecurityIncident(
            id=f"incident_{int(time.time() * 1000000)}",
            title=title,
            description=description,
            threat_level=threat_level,
            source=source,
            target=target,
            action_taken=action_taken,
            evidence=evidence or {}
        )
        
        with self.lock:
            self.incidents[incident.id] = incident
        
        self.logger.warning(
            f"安全事件: {title} - {description} (威胁级别: {threat_level.value})"
        )
        
        return incident
    
    def resolve_incident(self, incident_id: str) -> None:
        """
        解决安全事件
        
        Args:
            incident_id: 事件ID
        """
        with self.lock:
            if incident_id in self.incidents:
                incident = self.incidents[incident_id]
                incident.resolved = True
                incident.resolved_at = datetime.now()
                
                self.logger.info(f"安全事件已解决: {incident.title}")
    
    def get_audit_logs(self, user_id: Optional[str] = None,
                      tool_name: Optional[str] = None,
                      start_time: Optional[datetime] = None,
                      end_time: Optional[datetime] = None,
                      limit: int = 100) -> List[SecurityAuditLog]:
        """
        获取审计日志
        
        Args:
            user_id: 用户ID（可选）
            tool_name: 工具名称（可选）
            start_time: 开始时间（可选）
            end_time: 结束时间（可选）
            limit: 限制数量
        
        Returns:
            List[SecurityAuditLog]: 审计日志列表
        """
        with self.lock:
            filtered_logs = []
            
            for log in self.audit_logs:
                # 过滤条件
                if user_id and log.user_id != user_id:
                    continue
                if tool_name and log.tool_name != tool_name:
                    continue
                if start_time and log.timestamp < start_time:
                    continue
                if end_time and log.timestamp > end_time:
                    continue
                
                filtered_logs.append(log)
            
            # 按时间倒序排序
            filtered_logs.sort(key=lambda x: x.timestamp, reverse=True)
            
            return filtered_logs[:limit]
    
    def get_incidents(self, resolved: Optional[bool] = None,
                     threat_level: Optional[ThreatLevel] = None,
                     limit: int = 100) -> List[SecurityIncident]:
        """
        获取安全事件
        
        Args:
            resolved: 是否已解决（可选）
            threat_level: 威胁级别（可选）
            limit: 限制数量
        
        Returns:
            List[SecurityIncident]: 安全事件列表
        """
        with self.lock:
            filtered_incidents = []
            
            for incident in self.incidents.values():
                # 过滤条件
                if resolved is not None and incident.resolved != resolved:
                    continue
                if threat_level and incident.threat_level != threat_level:
                    continue
                
                filtered_incidents.append(incident)
            
            # 按时间倒序排序
            filtered_incidents.sort(key=lambda x: x.timestamp, reverse=True)
            
            return filtered_incidents[:limit]
    
    def get_security_stats(self) -> Dict[str, Any]:
        """
        获取安全统计信息
        
        Returns:
            Dict[str, Any]: 安全统计
        """
        with self.lock:
            # 统计审计日志
            total_logs = len(self.audit_logs)
            recent_logs = len([
                log for log in self.audit_logs
                if log.timestamp > datetime.now() - timedelta(hours=24)
            ])
            
            # 统计事件
            total_incidents = len(self.incidents)
            active_incidents = len([
                incident for incident in self.incidents.values()
                if not incident.resolved
            ])
            
            # 统计威胁级别
            threat_stats = {}
            for level in ThreatLevel:
                threat_stats[level.value] = len([
                    incident for incident in self.incidents.values()
                    if incident.threat_level == level and not incident.resolved
                ])
            
            return {
                'config': asdict(self.config),
                'policies_count': len(self.policies),
                'rules_count': len(self.rules),
                'rate_limit_rules_count': len(self.rate_limit_rules),
                'audit_logs': {
                    'total': total_logs,
                    'recent_24h': recent_logs
                },
                'incidents': {
                    'total': total_incidents,
                    'active': active_incidents,
                    'by_threat_level': threat_stats
                },
                'ip_ranges': {
                    'allowed': len(self.allowed_ips),
                    'blocked': len(self.blocked_ips)
                }
            }


# 全局安全管理器实例
_global_security_manager: Optional[SecurityManager] = None


def get_global_security_manager() -> Optional[SecurityManager]:
    """
    获取全局安全管理器实例
    
    Returns:
        Optional[SecurityManager]: 安全管理器实例
    """
    return _global_security_manager


def set_global_security_manager(manager: SecurityManager) -> None:
    """
    设置全局安全管理器实例
    
    Args:
        manager: 安全管理器实例
    """
    global _global_security_manager
    _global_security_manager = manager