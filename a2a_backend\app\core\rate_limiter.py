# -*- coding: utf-8 -*-
"""
A2A多智能体系统速率限制模块

提供API请求速率限制功能
"""

import time
from typing import Dict, Optional
from collections import defaultdict, deque
from fastapi import HTTPException, status
from app.core.config import get_settings
from app.core.logging import get_logger


class RateLimiter:
    """
    速率限制器
    
    基于滑动窗口算法实现API请求速率限制
    """
    
    def __init__(self):
        """
        初始化速率限制器
        """
        self.settings = get_settings()
        self.logger = get_logger("rate_limiter")
        
        # 存储每个客户端的请求记录
        self.requests: Dict[str, deque] = defaultdict(deque)
        
        # 默认限制配置
        self.default_limit = 100  # 每分钟最大请求数
        self.window_size = 60  # 时间窗口大小（秒）
        
    def check_rate_limit(self, client_id: str, limit: Optional[int] = None) -> bool:
        """
        检查客户端是否超过速率限制
        
        Args:
            client_id: 客户端标识符（通常是IP地址）
            limit: 自定义限制数量
            
        Returns:
            bool: True表示未超限，False表示超限
            
        Raises:
            HTTPException: 当超过速率限制时抛出429错误
        """
        current_time = time.time()
        limit = limit or self.default_limit
        
        # 获取客户端请求记录
        client_requests = self.requests[client_id]
        
        # 清理过期的请求记录
        while client_requests and client_requests[0] < current_time - self.window_size:
            client_requests.popleft()
        
        # 检查是否超过限制
        if len(client_requests) >= limit:
            self.logger.warning(f"客户端 {client_id} 超过速率限制: {len(client_requests)}/{limit}")
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail=f"请求过于频繁，请稍后再试。限制: {limit}次/分钟"
            )
        
        # 记录当前请求
        client_requests.append(current_time)
        
        return True
    
    def get_remaining_requests(self, client_id: str, limit: Optional[int] = None) -> int:
        """
        获取客户端剩余请求次数
        
        Args:
            client_id: 客户端标识符
            limit: 自定义限制数量
            
        Returns:
            int: 剩余请求次数
        """
        current_time = time.time()
        limit = limit or self.default_limit
        
        # 获取客户端请求记录
        client_requests = self.requests[client_id]
        
        # 清理过期的请求记录
        while client_requests and client_requests[0] < current_time - self.window_size:
            client_requests.popleft()
        
        return max(0, limit - len(client_requests))
    
    def reset_client_limit(self, client_id: str):
        """
        重置客户端的速率限制记录
        
        Args:
            client_id: 客户端标识符
        """
        if client_id in self.requests:
            del self.requests[client_id]
            self.logger.info(f"已重置客户端 {client_id} 的速率限制记录")
    
    def cleanup_expired_records(self):
        """
        清理所有过期的请求记录
        """
        current_time = time.time()
        expired_clients = []
        
        for client_id, client_requests in self.requests.items():
            # 清理过期记录
            while client_requests and client_requests[0] < current_time - self.window_size:
                client_requests.popleft()
            
            # 如果没有活跃请求，标记为过期
            if not client_requests:
                expired_clients.append(client_id)
        
        # 删除过期客户端记录
        for client_id in expired_clients:
            del self.requests[client_id]
        
        if expired_clients:
            self.logger.info(f"清理了 {len(expired_clients)} 个过期客户端记录")