-- =====================================================
-- A2A多智能体系统数据库设计
-- 创建时间: 2024
-- 数据库: MySQL 8.0+
-- 编码: UTF8MB4
-- =====================================================

-- 设置字符集和排序规则
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS a2a_system 
DEFAULT CHARACTER SET utf8mb4 
DEFAULT COLLATE utf8mb4_unicode_ci;

USE a2a_system;

-- =====================================================
-- 4.1 用户管理表
-- =====================================================

-- 用户表
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id VARCHAR(255) UNIQUE NOT NULL COMMENT '用户ID',
    username VARCHAR(100) UNIQUE NOT NULL COMMENT '用户名',
    email VARCHAR(255) UNIQUE NOT NULL COMMENT '邮箱',
    password_hash VARCHAR(255) NOT NULL COMMENT '密码哈希',
    full_name VARCHAR(255) COMMENT '全名',
    avatar_url VARCHAR(500) COMMENT '头像URL',
    phone VARCHAR(20) COMMENT '手机号',
    status ENUM('active', 'inactive', 'suspended', 'deleted') DEFAULT 'active' COMMENT '用户状态',
    role ENUM('admin', 'user', 'guest') DEFAULT 'user' COMMENT '用户角色',
    preferences JSON COMMENT '用户偏好设置',
    metadata JSON COMMENT '用户元数据',
    last_login_at TIMESTAMP NULL COMMENT '最后登录时间',
    email_verified_at TIMESTAMP NULL COMMENT '邮箱验证时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_status (status),
    INDEX idx_role (role)
) COMMENT='用户表';

-- 用户会话令牌表
CREATE TABLE user_tokens (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    token_id VARCHAR(255) UNIQUE NOT NULL COMMENT '令牌ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    token_type ENUM('access', 'refresh', 'reset', 'verify') NOT NULL COMMENT '令牌类型',
    token_hash VARCHAR(255) NOT NULL COMMENT '令牌哈希',
    expires_at TIMESTAMP NOT NULL COMMENT '过期时间',
    used_at TIMESTAMP NULL COMMENT '使用时间',
    revoked_at TIMESTAMP NULL COMMENT '撤销时间',
    metadata JSON COMMENT '令牌元数据',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_token_id (token_id),
    INDEX idx_user_id (user_id),
    INDEX idx_token_type (token_type),
    INDEX idx_expires_at (expires_at)
) COMMENT='用户令牌表';

-- 用户权限表
CREATE TABLE user_permissions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    resource_type VARCHAR(100) NOT NULL COMMENT '资源类型',
    resource_id VARCHAR(255) COMMENT '资源ID',
    permission VARCHAR(100) NOT NULL COMMENT '权限名称',
    granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '授权时间',
    expires_at TIMESTAMP NULL COMMENT '过期时间',
    granted_by BIGINT COMMENT '授权人ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (granted_by) REFERENCES users(id) ON DELETE SET NULL,
    UNIQUE KEY uk_user_resource_permission (user_id, resource_type, resource_id, permission),
    INDEX idx_user_id (user_id),
    INDEX idx_resource (resource_type, resource_id),
    INDEX idx_permission (permission)
) COMMENT='用户权限表';

-- 用户操作日志表
CREATE TABLE user_activity_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT COMMENT '用户ID',
    action VARCHAR(100) NOT NULL COMMENT '操作类型',
    resource_type VARCHAR(100) COMMENT '资源类型',
    resource_id VARCHAR(255) COMMENT '资源ID',
    details JSON COMMENT '操作详情',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    status ENUM('success', 'failed', 'error') DEFAULT 'success' COMMENT '操作状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_action (action),
    INDEX idx_resource (resource_type, resource_id),
    INDEX idx_created_at (created_at)
) COMMENT='用户操作日志表';

-- =====================================================
-- 4.2 核心业务表
-- =====================================================

-- 智能体表
CREATE TABLE agents (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL COMMENT '智能体名称',
    type ENUM('sequential', 'parallel', 'loop', 'custom') NOT NULL COMMENT '智能体类型',
    description TEXT COMMENT '智能体描述',
    config JSON COMMENT '智能体配置',
    status ENUM('active', 'inactive', 'error') DEFAULT 'active' COMMENT '状态',
    owner_id BIGINT COMMENT '所有者用户ID',
    is_public BOOLEAN DEFAULT FALSE COMMENT '是否公开',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (owner_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_name (name),
    INDEX idx_type (type),
    INDEX idx_status (status),
    INDEX idx_owner_id (owner_id)
) COMMENT='智能体表';

-- 会话表
CREATE TABLE sessions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    session_id VARCHAR(255) UNIQUE NOT NULL COMMENT '会话ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    agent_id BIGINT COMMENT '关联智能体ID',
    status ENUM('active', 'completed', 'error', 'timeout') DEFAULT 'active' COMMENT '会话状态',
    context JSON COMMENT '会话上下文',
    metadata JSON COMMENT '会话元数据',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (agent_id) REFERENCES agents(id) ON DELETE SET NULL,
    INDEX idx_session_id (session_id),
    INDEX idx_user_id (user_id),
    INDEX idx_agent_id (agent_id),
    INDEX idx_status (status)
) COMMENT='会话表';

-- 消息表
CREATE TABLE messages (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    session_id BIGINT NOT NULL COMMENT '会话ID',
    message_id VARCHAR(255) UNIQUE NOT NULL COMMENT '消息ID',
    role ENUM('user', 'assistant', 'system', 'tool') NOT NULL COMMENT '消息角色',
    content TEXT COMMENT '消息内容',
    metadata JSON COMMENT '消息元数据',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (session_id) REFERENCES sessions(id) ON DELETE CASCADE,
    INDEX idx_session_id (session_id),
    INDEX idx_message_id (message_id),
    INDEX idx_role (role),
    INDEX idx_created_at (created_at)
) COMMENT='消息表';

-- 任务表
CREATE TABLE tasks (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    task_id VARCHAR(255) UNIQUE NOT NULL COMMENT '任务ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    session_id BIGINT COMMENT '关联会话ID',
    agent_id BIGINT COMMENT '执行智能体ID',
    name VARCHAR(255) NOT NULL COMMENT '任务名称',
    description TEXT COMMENT '任务描述',
    status ENUM('pending', 'running', 'completed', 'failed', 'cancelled') DEFAULT 'pending' COMMENT '任务状态',
    priority INT DEFAULT 0 COMMENT '任务优先级',
    input_data JSON COMMENT '输入数据',
    output_data JSON COMMENT '输出数据',
    error_info JSON COMMENT '错误信息',
    started_at TIMESTAMP NULL COMMENT '开始时间',
    completed_at TIMESTAMP NULL COMMENT '完成时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (session_id) REFERENCES sessions(id) ON DELETE SET NULL,
    FOREIGN KEY (agent_id) REFERENCES agents(id) ON DELETE SET NULL,
    INDEX idx_task_id (task_id),
    INDEX idx_user_id (user_id),
    INDEX idx_session_id (session_id),
    INDEX idx_agent_id (agent_id),
    INDEX idx_status (status),
    INDEX idx_priority (priority)
) COMMENT='任务表';

-- 工作流表
CREATE TABLE workflows (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    workflow_id VARCHAR(255) UNIQUE NOT NULL COMMENT '工作流ID',
    name VARCHAR(255) NOT NULL COMMENT '工作流名称',
    description TEXT COMMENT '工作流描述',
    definition JSON NOT NULL COMMENT '工作流定义',
    status ENUM('active', 'inactive', 'deprecated') DEFAULT 'active' COMMENT '状态',
    version VARCHAR(50) DEFAULT '1.0.0' COMMENT '版本号',
    owner_id BIGINT COMMENT '所有者用户ID',
    is_public BOOLEAN DEFAULT FALSE COMMENT '是否公开',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (owner_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_workflow_id (workflow_id),
    INDEX idx_name (name),
    INDEX idx_status (status),
    INDEX idx_owner_id (owner_id)
) COMMENT='工作流表';

-- 工作流实例表
CREATE TABLE workflow_instances (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    instance_id VARCHAR(255) UNIQUE NOT NULL COMMENT '实例ID',
    workflow_id BIGINT NOT NULL COMMENT '工作流ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    session_id BIGINT COMMENT '关联会话ID',
    status ENUM('pending', 'running', 'completed', 'failed', 'cancelled') DEFAULT 'pending' COMMENT '执行状态',
    current_step VARCHAR(255) COMMENT '当前步骤',
    context JSON COMMENT '执行上下文',
    result JSON COMMENT '执行结果',
    error_info JSON COMMENT '错误信息',
    started_at TIMESTAMP NULL COMMENT '开始时间',
    completed_at TIMESTAMP NULL COMMENT '完成时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (workflow_id) REFERENCES workflows(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (session_id) REFERENCES sessions(id) ON DELETE SET NULL,
    INDEX idx_instance_id (instance_id),
    INDEX idx_workflow_id (workflow_id),
    INDEX idx_user_id (user_id),
    INDEX idx_session_id (session_id),
    INDEX idx_status (status)
) COMMENT='工作流实例表';

-- 工具表
CREATE TABLE tools (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    tool_id VARCHAR(255) UNIQUE NOT NULL COMMENT '工具ID',
    name VARCHAR(255) NOT NULL COMMENT '工具名称',
    description TEXT COMMENT '工具描述',
    type ENUM('builtin', 'mcp', 'custom') NOT NULL COMMENT '工具类型',
    config JSON COMMENT '工具配置',
    tool_schema JSON COMMENT '工具Schema',
    status ENUM('active', 'inactive', 'error') DEFAULT 'active' COMMENT '状态',
    version VARCHAR(50) DEFAULT '1.0.0' COMMENT '版本号',
    owner_id BIGINT COMMENT '所有者用户ID',
    is_public BOOLEAN DEFAULT TRUE COMMENT '是否公开',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (owner_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_tool_id (tool_id),
    INDEX idx_name (name),
    INDEX idx_type (type),
    INDEX idx_status (status),
    INDEX idx_owner_id (owner_id)
) COMMENT='工具表';

-- 工具执行记录表
CREATE TABLE tool_executions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    execution_id VARCHAR(255) UNIQUE NOT NULL COMMENT '执行ID',
    tool_id BIGINT NOT NULL COMMENT '工具ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    session_id BIGINT COMMENT '会话ID',
    task_id BIGINT COMMENT '任务ID',
    input_data JSON COMMENT '输入数据',
    output_data JSON COMMENT '输出数据',
    status ENUM('pending', 'running', 'completed', 'failed') DEFAULT 'pending' COMMENT '执行状态',
    error_info JSON COMMENT '错误信息',
    duration_ms INT COMMENT '执行时长(毫秒)',
    started_at TIMESTAMP NULL COMMENT '开始时间',
    completed_at TIMESTAMP NULL COMMENT '完成时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (tool_id) REFERENCES tools(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (session_id) REFERENCES sessions(id) ON DELETE SET NULL,
    FOREIGN KEY (task_id) REFERENCES tasks(id) ON DELETE SET NULL,
    INDEX idx_execution_id (execution_id),
    INDEX idx_tool_id (tool_id),
    INDEX idx_user_id (user_id),
    INDEX idx_session_id (session_id),
    INDEX idx_task_id (task_id),
    INDEX idx_status (status)
) COMMENT='工具执行记录表';

-- =====================================================
-- 4.3 配置管理表
-- =====================================================

-- 系统配置表
CREATE TABLE system_configs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    config_key VARCHAR(255) UNIQUE NOT NULL COMMENT '配置键',
    config_value JSON COMMENT '配置值',
    description TEXT COMMENT '配置描述',
    category VARCHAR(100) COMMENT '配置分类',
    is_encrypted BOOLEAN DEFAULT FALSE COMMENT '是否加密',
    is_public BOOLEAN DEFAULT FALSE COMMENT '是否公开',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_config_key (config_key),
    INDEX idx_category (category)
) COMMENT='系统配置表';

-- LLM配置表
CREATE TABLE llm_configs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    provider VARCHAR(100) NOT NULL COMMENT 'LLM提供商',
    model_name VARCHAR(255) NOT NULL COMMENT '模型名称',
    config JSON NOT NULL COMMENT 'LLM配置',
    api_key_encrypted TEXT COMMENT '加密的API密钥',
    endpoint_url VARCHAR(500) COMMENT '端点URL',
    status ENUM('active', 'inactive', 'error') DEFAULT 'active' COMMENT '状态',
    owner_id BIGINT COMMENT '所有者用户ID',
    is_public BOOLEAN DEFAULT FALSE COMMENT '是否公开',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (owner_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_provider (provider),
    INDEX idx_model_name (model_name),
    INDEX idx_status (status),
    INDEX idx_owner_id (owner_id)
) COMMENT='LLM配置表';

-- 工具配置表
CREATE TABLE tool_configs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    tool_id BIGINT NOT NULL COMMENT '工具ID',
    config_key VARCHAR(255) NOT NULL COMMENT '配置键',
    config_value JSON COMMENT '配置值',
    description TEXT COMMENT '配置描述',
    is_encrypted BOOLEAN DEFAULT FALSE COMMENT '是否加密',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (tool_id) REFERENCES tools(id) ON DELETE CASCADE,
    UNIQUE KEY uk_tool_config (tool_id, config_key),
    INDEX idx_tool_id (tool_id),
    INDEX idx_config_key (config_key)
) COMMENT='工具配置表';

-- 用户配置表
CREATE TABLE user_configs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    config_key VARCHAR(255) NOT NULL COMMENT '配置键',
    config_value JSON COMMENT '配置值',
    description TEXT COMMENT '配置描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY uk_user_config (user_id, config_key),
    INDEX idx_user_id (user_id),
    INDEX idx_config_key (config_key)
) COMMENT='用户配置表';

-- =====================================================
-- 4.4 工件存储表
-- =====================================================

-- 工件表
CREATE TABLE artifacts (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    artifact_id VARCHAR(255) UNIQUE NOT NULL COMMENT '工件ID',
    name VARCHAR(255) NOT NULL COMMENT '工件名称',
    description TEXT COMMENT '工件描述',
    type VARCHAR(100) NOT NULL COMMENT '工件类型',
    mime_type VARCHAR(255) COMMENT 'MIME类型',
    file_size BIGINT COMMENT '文件大小',
    file_hash VARCHAR(255) COMMENT '文件哈希',
    storage_type ENUM('database', 'filesystem') DEFAULT 'database' COMMENT '存储类型',
    storage_path VARCHAR(1000) COMMENT '存储路径',
    content LONGBLOB COMMENT '文件内容',
    metadata JSON COMMENT '工件元数据',
    owner_id BIGINT COMMENT '所有者用户ID',
    is_public BOOLEAN DEFAULT FALSE COMMENT '是否公开',
    status ENUM('active', 'archived', 'deleted') DEFAULT 'active' COMMENT '状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (owner_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_artifact_id (artifact_id),
    INDEX idx_name (name),
    INDEX idx_type (type),
    INDEX idx_owner_id (owner_id),
    INDEX idx_status (status)
) COMMENT='工件表';

-- 工件版本表
CREATE TABLE artifact_versions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    artifact_id BIGINT NOT NULL COMMENT '工件ID',
    version VARCHAR(50) NOT NULL COMMENT '版本号',
    description TEXT COMMENT '版本描述',
    file_size BIGINT COMMENT '文件大小',
    file_hash VARCHAR(255) COMMENT '文件哈希',
    storage_path VARCHAR(1000) COMMENT '存储路径',
    content LONGBLOB COMMENT '文件内容',
    metadata JSON COMMENT '版本元数据',
    created_by BIGINT COMMENT '创建者用户ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (artifact_id) REFERENCES artifacts(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    UNIQUE KEY uk_artifact_version (artifact_id, version),
    INDEX idx_artifact_id (artifact_id),
    INDEX idx_version (version),
    INDEX idx_created_by (created_by)
) COMMENT='工件版本表';

-- 工件访问记录表
CREATE TABLE artifact_access_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    artifact_id BIGINT NOT NULL COMMENT '工件ID',
    user_id BIGINT COMMENT '用户ID',
    action VARCHAR(100) NOT NULL COMMENT '操作类型',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    metadata JSON COMMENT '访问元数据',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (artifact_id) REFERENCES artifacts(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_artifact_id (artifact_id),
    INDEX idx_user_id (user_id),
    INDEX idx_action (action),
    INDEX idx_created_at (created_at)
) COMMENT='工件访问记录表';

-- =====================================================
-- 4.5 监控日志表
-- =====================================================

-- 性能指标表
CREATE TABLE performance_metrics (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    metric_name VARCHAR(255) NOT NULL COMMENT '指标名称',
    metric_value DECIMAL(20,6) NOT NULL COMMENT '指标值',
    metric_unit VARCHAR(50) COMMENT '指标单位',
    labels JSON COMMENT '指标标签',
    resource_type VARCHAR(100) COMMENT '资源类型',
    resource_id VARCHAR(255) COMMENT '资源ID',
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '时间戳',
    INDEX idx_metric_name (metric_name),
    INDEX idx_resource (resource_type, resource_id),
    INDEX idx_timestamp (timestamp)
) COMMENT='性能指标表';

-- 系统日志表
CREATE TABLE system_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    level ENUM('DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL') NOT NULL COMMENT '日志级别',
    logger_name VARCHAR(255) COMMENT '日志器名称',
    message TEXT NOT NULL COMMENT '日志消息',
    module VARCHAR(255) COMMENT '模块名称',
    function VARCHAR(255) COMMENT '函数名称',
    line_number INT COMMENT '行号',
    extra_data JSON COMMENT '额外数据',
    user_id BIGINT COMMENT '用户ID',
    session_id VARCHAR(255) COMMENT '会话ID',
    request_id VARCHAR(255) COMMENT '请求ID',
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '时间戳',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_level (level),
    INDEX idx_logger_name (logger_name),
    INDEX idx_module (module),
    INDEX idx_user_id (user_id),
    INDEX idx_session_id (session_id),
    INDEX idx_request_id (request_id),
    INDEX idx_timestamp (timestamp)
) COMMENT='系统日志表';

-- 错误日志表
CREATE TABLE error_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    error_type VARCHAR(255) NOT NULL COMMENT '错误类型',
    error_message TEXT NOT NULL COMMENT '错误消息',
    stack_trace TEXT COMMENT '堆栈跟踪',
    module VARCHAR(255) COMMENT '模块名称',
    function VARCHAR(255) COMMENT '函数名称',
    line_number INT COMMENT '行号',
    context JSON COMMENT '错误上下文',
    user_id BIGINT COMMENT '用户ID',
    session_id VARCHAR(255) COMMENT '会话ID',
    request_id VARCHAR(255) COMMENT '请求ID',
    resolved_at TIMESTAMP NULL COMMENT '解决时间',
    resolved_by BIGINT COMMENT '解决人ID',
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '时间戳',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (resolved_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_error_type (error_type),
    INDEX idx_module (module),
    INDEX idx_user_id (user_id),
    INDEX idx_session_id (session_id),
    INDEX idx_request_id (request_id),
    INDEX idx_timestamp (timestamp)
) COMMENT='错误日志表';

-- 审计日志表
CREATE TABLE audit_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT COMMENT '用户ID',
    action VARCHAR(100) NOT NULL COMMENT '操作类型',
    resource_type VARCHAR(100) NOT NULL COMMENT '资源类型',
    resource_id VARCHAR(255) COMMENT '资源ID',
    old_values JSON COMMENT '旧值',
    new_values JSON COMMENT '新值',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    session_id VARCHAR(255) COMMENT '会话ID',
    request_id VARCHAR(255) COMMENT '请求ID',
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '时间戳',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_action (action),
    INDEX idx_resource (resource_type, resource_id),
    INDEX idx_timestamp (timestamp)
) COMMENT='审计日志表';

-- =====================================================
-- 初始化数据
-- =====================================================

-- 插入默认系统配置
INSERT INTO system_configs (config_key, config_value, description, category, is_public) VALUES
('system.name', '"A2A多智能体系统"', '系统名称', 'system', TRUE),
('system.version', '"1.0.0"', '系统版本', 'system', TRUE),
('system.description', '"基于Google ADK的多智能体协作系统"', '系统描述', 'system', TRUE),
('auth.jwt_secret', '"your-secret-key-here"', 'JWT密钥', 'auth', FALSE),
('auth.token_expire_hours', '24', '令牌过期时间(小时)', 'auth', TRUE),
('llm.default_provider', '"openai"', '默认LLM提供商', 'llm', TRUE),
('workflow.max_concurrent_instances', '10', '最大并发工作流实例数', 'workflow', TRUE),
('artifact.max_file_size_mb', '100', '最大文件大小(MB)', 'artifact', TRUE);

-- 创建默认管理员用户
INSERT INTO users (user_id, username, email, password_hash, full_name, role, status) VALUES
('admin-001', 'admin', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj3bp.Gm.F5e', '系统管理员', 'admin', 'active');

-- 恢复外键检查
SET FOREIGN_KEY_CHECKS = 1;

-- =====================================================
-- 数据库创建完成
-- =====================================================