#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A2A多智能体系统 OpenAI GPT LLM集成

基于Google ADK LiteLLM的扩展实现
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any, AsyncIterator, Union
from datetime import datetime

from google.adk.models.lite_llm import LiteLlm as ADKLiteLLM
from google.genai.types import Content, Part, GenerateContentResponse
import openai
from openai import AsyncOpenAI

from app.services.llm_service import LLMConfig, LLMProvider
from .base_llm import BaseLLM, StreamingResponse


class OpenAILLM(BaseLLM):
    """
    OpenAI GPT LLM集成
    
    基于Google ADK LiteLLM的扩展实现，支持流式输出和自定义配置
    """
    
    def __init__(self, config: LLMConfig, user_id: int):
        """
        初始化OpenAI LLM
        
        Args:
            config: LLM配置
            user_id: 用户ID
        """
        super().__init__(config, user_id)
        
        self.logger = logging.getLogger(__name__)
        self._adk_llm: Optional[ADKLiteLLM] = None
        self._client: Optional[AsyncOpenAI] = None
        
        # 初始化OpenAI客户端
        self._initialize_client()
        
        self.logger.info(f"OpenAI LLM初始化完成，用户: {user_id}, 模型: {config.model_name}")
    
    def _initialize_client(self) -> None:
        """
        初始化OpenAI客户端
        """
        try:
            # 创建异步OpenAI客户端
            client_kwargs = {
                "api_key": self.config.api_key,
                "timeout": self.config.timeout,
            }
            
            # 如果有自定义端点，使用自定义端点
            if self.config.api_endpoint:
                client_kwargs["base_url"] = self.config.api_endpoint
            
            self._client = AsyncOpenAI(**client_kwargs)
            
            # 创建ADK LiteLLM实例
            self._adk_llm = ADKLiteLLM(
                model_name=f"openai/{self.config.model_name}",
                api_key=self.config.api_key
            )
            
        except Exception as e:
            self.logger.error(f"初始化OpenAI客户端失败: {e}")
            raise
    
    async def generate_content(
        self,
        messages: List[Content],
        stream: bool = False,
        **kwargs
    ) -> Union[GenerateContentResponse, AsyncIterator[GenerateContentResponse]]:
        """
        生成内容
        
        Args:
            messages: 消息列表
            stream: 是否流式输出
            **kwargs: 其他参数
        
        Returns:
            Union[GenerateContentResponse, AsyncIterator[GenerateContentResponse]]: 生成的内容
        """
        try:
            # 记录请求开始
            start_time = datetime.now()
            self.logger.info(f"开始生成内容，用户: {self.user_id}, 流式: {stream}")
            
            # 检查速率限制
            if not await self._check_rate_limit():
                raise Exception("超过速率限制")
            
            # 转换消息格式
            openai_messages = self._convert_messages_to_openai_format(messages)
            
            if stream:
                return self._generate_content_stream(openai_messages, **kwargs)
            else:
                return await self._generate_content_sync(openai_messages, **kwargs)
                
        except Exception as e:
            self.logger.error(f"生成内容失败: {e}")
            # 记录失败统计
            await self._record_usage(0, 0.0, False)
            raise
    
    async def _generate_content_sync(
        self,
        messages: List[Dict[str, Any]],
        **kwargs
    ) -> GenerateContentResponse:
        """
        同步生成内容
        
        Args:
            messages: OpenAI格式的消息列表
            **kwargs: 其他参数
        
        Returns:
            GenerateContentResponse: 生成的内容
        """
        try:
            # 构建请求参数
            request_params = {
                "model": self.config.model_name,
                "messages": messages,
                "temperature": self.config.temperature,
                "max_tokens": self.config.max_tokens,
                "top_p": self.config.extra_params.get("top_p", 1.0),
                "frequency_penalty": self.config.extra_params.get("frequency_penalty", 0.0),
                "presence_penalty": self.config.extra_params.get("presence_penalty", 0.0),
            }
            
            # 添加函数调用支持
            if "functions" in kwargs:
                request_params["functions"] = kwargs["functions"]
            if "function_call" in kwargs:
                request_params["function_call"] = kwargs["function_call"]
            
            # 添加工具调用支持（新版本）
            if "tools" in kwargs:
                request_params["tools"] = kwargs["tools"]
            if "tool_choice" in kwargs:
                request_params["tool_choice"] = kwargs["tool_choice"]
            
            # 调用OpenAI API
            response = await self._client.chat.completions.create(**request_params)
            
            # 转换为ADK格式
            adk_response = self._convert_to_adk_response(response)
            
            # 记录使用统计
            tokens_used = self._count_tokens_from_openai_response(response)
            cost = self._calculate_cost(tokens_used)
            await self._record_usage(tokens_used, cost, True)
            
            self.logger.info(f"内容生成完成，用户: {self.user_id}, tokens: {tokens_used}")
            return adk_response
            
        except Exception as e:
            self.logger.error(f"同步生成内容失败: {e}")
            raise
    
    async def _generate_content_stream(
        self,
        messages: List[Dict[str, Any]],
        **kwargs
    ) -> AsyncIterator[GenerateContentResponse]:
        """
        流式生成内容
        
        Args:
            messages: OpenAI格式的消息列表
            **kwargs: 其他参数
        
        Yields:
            GenerateContentResponse: 生成的内容片段
        """
        try:
            total_tokens = 0
            accumulated_content = ""
            
            # 构建请求参数
            request_params = {
                "model": self.config.model_name,
                "messages": messages,
                "temperature": self.config.temperature,
                "max_tokens": self.config.max_tokens,
                "top_p": self.config.extra_params.get("top_p", 1.0),
                "frequency_penalty": self.config.extra_params.get("frequency_penalty", 0.0),
                "presence_penalty": self.config.extra_params.get("presence_penalty", 0.0),
                "stream": True,
            }
            
            # 添加函数调用支持
            if "functions" in kwargs:
                request_params["functions"] = kwargs["functions"]
            if "function_call" in kwargs:
                request_params["function_call"] = kwargs["function_call"]
            
            # 添加工具调用支持（新版本）
            if "tools" in kwargs:
                request_params["tools"] = kwargs["tools"]
            if "tool_choice" in kwargs:
                request_params["tool_choice"] = kwargs["tool_choice"]
            
            # 调用OpenAI流式API
            stream = await self._client.chat.completions.create(**request_params)
            
            async for chunk in stream:
                if chunk.choices and chunk.choices[0].delta:
                    delta = chunk.choices[0].delta
                    
                    # 处理内容
                    if hasattr(delta, 'content') and delta.content:
                        accumulated_content += delta.content
                        
                        # 转换为ADK格式
                        adk_chunk = self._create_adk_chunk(delta.content, False)
                        
                        # 估算tokens
                        chunk_tokens = await self.estimate_tokens(delta.content)
                        total_tokens += chunk_tokens
                        
                        yield adk_chunk
                    
                    # 处理函数调用
                    if hasattr(delta, 'function_call') and delta.function_call:
                        # 处理函数调用逻辑
                        pass
                    
                    # 处理工具调用
                    if hasattr(delta, 'tool_calls') and delta.tool_calls:
                        # 处理工具调用逻辑
                        pass
                
                # 检查是否完成
                if chunk.choices and chunk.choices[0].finish_reason:
                    # 发送完成标记
                    final_chunk = self._create_adk_chunk("", True)
                    yield final_chunk
                    break
            
            # 记录使用统计
            cost = self._calculate_cost(total_tokens)
            await self._record_usage(total_tokens, cost, True)
            
            self.logger.info(f"流式内容生成完成，用户: {self.user_id}, tokens: {total_tokens}")
            
        except Exception as e:
            self.logger.error(f"流式生成内容失败: {e}")
            raise
    
    def _convert_messages_to_openai_format(self, messages: List[Content]) -> List[Dict[str, Any]]:
        """
        转换消息为OpenAI格式
        
        Args:
            messages: ADK消息列表
        
        Returns:
            List[Dict[str, Any]]: OpenAI格式的消息列表
        """
        try:
            openai_messages = []
            
            for message in messages:
                # 确定角色
                role = "user"  # 默认为用户消息
                
                # 处理消息内容
                content_parts = []
                for part in message.parts:
                    if hasattr(part, 'text') and part.text:
                        content_parts.append({
                            "type": "text",
                            "text": part.text
                        })
                    elif hasattr(part, 'inline_data') and part.inline_data:
                        # 处理图片等内联数据
                        if part.inline_data.mime_type.startswith("image/"):
                            content_parts.append({
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:{part.inline_data.mime_type};base64,{part.inline_data.data}"
                                }
                            })
                
                # 如果只有一个文本部分，简化格式
                if len(content_parts) == 1 and content_parts[0]["type"] == "text":
                    content = content_parts[0]["text"]
                else:
                    content = content_parts
                
                openai_messages.append({
                    "role": role,
                    "content": content
                })
            
            return openai_messages
            
        except Exception as e:
            self.logger.error(f"转换消息为OpenAI格式失败: {e}")
            return []
    
    def _convert_to_adk_response(self, response: Any) -> GenerateContentResponse:
        """
        转换为ADK响应格式
        
        Args:
            response: OpenAI响应
        
        Returns:
            GenerateContentResponse: ADK格式响应
        """
        try:
            # 提取响应内容
            text_content = ""
            function_call = None
            tool_calls = None
            
            if response.choices and response.choices[0].message:
                message = response.choices[0].message
                
                if hasattr(message, 'content') and message.content:
                    text_content = message.content
                
                if hasattr(message, 'function_call') and message.function_call:
                    function_call = message.function_call
                
                if hasattr(message, 'tool_calls') and message.tool_calls:
                    tool_calls = message.tool_calls
            
            # 创建ADK内容
            parts = []
            if text_content:
                parts.append(Part(text=text_content))
            
            # 处理函数调用
            if function_call:
                # 将函数调用信息添加到parts中
                function_info = {
                    "function_call": {
                        "name": function_call.name,
                        "arguments": function_call.arguments
                    }
                }
                parts.append(Part(text=f"[Function Call: {function_info}]"))
            
            # 处理工具调用
            if tool_calls:
                for tool_call in tool_calls:
                    tool_info = {
                        "tool_call": {
                            "id": tool_call.id,
                            "type": tool_call.type,
                            "function": {
                                "name": tool_call.function.name,
                                "arguments": tool_call.function.arguments
                            }
                        }
                    }
                    parts.append(Part(text=f"[Tool Call: {tool_info}]"))
            
            content = Content(parts=parts)
            
            # 创建ADK响应
            adk_response = type('GenerateContentResponse', (), {
                'candidates': [type('Candidate', (), {
                    'content': content,
                    'finish_reason': response.choices[0].finish_reason if response.choices else None,
                    'safety_ratings': []
                })()],
                'usage_metadata': type('UsageMetadata', (), {
                    'prompt_token_count': response.usage.prompt_tokens if response.usage else 0,
                    'candidates_token_count': response.usage.completion_tokens if response.usage else 0,
                    'total_token_count': response.usage.total_tokens if response.usage else 0
                })()
            })()
            
            return adk_response
            
        except Exception as e:
            self.logger.error(f"转换ADK响应格式失败: {e}")
            # 返回一个空的响应
            return type('GenerateContentResponse', (), {
                'candidates': [],
                'usage_metadata': None
            })()
    
    def _create_adk_chunk(self, content: str, is_complete: bool) -> GenerateContentResponse:
        """
        创建ADK格式的流式响应片段
        
        Args:
            content: 内容
            is_complete: 是否完成
        
        Returns:
            GenerateContentResponse: ADK格式响应片段
        """
        try:
            parts = [Part(text=content)] if content else []
            adk_content = Content(parts=parts)
            
            # 创建ADK响应片段
            adk_chunk = type('GenerateContentResponse', (), {
                'candidates': [type('Candidate', (), {
                    'content': adk_content,
                    'finish_reason': 'STOP' if is_complete else None,
                    'safety_ratings': []
                })()],
                'usage_metadata': None
            })()
            
            return adk_chunk
            
        except Exception as e:
            self.logger.error(f"创建ADK响应片段失败: {e}")
            return type('GenerateContentResponse', (), {
                'candidates': [],
                'usage_metadata': None
            })()
    
    def _count_tokens_from_openai_response(self, response: Any) -> int:
        """
        从OpenAI响应中统计token数量
        
        Args:
            response: OpenAI响应
        
        Returns:
            int: token数量
        """
        try:
            if hasattr(response, 'usage') and response.usage:
                return response.usage.total_tokens
            
            # 如果没有usage信息，估算token数量
            text_content = ""
            if response.choices and response.choices[0].message:
                message = response.choices[0].message
                if hasattr(message, 'content') and message.content:
                    text_content = message.content
            
            # 简单估算：1个token约等于4个字符
            return len(text_content) // 4
            
        except Exception as e:
            self.logger.error(f"统计token数量失败: {e}")
            return 0
    
    def _calculate_cost(self, tokens: int) -> float:
        """
        计算成本
        
        Args:
            tokens: token数量
        
        Returns:
            float: 成本
        """
        try:
            # OpenAI的定价（示例，实际价格可能不同）
            # 这里需要根据实际的定价来计算
            cost_per_1k_tokens = self.config.extra_params.get("cost_per_1k_tokens", 0.002)
            return (tokens / 1000) * cost_per_1k_tokens
            
        except Exception as e:
            self.logger.error(f"计算成本失败: {e}")
            return 0.0
    
    async def get_model_info(self) -> Dict[str, Any]:
        """
        获取模型信息
        
        Returns:
            Dict[str, Any]: 模型信息
        """
        try:
            return {
                "provider": LLMProvider.OPENAI,
                "model_name": self.config.model_name,
                "max_tokens": self.config.max_tokens,
                "temperature": self.config.temperature,
                "supports_streaming": True,
                "supports_function_calling": True,
                "supports_vision": "vision" in self.config.model_name.lower(),
                "context_window": self._get_context_window_for_model()
            }
            
        except Exception as e:
            self.logger.error(f"获取模型信息失败: {e}")
            return {}
    
    def _get_context_window_for_model(self) -> int:
        """
        根据模型名称获取上下文窗口大小
        
        Returns:
            int: 上下文窗口大小
        """
        model_context_windows = {
            "gpt-4": 8192,
            "gpt-4-32k": 32768,
            "gpt-4-turbo": 128000,
            "gpt-4-turbo-preview": 128000,
            "gpt-3.5-turbo": 4096,
            "gpt-3.5-turbo-16k": 16384,
        }
        
        for model_prefix, context_window in model_context_windows.items():
            if self.config.model_name.startswith(model_prefix):
                return context_window
        
        # 默认值
        return self.config.extra_params.get("context_window", 4096)
    
    def supports_function_calling(self) -> bool:
        """
        是否支持函数调用
        
        Returns:
            bool: 是否支持函数调用
        """
        # GPT-4和GPT-3.5-turbo支持函数调用
        return "gpt-4" in self.config.model_name or "gpt-3.5-turbo" in self.config.model_name
    
    def supports_vision(self) -> bool:
        """
        是否支持视觉输入
        
        Returns:
            bool: 是否支持视觉输入
        """
        # GPT-4 Vision模型支持视觉输入
        return "vision" in self.config.model_name.lower() or "gpt-4v" in self.config.model_name.lower()
    
    async def validate_config(self) -> bool:
        """
        验证配置
        
        Returns:
            bool: 配置是否有效
        """
        try:
            # 检查必需的配置
            if not self.config.api_key:
                self.logger.error("缺少API密钥")
                return False
            
            if not self.config.model_name:
                self.logger.error("缺少模型名称")
                return False
            
            # 尝试进行简单的API调用来验证配置
            test_messages = [{
                "role": "user",
                "content": "Hello"
            }]
            
            response = await self._client.chat.completions.create(
                model=self.config.model_name,
                messages=test_messages,
                max_tokens=10
            )
            
            if response and response.choices:
                self.logger.info("配置验证成功")
                return True
            else:
                self.logger.error("配置验证失败：无效响应")
                return False
                
        except Exception as e:
            self.logger.error(f"配置验证失败: {e}")
            return False
    
    async def cleanup(self) -> None:
        """
        清理资源
        """
        try:
            if self._client:
                await self._client.close()
            self._client = None
            self._adk_llm = None
            self.logger.info(f"OpenAI LLM资源清理完成，用户: {self.user_id}")
            
        except Exception as e:
            self.logger.error(f"清理资源失败: {e}")
    
    @classmethod
    async def create(
        cls,
        config: LLMConfig,
        user_id: int,
        **kwargs
    ) -> "OpenAILLM":
        """
        创建OpenAI LLM实例
        
        Args:
            config: LLM配置
            user_id: 用户ID
            **kwargs: 其他参数
        
        Returns:
            OpenAILLM: OpenAI LLM实例
        """
        try:
            instance = cls(config, user_id)
            
            # 验证配置
            if not await instance.validate_config():
                raise Exception("OpenAI LLM配置验证失败")
            
            return instance
            
        except Exception as e:
            logging.getLogger(__name__).error(f"创建OpenAI LLM实例失败: {e}")
            raise