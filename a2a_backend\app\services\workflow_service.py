# -*- coding: utf-8 -*-
"""
A2A多智能体系统工作流服务

提供工作流的创建、管理、执行和监控功能，包含用户权限验证
"""

import logging
import asyncio
import json
import uuid
from typing import Dict, List, Optional, Any, Union, Tuple
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc, asc, func

from app.models.workflow import Workflow, WorkflowExecution, WorkflowStep
from app.models.user import User
from app.models.agent import Agent
from app.models.task import Task
from app.schemas.workflow import (
    WorkflowCreate, WorkflowUpdate, WorkflowResponse,
    WorkflowExecutionCreate, WorkflowExecutionResponse,
    WorkflowNodeCreate, WorkflowNodeResponse,
    WorkflowType, WorkflowStatus, WorkflowExecutionStatus
)
from app.core.database import get_db
from app.core.logging import get_logger
from app.auth.permissions import check_user_permission
from app.core.config import get_settings


class WorkflowService:
    """
    工作流服务类
    
    提供以下功能：
    1. 工作流的创建、更新、删除、查询（包含用户权限验证）
    2. 工作流定义的验证和解析（支持用户级别配置）
    3. 工作流实例的管理和执行（包含所有者权限控制）
    4. 工作流状态的跟踪和持久化（支持用户数据隔离）
    5. 工作流性能监控和统计分析
    6. 工作流模板管理和版本控制
    """
    
    def __init__(self, db: Session, user_id: int, logger: Optional[logging.Logger] = None):
        """
        初始化工作流服务
        
        Args:
            db: 数据库会话
            user_id: 当前用户ID
            logger: 日志记录器
        """
        self.db = db
        self.user_id = user_id
        self.logger = logger or get_logger(f"workflow_service_{user_id}")
        self.settings = get_settings()
        
        # 工作流配置
        self.max_workflow_nodes = 100
        self.max_execution_time = 3600  # 1小时
        self.max_concurrent_executions = 10
        
        self.logger.info(f"WorkflowService已初始化，用户ID: {user_id}")
    
    async def _check_workflow_permission(
        self, 
        workflow_id: int, 
        action: str = "read",
        owner_id: Optional[int] = None
    ) -> bool:
        """
        检查工作流权限
        
        Args:
            workflow_id: 工作流ID
            action: 操作类型（read, write, execute, delete）
            owner_id: 资源所有者ID
            
        Returns:
            bool: 是否有权限
        """
        try:
            return await check_user_permission(
                user_id=self.user_id,
                owner_id=owner_id,
                resource_type="workflow",
                action=action,
                resource_id=str(workflow_id)
            )
        except Exception as e:
            self.logger.error(f"检查工作流权限错误: {str(e)}")
            return False
    
    async def _validate_workflow_definition(self, definition: Dict[str, Any]) -> Tuple[bool, Optional[str]]:
        """
        验证工作流定义
        
        Args:
            definition: 工作流定义
            
        Returns:
            Tuple[bool, Optional[str]]: (是否有效, 错误信息)
        """
        try:
            # 检查必需字段
            required_fields = ["nodes", "edges", "start_node", "end_nodes"]
            for field in required_fields:
                if field not in definition:
                    return False, f"缺少必需字段: {field}"
            
            nodes = definition.get("nodes", [])
            edges = definition.get("edges", [])
            start_node = definition.get("start_node")
            end_nodes = definition.get("end_nodes", [])
            
            # 检查节点数量限制
            if len(nodes) > self.max_workflow_nodes:
                return False, f"节点数量超过限制: {len(nodes)} > {self.max_workflow_nodes}"
            
            # 检查节点ID唯一性
            node_ids = [node.get("id") for node in nodes]
            if len(node_ids) != len(set(node_ids)):
                return False, "节点ID不唯一"
            
            # 检查开始节点是否存在
            if start_node not in node_ids:
                return False, f"开始节点不存在: {start_node}"
            
            # 检查结束节点是否存在
            for end_node in end_nodes:
                if end_node not in node_ids:
                    return False, f"结束节点不存在: {end_node}"
            
            # 检查边的有效性
            for edge in edges:
                source = edge.get("source")
                target = edge.get("target")
                
                if source not in node_ids:
                    return False, f"边的源节点不存在: {source}"
                if target not in node_ids:
                    return False, f"边的目标节点不存在: {target}"
            
            # 检查工作流连通性（简单检查）
            # 这里可以添加更复杂的图算法来验证工作流的连通性和循环检测
            
            return True, None
        except Exception as e:
            self.logger.error(f"验证工作流定义错误: {str(e)}")
            return False, f"验证错误: {str(e)}"
    
    async def create_workflow(
        self, 
        workflow_data: WorkflowCreate,
        owner_id: Optional[int] = None
    ) -> Optional[WorkflowResponse]:
        """
        创建工作流
        
        Args:
            workflow_data: 工作流创建数据
            owner_id: 所有者ID，默认为当前用户
            
        Returns:
            Optional[WorkflowResponse]: 创建的工作流信息
        """
        try:
            # 设置所有者
            if owner_id is None:
                owner_id = self.user_id
            
            # 检查用户是否有创建权限
            has_permission = await check_user_permission(
                user_id=self.user_id,
                owner_id=owner_id,
                resource_type="workflow",
                action="create"
            )
            
            if not has_permission:
                self.logger.warning(f"用户 {self.user_id} 没有创建工作流的权限")
                return None
            
            # 验证工作流定义
            is_valid, error_msg = await self._validate_workflow_definition(workflow_data.definition)
            if not is_valid:
                self.logger.warning(f"工作流定义无效: {error_msg}")
                raise ValueError(f"工作流定义无效: {error_msg}")
            
            # 创建工作流记录
            workflow = Workflow(
                user_id=self.user_id,
                owner_id=owner_id,
                workflow_id=str(uuid.uuid4()),
                name=workflow_data.name,
                description=workflow_data.description,
                workflow_type=workflow_data.workflow_type.value,
                category=workflow_data.category,
                definition=json.dumps(workflow_data.definition, ensure_ascii=False),
                config=json.dumps(workflow_data.config or {}, ensure_ascii=False),
                input_schema=json.dumps(workflow_data.input_schema or {}, ensure_ascii=False),
                output_schema=json.dumps(workflow_data.output_schema or {}, ensure_ascii=False),
                trigger_config=json.dumps(workflow_data.trigger_config or {}, ensure_ascii=False),
                timeout=workflow_data.timeout,
                max_retries=workflow_data.max_retries,
                tags=json.dumps(workflow_data.tags or [], ensure_ascii=False),
                is_public=workflow_data.is_public,
                status=WorkflowStatus.DRAFT.value
            )
            
            self.db.add(workflow)
            self.db.commit()
            self.db.refresh(workflow)
            
            # 创建工作流节点
            await self._create_workflow_nodes(workflow.id, workflow_data.definition.get("nodes", []))
            
            self.logger.info(f"工作流创建成功: {workflow.id}")
            
            return WorkflowResponse(
                id=workflow.id,
                workflow_id=workflow.workflow_id,
                name=workflow.name,
                description=workflow.description,
                workflow_type=WorkflowType(workflow.workflow_type),
                status=WorkflowStatus(workflow.status),
                category=workflow.category,
                definition=json.loads(workflow.definition),
                config=json.loads(workflow.config or "{}"),
                input_schema=json.loads(workflow.input_schema or "{}"),
                output_schema=json.loads(workflow.output_schema or "{}"),
                trigger_config=json.loads(workflow.trigger_config or "{}"),
                timeout=workflow.timeout,
                max_retries=workflow.max_retries,
                tags=json.loads(workflow.tags or "[]"),
                is_public=workflow.is_public,
                execution_count=workflow.execution_count,
                success_count=workflow.success_count,
                average_duration=workflow.average_duration,
                created_at=workflow.created_at,
                updated_at=workflow.updated_at,
                last_executed_at=workflow.last_executed_at
            )
        except Exception as e:
            self.db.rollback()
            self.logger.error(f"创建工作流错误: {str(e)}")
            raise
    
    async def _create_workflow_nodes(self, workflow_id: int, nodes_data: List[Dict[str, Any]]) -> None:
        """
        创建工作流节点
        
        Args:
            workflow_id: 工作流ID
            nodes_data: 节点数据列表
        """
        try:
            for node_data in nodes_data:
                node = WorkflowStep(
                    workflow_id=workflow_id,
                    node_id=node_data.get("id"),
                    name=node_data.get("name", ""),
                    node_type=node_data.get("type", "task"),
                    config=json.dumps(node_data.get("config", {}), ensure_ascii=False),
                    position_x=node_data.get("position", {}).get("x", 0),
                    position_y=node_data.get("position", {}).get("y", 0),
                    agent_id=node_data.get("agent_id"),
                    tool_id=node_data.get("tool_id"),
                    dependencies=json.dumps(node_data.get("dependencies", []), ensure_ascii=False),
                    conditions=json.dumps(node_data.get("conditions", {}), ensure_ascii=False),
                    timeout=node_data.get("timeout"),
                    max_retries=node_data.get("max_retries", 3)
                )
                
                self.db.add(node)
            
            self.db.commit()
        except Exception as e:
            self.db.rollback()
            self.logger.error(f"创建工作流节点错误: {str(e)}")
            raise
    
    async def get_workflow(self, workflow_id: int) -> Optional[WorkflowResponse]:
        """
        获取工作流详情
        
        Args:
            workflow_id: 工作流ID
            
        Returns:
            Optional[WorkflowResponse]: 工作流信息
        """
        try:
            # 查询工作流
            workflow = self.db.query(Workflow).filter(Workflow.id == workflow_id).first()
            if not workflow:
                return None
            
            # 检查权限
            has_permission = await self._check_workflow_permission(
                workflow_id=workflow_id,
                action="read",
                owner_id=workflow.owner_id
            )
            
            if not has_permission:
                self.logger.warning(f"用户 {self.user_id} 没有访问工作流 {workflow_id} 的权限")
                return None
            
            return WorkflowResponse(
                id=workflow.id,
                workflow_id=workflow.workflow_id,
                name=workflow.name,
                description=workflow.description,
                workflow_type=WorkflowType(workflow.workflow_type),
                status=WorkflowStatus(workflow.status),
                category=workflow.category,
                definition=json.loads(workflow.definition),
                config=json.loads(workflow.config or "{}"),
                input_schema=json.loads(workflow.input_schema or "{}"),
                output_schema=json.loads(workflow.output_schema or "{}"),
                trigger_config=json.loads(workflow.trigger_config or "{}"),
                timeout=workflow.timeout,
                max_retries=workflow.max_retries,
                tags=json.loads(workflow.tags or "[]"),
                is_public=workflow.is_public,
                execution_count=workflow.execution_count,
                success_count=workflow.success_count,
                average_duration=workflow.average_duration,
                created_at=workflow.created_at,
                updated_at=workflow.updated_at,
                last_executed_at=workflow.last_executed_at
            )
        except Exception as e:
            self.logger.error(f"获取工作流错误: {str(e)}")
            return None
    
    async def update_workflow(
        self, 
        workflow_id: int, 
        workflow_data: WorkflowUpdate
    ) -> Optional[WorkflowResponse]:
        """
        更新工作流
        
        Args:
            workflow_id: 工作流ID
            workflow_data: 更新数据
            
        Returns:
            Optional[WorkflowResponse]: 更新后的工作流信息
        """
        try:
            # 查询工作流
            workflow = self.db.query(Workflow).filter(Workflow.id == workflow_id).first()
            if not workflow:
                return None
            
            # 检查权限
            has_permission = await self._check_workflow_permission(
                workflow_id=workflow_id,
                action="write",
                owner_id=workflow.owner_id
            )
            
            if not has_permission:
                self.logger.warning(f"用户 {self.user_id} 没有更新工作流 {workflow_id} 的权限")
                return None
            
            # 更新字段
            update_data = workflow_data.dict(exclude_unset=True)
            
            for field, value in update_data.items():
                if field == "definition" and value is not None:
                    # 验证工作流定义
                    is_valid, error_msg = await self._validate_workflow_definition(value)
                    if not is_valid:
                        raise ValueError(f"工作流定义无效: {error_msg}")
                    setattr(workflow, field, json.dumps(value, ensure_ascii=False))
                elif field in ["config", "input_schema", "output_schema", "trigger_config", "tags"] and value is not None:
                    setattr(workflow, field, json.dumps(value, ensure_ascii=False))
                elif field == "workflow_type" and value is not None:
                    setattr(workflow, field, value.value)
                elif field == "status" and value is not None:
                    setattr(workflow, field, value.value)
                elif hasattr(workflow, field) and value is not None:
                    setattr(workflow, field, value)
            
            workflow.updated_at = datetime.utcnow()
            
            self.db.commit()
            self.db.refresh(workflow)
            
            # 如果定义更新了，重新创建节点
            if "definition" in update_data:
                await self._recreate_workflow_nodes(workflow_id, update_data["definition"].get("nodes", []))
            
            self.logger.info(f"工作流更新成功: {workflow_id}")
            
            return await self.get_workflow(workflow_id)
        except Exception as e:
            self.db.rollback()
            self.logger.error(f"更新工作流错误: {str(e)}")
            raise
    
    async def _recreate_workflow_nodes(self, workflow_id: int, nodes_data: List[Dict[str, Any]]) -> None:
        """
        重新创建工作流节点
        
        Args:
            workflow_id: 工作流ID
            nodes_data: 节点数据列表
        """
        try:
            # 删除现有节点
            self.db.query(WorkflowStep).filter(WorkflowStep.workflow_id == workflow_id).delete()
            
            # 创建新节点
            await self._create_workflow_nodes(workflow_id, nodes_data)
        except Exception as e:
            self.db.rollback()
            self.logger.error(f"重新创建工作流节点错误: {str(e)}")
            raise
    
    async def delete_workflow(self, workflow_id: int) -> bool:
        """
        删除工作流
        
        Args:
            workflow_id: 工作流ID
            
        Returns:
            bool: 是否删除成功
        """
        try:
            # 查询工作流
            workflow = self.db.query(Workflow).filter(Workflow.id == workflow_id).first()
            if not workflow:
                return False
            
            # 检查权限
            has_permission = await self._check_workflow_permission(
                workflow_id=workflow_id,
                action="delete",
                owner_id=workflow.owner_id
            )
            
            if not has_permission:
                self.logger.warning(f"用户 {self.user_id} 没有删除工作流 {workflow_id} 的权限")
                return False
            
            # 检查是否有正在执行的实例
            running_executions = self.db.query(WorkflowExecution).filter(
                and_(
                    WorkflowExecution.workflow_id == workflow_id,
                    WorkflowExecution.status.in_(["pending", "running", "paused"])
                )
            ).count()
            
            if running_executions > 0:
                self.logger.warning(f"工作流 {workflow_id} 有正在执行的实例，无法删除")
                return False
            
            # 软删除
            workflow.is_active = False
            workflow.status = WorkflowStatus.ARCHIVED.value
            workflow.updated_at = datetime.utcnow()
            
            self.db.commit()
            
            self.logger.info(f"工作流删除成功: {workflow_id}")
            return True
        except Exception as e:
            self.db.rollback()
            self.logger.error(f"删除工作流错误: {str(e)}")
            return False
    
    async def list_workflows(
        self,
        page: int = 1,
        size: int = 20,
        workflow_type: Optional[WorkflowType] = None,
        status: Optional[WorkflowStatus] = None,
        category: Optional[str] = None,
        is_public: Optional[bool] = None,
        search: Optional[str] = None,
        sort_by: str = "created_at",
        sort_order: str = "desc"
    ) -> Tuple[List[WorkflowResponse], int]:
        """
        获取工作流列表
        
        Args:
            page: 页码
            size: 每页数量
            workflow_type: 工作流类型过滤
            status: 状态过滤
            category: 分类过滤
            is_public: 是否公开过滤
            search: 搜索关键词
            sort_by: 排序字段
            sort_order: 排序方向
            
        Returns:
            Tuple[List[WorkflowResponse], int]: (工作流列表, 总数)
        """
        try:
            # 构建查询
            query = self.db.query(Workflow).filter(Workflow.is_active == True)
            
            # 权限过滤：只显示用户有权限访问的工作流
            query = query.filter(
                or_(
                    Workflow.user_id == self.user_id,
                    Workflow.owner_id == self.user_id,
                    Workflow.is_public == True
                )
            )
            
            # 应用过滤条件
            if workflow_type:
                query = query.filter(Workflow.workflow_type == workflow_type.value)
            
            if status:
                query = query.filter(Workflow.status == status.value)
            
            if category:
                query = query.filter(Workflow.category == category)
            
            if is_public is not None:
                query = query.filter(Workflow.is_public == is_public)
            
            if search:
                search_pattern = f"%{search}%"
                query = query.filter(
                    or_(
                        Workflow.name.ilike(search_pattern),
                        Workflow.description.ilike(search_pattern),
                        Workflow.category.ilike(search_pattern)
                    )
                )
            
            # 获取总数
            total = query.count()
            
            # 应用排序
            if hasattr(Workflow, sort_by):
                if sort_order.lower() == "desc":
                    query = query.order_by(desc(getattr(Workflow, sort_by)))
                else:
                    query = query.order_by(asc(getattr(Workflow, sort_by)))
            
            # 应用分页
            offset = (page - 1) * size
            workflows = query.offset(offset).limit(size).all()
            
            # 转换为响应格式
            workflow_responses = []
            for workflow in workflows:
                workflow_response = WorkflowResponse(
                    id=workflow.id,
                    workflow_id=workflow.workflow_id,
                    name=workflow.name,
                    description=workflow.description,
                    workflow_type=WorkflowType(workflow.workflow_type),
                    status=WorkflowStatus(workflow.status),
                    category=workflow.category,
                    definition=json.loads(workflow.definition),
                    config=json.loads(workflow.config or "{}"),
                    input_schema=json.loads(workflow.input_schema or "{}"),
                    output_schema=json.loads(workflow.output_schema or "{}"),
                    trigger_config=json.loads(workflow.trigger_config or "{}"),
                    timeout=workflow.timeout,
                    max_retries=workflow.max_retries,
                    tags=json.loads(workflow.tags or "[]"),
                    is_public=workflow.is_public,
                    execution_count=workflow.execution_count,
                    success_count=workflow.success_count,
                    average_duration=workflow.average_duration,
                    created_at=workflow.created_at,
                    updated_at=workflow.updated_at,
                    last_executed_at=workflow.last_executed_at
                )
                workflow_responses.append(workflow_response)
            
            return workflow_responses, total
        except Exception as e:
            self.logger.error(f"获取工作流列表错误: {str(e)}")
            return [], 0
    
    async def execute_workflow(
        self,
        workflow_id: int,
        input_data: Optional[Dict[str, Any]] = None,
        execution_config: Optional[Dict[str, Any]] = None
    ) -> Optional[str]:
        """
        执行工作流
        
        Args:
            workflow_id: 工作流ID
            input_data: 输入数据
            execution_config: 执行配置
            
        Returns:
            Optional[str]: 执行ID
        """
        try:
            # 查询工作流
            workflow = self.db.query(Workflow).filter(Workflow.id == workflow_id).first()
            if not workflow:
                return None
            
            # 检查权限
            has_permission = await self._check_workflow_permission(
                workflow_id=workflow_id,
                action="execute",
                owner_id=workflow.owner_id
            )
            
            if not has_permission:
                self.logger.warning(f"用户 {self.user_id} 没有执行工作流 {workflow_id} 的权限")
                return None
            
            # 检查工作流状态
            if workflow.status != WorkflowStatus.ACTIVE.value:
                self.logger.warning(f"工作流 {workflow_id} 状态不是活跃状态，无法执行")
                return None
            
            # 检查并发执行限制
            running_count = self.db.query(WorkflowExecution).filter(
                and_(
                    WorkflowExecution.workflow_id == workflow_id,
                    WorkflowExecution.status.in_(["pending", "running"])
                )
            ).count()
            
            if running_count >= self.max_concurrent_executions:
                self.logger.warning(f"工作流 {workflow_id} 并发执行数量超过限制")
                return None
            
            # 创建执行记录
            execution_id = str(uuid.uuid4())
            execution = WorkflowExecution(
                workflow_id=workflow_id,
                user_id=self.user_id,
                owner_id=workflow.owner_id,
                execution_id=execution_id,
                status=WorkflowExecutionStatus.PENDING.value,
                input_data=json.dumps(input_data or {}, ensure_ascii=False),
                config=json.dumps(execution_config or {}, ensure_ascii=False),
                started_at=datetime.utcnow()
            )
            
            self.db.add(execution)
            self.db.commit()
            
            # 更新工作流统计
            workflow.execution_count += 1
            workflow.last_executed_at = datetime.utcnow()
            self.db.commit()
            
            self.logger.info(f"工作流执行已创建: {execution_id}")
            
            # 这里应该启动实际的工作流执行
            # 在实际实现中，这里会调用工作流引擎来执行工作流
            # await self._start_workflow_execution(execution_id)
            
            return execution_id
        except Exception as e:
            self.db.rollback()
            self.logger.error(f"执行工作流错误: {str(e)}")
            return None
    
    async def get_workflow_execution(
        self, 
        execution_id: str
    ) -> Optional[WorkflowExecutionResponse]:
        """
        获取工作流执行详情
        
        Args:
            execution_id: 执行ID
            
        Returns:
            Optional[WorkflowExecutionResponse]: 执行信息
        """
        try:
            # 查询执行记录
            execution = self.db.query(WorkflowExecution).filter(
                WorkflowExecution.execution_id == execution_id
            ).first()
            
            if not execution:
                return None
            
            # 检查权限
            has_permission = await self._check_workflow_permission(
                workflow_id=execution.workflow_id,
                action="read",
                owner_id=execution.owner_id
            )
            
            if not has_permission:
                self.logger.warning(f"用户 {self.user_id} 没有访问执行 {execution_id} 的权限")
                return None
            
            return WorkflowExecutionResponse(
                id=execution.id,
                execution_id=execution.execution_id,
                workflow_id=execution.workflow_id,
                user_id=execution.user_id,
                status=WorkflowExecutionStatus(execution.status),
                input_data=json.loads(execution.input_data or "{}"),
                output_data=json.loads(execution.output_data or "{}"),
                config=json.loads(execution.config or "{}"),
                current_node=execution.current_node,
                completed_nodes=json.loads(execution.completed_nodes or "[]"),
                failed_nodes=json.loads(execution.failed_nodes or "[]"),
                error_info=json.loads(execution.error_info or "{}"),
                metrics=json.loads(execution.metrics or "{}"),
                started_at=execution.started_at,
                completed_at=execution.completed_at,
                created_at=execution.created_at,
                updated_at=execution.updated_at
            )
        except Exception as e:
            self.logger.error(f"获取工作流执行错误: {str(e)}")
            return None
    
    async def cancel_workflow_execution(self, execution_id: str) -> bool:
        """
        取消工作流执行
        
        Args:
            execution_id: 执行ID
            
        Returns:
            bool: 是否取消成功
        """
        try:
            # 查询执行记录
            execution = self.db.query(WorkflowExecution).filter(
                WorkflowExecution.execution_id == execution_id
            ).first()
            
            if not execution:
                return False
            
            # 检查权限
            has_permission = await self._check_workflow_permission(
                workflow_id=execution.workflow_id,
                action="execute",
                owner_id=execution.owner_id
            )
            
            if not has_permission:
                self.logger.warning(f"用户 {self.user_id} 没有取消执行 {execution_id} 的权限")
                return False
            
            # 检查状态
            if execution.status not in ["pending", "running", "paused"]:
                self.logger.warning(f"执行 {execution_id} 状态不允许取消")
                return False
            
            # 更新状态
            execution.status = WorkflowExecutionStatus.CANCELLED.value
            execution.completed_at = datetime.utcnow()
            execution.updated_at = datetime.utcnow()
            
            self.db.commit()
            
            self.logger.info(f"工作流执行已取消: {execution_id}")
            
            # 这里应该通知工作流引擎停止执行
            # await self._stop_workflow_execution(execution_id)
            
            return True
        except Exception as e:
            self.db.rollback()
            self.logger.error(f"取消工作流执行错误: {str(e)}")
            return False
    
    async def get_workflow_statistics(self, workflow_id: int) -> Optional[Dict[str, Any]]:
        """
        获取工作流统计信息
        
        Args:
            workflow_id: 工作流ID
            
        Returns:
            Optional[Dict[str, Any]]: 统计信息
        """
        try:
            # 查询工作流
            workflow = self.db.query(Workflow).filter(Workflow.id == workflow_id).first()
            if not workflow:
                return None
            
            # 检查权限
            has_permission = await self._check_workflow_permission(
                workflow_id=workflow_id,
                action="read",
                owner_id=workflow.owner_id
            )
            
            if not has_permission:
                return None
            
            # 查询执行统计
            executions = self.db.query(WorkflowExecution).filter(
                WorkflowExecution.workflow_id == workflow_id
            ).all()
            
            total_executions = len(executions)
            successful_executions = len([e for e in executions if e.status == "completed"])
            failed_executions = len([e for e in executions if e.status == "failed"])
            
            # 计算平均执行时间
            completed_executions = [e for e in executions if e.completed_at and e.started_at]
            if completed_executions:
                total_duration = sum([
                    (e.completed_at - e.started_at).total_seconds() 
                    for e in completed_executions
                ])
                average_duration = total_duration / len(completed_executions)
            else:
                average_duration = 0
            
            # 最近执行统计
            recent_executions = self.db.query(WorkflowExecution).filter(
                and_(
                    WorkflowExecution.workflow_id == workflow_id,
                    WorkflowExecution.created_at >= datetime.utcnow() - timedelta(days=30)
                )
            ).all()
            
            return {
                "total_executions": total_executions,
                "successful_executions": successful_executions,
                "failed_executions": failed_executions,
                "success_rate": successful_executions / total_executions if total_executions > 0 else 0,
                "average_duration": average_duration,
                "recent_executions_count": len(recent_executions),
                "last_execution_at": workflow.last_executed_at,
                "created_at": workflow.created_at,
                "updated_at": workflow.updated_at
            }
        except Exception as e:
            self.logger.error(f"获取工作流统计错误: {str(e)}")
            return None