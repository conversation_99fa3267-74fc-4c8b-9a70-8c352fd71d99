#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A2A多智能体系统权限管理模块

实现基于角色的权限控制系统（RBAC）
"""

import json
from enum import Enum
from typing import List, Dict, Any, Optional, Set
from datetime import datetime
from loguru import logger
from sqlalchemy import text

from app.core.database import get_database_manager


class Permission(Enum):
    """
    系统权限枚举
    
    定义系统中所有可用的权限
    """
    # 用户管理权限
    USER_CREATE = "user:create"
    USER_READ = "user:read"
    USER_UPDATE = "user:update"
    USER_DELETE = "user:delete"
    USER_LIST = "user:list"
    
    # 智能体管理权限
    AGENT_CREATE = "agent:create"
    AGENT_READ = "agent:read"
    AGENT_UPDATE = "agent:update"
    AGENT_DELETE = "agent:delete"
    AGENT_LIST = "agent:list"
    AGENT_EXECUTE = "agent:execute"
    
    # 会话管理权限
    SESSION_CREATE = "session:create"
    SESSION_READ = "session:read"
    SESSION_UPDATE = "session:update"
    SESSION_DELETE = "session:delete"
    SESSION_LIST = "session:list"
    
    # 消息管理权限
    MESSAGE_CREATE = "message:create"
    MESSAGE_READ = "message:read"
    MESSAGE_UPDATE = "message:update"
    MESSAGE_DELETE = "message:delete"
    MESSAGE_LIST = "message:list"
    
    # 任务管理权限
    TASK_CREATE = "task:create"
    TASK_READ = "task:read"
    TASK_UPDATE = "task:update"
    TASK_DELETE = "task:delete"
    TASK_LIST = "task:list"
    TASK_EXECUTE = "task:execute"
    
    # 工作流管理权限
    WORKFLOW_CREATE = "workflow:create"
    WORKFLOW_READ = "workflow:read"
    WORKFLOW_UPDATE = "workflow:update"
    WORKFLOW_DELETE = "workflow:delete"
    WORKFLOW_LIST = "workflow:list"
    WORKFLOW_EXECUTE = "workflow:execute"
    
    # 工具管理权限
    TOOL_CREATE = "tool:create"
    TOOL_READ = "tool:read"
    TOOL_UPDATE = "tool:update"
    TOOL_DELETE = "tool:delete"
    TOOL_LIST = "tool:list"
    TOOL_EXECUTE = "tool:execute"
    
    # 工件管理权限
    ARTIFACT_CREATE = "artifact:create"
    ARTIFACT_READ = "artifact:read"
    ARTIFACT_UPDATE = "artifact:update"
    ARTIFACT_DELETE = "artifact:delete"
    ARTIFACT_LIST = "artifact:list"
    ARTIFACT_DOWNLOAD = "artifact:download"
    
    # 系统管理权限
    SYSTEM_CONFIG = "system:config"
    SYSTEM_MONITOR = "system:monitor"
    SYSTEM_LOG = "system:log"
    SYSTEM_BACKUP = "system:backup"
    
    # 管理员权限
    ADMIN_ALL = "admin:all"


class Role(Enum):
    """
    系统角色枚举
    
    定义系统中的标准角色
    """
    SUPER_ADMIN = "super_admin"  # 超级管理员
    ADMIN = "admin"              # 管理员
    DEVELOPER = "developer"      # 开发者
    USER = "user"                # 普通用户
    GUEST = "guest"              # 访客


class PermissionChecker:
    """
    权限检查器
    
    负责检查用户权限和角色
    """
    
    def __init__(self):
        # 角色权限映射
        self.role_permissions = {
            Role.SUPER_ADMIN: [Permission.ADMIN_ALL],  # 超级管理员拥有所有权限
            Role.ADMIN: [
                # 用户管理
                Permission.USER_CREATE, Permission.USER_READ, Permission.USER_UPDATE,
                Permission.USER_DELETE, Permission.USER_LIST,
                # 智能体管理
                Permission.AGENT_CREATE, Permission.AGENT_READ, Permission.AGENT_UPDATE,
                Permission.AGENT_DELETE, Permission.AGENT_LIST, Permission.AGENT_EXECUTE,
                # 系统管理
                Permission.SYSTEM_CONFIG, Permission.SYSTEM_MONITOR, Permission.SYSTEM_LOG,
                # 工具管理
                Permission.TOOL_CREATE, Permission.TOOL_READ, Permission.TOOL_UPDATE,
                Permission.TOOL_DELETE, Permission.TOOL_LIST, Permission.TOOL_EXECUTE,
            ],
            Role.DEVELOPER: [
                # 智能体管理
                Permission.AGENT_CREATE, Permission.AGENT_READ, Permission.AGENT_UPDATE,
                Permission.AGENT_LIST, Permission.AGENT_EXECUTE,
                # 会话管理
                Permission.SESSION_CREATE, Permission.SESSION_READ, Permission.SESSION_UPDATE,
                Permission.SESSION_LIST,
                # 任务管理
                Permission.TASK_CREATE, Permission.TASK_READ, Permission.TASK_UPDATE,
                Permission.TASK_LIST, Permission.TASK_EXECUTE,
                # 工作流管理
                Permission.WORKFLOW_CREATE, Permission.WORKFLOW_READ, Permission.WORKFLOW_UPDATE,
                Permission.WORKFLOW_LIST, Permission.WORKFLOW_EXECUTE,
                # 工具管理
                Permission.TOOL_READ, Permission.TOOL_LIST, Permission.TOOL_EXECUTE,
                # 工件管理
                Permission.ARTIFACT_CREATE, Permission.ARTIFACT_READ, Permission.ARTIFACT_UPDATE,
                Permission.ARTIFACT_LIST, Permission.ARTIFACT_DOWNLOAD,
            ],
            Role.USER: [
                # 基本读取权限
                Permission.AGENT_READ, Permission.AGENT_LIST, Permission.AGENT_EXECUTE,
                Permission.SESSION_CREATE, Permission.SESSION_READ, Permission.SESSION_LIST,
                Permission.MESSAGE_CREATE, Permission.MESSAGE_READ, Permission.MESSAGE_LIST,
                Permission.TASK_READ, Permission.TASK_LIST,
                Permission.TOOL_READ, Permission.TOOL_LIST, Permission.TOOL_EXECUTE,
                Permission.ARTIFACT_READ, Permission.ARTIFACT_LIST, Permission.ARTIFACT_DOWNLOAD,
            ],
            Role.GUEST: [
                # 只读权限
                Permission.AGENT_READ, Permission.AGENT_LIST,
                Permission.TOOL_READ, Permission.TOOL_LIST,
            ]
        }
    
    async def check_permission(self, user_id: int, permission: Permission) -> bool:
        """
        检查用户是否具有指定权限
        
        Args:
            user_id: 用户ID
            permission: 要检查的权限
            
        Returns:
            bool: 是否具有权限
        """
        try:
            # 获取用户角色和权限
            user_permissions = await self._get_user_permissions(user_id)
            
            # 检查是否有超级管理员权限
            if Permission.ADMIN_ALL in user_permissions:
                return True
            
            # 检查具体权限
            return permission in user_permissions
        
        except Exception as e:
            logger.error(f"检查用户权限失败: {str(e)}", extra={"user_id": user_id, "permission": permission.value})
            return False
    
    async def check_permissions(self, user_id: int, permissions: List[Permission]) -> bool:
        """
        检查用户是否具有所有指定权限
        
        Args:
            user_id: 用户ID
            permissions: 要检查的权限列表
            
        Returns:
            bool: 是否具有所有权限
        """
        try:
            # 获取用户权限
            user_permissions = await self._get_user_permissions(user_id)
            
            # 检查是否有超级管理员权限
            if Permission.ADMIN_ALL in user_permissions:
                return True
            
            # 检查是否具有所有权限
            return all(permission in user_permissions for permission in permissions)
        
        except Exception as e:
            logger.error(f"检查用户权限失败: {str(e)}", extra={"user_id": user_id})
            return False
    
    async def check_any_permission(self, user_id: int, permissions: List[Permission]) -> bool:
        """
        检查用户是否具有任一指定权限
        
        Args:
            user_id: 用户ID
            permissions: 要检查的权限列表
            
        Returns:
            bool: 是否具有任一权限
        """
        try:
            # 获取用户权限
            user_permissions = await self._get_user_permissions(user_id)
            
            # 检查是否有超级管理员权限
            if Permission.ADMIN_ALL in user_permissions:
                return True
            
            # 检查是否具有任一权限
            return any(permission in user_permissions for permission in permissions)
        
        except Exception as e:
            logger.error(f"检查用户权限失败: {str(e)}", extra={"user_id": user_id})
            return False
    
    async def check_resource_permission(
        self,
        user_id: int,
        permission: Permission,
        resource_id: Optional[int] = None,
        resource_type: Optional[str] = None
    ) -> bool:
        """
        检查用户对特定资源的权限
        
        Args:
            user_id: 用户ID
            permission: 要检查的权限
            resource_id: 资源ID（可选）
            resource_type: 资源类型（可选）
            
        Returns:
            bool: 是否具有权限
        """
        try:
            # 首先检查基本权限
            has_basic_permission = await self.check_permission(user_id, permission)
            if not has_basic_permission:
                return False
            
            # 如果没有指定资源，则只检查基本权限
            if not resource_id or not resource_type:
                return True
            
            # 检查资源所有权
            is_owner = await self._check_resource_ownership(user_id, resource_id, resource_type)
            if is_owner:
                return True
            
            # 检查资源共享权限
            has_shared_access = await self._check_resource_shared_access(user_id, resource_id, resource_type)
            return has_shared_access
        
        except Exception as e:
            logger.error(f"检查资源权限失败: {str(e)}", extra={
                "user_id": user_id,
                "permission": permission.value,
                "resource_id": resource_id,
                "resource_type": resource_type
            })
            return False
    
    async def get_user_role(self, user_id: int) -> Optional[Role]:
        """
        获取用户角色
        
        Args:
            user_id: 用户ID
            
        Returns:
            Optional[Role]: 用户角色
        """
        try:
            db_manager = get_database_manager()
            engine = db_manager.get_engine()
            
            sql = """
                SELECT role FROM users
                WHERE id = :user_id AND deleted_at IS NULL
            """
            
            async with engine.begin() as conn:
                result = await conn.execute(text(sql), {"user_id": user_id})
                row = result.fetchone()
                
                if row:
                    role_value = row[0]
                    try:
                        return Role(role_value)
                    except ValueError:
                        logger.warning(f"未知的用户角色: {role_value}", extra={"user_id": user_id})
                        return Role.USER  # 默认角色
                
                return None
        
        except Exception as e:
            logger.error(f"获取用户角色失败: {str(e)}", extra={"user_id": user_id})
            return None
    
    async def _get_user_permissions(self, user_id: int) -> Set[Permission]:
        """
        获取用户的所有权限
        
        Args:
            user_id: 用户ID
            
        Returns:
            Set[Permission]: 用户权限集合
        """
        permissions = set()
        
        try:
            # 获取用户角色
            user_role = await self.get_user_role(user_id)
            if user_role and user_role in self.role_permissions:
                permissions.update(self.role_permissions[user_role])
            
            # 获取用户的自定义权限
            custom_permissions = await self._get_user_custom_permissions(user_id)
            permissions.update(custom_permissions)
            
            return permissions
        
        except Exception as e:
            logger.error(f"获取用户权限失败: {str(e)}", extra={"user_id": user_id})
            return set()
    
    async def _get_user_custom_permissions(self, user_id: int) -> Set[Permission]:
        """
        获取用户的自定义权限
        
        Args:
            user_id: 用户ID
            
        Returns:
            Set[Permission]: 自定义权限集合
        """
        try:
            db_manager = get_database_manager()
            engine = db_manager.get_engine()
            
            sql = """
                SELECT permission_name FROM user_permissions
                WHERE user_id = :user_id AND is_granted = TRUE
            """
            
            async with engine.begin() as conn:
                result = await conn.execute(text(sql), {"user_id": user_id})
                rows = result.fetchall()
                
                permissions = set()
                for row in rows:
                    permission_name = row[0]
                    try:
                        permission = Permission(permission_name)
                        permissions.add(permission)
                    except ValueError:
                        logger.warning(f"未知的权限: {permission_name}", extra={"user_id": user_id})
                
                return permissions
        
        except Exception as e:
            logger.error(f"获取用户自定义权限失败: {str(e)}", extra={"user_id": user_id})
            return set()
    
    async def _check_resource_ownership(self, user_id: int, resource_id: int, resource_type: str) -> bool:
        """
        检查用户是否拥有指定资源
        
        Args:
            user_id: 用户ID
            resource_id: 资源ID
            resource_type: 资源类型
            
        Returns:
            bool: 是否拥有资源
        """
        try:
            db_manager = get_database_manager()
            engine = db_manager.get_engine()
            
            # 根据资源类型构建查询
            table_map = {
                "agent": "agents",
                "session": "sessions",
                "task": "tasks",
                "workflow": "workflows",
                "artifact": "artifacts"
            }
            
            table_name = table_map.get(resource_type)
            if not table_name:
                return False
            
            sql = f"""
                SELECT id FROM {table_name}
                WHERE id = :resource_id AND user_id = :user_id AND deleted_at IS NULL
            """
            
            async with engine.begin() as conn:
                result = await conn.execute(text(sql), {
                    "resource_id": resource_id,
                    "user_id": user_id
                })
                
                return result.fetchone() is not None
        
        except Exception as e:
            logger.error(f"检查资源所有权失败: {str(e)}", extra={
                "user_id": user_id,
                "resource_id": resource_id,
                "resource_type": resource_type
            })
            return False
    
    async def _check_resource_shared_access(self, user_id: int, resource_id: int, resource_type: str) -> bool:
        """
        检查用户是否有资源的共享访问权限
        
        Args:
            user_id: 用户ID
            resource_id: 资源ID
            resource_type: 资源类型
            
        Returns:
            bool: 是否有共享访问权限
        """
        # 这里可以实现资源共享逻辑
        # 例如检查资源是否为公开、是否在共享列表中等
        return False
    
    async def grant_permission(self, user_id: int, permission: Permission) -> bool:
        """
        授予用户权限
        
        Args:
            user_id: 用户ID
            permission: 要授予的权限
            
        Returns:
            bool: 是否成功
        """
        try:
            db_manager = get_database_manager()
            engine = db_manager.get_engine()
            
            sql = """
                INSERT INTO user_permissions (user_id, permission_name, is_granted, granted_at)
                VALUES (:user_id, :permission_name, TRUE, :granted_at)
                ON DUPLICATE KEY UPDATE
                is_granted = TRUE, granted_at = :granted_at
            """
            
            async with engine.begin() as conn:
                await conn.execute(text(sql), {
                    "user_id": user_id,
                    "permission_name": permission.value,
                    "granted_at": datetime.now()
                })
            
            logger.info(f"为用户 {user_id} 授予权限 {permission.value}")
            return True
        
        except Exception as e:
            logger.error(f"授予权限失败: {str(e)}", extra={"user_id": user_id, "permission": permission.value})
            return False
    
    async def revoke_permission(self, user_id: int, permission: Permission) -> bool:
        """
        撤销用户权限
        
        Args:
            user_id: 用户ID
            permission: 要撤销的权限
            
        Returns:
            bool: 是否成功
        """
        try:
            db_manager = get_database_manager()
            engine = db_manager.get_engine()
            
            sql = """
                UPDATE user_permissions
                SET is_granted = FALSE, revoked_at = :revoked_at
                WHERE user_id = :user_id AND permission_name = :permission_name
            """
            
            async with engine.begin() as conn:
                result = await conn.execute(text(sql), {
                    "user_id": user_id,
                    "permission_name": permission.value,
                    "revoked_at": datetime.now()
                })
            
            if result.rowcount > 0:
                logger.info(f"撤销用户 {user_id} 的权限 {permission.value}")
                return True
            
            return False
        
        except Exception as e:
            logger.error(f"撤销权限失败: {str(e)}", extra={"user_id": user_id, "permission": permission.value})
            return False


def require_permission(permission: Permission):
    """
    权限装饰器
    
    用于装饰需要特定权限的API端点
    
    Args:
        permission: 所需权限
    """
    def decorator(func):
        func._required_permission = permission
        return func
    return decorator


def require_permissions(permissions: List[Permission]):
    """
    多权限装饰器
    
    用于装饰需要多个权限的API端点
    
    Args:
        permissions: 所需权限列表
    """
    def decorator(func):
        func._required_permissions = permissions
        return func
    return decorator


def require_any_permission(permissions: List[Permission]):
    """
    任一权限装饰器
    
    用于装饰需要任一权限的API端点
    
    Args:
        permissions: 权限列表（满足任一即可）
    """
    def decorator(func):
        func._required_any_permissions = permissions
        return func
    return decorator


def require_role(role: Role):
    """
    角色装饰器
    
    用于装饰需要特定角色的API端点
    
    Args:
        role: 所需角色
    """
    def decorator(func):
        func._required_role = role
        return func
    return decorator


# 全局权限检查器实例
_permission_checker: Optional[PermissionChecker] = None


def get_permission_checker() -> PermissionChecker:
    """
    获取权限检查器实例（单例模式）
    
    Returns:
        PermissionChecker: 权限检查器实例
    """
    global _permission_checker
    if _permission_checker is None:
        _permission_checker = PermissionChecker()
    return _permission_checker


async def check_user_permission(user_id: str, permission: Permission) -> bool:
    """
    检查用户是否具有指定权限
    
    Args:
        user_id: 用户ID
        permission: 权限枚举
    
    Returns:
        bool: 是否具有权限
    """
    try:
        checker = get_permission_checker()
        return await checker.check_permission(user_id, permission)
    except Exception as e:
        logger.error(f"检查用户权限失败: {e}")
        return False