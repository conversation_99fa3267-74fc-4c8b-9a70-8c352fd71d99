#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A2A多智能体系统数据模型模块

包含所有数据模型的定义和导出
"""

__version__ = "1.0.0"
__author__ = "A2A Team"
__description__ = "A2A多智能体系统数据模型"

# 导入基础模型
from .base import BaseModel

# 导入用户相关模型
from .user import (
    User,
    UserToken,
    UserPermission,
    UserActivityLog
)

# 导入智能体相关模型
from .agent import (
    Agent,
    AgentTemplate,
    AgentCollaboration
)

# 导入会话相关模型
from .session import (
    Session,
    SessionShare,
    SessionBookmark
)

# 导入消息相关模型
from .message import (
    Message,
    MessageReaction,
    MessageEdit,
    MessageTemplate
)

# 导入任务相关模型
from .task import (
    Task,
    TaskExecution,
    TaskTemplate
)

# 导入内存相关模型
from .memory import (
    Memory,
    MemoryEntry
)

# 导入工作流相关模型
from .workflow import (
    Workflow,
    WorkflowStep,
    WorkflowExecution,
    WorkflowStepExecution
)

# 导入工具相关模型
from .tool import (
    Tool,
    AgentTool,
    ToolExecution,
    ToolCategory
)

# 导入工件相关模型
from .artifact import (
    Artifact,
    ArtifactShare,
    ArtifactVersion
)

# 导入配置相关模型
from .config import (
    SystemConfig,
    UserConfig,
    AgentConfig,
    ConfigTemplate
)

# 导入日志和性能指标模型
from .log import (
    SystemLog,
    PerformanceMetric,
    ApiLog,
    ErrorLog
)

# 导出所有模型
__all__ = [
    # 基础模型
    "BaseModel",
    
    # 用户相关模型
    "User",
    "UserToken",
    "UserPermission",
    "UserActivityLog",
    
    # 智能体相关模型
    "Agent",
    "AgentTemplate",
    "AgentCollaboration",
    
    # 会话相关模型
    "Session",
    "SessionShare",
    "SessionBookmark",
    
    # 消息相关模型
    "Message",
    "MessageReaction",
    "MessageEdit",
    "MessageTemplate",
    
    # 任务相关模型
    "Task",
    "TaskExecution",
    "TaskTemplate",
    
    # 内存相关模型
    "Memory",
    "MemoryEntry",
    
    # 工作流相关模型
    "Workflow",
    "WorkflowStep",
    "WorkflowExecution",
    "WorkflowStepExecution",
    
    # 工具相关模型
    "Tool",
    "AgentTool",
    "ToolExecution",
    "ToolCategory",
    
    # 工件相关模型
    "Artifact",
    "ArtifactShare",
    "ArtifactVersion",
    
    # 配置相关模型
    "SystemConfig",
    "UserConfig",
    "AgentConfig",
    "ConfigTemplate",
    
    # 日志和性能指标模型
    "SystemLog",
    "PerformanceMetric",
    "ApiLog",
    "ErrorLog",
]