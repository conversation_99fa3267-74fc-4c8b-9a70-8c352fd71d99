#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A2A多智能体系统 工具配置管理服务

提供工具配置的数据库存储、用户权限管理和动态注册功能
"""

import asyncio
import logging
import json
from typing import Dict, List, Any, Optional, Set, Type
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from enum import Enum
import uuid

# SQLAlchemy imports
from sqlalchemy import Column, String, Text, DateTime, Boolean, Integer, JSON, ForeignKey, Index
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship, sessionmaker
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine

# Google ADK imports
from google.ai.generativelanguage import FunctionDeclaration

from ..adk.tools.base_tool import (
    BaseTool, ToolConfig, ToolResult, ToolError, ToolStatus,
    ToolExecutionContext, ToolPermission
)
from ..adk.tools.tool_registry import (
    <PERSON>lRegistry, ToolCategory, RegistrationStatus, ToolFilter,
    ToolRegistration, get_global_registry
)
from .database_service import DatabaseService
from .user_service import UserService


Base = declarative_base()


class ToolConfigModel(Base):
    """工具配置数据模型"""
    __tablename__ = 'tool_configs'
    
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    name = Column(String(100), unique=True, nullable=False, index=True)
    tool_class = Column(String(200), nullable=False)
    category = Column(String(50), nullable=False)
    version = Column(String(20), nullable=False, default='1.0.0')
    description = Column(Text)
    author = Column(String(100))
    config_data = Column(JSON, nullable=False)
    dependencies = Column(JSON, default=list)
    permissions = Column(JSON, default=list)
    meta_data = Column("metadata", JSON, default=dict)
    status = Column(String(20), nullable=False, default='active')
    is_builtin = Column(Boolean, default=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    created_by = Column(String(36), ForeignKey('users.id'))
    
    # 关系
    user_permissions = relationship('UserToolPermissionModel', back_populates='tool')
    execution_logs = relationship('ToolExecutionLogModel', back_populates='tool')
    
    # 索引
    __table_args__ = (
        Index('idx_tool_category_status', 'category', 'status'),
        Index('idx_tool_created_at', 'created_at'),
    )


class UserToolPermissionModel(Base):
    """用户工具权限数据模型"""
    __tablename__ = 'user_tool_permissions'
    
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(String(36), ForeignKey('users.id'), nullable=False)
    tool_id = Column(String(36), ForeignKey('tool_configs.id'), nullable=False)
    permissions = Column(JSON, nullable=False)  # List of permission strings
    granted_by = Column(String(36), ForeignKey('users.id'))
    granted_at = Column(DateTime, default=datetime.utcnow)
    expires_at = Column(DateTime)
    is_active = Column(Boolean, default=True)
    
    # 关系
    tool = relationship('ToolConfigModel', back_populates='user_permissions')
    
    # 索引
    __table_args__ = (
        Index('idx_user_tool_permissions', 'user_id', 'tool_id'),
        Index('idx_permission_expires', 'expires_at'),
    )


class ToolExecutionLogModel(Base):
    """工具执行日志数据模型"""
    __tablename__ = 'tool_execution_logs'
    
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    execution_id = Column(String(36), nullable=False, index=True)
    tool_id = Column(String(36), ForeignKey('tool_configs.id'), nullable=False)
    user_id = Column(String(36), ForeignKey('users.id'), nullable=False)
    status = Column(String(20), nullable=False)
    input_data = Column(JSON)
    output_data = Column(JSON)
    error_info = Column(JSON)
    execution_time = Column(Integer)  # 执行时间（毫秒）
    started_at = Column(DateTime, default=datetime.utcnow)
    completed_at = Column(DateTime)
    
    # 关系
    tool = relationship('ToolConfigModel', back_populates='execution_logs')
    
    # 索引
    __table_args__ = (
        Index('idx_execution_user_tool', 'user_id', 'tool_id'),
        Index('idx_execution_started_at', 'started_at'),
        Index('idx_execution_status', 'status'),
    )


class ToolVersionModel(Base):
    """工具版本管理数据模型"""
    __tablename__ = 'tool_versions'
    
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    tool_id = Column(String(36), ForeignKey('tool_configs.id'), nullable=False)
    version = Column(String(20), nullable=False)
    config_data = Column(JSON, nullable=False)
    change_log = Column(Text)
    is_active = Column(Boolean, default=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    created_by = Column(String(36), ForeignKey('users.id'))
    
    # 索引
    __table_args__ = (
        Index('idx_tool_version', 'tool_id', 'version'),
        Index('idx_version_created_at', 'created_at'),
    )


@dataclass
class ToolServiceConfig:
    """工具服务配置"""
    database_url: str
    cache_ttl: int = 3600  # 缓存TTL（秒）
    max_execution_time: int = 300  # 最大执行时间（秒）
    enable_execution_log: bool = True
    enable_permission_cache: bool = True
    auto_cleanup_logs: bool = True
    log_retention_days: int = 30


class ToolOperationType(Enum):
    """工具操作类型枚举"""
    REGISTER = "register"
    UNREGISTER = "unregister"
    UPDATE = "update"
    EXECUTE = "execute"
    PERMISSION_GRANT = "permission_grant"
    PERMISSION_REVOKE = "permission_revoke"


@dataclass
class ToolOperationLog:
    """工具操作日志"""
    operation_type: ToolOperationType
    tool_name: str
    user_id: str
    details: Dict[str, Any]
    timestamp: datetime
    success: bool
    error_message: Optional[str] = None


class ToolServiceError(Exception):
    """工具服务异常"""
    pass


class ToolService:
    """工具配置管理服务"""
    
    def __init__(
        self,
        config: ToolServiceConfig,
        database_service: DatabaseService,
        user_service: UserService,
        registry: Optional[ToolRegistry] = None
    ):
        """
        初始化工具服务
        
        Args:
            config: 服务配置
            database_service: 数据库服务
            user_service: 用户服务
            registry: 工具注册器
        """
        self.config = config
        self.database_service = database_service
        self.user_service = user_service
        self.registry = registry or get_global_registry()
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 缓存
        self._config_cache: Dict[str, ToolConfigModel] = {}
        self._permission_cache: Dict[str, Dict[str, Set[ToolPermission]]] = {}
        self._cache_timestamps: Dict[str, datetime] = {}
        
        # 操作日志
        self._operation_logs: List[ToolOperationLog] = []
        
        # 初始化数据库引擎
        self._engine = None
        self._session_factory = None
    
    async def initialize(self) -> None:
        """
        初始化服务
        """
        try:
            # 创建数据库引擎
            self._engine = create_async_engine(
                self.config.database_url,
                echo=False,
                pool_pre_ping=True
            )
            
            # 创建会话工厂
            self._session_factory = sessionmaker(
                self._engine,
                class_=AsyncSession,
                expire_on_commit=False
            )
            
            # 创建数据库表
            async with self._engine.begin() as conn:
                await conn.run_sync(Base.metadata.create_all)
            
            # 加载工具配置
            await self._load_tool_configs()
            
            # 启动清理任务
            if self.config.auto_cleanup_logs:
                asyncio.create_task(self._cleanup_task())
            
            self.logger.info("工具服务初始化完成")
            
        except Exception as e:
            self.logger.error(f"工具服务初始化失败: {e}")
            raise ToolServiceError(f"初始化失败: {str(e)}")
    
    async def register_tool(
        self,
        name: str,
        tool_class: Type[BaseTool],
        config: ToolConfig,
        category: ToolCategory,
        user_id: str,
        version: str = "1.0.0",
        description: str = "",
        author: str = "",
        dependencies: Optional[List[str]] = None,
        permissions: Optional[Set[ToolPermission]] = None,
        metadata: Optional[Dict[str, Any]] = None,
        is_builtin: bool = False
    ) -> bool:
        """
        注册工具
        
        Args:
            name: 工具名称
            tool_class: 工具类
            config: 工具配置
            category: 工具分类
            user_id: 用户ID
            version: 版本号
            description: 描述
            author: 作者
            dependencies: 依赖列表
            permissions: 权限集合
            metadata: 元数据
            is_builtin: 是否为内置工具
        
        Returns:
            bool: 注册是否成功
        """
        start_time = datetime.now()
        
        try:
            # 检查用户权限
            if not is_builtin and not await self._check_admin_permission(user_id):
                raise ToolServiceError("用户无权限注册工具")
            
            # 检查工具是否已存在
            existing_tool = await self._get_tool_config(name)
            if existing_tool:
                raise ToolServiceError(f"工具 {name} 已存在")
            
            # 序列化配置
            config_data = self._serialize_config(config)
            
            # 创建工具配置记录
            async with self._get_session() as session:
                tool_config = ToolConfigModel(
                    name=name,
                    tool_class=f"{tool_class.__module__}.{tool_class.__name__}",
                    category=category.value,
                    version=version,
                    description=description,
                    author=author,
                    config_data=config_data,
                    dependencies=dependencies or [],
                    permissions=[p.value for p in (permissions or set())],
                    metadata=metadata or {},
                    is_builtin=is_builtin,
                    created_by=user_id
                )
                
                session.add(tool_config)
                await session.commit()
                
                # 更新缓存
                self._config_cache[name] = tool_config
                self._cache_timestamps[name] = datetime.now()
            
            # 在注册器中注册工具
            success = await self.registry.register_tool(
                name=name,
                tool_class=tool_class,
                config=config,
                category=category,
                version=version,
                description=description,
                author=author,
                dependencies=dependencies,
                permissions=permissions,
                metadata=metadata
            )
            
            if not success:
                # 如果注册器注册失败，删除数据库记录
                await self._delete_tool_config(name)
                raise ToolServiceError("工具注册器注册失败")
            
            # 记录操作日志
            await self._log_operation(
                ToolOperationType.REGISTER,
                name,
                user_id,
                {
                    'category': category.value,
                    'version': version,
                    'is_builtin': is_builtin
                },
                True
            )
            
            self.logger.info(f"工具 {name} 注册成功")
            return True
            
        except Exception as e:
            # 记录操作日志
            await self._log_operation(
                ToolOperationType.REGISTER,
                name,
                user_id,
                {'error': str(e)},
                False,
                str(e)
            )
            
            self.logger.error(f"工具 {name} 注册失败: {e}")
            return False
    
    async def unregister_tool(self, name: str, user_id: str) -> bool:
        """
        注销工具
        
        Args:
            name: 工具名称
            user_id: 用户ID
        
        Returns:
            bool: 注销是否成功
        """
        try:
            # 检查用户权限
            if not await self._check_admin_permission(user_id):
                raise ToolServiceError("用户无权限注销工具")
            
            # 检查工具是否存在
            tool_config = await self._get_tool_config(name)
            if not tool_config:
                raise ToolServiceError(f"工具 {name} 不存在")
            
            # 检查是否为内置工具
            if tool_config.is_builtin:
                raise ToolServiceError("不能注销内置工具")
            
            # 从注册器中注销
            success = await self.registry.unregister_tool(name)
            if not success:
                raise ToolServiceError("工具注册器注销失败")
            
            # 删除数据库记录
            await self._delete_tool_config(name)
            
            # 清理缓存
            if name in self._config_cache:
                del self._config_cache[name]
            if name in self._cache_timestamps:
                del self._cache_timestamps[name]
            
            # 记录操作日志
            await self._log_operation(
                ToolOperationType.UNREGISTER,
                name,
                user_id,
                {},
                True
            )
            
            self.logger.info(f"工具 {name} 注销成功")
            return True
            
        except Exception as e:
            # 记录操作日志
            await self._log_operation(
                ToolOperationType.UNREGISTER,
                name,
                user_id,
                {'error': str(e)},
                False,
                str(e)
            )
            
            self.logger.error(f"工具 {name} 注销失败: {e}")
            return False
    
    async def update_tool_config(
        self,
        name: str,
        config: ToolConfig,
        user_id: str,
        version: Optional[str] = None,
        change_log: Optional[str] = None
    ) -> bool:
        """
        更新工具配置
        
        Args:
            name: 工具名称
            config: 新配置
            user_id: 用户ID
            version: 新版本号
            change_log: 变更日志
        
        Returns:
            bool: 更新是否成功
        """
        try:
            # 检查用户权限
            if not await self._check_admin_permission(user_id):
                raise ToolServiceError("用户无权限更新工具配置")
            
            # 获取当前配置
            tool_config = await self._get_tool_config(name)
            if not tool_config:
                raise ToolServiceError(f"工具 {name} 不存在")
            
            # 序列化新配置
            config_data = self._serialize_config(config)
            
            async with self._get_session() as session:
                # 如果指定了新版本，创建版本记录
                if version and version != tool_config.version:
                    version_record = ToolVersionModel(
                        tool_id=tool_config.id,
                        version=tool_config.version,
                        config_data=tool_config.config_data,
                        change_log=change_log or "",
                        is_active=False,
                        created_by=user_id
                    )
                    session.add(version_record)
                    
                    # 更新工具版本
                    tool_config.version = version
                
                # 更新配置
                tool_config.config_data = config_data
                tool_config.updated_at = datetime.utcnow()
                
                await session.commit()
                
                # 更新缓存
                self._config_cache[name] = tool_config
                self._cache_timestamps[name] = datetime.now()
            
            # 记录操作日志
            await self._log_operation(
                ToolOperationType.UPDATE,
                name,
                user_id,
                {
                    'new_version': version,
                    'change_log': change_log
                },
                True
            )
            
            self.logger.info(f"工具 {name} 配置更新成功")
            return True
            
        except Exception as e:
            # 记录操作日志
            await self._log_operation(
                ToolOperationType.UPDATE,
                name,
                user_id,
                {'error': str(e)},
                False,
                str(e)
            )
            
            self.logger.error(f"工具 {name} 配置更新失败: {e}")
            return False
    
    async def grant_user_permission(
        self,
        user_id: str,
        tool_name: str,
        permissions: Set[ToolPermission],
        granted_by: str,
        expires_at: Optional[datetime] = None
    ) -> bool:
        """
        授予用户工具权限
        
        Args:
            user_id: 用户ID
            tool_name: 工具名称
            permissions: 权限集合
            granted_by: 授权者ID
            expires_at: 过期时间
        
        Returns:
            bool: 授权是否成功
        """
        try:
            # 检查授权者权限
            if not await self._check_admin_permission(granted_by):
                raise ToolServiceError("用户无权限授予工具权限")
            
            # 检查工具是否存在
            tool_config = await self._get_tool_config(tool_name)
            if not tool_config:
                raise ToolServiceError(f"工具 {tool_name} 不存在")
            
            # 检查用户是否存在
            user_exists = await self.user_service.user_exists(user_id)
            if not user_exists:
                raise ToolServiceError(f"用户 {user_id} 不存在")
            
            async with self._get_session() as session:
                # 检查是否已有权限记录
                existing_permission = await session.query(UserToolPermissionModel).filter_by(
                    user_id=user_id,
                    tool_id=tool_config.id,
                    is_active=True
                ).first()
                
                if existing_permission:
                    # 更新现有权限
                    existing_permission.permissions = [p.value for p in permissions]
                    existing_permission.granted_by = granted_by
                    existing_permission.granted_at = datetime.utcnow()
                    existing_permission.expires_at = expires_at
                else:
                    # 创建新权限记录
                    permission_record = UserToolPermissionModel(
                        user_id=user_id,
                        tool_id=tool_config.id,
                        permissions=[p.value for p in permissions],
                        granted_by=granted_by,
                        expires_at=expires_at
                    )
                    session.add(permission_record)
                
                await session.commit()
            
            # 更新注册器权限
            await self.registry.set_user_permissions(user_id, tool_name, permissions)
            
            # 清理权限缓存
            if user_id in self._permission_cache:
                if tool_name in self._permission_cache[user_id]:
                    del self._permission_cache[user_id][tool_name]
            
            # 记录操作日志
            await self._log_operation(
                ToolOperationType.PERMISSION_GRANT,
                tool_name,
                granted_by,
                {
                    'target_user': user_id,
                    'permissions': [p.value for p in permissions],
                    'expires_at': expires_at.isoformat() if expires_at else None
                },
                True
            )
            
            self.logger.info(f"用户 {user_id} 工具 {tool_name} 权限授予成功")
            return True
            
        except Exception as e:
            # 记录操作日志
            await self._log_operation(
                ToolOperationType.PERMISSION_GRANT,
                tool_name,
                granted_by,
                {
                    'target_user': user_id,
                    'error': str(e)
                },
                False,
                str(e)
            )
            
            self.logger.error(f"用户 {user_id} 工具 {tool_name} 权限授予失败: {e}")
            return False
    
    async def revoke_user_permission(
        self,
        user_id: str,
        tool_name: str,
        revoked_by: str
    ) -> bool:
        """
        撤销用户工具权限
        
        Args:
            user_id: 用户ID
            tool_name: 工具名称
            revoked_by: 撤销者ID
        
        Returns:
            bool: 撤销是否成功
        """
        try:
            # 检查撤销者权限
            if not await self._check_admin_permission(revoked_by):
                raise ToolServiceError("用户无权限撤销工具权限")
            
            # 检查工具是否存在
            tool_config = await self._get_tool_config(tool_name)
            if not tool_config:
                raise ToolServiceError(f"工具 {tool_name} 不存在")
            
            async with self._get_session() as session:
                # 查找权限记录
                permission_record = await session.query(UserToolPermissionModel).filter_by(
                    user_id=user_id,
                    tool_id=tool_config.id,
                    is_active=True
                ).first()
                
                if permission_record:
                    # 标记为非活跃
                    permission_record.is_active = False
                    await session.commit()
            
            # 更新注册器权限
            await self.registry.set_user_permissions(user_id, tool_name, set())
            
            # 清理权限缓存
            if user_id in self._permission_cache:
                if tool_name in self._permission_cache[user_id]:
                    del self._permission_cache[user_id][tool_name]
            
            # 记录操作日志
            await self._log_operation(
                ToolOperationType.PERMISSION_REVOKE,
                tool_name,
                revoked_by,
                {'target_user': user_id},
                True
            )
            
            self.logger.info(f"用户 {user_id} 工具 {tool_name} 权限撤销成功")
            return True
            
        except Exception as e:
            # 记录操作日志
            await self._log_operation(
                ToolOperationType.PERMISSION_REVOKE,
                tool_name,
                revoked_by,
                {
                    'target_user': user_id,
                    'error': str(e)
                },
                False,
                str(e)
            )
            
            self.logger.error(f"用户 {user_id} 工具 {tool_name} 权限撤销失败: {e}")
            return False
    
    async def get_user_permissions(
        self,
        user_id: str,
        tool_name: str
    ) -> Set[ToolPermission]:
        """
        获取用户工具权限
        
        Args:
            user_id: 用户ID
            tool_name: 工具名称
        
        Returns:
            Set[ToolPermission]: 权限集合
        """
        # 检查缓存
        if (self.config.enable_permission_cache and
            user_id in self._permission_cache and
            tool_name in self._permission_cache[user_id]):
            return self._permission_cache[user_id][tool_name]
        
        permissions = set()
        
        try:
            # 获取工具配置
            tool_config = await self._get_tool_config(tool_name)
            if not tool_config:
                return permissions
            
            async with self._get_session() as session:
                # 查询用户权限
                permission_record = await session.query(UserToolPermissionModel).filter_by(
                    user_id=user_id,
                    tool_id=tool_config.id,
                    is_active=True
                ).first()
                
                if permission_record:
                    # 检查是否过期
                    if (permission_record.expires_at is None or
                        permission_record.expires_at > datetime.utcnow()):
                        permissions = {ToolPermission(p) for p in permission_record.permissions}
                    else:
                        # 权限已过期，标记为非活跃
                        permission_record.is_active = False
                        await session.commit()
                else:
                    # 使用默认权限
                    permissions = {ToolPermission(p) for p in tool_config.permissions}
            
            # 缓存权限
            if self.config.enable_permission_cache:
                if user_id not in self._permission_cache:
                    self._permission_cache[user_id] = {}
                self._permission_cache[user_id][tool_name] = permissions
            
        except Exception as e:
            self.logger.error(f"获取用户 {user_id} 工具 {tool_name} 权限失败: {e}")
        
        return permissions
    
    async def execute_tool(
        self,
        tool_name: str,
        context: ToolExecutionContext,
        **kwargs
    ) -> ToolResult:
        """
        执行工具
        
        Args:
            tool_name: 工具名称
            context: 执行上下文
            **kwargs: 额外参数
        
        Returns:
            ToolResult: 执行结果
        """
        start_time = datetime.now()
        
        try:
            # 记录执行开始
            if self.config.enable_execution_log:
                await self._log_execution_start(tool_name, context, kwargs)
            
            # 执行工具
            result = await self.registry.execute_tool(tool_name, context, **kwargs)
            
            # 记录执行完成
            if self.config.enable_execution_log:
                execution_time = int((datetime.now() - start_time).total_seconds() * 1000)
                await self._log_execution_complete(tool_name, context, result, execution_time)
            
            # 记录操作日志
            await self._log_operation(
                ToolOperationType.EXECUTE,
                tool_name,
                context.user_id,
                {
                    'execution_id': context.execution_id,
                    'status': result.status.value
                },
                result.status == ToolStatus.SUCCESS
            )
            
            return result
            
        except Exception as e:
            self.logger.error(f"工具 {tool_name} 执行异常: {e}")
            
            # 记录执行错误
            if self.config.enable_execution_log:
                execution_time = int((datetime.now() - start_time).total_seconds() * 1000)
                error_result = ToolResult(
                    tool_name=tool_name,
                    execution_id=context.execution_id,
                    status=ToolStatus.FAILED,
                    error=ToolError(
                        code="EXECUTION_ERROR",
                        message=str(e)
                    ),
                    user_id=context.user_id
                )
                await self._log_execution_complete(tool_name, context, error_result, execution_time)
            
            # 记录操作日志
            await self._log_operation(
                ToolOperationType.EXECUTE,
                tool_name,
                context.user_id,
                {
                    'execution_id': context.execution_id,
                    'error': str(e)
                },
                False,
                str(e)
            )
            
            raise
    
    async def get_tools_for_user(
        self,
        user_id: str,
        tool_filter: Optional[ToolFilter] = None
    ) -> List[Dict[str, Any]]:
        """
        获取用户可用的工具列表
        
        Args:
            user_id: 用户ID
            tool_filter: 工具过滤器
        
        Returns:
            List[Dict[str, Any]]: 工具信息列表
        """
        tools = []
        
        try:
            # 从注册器获取工具
            registry_tools = await self.registry.get_tools_for_user(user_id, tool_filter)
            
            for tool in registry_tools:
                tool_info = self.registry.get_tool_info(tool.name)
                if tool_info:
                    # 添加用户权限信息
                    user_permissions = await self.get_user_permissions(user_id, tool.name)
                    tool_info['user_permissions'] = [p.value for p in user_permissions]
                    tools.append(tool_info)
            
        except Exception as e:
            self.logger.error(f"获取用户 {user_id} 工具列表失败: {e}")
        
        return tools
    
    async def get_execution_history(
        self,
        user_id: str,
        tool_name: Optional[str] = None,
        limit: int = 100,
        offset: int = 0
    ) -> List[Dict[str, Any]]:
        """
        获取工具执行历史
        
        Args:
            user_id: 用户ID
            tool_name: 工具名称（可选）
            limit: 限制数量
            offset: 偏移量
        
        Returns:
            List[Dict[str, Any]]: 执行历史列表
        """
        history = []
        
        try:
            async with self._get_session() as session:
                query = session.query(ToolExecutionLogModel).filter_by(user_id=user_id)
                
                if tool_name:
                    tool_config = await self._get_tool_config(tool_name)
                    if tool_config:
                        query = query.filter_by(tool_id=tool_config.id)
                
                query = query.order_by(ToolExecutionLogModel.started_at.desc())
                query = query.offset(offset).limit(limit)
                
                logs = await query.all()
                
                for log in logs:
                    history.append({
                        'execution_id': log.execution_id,
                        'tool_name': log.tool.name,
                        'status': log.status,
                        'execution_time': log.execution_time,
                        'started_at': log.started_at.isoformat(),
                        'completed_at': log.completed_at.isoformat() if log.completed_at else None,
                        'error_info': log.error_info
                    })
            
        except Exception as e:
            self.logger.error(f"获取用户 {user_id} 执行历史失败: {e}")
        
        return history
    
    async def get_tool_versions(self, tool_name: str) -> List[Dict[str, Any]]:
        """
        获取工具版本历史
        
        Args:
            tool_name: 工具名称
        
        Returns:
            List[Dict[str, Any]]: 版本历史列表
        """
        versions = []
        
        try:
            tool_config = await self._get_tool_config(tool_name)
            if not tool_config:
                return versions
            
            async with self._get_session() as session:
                version_records = await session.query(ToolVersionModel).filter_by(
                    tool_id=tool_config.id
                ).order_by(ToolVersionModel.created_at.desc()).all()
                
                for record in version_records:
                    versions.append({
                        'version': record.version,
                        'change_log': record.change_log,
                        'created_at': record.created_at.isoformat(),
                        'is_active': record.is_active
                    })
            
        except Exception as e:
            self.logger.error(f"获取工具 {tool_name} 版本历史失败: {e}")
        
        return versions
    
    async def _load_tool_configs(self) -> None:
        """
        从数据库加载工具配置
        """
        try:
            async with self._get_session() as session:
                tool_configs = await session.query(ToolConfigModel).filter_by(
                    status='active'
                ).all()
                
                for config in tool_configs:
                    try:
                        # 动态导入工具类
                        module_name, class_name = config.tool_class.rsplit('.', 1)
                        module = __import__(module_name, fromlist=[class_name])
                        tool_class = getattr(module, class_name)
                        
                        # 反序列化配置
                        tool_config = self._deserialize_config(config.config_data, tool_class)
                        
                        # 注册工具
                        await self.registry.register_tool(
                            name=config.name,
                            tool_class=tool_class,
                            config=tool_config,
                            category=ToolCategory(config.category),
                            version=config.version,
                            description=config.description,
                            author=config.author,
                            dependencies=config.dependencies,
                            permissions={ToolPermission(p) for p in config.permissions},
                            metadata=config.metadata
                        )
                        
                        # 缓存配置
                        self._config_cache[config.name] = config
                        self._cache_timestamps[config.name] = datetime.now()
                        
                        self.logger.info(f"工具 {config.name} 加载成功")
                        
                    except Exception as e:
                        self.logger.error(f"工具 {config.name} 加载失败: {e}")
            
        except Exception as e:
            self.logger.error(f"加载工具配置失败: {e}")
    
    async def _get_tool_config(self, name: str) -> Optional[ToolConfigModel]:
        """
        获取工具配置
        
        Args:
            name: 工具名称
        
        Returns:
            Optional[ToolConfigModel]: 工具配置
        """
        # 检查缓存
        if (name in self._config_cache and
            name in self._cache_timestamps and
            (datetime.now() - self._cache_timestamps[name]).seconds < self.config.cache_ttl):
            return self._config_cache[name]
        
        # 从数据库查询
        try:
            async with self._get_session() as session:
                config = await session.query(ToolConfigModel).filter_by(
                    name=name,
                    status='active'
                ).first()
                
                if config:
                    # 更新缓存
                    self._config_cache[name] = config
                    self._cache_timestamps[name] = datetime.now()
                
                return config
                
        except Exception as e:
            self.logger.error(f"获取工具 {name} 配置失败: {e}")
            return None
    
    async def _delete_tool_config(self, name: str) -> None:
        """
        删除工具配置
        
        Args:
            name: 工具名称
        """
        try:
            async with self._get_session() as session:
                config = await session.query(ToolConfigModel).filter_by(name=name).first()
                if config:
                    await session.delete(config)
                    await session.commit()
                    
        except Exception as e:
            self.logger.error(f"删除工具 {name} 配置失败: {e}")
    
    def _serialize_config(self, config: ToolConfig) -> Dict[str, Any]:
        """
        序列化工具配置
        
        Args:
            config: 工具配置
        
        Returns:
            Dict[str, Any]: 序列化后的配置
        """
        try:
            return asdict(config)
        except Exception:
            # 如果不是dataclass，尝试转换为字典
            return config.__dict__ if hasattr(config, '__dict__') else {}
    
    def _deserialize_config(self, config_data: Dict[str, Any], tool_class: Type[BaseTool]) -> ToolConfig:
        """
        反序列化工具配置
        
        Args:
            config_data: 配置数据
            tool_class: 工具类
        
        Returns:
            ToolConfig: 工具配置
        """
        # 获取工具类的配置类
        config_class = getattr(tool_class, 'CONFIG_CLASS', ToolConfig)
        
        try:
            return config_class(**config_data)
        except Exception:
            # 如果反序列化失败，返回基础配置
            return ToolConfig(**config_data)
    
    async def _check_admin_permission(self, user_id: str) -> bool:
        """
        检查管理员权限
        
        Args:
            user_id: 用户ID
        
        Returns:
            bool: 是否有管理员权限
        """
        try:
            return await self.user_service.is_admin(user_id)
        except Exception as e:
            self.logger.error(f"检查用户 {user_id} 管理员权限失败: {e}")
            return False
    
    async def _log_operation(
        self,
        operation_type: ToolOperationType,
        tool_name: str,
        user_id: str,
        details: Dict[str, Any],
        success: bool,
        error_message: Optional[str] = None
    ) -> None:
        """
        记录操作日志
        
        Args:
            operation_type: 操作类型
            tool_name: 工具名称
            user_id: 用户ID
            details: 详细信息
            success: 是否成功
            error_message: 错误消息
        """
        log = ToolOperationLog(
            operation_type=operation_type,
            tool_name=tool_name,
            user_id=user_id,
            details=details,
            timestamp=datetime.now(),
            success=success,
            error_message=error_message
        )
        
        self._operation_logs.append(log)
        
        # 限制日志数量
        if len(self._operation_logs) > 1000:
            self._operation_logs = self._operation_logs[-500:]
    
    async def _log_execution_start(
        self,
        tool_name: str,
        context: ToolExecutionContext,
        input_data: Dict[str, Any]
    ) -> None:
        """
        记录执行开始
        
        Args:
            tool_name: 工具名称
            context: 执行上下文
            input_data: 输入数据
        """
        try:
            tool_config = await self._get_tool_config(tool_name)
            if not tool_config:
                return
            
            async with self._get_session() as session:
                log = ToolExecutionLogModel(
                    execution_id=context.execution_id,
                    tool_id=tool_config.id,
                    user_id=context.user_id,
                    status='running',
                    input_data=input_data
                )
                session.add(log)
                await session.commit()
                
        except Exception as e:
            self.logger.error(f"记录工具 {tool_name} 执行开始失败: {e}")
    
    async def _log_execution_complete(
        self,
        tool_name: str,
        context: ToolExecutionContext,
        result: ToolResult,
        execution_time: int
    ) -> None:
        """
        记录执行完成
        
        Args:
            tool_name: 工具名称
            context: 执行上下文
            result: 执行结果
            execution_time: 执行时间（毫秒）
        """
        try:
            async with self._get_session() as session:
                log = await session.query(ToolExecutionLogModel).filter_by(
                    execution_id=context.execution_id
                ).first()
                
                if log:
                    log.status = result.status.value
                    log.output_data = result.data if result.data else None
                    log.error_info = asdict(result.error) if result.error else None
                    log.execution_time = execution_time
                    log.completed_at = datetime.utcnow()
                    
                    await session.commit()
                    
        except Exception as e:
            self.logger.error(f"记录工具 {tool_name} 执行完成失败: {e}")
    
    async def _cleanup_task(self) -> None:
        """
        清理任务
        """
        while True:
            try:
                await asyncio.sleep(3600)  # 每小时执行一次
                
                # 清理过期的执行日志
                cutoff_date = datetime.utcnow() - timedelta(days=self.config.log_retention_days)
                
                async with self._get_session() as session:
                    await session.query(ToolExecutionLogModel).filter(
                        ToolExecutionLogModel.started_at < cutoff_date
                    ).delete()
                    
                    await session.commit()
                
                # 清理过期的权限
                async with self._get_session() as session:
                    expired_permissions = await session.query(UserToolPermissionModel).filter(
                        UserToolPermissionModel.expires_at < datetime.utcnow(),
                        UserToolPermissionModel.is_active == True
                    ).all()
                    
                    for permission in expired_permissions:
                        permission.is_active = False
                    
                    await session.commit()
                
                # 清理缓存
                now = datetime.now()
                expired_keys = [
                    key for key, timestamp in self._cache_timestamps.items()
                    if (now - timestamp).seconds > self.config.cache_ttl
                ]
                
                for key in expired_keys:
                    if key in self._config_cache:
                        del self._config_cache[key]
                    if key in self._cache_timestamps:
                        del self._cache_timestamps[key]
                
                self.logger.info("清理任务完成")
                
            except Exception as e:
                self.logger.error(f"清理任务失败: {e}")
    
    def _get_session(self) -> AsyncSession:
        """
        获取数据库会话
        
        Returns:
            AsyncSession: 数据库会话
        """
        if not self._session_factory:
            raise ToolServiceError("数据库会话工厂未初始化")
        return self._session_factory()
    
    async def cleanup(self) -> None:
        """
        清理资源
        """
        try:
            if self._engine:
                await self._engine.dispose()
            
            self._config_cache.clear()
            self._permission_cache.clear()
            self._cache_timestamps.clear()
            self._operation_logs.clear()
            
            self.logger.info("工具服务清理完成")
            
        except Exception as e:
            self.logger.error(f"工具服务清理失败: {e}")