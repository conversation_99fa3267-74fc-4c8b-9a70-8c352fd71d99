# -*- coding: utf-8 -*-
"""
A2A多智能体系统监控API接口

提供系统监控、性能分析、告警管理等RESTful API接口
"""

import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from fastapi import APIRouter, Depends, HTTPException, Query, Path, Body
from fastapi.responses import StreamingResponse
from pydantic import BaseModel, Field
from sqlalchemy.orm import Session

from ...core.database import get_db
from ...auth.dependencies import get_current_user, require_permission
from ...auth.permissions import Permission
from ...models import User
from ...services.monitoring_service import (
    monitoring_service,
    MetricType,
    AlertLevel,
    AlertStatus,
    MonitoringScope,
    Metric,
    Alert,
    AlertRule
)
from ...core.logging import get_logger

logger = get_logger(__name__)

# 创建路由器
router = APIRouter(prefix="/monitoring")


# ==================== 请求/响应模型 ====================

class MetricRequest(BaseModel):
    """指标请求模型"""
    name: str = Field(..., description="指标名称")
    metric_type: MetricType = Field(..., description="指标类型")
    value: Union[int, float] = Field(..., description="指标值")
    labels: Optional[Dict[str, str]] = Field(None, description="标签")
    scope: MonitoringScope = Field(MonitoringScope.APPLICATION, description="监控范围")
    unit: Optional[str] = Field(None, description="单位")
    description: Optional[str] = Field(None, description="描述")


class AlertRequest(BaseModel):
    """告警请求模型"""
    name: str = Field(..., description="告警名称")
    level: AlertLevel = Field(..., description="告警级别")
    message: str = Field(..., description="告警消息")
    scope: MonitoringScope = Field(MonitoringScope.APPLICATION, description="监控范围")
    source: Optional[str] = Field(None, description="来源")
    target: Optional[str] = Field(None, description="目标")
    user_id: Optional[int] = Field(None, description="用户ID")
    session_id: Optional[str] = Field(None, description="会话ID")
    agent_id: Optional[str] = Field(None, description="智能体ID")
    workflow_id: Optional[str] = Field(None, description="工作流ID")
    task_id: Optional[str] = Field(None, description="任务ID")
    labels: Optional[Dict[str, str]] = Field(None, description="标签")
    metadata: Optional[Dict[str, Any]] = Field(None, description="元数据")


class AlertRuleRequest(BaseModel):
    """告警规则请求模型"""
    name: str = Field(..., description="规则名称")
    metric_name: str = Field(..., description="指标名称")
    condition: str = Field(..., description="条件表达式")
    threshold: Union[int, float] = Field(..., description="阈值")
    level: AlertLevel = Field(..., description="告警级别")
    scope: MonitoringScope = Field(..., description="监控范围")
    enabled: bool = Field(True, description="是否启用")
    labels: Optional[Dict[str, str]] = Field(None, description="标签")
    metadata: Optional[Dict[str, Any]] = Field(None, description="元数据")
    cooldown_seconds: int = Field(300, description="冷却时间（秒）")


class MetricResponse(BaseModel):
    """指标响应模型"""
    metric_id: str
    name: str
    metric_type: str
    value: Union[int, float]
    timestamp: str
    labels: Dict[str, str]
    scope: str
    unit: Optional[str]
    description: Optional[str]


class AlertResponse(BaseModel):
    """告警响应模型"""
    alert_id: str
    name: str
    level: str
    message: str
    timestamp: str
    status: str
    scope: str
    source: Optional[str]
    target: Optional[str]
    user_id: Optional[int]
    session_id: Optional[str]
    agent_id: Optional[str]
    workflow_id: Optional[str]
    task_id: Optional[str]
    labels: Dict[str, str]
    metadata: Dict[str, Any]
    resolved_at: Optional[str]
    acknowledged_at: Optional[str]
    acknowledged_by: Optional[str]


class SystemStatusResponse(BaseModel):
    """系统状态响应模型"""
    status: str = Field(..., description="系统状态")
    timestamp: str = Field(..., description="时间戳")
    uptime: float = Field(..., description="运行时间（秒）")
    version: str = Field(..., description="版本")
    environment: str = Field(..., description="环境")
    metrics_summary: Dict[str, Any] = Field(..., description="指标摘要")
    alerts_summary: Dict[str, Any] = Field(..., description="告警摘要")
    performance_summary: Dict[str, Any] = Field(..., description="性能摘要")


class PerformanceReportResponse(BaseModel):
    """性能报告响应模型"""
    report_id: str = Field(..., description="报告ID")
    generated_at: str = Field(..., description="生成时间")
    time_range: Dict[str, str] = Field(..., description="时间范围")
    system_metrics: Dict[str, Any] = Field(..., description="系统指标")
    application_metrics: Dict[str, Any] = Field(..., description="应用指标")
    performance_analysis: Dict[str, Any] = Field(..., description="性能分析")
    recommendations: List[str] = Field(..., description="优化建议")


# ==================== 系统状态接口 ====================

@router.get("/status", response_model=SystemStatusResponse)
async def get_system_status(
    current_user: User = Depends(get_current_user),
    _: None = Depends(require_permission(Permission.ADMIN_READ))
):
    """
    获取系统状态
    
    需要管理员权限
    """
    try:
        # 获取监控统计
        stats = await monitoring_service.get_monitoring_stats()
        
        # 获取系统指标
        system_metrics = await monitoring_service.get_metrics(
            scope=MonitoringScope.SYSTEM,
            limit=10
        )
        
        # 获取活跃告警
        active_alerts = await monitoring_service.get_alerts(
            status=AlertStatus.ACTIVE,
            limit=10
        )
        
        # 计算运行时间（简化实现）
        uptime = 3600.0  # 假设运行1小时
        
        return SystemStatusResponse(
            status="healthy" if len(active_alerts) == 0 else "warning",
            timestamp=datetime.utcnow().isoformat(),
            uptime=uptime,
            version="1.0.0",
            environment="production",
            metrics_summary={
                "total_metrics": stats.get("total_metrics", 0),
                "recent_metrics": len(system_metrics),
                "metric_types": stats.get("metric_stats", {}).get("metric_types", {})
            },
            alerts_summary={
                "total_alerts": stats.get("total_alerts", 0),
                "active_alerts": len(active_alerts),
                "alert_levels": stats.get("alert_stats", {}).get("alert_levels", {})
            },
            performance_summary={
                "cpu_usage": next((m.value for m in system_metrics if m.name == "system_cpu_usage"), 0),
                "memory_usage": next((m.value for m in system_metrics if m.name == "system_memory_usage"), 0),
                "disk_usage": next((m.value for m in system_metrics if m.name == "system_disk_usage"), 0)
            }
        )
        
    except Exception as e:
        logger.error(f"获取系统状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取系统状态失败")


# ==================== 指标管理接口 ====================

@router.post("/metrics", response_model=Dict[str, str])
async def create_metric(
    metric_request: MetricRequest,
    current_user: User = Depends(get_current_user)
):
    """
    创建指标
    
    用户可以创建自己的指标
    """
    try:
        # 添加用户标签
        labels = metric_request.labels or {}
        labels["user_id"] = str(current_user.id)
        
        metric_id = await monitoring_service.collect_metric(
            name=metric_request.name,
            metric_type=metric_request.metric_type,
            value=metric_request.value,
            labels=labels,
            scope=metric_request.scope,
            unit=metric_request.unit,
            description=metric_request.description
        )
        
        return {"metric_id": metric_id, "message": "指标创建成功"}
        
    except Exception as e:
        logger.error(f"创建指标失败: {str(e)}")
        raise HTTPException(status_code=500, detail="创建指标失败")


@router.get("/metrics", response_model=List[MetricResponse])
async def get_metrics(
    name_pattern: Optional[str] = Query(None, description="名称模式"),
    scope: Optional[MonitoringScope] = Query(None, description="监控范围"),
    start_time: Optional[datetime] = Query(None, description="开始时间"),
    end_time: Optional[datetime] = Query(None, description="结束时间"),
    limit: int = Query(100, ge=1, le=1000, description="限制数量"),
    current_user: User = Depends(get_current_user)
):
    """
    获取指标列表
    
    用户只能查看自己的指标，管理员可以查看所有指标
    """
    try:
        # 构建时间范围
        time_range = None
        if start_time and end_time:
            time_range = (start_time, end_time)
        
        # 获取指标
        metrics = await monitoring_service.get_metrics(
            name_pattern=name_pattern,
            scope=scope,
            time_range=time_range,
            limit=limit
        )
        
        # 权限过滤
        if not current_user.is_admin:
            # 非管理员只能查看自己的指标
            metrics = [
                m for m in metrics 
                if m.labels.get("user_id") == str(current_user.id)
            ]
        
        return [
            MetricResponse(
                metric_id=m.metric_id,
                name=m.name,
                metric_type=m.metric_type.value,
                value=m.value,
                timestamp=m.timestamp.isoformat(),
                labels=m.labels,
                scope=m.scope.value,
                unit=m.unit,
                description=m.description
            )
            for m in metrics
        ]
        
    except Exception as e:
        logger.error(f"获取指标列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取指标列表失败")


@router.get("/metrics/{metric_name}/history")
async def get_metric_history(
    metric_name: str = Path(..., description="指标名称"),
    limit: int = Query(100, ge=1, le=1000, description="限制数量"),
    current_user: User = Depends(get_current_user)
):
    """
    获取指标历史数据
    """
    try:
        # 构建标签过滤（非管理员只能查看自己的数据）
        labels = {}
        if not current_user.is_admin:
            labels["user_id"] = str(current_user.id)
        
        history = await monitoring_service.metric_collector.get_metric_history(
            name=metric_name,
            labels=labels,
            limit=limit
        )
        
        return {
            "metric_name": metric_name,
            "history": history,
            "count": len(history)
        }
        
    except Exception as e:
        logger.error(f"获取指标历史失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取指标历史失败")


@router.get("/metrics/{metric_name}/aggregates")
async def get_metric_aggregates(
    metric_name: str = Path(..., description="指标名称"),
    current_user: User = Depends(get_current_user)
):
    """
    获取指标聚合数据
    """
    try:
        # 构建标签过滤（非管理员只能查看自己的数据）
        labels = {}
        if not current_user.is_admin:
            labels["user_id"] = str(current_user.id)
        
        aggregates = await monitoring_service.metric_collector.get_metric_aggregates(
            name=metric_name,
            labels=labels
        )
        
        return {
            "metric_name": metric_name,
            "aggregates": aggregates
        }
        
    except Exception as e:
        logger.error(f"获取指标聚合数据失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取指标聚合数据失败")


# ==================== 告警管理接口 ====================

@router.post("/alerts", response_model=Dict[str, str])
async def create_alert(
    alert_request: AlertRequest,
    current_user: User = Depends(get_current_user)
):
    """
    创建告警
    
    用户可以创建自己的告警
    """
    try:
        # 设置用户ID
        user_id = alert_request.user_id or current_user.id
        
        # 非管理员只能创建自己的告警
        if not current_user.is_admin and user_id != current_user.id:
            raise HTTPException(status_code=403, detail="权限不足")
        
        alert_id = await monitoring_service.create_alert(
            name=alert_request.name,
            level=alert_request.level,
            message=alert_request.message,
            scope=alert_request.scope,
            source=alert_request.source,
            target=alert_request.target,
            user_id=user_id,
            session_id=alert_request.session_id,
            agent_id=alert_request.agent_id,
            workflow_id=alert_request.workflow_id,
            task_id=alert_request.task_id,
            labels=alert_request.labels,
            metadata=alert_request.metadata
        )
        
        return {"alert_id": alert_id, "message": "告警创建成功"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建告警失败: {str(e)}")
        raise HTTPException(status_code=500, detail="创建告警失败")


@router.get("/alerts", response_model=List[AlertResponse])
async def get_alerts(
    status: Optional[AlertStatus] = Query(None, description="告警状态"),
    level: Optional[AlertLevel] = Query(None, description="告警级别"),
    scope: Optional[MonitoringScope] = Query(None, description="监控范围"),
    start_time: Optional[datetime] = Query(None, description="开始时间"),
    end_time: Optional[datetime] = Query(None, description="结束时间"),
    limit: int = Query(100, ge=1, le=1000, description="限制数量"),
    current_user: User = Depends(get_current_user)
):
    """
    获取告警列表
    
    用户只能查看自己的告警，管理员可以查看所有告警
    """
    try:
        # 构建时间范围
        time_range = None
        if start_time and end_time:
            time_range = (start_time, end_time)
        
        # 获取告警
        alerts = await monitoring_service.get_alerts(
            status=status,
            level=level,
            scope=scope,
            time_range=time_range,
            limit=limit
        )
        
        # 权限过滤
        if not current_user.is_admin:
            # 非管理员只能查看自己的告警
            alerts = [
                a for a in alerts 
                if a.user_id == current_user.id
            ]
        
        return [
            AlertResponse(
                alert_id=a.alert_id,
                name=a.name,
                level=a.level.value,
                message=a.message,
                timestamp=a.timestamp.isoformat(),
                status=a.status.value,
                scope=a.scope.value,
                source=a.source,
                target=a.target,
                user_id=a.user_id,
                session_id=a.session_id,
                agent_id=a.agent_id,
                workflow_id=a.workflow_id,
                task_id=a.task_id,
                labels=a.labels,
                metadata=a.metadata,
                resolved_at=a.resolved_at.isoformat() if a.resolved_at else None,
                acknowledged_at=a.acknowledged_at.isoformat() if a.acknowledged_at else None,
                acknowledged_by=a.acknowledged_by
            )
            for a in alerts
        ]
        
    except Exception as e:
        logger.error(f"获取告警列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取告警列表失败")


@router.post("/alerts/{alert_id}/resolve")
async def resolve_alert(
    alert_id: str = Path(..., description="告警ID"),
    current_user: User = Depends(get_current_user)
):
    """
    解决告警
    """
    try:
        # 检查告警是否存在
        alert = monitoring_service.alerts.get(alert_id)
        if not alert:
            raise HTTPException(status_code=404, detail="告警不存在")
        
        # 权限检查
        if not current_user.is_admin and alert.user_id != current_user.id:
            raise HTTPException(status_code=403, detail="权限不足")
        
        success = await monitoring_service.resolve_alert(
            alert_id=alert_id,
            resolved_by=current_user.username
        )
        
        if success:
            return {"message": "告警已解决"}
        else:
            raise HTTPException(status_code=400, detail="解决告警失败")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"解决告警失败: {str(e)}")
        raise HTTPException(status_code=500, detail="解决告警失败")


@router.post("/alerts/{alert_id}/acknowledge")
async def acknowledge_alert(
    alert_id: str = Path(..., description="告警ID"),
    current_user: User = Depends(get_current_user)
):
    """
    确认告警
    """
    try:
        # 检查告警是否存在
        alert = monitoring_service.alerts.get(alert_id)
        if not alert:
            raise HTTPException(status_code=404, detail="告警不存在")
        
        # 权限检查
        if not current_user.is_admin and alert.user_id != current_user.id:
            raise HTTPException(status_code=403, detail="权限不足")
        
        success = await monitoring_service.acknowledge_alert(
            alert_id=alert_id,
            acknowledged_by=current_user.username
        )
        
        if success:
            return {"message": "告警已确认"}
        else:
            raise HTTPException(status_code=400, detail="确认告警失败")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"确认告警失败: {str(e)}")
        raise HTTPException(status_code=500, detail="确认告警失败")


# ==================== 告警规则管理接口 ====================

@router.post("/alert-rules", response_model=Dict[str, str])
async def create_alert_rule(
    rule_request: AlertRuleRequest,
    current_user: User = Depends(get_current_user)
):
    """
    创建告警规则
    
    用户可以创建自己的告警规则
    """
    try:
        # 添加用户标签
        labels = rule_request.labels or {}
        labels["user_id"] = str(current_user.id)
        
        rule_id = await monitoring_service.create_alert_rule(
            name=rule_request.name,
            metric_name=rule_request.metric_name,
            condition=rule_request.condition,
            threshold=rule_request.threshold,
            level=rule_request.level,
            scope=rule_request.scope,
            enabled=rule_request.enabled,
            labels=labels,
            metadata=rule_request.metadata,
            cooldown_seconds=rule_request.cooldown_seconds
        )
        
        return {"rule_id": rule_id, "message": "告警规则创建成功"}
        
    except Exception as e:
        logger.error(f"创建告警规则失败: {str(e)}")
        raise HTTPException(status_code=500, detail="创建告警规则失败")


@router.get("/alert-rules")
async def get_alert_rules(
    current_user: User = Depends(get_current_user)
):
    """
    获取告警规则列表
    
    用户只能查看自己的规则，管理员可以查看所有规则
    """
    try:
        rules = list(monitoring_service.alert_rules.values())
        
        # 权限过滤
        if not current_user.is_admin:
            # 非管理员只能查看自己的规则
            rules = [
                r for r in rules 
                if r.labels.get("user_id") == str(current_user.id)
            ]
        
        return [
            {
                "rule_id": r.rule_id,
                "name": r.name,
                "metric_name": r.metric_name,
                "condition": r.condition,
                "threshold": r.threshold,
                "level": r.level.value,
                "scope": r.scope.value,
                "enabled": r.enabled,
                "labels": r.labels,
                "metadata": r.metadata,
                "cooldown_seconds": r.cooldown_seconds,
                "last_triggered": r.last_triggered.isoformat() if r.last_triggered else None
            }
            for r in rules
        ]
        
    except Exception as e:
        logger.error(f"获取告警规则列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取告警规则列表失败")


# ==================== 性能报告接口 ====================

@router.get("/performance-report", response_model=PerformanceReportResponse)
async def get_performance_report(
    start_time: Optional[datetime] = Query(None, description="开始时间"),
    end_time: Optional[datetime] = Query(None, description="结束时间"),
    current_user: User = Depends(get_current_user)
):
    """
    获取性能报告
    
    用户可以查看自己的性能报告，管理员可以查看系统性能报告
    """
    try:
        # 设置默认时间范围（最近24小时）
        if not end_time:
            end_time = datetime.utcnow()
        if not start_time:
            start_time = end_time - timedelta(hours=24)
        
        # 获取系统指标
        system_metrics = await monitoring_service.get_metrics(
            scope=MonitoringScope.SYSTEM,
            time_range=(start_time, end_time),
            limit=1000
        )
        
        # 获取应用指标
        application_metrics = await monitoring_service.get_metrics(
            scope=MonitoringScope.APPLICATION,
            time_range=(start_time, end_time),
            limit=1000
        )
        
        # 权限过滤（非管理员只能查看自己的应用指标）
        if not current_user.is_admin:
            application_metrics = [
                m for m in application_metrics 
                if m.labels.get("user_id") == str(current_user.id)
            ]
        
        # 分析性能数据
        performance_analysis = {
            "cpu_analysis": _analyze_cpu_metrics(system_metrics),
            "memory_analysis": _analyze_memory_metrics(system_metrics),
            "disk_analysis": _analyze_disk_metrics(system_metrics),
            "application_analysis": _analyze_application_metrics(application_metrics)
        }
        
        # 生成优化建议
        recommendations = _generate_recommendations(performance_analysis)
        
        return PerformanceReportResponse(
            report_id=f"report_{int(datetime.utcnow().timestamp())}",
            generated_at=datetime.utcnow().isoformat(),
            time_range={
                "start_time": start_time.isoformat(),
                "end_time": end_time.isoformat()
            },
            system_metrics={
                "cpu_metrics": [m.to_dict() for m in system_metrics if "cpu" in m.name],
                "memory_metrics": [m.to_dict() for m in system_metrics if "memory" in m.name],
                "disk_metrics": [m.to_dict() for m in system_metrics if "disk" in m.name],
                "network_metrics": [m.to_dict() for m in system_metrics if "network" in m.name]
            },
            application_metrics={
                "user_metrics": [m.to_dict() for m in application_metrics if "user" in m.name],
                "agent_metrics": [m.to_dict() for m in application_metrics if "agent" in m.name],
                "session_metrics": [m.to_dict() for m in application_metrics if "session" in m.name],
                "task_metrics": [m.to_dict() for m in application_metrics if "task" in m.name]
            },
            performance_analysis=performance_analysis,
            recommendations=recommendations
        )
        
    except Exception as e:
        logger.error(f"获取性能报告失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取性能报告失败")


# ==================== 监控数据导出接口 ====================

@router.get("/export/metrics")
async def export_metrics(
    format: str = Query("json", description="导出格式 (json, csv)"),
    start_time: Optional[datetime] = Query(None, description="开始时间"),
    end_time: Optional[datetime] = Query(None, description="结束时间"),
    current_user: User = Depends(get_current_user)
):
    """
    导出监控数据
    
    用户只能导出自己的数据，管理员可以导出所有数据
    """
    try:
        # 构建时间范围
        time_range = None
        if start_time and end_time:
            time_range = (start_time, end_time)
        
        # 获取指标
        metrics = await monitoring_service.get_metrics(
            time_range=time_range,
            limit=10000
        )
        
        # 权限过滤
        if not current_user.is_admin:
            metrics = [
                m for m in metrics 
                if m.labels.get("user_id") == str(current_user.id)
            ]
        
        if format.lower() == "csv":
            # CSV格式导出
            import csv
            import io
            
            output = io.StringIO()
            writer = csv.writer(output)
            
            # 写入标题行
            writer.writerow([
                "metric_id", "name", "type", "value", "timestamp", 
                "scope", "unit", "description", "labels"
            ])
            
            # 写入数据行
            for metric in metrics:
                writer.writerow([
                    metric.metric_id,
                    metric.name,
                    metric.metric_type.value,
                    metric.value,
                    metric.timestamp.isoformat(),
                    metric.scope.value,
                    metric.unit or "",
                    metric.description or "",
                    str(metric.labels)
                ])
            
            output.seek(0)
            
            return StreamingResponse(
                io.BytesIO(output.getvalue().encode()),
                media_type="text/csv",
                headers={"Content-Disposition": "attachment; filename=metrics.csv"}
            )
        else:
            # JSON格式导出
            data = {
                "export_time": datetime.utcnow().isoformat(),
                "time_range": {
                    "start_time": start_time.isoformat() if start_time else None,
                    "end_time": end_time.isoformat() if end_time else None
                },
                "metrics": [m.to_dict() for m in metrics],
                "count": len(metrics)
            }
            
            return data
        
    except Exception as e:
        logger.error(f"导出监控数据失败: {str(e)}")
        raise HTTPException(status_code=500, detail="导出监控数据失败")


# ==================== 系统诊断接口 ====================

@router.get("/diagnostics")
async def get_system_diagnostics(
    current_user: User = Depends(get_current_user),
    _: None = Depends(require_permission(Permission.ADMIN_READ))
):
    """
    获取系统诊断信息
    
    需要管理员权限
    """
    try:
        # 获取监控统计
        stats = await monitoring_service.get_monitoring_stats()
        
        # 获取最近的系统指标
        recent_metrics = await monitoring_service.get_metrics(
            scope=MonitoringScope.SYSTEM,
            time_range=(datetime.utcnow() - timedelta(minutes=5), datetime.utcnow()),
            limit=100
        )
        
        # 获取活跃告警
        active_alerts = await monitoring_service.get_alerts(
            status=AlertStatus.ACTIVE,
            limit=50
        )
        
        # 系统健康检查
        health_checks = {
            "cpu_health": _check_cpu_health(recent_metrics),
            "memory_health": _check_memory_health(recent_metrics),
            "disk_health": _check_disk_health(recent_metrics),
            "alert_health": _check_alert_health(active_alerts)
        }
        
        # 性能瓶颈分析
        bottlenecks = _identify_bottlenecks(recent_metrics, active_alerts)
        
        return {
            "timestamp": datetime.utcnow().isoformat(),
            "monitoring_stats": stats,
            "health_checks": health_checks,
            "recent_metrics_summary": {
                "count": len(recent_metrics),
                "types": list(set(m.metric_type.value for m in recent_metrics)),
                "scopes": list(set(m.scope.value for m in recent_metrics))
            },
            "active_alerts_summary": {
                "count": len(active_alerts),
                "levels": list(set(a.level.value for a in active_alerts)),
                "scopes": list(set(a.scope.value for a in active_alerts))
            },
            "bottlenecks": bottlenecks,
            "recommendations": _generate_diagnostic_recommendations(health_checks, bottlenecks)
        }
        
    except Exception as e:
        logger.error(f"获取系统诊断信息失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取系统诊断信息失败")


# ==================== 辅助函数 ====================

def _analyze_cpu_metrics(metrics: List[Metric]) -> Dict[str, Any]:
    """分析CPU指标"""
    cpu_metrics = [m for m in metrics if "cpu" in m.name.lower()]
    if not cpu_metrics:
        return {"status": "no_data"}
    
    values = [m.value for m in cpu_metrics]
    return {
        "status": "normal" if max(values) < 80 else "warning",
        "avg_usage": sum(values) / len(values),
        "max_usage": max(values),
        "min_usage": min(values),
        "sample_count": len(values)
    }


def _analyze_memory_metrics(metrics: List[Metric]) -> Dict[str, Any]:
    """分析内存指标"""
    memory_metrics = [m for m in metrics if "memory" in m.name.lower()]
    if not memory_metrics:
        return {"status": "no_data"}
    
    values = [m.value for m in memory_metrics]
    return {
        "status": "normal" if max(values) < 85 else "warning",
        "avg_usage": sum(values) / len(values),
        "max_usage": max(values),
        "min_usage": min(values),
        "sample_count": len(values)
    }


def _analyze_disk_metrics(metrics: List[Metric]) -> Dict[str, Any]:
    """分析磁盘指标"""
    disk_metrics = [m for m in metrics if "disk" in m.name.lower()]
    if not disk_metrics:
        return {"status": "no_data"}
    
    values = [m.value for m in disk_metrics]
    return {
        "status": "normal" if max(values) < 90 else "warning",
        "avg_usage": sum(values) / len(values),
        "max_usage": max(values),
        "min_usage": min(values),
        "sample_count": len(values)
    }


def _analyze_application_metrics(metrics: List[Metric]) -> Dict[str, Any]:
    """分析应用指标"""
    return {
        "total_metrics": len(metrics),
        "metric_types": list(set(m.metric_type.value for m in metrics)),
        "scopes": list(set(m.scope.value for m in metrics)),
        "time_range": {
            "start": min(m.timestamp for m in metrics).isoformat() if metrics else None,
            "end": max(m.timestamp for m in metrics).isoformat() if metrics else None
        }
    }


def _generate_recommendations(analysis: Dict[str, Any]) -> List[str]:
    """生成优化建议"""
    recommendations = []
    
    # CPU建议
    cpu_analysis = analysis.get("cpu_analysis", {})
    if cpu_analysis.get("status") == "warning":
        recommendations.append("CPU使用率较高，建议优化计算密集型任务")
    
    # 内存建议
    memory_analysis = analysis.get("memory_analysis", {})
    if memory_analysis.get("status") == "warning":
        recommendations.append("内存使用率较高，建议优化内存使用或增加内存")
    
    # 磁盘建议
    disk_analysis = analysis.get("disk_analysis", {})
    if disk_analysis.get("status") == "warning":
        recommendations.append("磁盘使用率较高，建议清理不必要的文件或扩展存储")
    
    if not recommendations:
        recommendations.append("系统运行正常，无特殊优化建议")
    
    return recommendations


def _check_cpu_health(metrics: List[Metric]) -> Dict[str, Any]:
    """检查CPU健康状态"""
    cpu_metrics = [m for m in metrics if "cpu" in m.name.lower()]
    if not cpu_metrics:
        return {"status": "unknown", "message": "无CPU数据"}
    
    avg_cpu = sum(m.value for m in cpu_metrics) / len(cpu_metrics)
    
    if avg_cpu > 90:
        return {"status": "critical", "message": f"CPU使用率过高: {avg_cpu:.1f}%"}
    elif avg_cpu > 80:
        return {"status": "warning", "message": f"CPU使用率较高: {avg_cpu:.1f}%"}
    else:
        return {"status": "healthy", "message": f"CPU使用率正常: {avg_cpu:.1f}%"}


def _check_memory_health(metrics: List[Metric]) -> Dict[str, Any]:
    """检查内存健康状态"""
    memory_metrics = [m for m in metrics if "memory" in m.name.lower()]
    if not memory_metrics:
        return {"status": "unknown", "message": "无内存数据"}
    
    avg_memory = sum(m.value for m in memory_metrics) / len(memory_metrics)
    
    if avg_memory > 95:
        return {"status": "critical", "message": f"内存使用率过高: {avg_memory:.1f}%"}
    elif avg_memory > 85:
        return {"status": "warning", "message": f"内存使用率较高: {avg_memory:.1f}%"}
    else:
        return {"status": "healthy", "message": f"内存使用率正常: {avg_memory:.1f}%"}


def _check_disk_health(metrics: List[Metric]) -> Dict[str, Any]:
    """检查磁盘健康状态"""
    disk_metrics = [m for m in metrics if "disk" in m.name.lower()]
    if not disk_metrics:
        return {"status": "unknown", "message": "无磁盘数据"}
    
    avg_disk = sum(m.value for m in disk_metrics) / len(disk_metrics)
    
    if avg_disk > 98:
        return {"status": "critical", "message": f"磁盘使用率过高: {avg_disk:.1f}%"}
    elif avg_disk > 90:
        return {"status": "warning", "message": f"磁盘使用率较高: {avg_disk:.1f}%"}
    else:
        return {"status": "healthy", "message": f"磁盘使用率正常: {avg_disk:.1f}%"}


def _check_alert_health(alerts: List[Alert]) -> Dict[str, Any]:
    """检查告警健康状态"""
    if not alerts:
        return {"status": "healthy", "message": "无活跃告警"}
    
    critical_alerts = [a for a in alerts if a.level == AlertLevel.CRITICAL]
    warning_alerts = [a for a in alerts if a.level == AlertLevel.WARNING]
    
    if critical_alerts:
        return {
            "status": "critical", 
            "message": f"存在{len(critical_alerts)}个严重告警"
        }
    elif warning_alerts:
        return {
            "status": "warning", 
            "message": f"存在{len(warning_alerts)}个警告告警"
        }
    else:
        return {
            "status": "healthy", 
            "message": f"存在{len(alerts)}个一般告警"
        }


def _identify_bottlenecks(metrics: List[Metric], alerts: List[Alert]) -> List[Dict[str, Any]]:
    """识别性能瓶颈"""
    bottlenecks = []
    
    # 基于指标识别瓶颈
    cpu_metrics = [m for m in metrics if "cpu" in m.name.lower()]
    if cpu_metrics:
        max_cpu = max(m.value for m in cpu_metrics)
        if max_cpu > 90:
            bottlenecks.append({
                "type": "cpu",
                "severity": "high",
                "description": f"CPU使用率达到{max_cpu:.1f}%",
                "recommendation": "优化CPU密集型任务或增加计算资源"
            })
    
    # 基于告警识别瓶颈
    critical_alerts = [a for a in alerts if a.level == AlertLevel.CRITICAL]
    for alert in critical_alerts:
        bottlenecks.append({
            "type": "alert",
            "severity": "critical",
            "description": alert.message,
            "recommendation": "立即处理严重告警"
        })
    
    return bottlenecks


def _generate_diagnostic_recommendations(health_checks: Dict[str, Any], bottlenecks: List[Dict[str, Any]]) -> List[str]:
    """生成诊断建议"""
    recommendations = []
    
    # 基于健康检查生成建议
    for check_name, check_result in health_checks.items():
        if check_result.get("status") == "critical":
            recommendations.append(f"紧急处理: {check_result.get('message')}")
        elif check_result.get("status") == "warning":
            recommendations.append(f"注意: {check_result.get('message')}")
    
    # 基于瓶颈生成建议
    for bottleneck in bottlenecks:
        recommendations.append(bottleneck.get("recommendation", "需要进一步分析"))
    
    if not recommendations:
        recommendations.append("系统运行正常，无特殊建议")
    
    return recommendations