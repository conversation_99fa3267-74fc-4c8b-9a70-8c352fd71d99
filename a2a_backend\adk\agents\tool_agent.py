#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A2A多智能体系统 工具智能体

专门处理工具调用，包含工具权限验证
"""

import logging
import asyncio
import json
import inspect
from typing import Dict, List, Optional, Any, Union, Callable, Awaitable, Type
from datetime import datetime
import uuid

from google.adk.agents.llm_agent import Agent
from google.ai.generativelanguage import Content, Part
from google.generativeai.types import generation_types

from app.models.user import User
from app.models.agent import Agent as AgentModel
from app.models.tool import Tool, ToolExecution
from app.core.logging import get_logger
from app.auth.permissions import check_user_permission

class ToolAgent(Agent):
    """
    工具智能体，专门处理工具调用，包含工具权限验证
    
    提供以下功能：
    1. 工具注册和管理
    2. 工具权限验证
    3. 工具调用执行
    4. 工具调用历史记录
    5. 工具调用监控
    6. 错误处理和重试
    """
    
    def __init__(
        self,
        user_id: str,
        owner_id: str,
        agent_model: AgentModel,
        logger: Optional[logging.Logger] = None,
        **kwargs
    ):
        """
        初始化工具智能体
        
        Args:
            user_id: 用户ID
            owner_id: 拥有者ID
            agent_model: 智能体模型
            options: ADK Agent选项
            logger: 日志记录器
        """
        self.user_id = user_id
        self.owner_id = owner_id
        self.agent_model = agent_model
        self.logger = logger or get_logger(f"tool_agent_{agent_model.id}")
        
        # 初始化基类
        super().__init__(
            name=agent_model.name,
            description=agent_model.description
        )
        
        # 工具注册表
        self.registered_tools: Dict[str, Dict[str, Any]] = {}
        self.tool_instances: Dict[str, Any] = {}
        
        # 执行统计
        self.execution_stats = {
            "total_calls": 0,
            "successful_calls": 0,
            "failed_calls": 0,
            "tools_used": {},
            "average_execution_time": 0.0,
            "last_call": None
        }
        
        self.logger.info(f"ToolAgent已初始化，智能体ID: {agent_model.id}")
    
    async def _check_permission(self, action: str = "use") -> bool:
        """
        检查用户权限
        
        Args:
            action: 操作类型
            
        Returns:
            bool: 是否有权限
        """
        try:
            # 检查用户是否存在
            user = await User.get_by_id(self.user_id)
            if not user:
                self.logger.error(f"用户不存在: {self.user_id}")
                return False
            
            # 检查用户是否有权限使用此智能体
            has_permission = await check_user_permission(
                user_id=self.user_id,
                owner_id=self.owner_id,
                resource_type="agent",
                action=action,
                resource_id=str(self.agent_model.id)
            )
            
            if not has_permission:
                self.logger.error(f"用户 {self.user_id} 没有权限{action}智能体 {self.agent_model.id}")
            
            return has_permission
        except Exception as e:
            self.logger.error(f"权限检查错误: {str(e)}")
            return False
    
    async def _check_tool_permission(self, tool_name: str, action: str = "execute") -> bool:
        """
        检查工具权限
        
        Args:
            tool_name: 工具名称
            action: 操作类型
            
        Returns:
            bool: 是否有权限
        """
        try:
            # 获取工具信息
            tool = await Tool.get_by_name(tool_name)
            if not tool:
                self.logger.error(f"工具不存在: {tool_name}")
                return False
            
            # 检查用户是否有权限使用此工具
            has_permission = await check_user_permission(
                user_id=self.user_id,
                owner_id=self.owner_id,
                resource_type="tool",
                action=action,
                resource_id=str(tool.id)
            )
            
            if not has_permission:
                self.logger.error(f"用户 {self.user_id} 没有权限{action}工具 {tool_name}")
            
            return has_permission
        except Exception as e:
            self.logger.error(f"工具权限检查错误: {str(e)}")
            return False
    
    def register_tool(
        self,
        name: str,
        func: Callable,
        description: str = "",
        parameters: Optional[Dict[str, Any]] = None,
        required_permissions: Optional[List[str]] = None
    ) -> None:
        """
        注册工具
        
        Args:
            name: 工具名称
            func: 工具函数
            description: 工具描述
            parameters: 工具参数定义
            required_permissions: 所需权限列表
        """
        try:
            # 获取函数签名
            sig = inspect.signature(func)
            
            # 自动生成参数定义
            if parameters is None:
                parameters = {}
                for param_name, param in sig.parameters.items():
                    param_info = {
                        "type": "string",  # 默认类型
                        "required": param.default == inspect.Parameter.empty
                    }
                    
                    # 尝试从类型注解获取类型信息
                    if param.annotation != inspect.Parameter.empty:
                        if param.annotation == int:
                            param_info["type"] = "integer"
                        elif param.annotation == float:
                            param_info["type"] = "number"
                        elif param.annotation == bool:
                            param_info["type"] = "boolean"
                        elif param.annotation == list:
                            param_info["type"] = "array"
                        elif param.annotation == dict:
                            param_info["type"] = "object"
                    
                    parameters[param_name] = param_info
            
            # 注册工具
            self.registered_tools[name] = {
                "func": func,
                "description": description,
                "parameters": parameters,
                "required_permissions": required_permissions or [],
                "is_async": asyncio.iscoroutinefunction(func)
            }
            
            self.logger.info(f"工具已注册: {name}")
        except Exception as e:
            self.logger.error(f"注册工具错误: {str(e)}")
            raise e
    
    def unregister_tool(self, name: str) -> None:
        """
        注销工具
        
        Args:
            name: 工具名称
        """
        if name in self.registered_tools:
            del self.registered_tools[name]
            self.logger.info(f"工具已注销: {name}")
        else:
            self.logger.warning(f"工具不存在: {name}")
    
    def get_registered_tools(self) -> Dict[str, Dict[str, Any]]:
        """
        获取已注册的工具列表
        
        Returns:
            Dict[str, Dict[str, Any]]: 工具列表
        """
        tools_info = {}
        for name, tool_info in self.registered_tools.items():
            tools_info[name] = {
                "description": tool_info["description"],
                "parameters": tool_info["parameters"],
                "required_permissions": tool_info["required_permissions"]
            }
        return tools_info
    
    async def _create_execution_record(
        self,
        tool_name: str,
        parameters: Dict[str, Any]
    ) -> Optional[ToolExecution]:
        """
        创建工具执行记录
        
        Args:
            tool_name: 工具名称
            parameters: 执行参数
            
        Returns:
            Optional[ToolExecution]: 工具执行记录
        """
        try:
            # 获取工具信息
            tool = await Tool.get_by_name(tool_name)
            if not tool:
                return None
            
            execution = ToolExecution(
                tool_id=tool.id,
                agent_id=self.agent_model.id,
                user_id=self.user_id,
                owner_id=self.owner_id,
                execution_id=str(uuid.uuid4()),
                parameters=parameters,
                status="running"
            )
            
            await execution.save()
            return execution
        except Exception as e:
            self.logger.error(f"创建工具执行记录错误: {str(e)}")
            return None
    
    async def _update_execution_record(
        self,
        execution: ToolExecution,
        status: str,
        result: Optional[Any] = None,
        error: Optional[str] = None
    ) -> None:
        """
        更新工具执行记录
        
        Args:
            execution: 工具执行记录
            status: 执行状态
            result: 执行结果
            error: 错误信息
        """
        try:
            execution.status = status
            execution.result = result
            execution.error = error
            execution.end_time = datetime.now() if status in ["completed", "failed"] else None
            
            await execution.save()
        except Exception as e:
            self.logger.error(f"更新工具执行记录错误: {str(e)}")
    
    async def call_tool(
        self,
        tool_name: str,
        parameters: Optional[Dict[str, Any]] = None
    ) -> Any:
        """
        调用工具
        
        Args:
            tool_name: 工具名称
            parameters: 工具参数
            
        Returns:
            Any: 工具执行结果
        """
        # 检查基本权限
        has_permission = await self._check_permission("use")
        if not has_permission:
            raise PermissionError(f"用户 {self.user_id} 没有权限使用智能体 {self.agent_model.id}")
        
        # 检查工具权限
        has_tool_permission = await self._check_tool_permission(tool_name, "execute")
        if not has_tool_permission:
            raise PermissionError(f"用户 {self.user_id} 没有权限执行工具 {tool_name}")
        
        # 检查工具是否已注册
        if tool_name not in self.registered_tools:
            raise ValueError(f"工具未注册: {tool_name}")
        
        tool_info = self.registered_tools[tool_name]
        parameters = parameters or {}
        
        # 创建执行记录
        execution = await self._create_execution_record(tool_name, parameters)
        
        start_time = datetime.now()
        
        try:
            # 验证参数
            self._validate_parameters(tool_name, parameters, tool_info["parameters"])
            
            # 执行工具
            func = tool_info["func"]
            if tool_info["is_async"]:
                result = await func(**parameters)
            else:
                result = func(**parameters)
            
            # 更新统计
            end_time = datetime.now()
            execution_time = (end_time - start_time).total_seconds()
            
            self.execution_stats["total_calls"] += 1
            self.execution_stats["successful_calls"] += 1
            self.execution_stats["last_call"] = end_time.isoformat()
            
            # 更新工具使用统计
            if tool_name not in self.execution_stats["tools_used"]:
                self.execution_stats["tools_used"][tool_name] = 0
            self.execution_stats["tools_used"][tool_name] += 1
            
            # 更新平均执行时间
            total_time = self.execution_stats["average_execution_time"] * (self.execution_stats["total_calls"] - 1)
            self.execution_stats["average_execution_time"] = (total_time + execution_time) / self.execution_stats["total_calls"]
            
            # 更新执行记录
            if execution:
                await self._update_execution_record(execution, "completed", result)
            
            self.logger.info(f"工具调用成功: {tool_name}")
            return result
        except Exception as e:
            # 更新统计
            self.execution_stats["total_calls"] += 1
            self.execution_stats["failed_calls"] += 1
            
            # 更新执行记录
            if execution:
                await self._update_execution_record(execution, "failed", error=str(e))
            
            self.logger.error(f"工具调用失败: {tool_name}, 错误: {str(e)}")
            raise e
    
    def _validate_parameters(
        self,
        tool_name: str,
        parameters: Dict[str, Any],
        parameter_schema: Dict[str, Any]
    ) -> None:
        """
        验证工具参数
        
        Args:
            tool_name: 工具名称
            parameters: 实际参数
            parameter_schema: 参数模式
        """
        # 检查必需参数
        for param_name, param_info in parameter_schema.items():
            if param_info.get("required", False) and param_name not in parameters:
                raise ValueError(f"工具 {tool_name} 缺少必需参数: {param_name}")
        
        # 检查参数类型
        for param_name, param_value in parameters.items():
            if param_name in parameter_schema:
                param_info = parameter_schema[param_name]
                expected_type = param_info.get("type", "string")
                
                if not self._check_parameter_type(param_value, expected_type):
                    raise ValueError(f"工具 {tool_name} 参数 {param_name} 类型错误，期望: {expected_type}")
    
    def _check_parameter_type(self, value: Any, expected_type: str) -> bool:
        """
        检查参数类型
        
        Args:
            value: 参数值
            expected_type: 期望类型
            
        Returns:
            bool: 类型是否匹配
        """
        if expected_type == "string":
            return isinstance(value, str)
        elif expected_type == "integer":
            return isinstance(value, int)
        elif expected_type == "number":
            return isinstance(value, (int, float))
        elif expected_type == "boolean":
            return isinstance(value, bool)
        elif expected_type == "array":
            return isinstance(value, list)
        elif expected_type == "object":
            return isinstance(value, dict)
        else:
            return True  # 未知类型，允许通过
    
    async def batch_call_tools(
        self,
        tool_calls: List[Dict[str, Any]]
    ) -> List[Any]:
        """
        批量调用工具
        
        Args:
            tool_calls: 工具调用列表，每个元素包含tool_name和parameters
            
        Returns:
            List[Any]: 工具执行结果列表
        """
        results = []
        
        for tool_call in tool_calls:
            tool_name = tool_call.get("tool_name")
            parameters = tool_call.get("parameters", {})
            
            try:
                result = await self.call_tool(tool_name, parameters)
                results.append({
                    "success": True,
                    "tool_name": tool_name,
                    "result": result
                })
            except Exception as e:
                results.append({
                    "success": False,
                    "tool_name": tool_name,
                    "error": str(e)
                })
        
        return results
    
    async def generate_with_tools(
        self,
        prompt: str,
        available_tools: Optional[List[str]] = None,
        max_tool_calls: int = 10
    ) -> str:
        """
        使用工具生成响应
        
        Args:
            prompt: 输入提示
            available_tools: 可用工具列表
            max_tool_calls: 最大工具调用次数
            
        Returns:
            str: 生成的响应
        """
        # 检查权限
        has_permission = await self._check_permission("use")
        if not has_permission:
            raise PermissionError(f"用户 {self.user_id} 没有权限使用智能体 {self.agent_model.id}")
        
        # 获取可用工具
        if available_tools is None:
            available_tools = list(self.registered_tools.keys())
        
        # 过滤用户有权限的工具
        permitted_tools = []
        for tool_name in available_tools:
            if await self._check_tool_permission(tool_name, "execute"):
                permitted_tools.append(tool_name)
        
        # 构建工具信息
        tools_info = {}
        for tool_name in permitted_tools:
            if tool_name in self.registered_tools:
                tools_info[tool_name] = self.registered_tools[tool_name]
        
        # 这里需要实现与LLM的交互逻辑
        # 暂时返回简单的响应
        response = f"处理提示: {prompt}\n可用工具: {', '.join(permitted_tools)}"
        
        return response
    
    def get_execution_stats(self) -> Dict[str, Any]:
        """
        获取执行统计
        
        Returns:
            Dict[str, Any]: 执行统计信息
        """
        return self.execution_stats.copy()
    
    def get_tool_usage_stats(self) -> Dict[str, Any]:
        """
        获取工具使用统计
        
        Returns:
            Dict[str, Any]: 工具使用统计信息
        """
        return {
            "total_tools_registered": len(self.registered_tools),
            "tools_used": self.execution_stats["tools_used"],
            "most_used_tool": max(self.execution_stats["tools_used"], key=self.execution_stats["tools_used"].get) if self.execution_stats["tools_used"] else None,
            "success_rate": self.execution_stats["successful_calls"] / max(self.execution_stats["total_calls"], 1) * 100
        }
    
    @classmethod
    async def create(
        cls,
        user_id: int,
        owner_id: int,
        agent_model: AgentModel,
        **kwargs
    ) -> "ToolAgent":
        """
        创建ToolAgent实例的工厂方法
        
        Args:
            user_id: 用户ID
            owner_id: 拥有者ID
            agent_model: 智能体模型
            **kwargs: 其他参数
            
        Returns:
            ToolAgent: ToolAgent实例
        """
        # 检查用户权限
        user = await User.get_by_id(user_id)
        if not user:
            raise ValueError(f"用户不存在: {user_id}")
        
        has_permission = await check_user_permission(
            user_id=user_id,
            owner_id=owner_id,
            resource_type="agent",
            action="create",
            resource_id=str(agent_model.id)
        )
        if not has_permission:
            raise PermissionError(f"用户 {user_id} 没有权限创建工具智能体")
        
        # 创建实例
        instance = cls(
            user_id=user_id,
            owner_id=owner_id,
            agent_model=agent_model,
            **kwargs
        )
        
        return instance