#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A2A多智能体系统 工具监控

提供工具执行监控、性能分析、健康检查和告警功能
"""

import asyncio
import logging
import time
import threading
from typing import Dict, List, Any, Optional, Callable, Set, Union
from dataclasses import dataclass, field, asdict
from enum import Enum
from datetime import datetime, timedelta
from collections import defaultdict, deque
import statistics
import json

# Google ADK imports
from google.ai.generativelanguage import FunctionDeclaration, Schema, Type as SchemaType

from .base_tool import (
    BaseTool, ToolConfig, ToolResult, ToolError, ToolStatus,
    ToolExecutionContext, ToolPermission
)
from .tool_registry import ToolRegistry, get_global_registry
from .tool_executor import (
    ToolExecutor, ExecutionResult, ExecutionState, get_global_executor
)


class MonitorLevel(Enum):
    """监控级别枚举"""
    NONE = "none"        # 无监控
    BASIC = "basic"      # 基础监控
    DETAILED = "detailed"  # 详细监控
    FULL = "full"        # 完整监控


class AlertLevel(Enum):
    """告警级别枚举"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


class MetricType(Enum):
    """指标类型枚举"""
    COUNTER = "counter"      # 计数器
    GAUGE = "gauge"          # 仪表盘
    HISTOGRAM = "histogram"  # 直方图
    TIMER = "timer"          # 计时器


class HealthStatus(Enum):
    """健康状态枚举"""
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNHEALTHY = "unhealthy"
    UNKNOWN = "unknown"


@dataclass
class MetricValue:
    """指标值"""
    name: str
    value: Union[int, float]
    metric_type: MetricType
    timestamp: datetime = field(default_factory=datetime.now)
    tags: Dict[str, str] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class PerformanceMetrics:
    """性能指标"""
    # 执行指标
    total_executions: int = 0
    successful_executions: int = 0
    failed_executions: int = 0
    
    # 时间指标
    total_execution_time: float = 0.0
    average_execution_time: float = 0.0
    min_execution_time: float = float('inf')
    max_execution_time: float = 0.0
    
    # 并发指标
    current_concurrent_executions: int = 0
    max_concurrent_executions: int = 0
    
    # 错误指标
    error_rate: float = 0.0
    timeout_count: int = 0
    retry_count: int = 0
    
    # 资源指标
    memory_usage: float = 0.0
    cpu_usage: float = 0.0
    
    # 缓存指标
    cache_hits: int = 0
    cache_misses: int = 0
    cache_hit_rate: float = 0.0
    
    # 时间戳
    last_updated: datetime = field(default_factory=datetime.now)


@dataclass
class HealthCheck:
    """健康检查"""
    name: str
    description: str
    check_function: Callable[[], bool]
    interval: int = 60  # 检查间隔（秒）
    timeout: int = 10   # 超时时间（秒）
    enabled: bool = True
    last_check: Optional[datetime] = None
    last_result: Optional[bool] = None
    consecutive_failures: int = 0
    max_failures: int = 3


@dataclass
class Alert:
    """告警"""
    id: str
    level: AlertLevel
    title: str
    message: str
    source: str  # 告警源（工具名称、组件等）
    timestamp: datetime = field(default_factory=datetime.now)
    resolved: bool = False
    resolved_at: Optional[datetime] = None
    tags: Dict[str, str] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class AlertRule:
    """告警规则"""
    name: str
    description: str
    condition: str  # 告警条件表达式
    level: AlertLevel
    enabled: bool = True
    cooldown: int = 300  # 冷却时间（秒）
    last_triggered: Optional[datetime] = None
    notification_channels: List[str] = field(default_factory=list)


@dataclass
class MonitorConfig:
    """监控配置"""
    # 监控级别
    level: MonitorLevel = MonitorLevel.BASIC
    
    # 指标收集
    enable_metrics: bool = True
    metrics_interval: int = 60  # 指标收集间隔（秒）
    metrics_retention: int = 86400  # 指标保留时间（秒）
    
    # 健康检查
    enable_health_checks: bool = True
    health_check_interval: int = 30
    
    # 告警
    enable_alerts: bool = True
    alert_evaluation_interval: int = 30
    
    # 性能监控
    enable_performance_monitoring: bool = True
    performance_sample_rate: float = 1.0  # 采样率
    
    # 日志
    enable_monitoring_logs: bool = True
    log_level: str = "INFO"
    
    # 存储
    storage_backend: str = "memory"  # memory, file, database
    storage_config: Dict[str, Any] = field(default_factory=dict)


class ToolMonitorError(Exception):
    """工具监控异常"""
    pass


class MetricsCollector:
    """指标收集器"""
    
    def __init__(self, retention_seconds: int = 86400):
        """
        初始化指标收集器
        
        Args:
            retention_seconds: 指标保留时间（秒）
        """
        self.retention_seconds = retention_seconds
        self.metrics: Dict[str, deque] = defaultdict(lambda: deque())
        self.lock = threading.RLock()
    
    def record_metric(self, metric: MetricValue) -> None:
        """
        记录指标
        
        Args:
            metric: 指标值
        """
        with self.lock:
            self.metrics[metric.name].append(metric)
            self._cleanup_old_metrics(metric.name)
    
    def get_metrics(self, name: str, 
                   start_time: Optional[datetime] = None,
                   end_time: Optional[datetime] = None) -> List[MetricValue]:
        """
        获取指标
        
        Args:
            name: 指标名称
            start_time: 开始时间
            end_time: 结束时间
        
        Returns:
            List[MetricValue]: 指标列表
        """
        with self.lock:
            metrics = list(self.metrics.get(name, []))
            
            if start_time or end_time:
                filtered_metrics = []
                for metric in metrics:
                    if start_time and metric.timestamp < start_time:
                        continue
                    if end_time and metric.timestamp > end_time:
                        continue
                    filtered_metrics.append(metric)
                return filtered_metrics
            
            return metrics
    
    def get_latest_metric(self, name: str) -> Optional[MetricValue]:
        """
        获取最新指标
        
        Args:
            name: 指标名称
        
        Returns:
            Optional[MetricValue]: 最新指标
        """
        with self.lock:
            metrics = self.metrics.get(name)
            return metrics[-1] if metrics else None
    
    def get_metric_summary(self, name: str, 
                          duration_seconds: int = 3600) -> Dict[str, Any]:
        """
        获取指标摘要
        
        Args:
            name: 指标名称
            duration_seconds: 统计时长（秒）
        
        Returns:
            Dict[str, Any]: 指标摘要
        """
        end_time = datetime.now()
        start_time = end_time - timedelta(seconds=duration_seconds)
        
        metrics = self.get_metrics(name, start_time, end_time)
        if not metrics:
            return {}
        
        values = [m.value for m in metrics]
        
        return {
            'count': len(values),
            'min': min(values),
            'max': max(values),
            'avg': statistics.mean(values),
            'median': statistics.median(values),
            'sum': sum(values),
            'latest': values[-1] if values else None,
            'start_time': start_time.isoformat(),
            'end_time': end_time.isoformat()
        }
    
    def _cleanup_old_metrics(self, name: str) -> None:
        """
        清理过期指标
        
        Args:
            name: 指标名称
        """
        cutoff_time = datetime.now() - timedelta(seconds=self.retention_seconds)
        metrics = self.metrics[name]
        
        while metrics and metrics[0].timestamp < cutoff_time:
            metrics.popleft()
    
    def get_all_metric_names(self) -> List[str]:
        """
        获取所有指标名称
        
        Returns:
            List[str]: 指标名称列表
        """
        with self.lock:
            return list(self.metrics.keys())
    
    def clear_metrics(self, name: Optional[str] = None) -> None:
        """
        清除指标
        
        Args:
            name: 指标名称（可选，不提供则清除所有）
        """
        with self.lock:
            if name:
                if name in self.metrics:
                    self.metrics[name].clear()
            else:
                self.metrics.clear()


class AlertManager:
    """告警管理器"""
    
    def __init__(self):
        """
        初始化告警管理器
        """
        self.alerts: Dict[str, Alert] = {}
        self.alert_rules: Dict[str, AlertRule] = {}
        self.notification_handlers: Dict[str, Callable[[Alert], None]] = {}
        self.lock = threading.RLock()
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def add_alert_rule(self, rule: AlertRule) -> None:
        """
        添加告警规则
        
        Args:
            rule: 告警规则
        """
        with self.lock:
            self.alert_rules[rule.name] = rule
            self.logger.info(f"告警规则已添加: {rule.name}")
    
    def remove_alert_rule(self, name: str) -> None:
        """
        移除告警规则
        
        Args:
            name: 规则名称
        """
        with self.lock:
            if name in self.alert_rules:
                del self.alert_rules[name]
                self.logger.info(f"告警规则已移除: {name}")
    
    def trigger_alert(self, alert: Alert) -> None:
        """
        触发告警
        
        Args:
            alert: 告警
        """
        with self.lock:
            self.alerts[alert.id] = alert
            self.logger.warning(f"告警触发: {alert.title} - {alert.message}")
            
            # 发送通知
            self._send_notifications(alert)
    
    def resolve_alert(self, alert_id: str) -> None:
        """
        解决告警
        
        Args:
            alert_id: 告警ID
        """
        with self.lock:
            if alert_id in self.alerts:
                alert = self.alerts[alert_id]
                alert.resolved = True
                alert.resolved_at = datetime.now()
                self.logger.info(f"告警已解决: {alert.title}")
    
    def evaluate_rules(self, metrics: Dict[str, Any]) -> None:
        """
        评估告警规则
        
        Args:
            metrics: 指标数据
        """
        with self.lock:
            for rule in self.alert_rules.values():
                if not rule.enabled:
                    continue
                
                # 检查冷却时间
                if rule.last_triggered:
                    time_since_last = (datetime.now() - rule.last_triggered).seconds
                    if time_since_last < rule.cooldown:
                        continue
                
                # 评估条件
                if self._evaluate_condition(rule.condition, metrics):
                    alert = Alert(
                        id=f"{rule.name}_{int(time.time())}",
                        level=rule.level,
                        title=f"告警: {rule.name}",
                        message=rule.description,
                        source="monitor"
                    )
                    
                    self.trigger_alert(alert)
                    rule.last_triggered = datetime.now()
    
    def _evaluate_condition(self, condition: str, metrics: Dict[str, Any]) -> bool:
        """
        评估告警条件
        
        Args:
            condition: 条件表达式
            metrics: 指标数据
        
        Returns:
            bool: 是否满足条件
        """
        try:
            # 简单的条件评估（可以扩展为更复杂的表达式引擎）
            # 例如: "error_rate > 0.1" 或 "response_time > 1000"
            
            # 替换指标名称为实际值
            eval_condition = condition
            for metric_name, metric_value in metrics.items():
                eval_condition = eval_condition.replace(metric_name, str(metric_value))
            
            # 安全的表达式评估（仅支持基本运算符）
            allowed_names = {
                '__builtins__': {},
                'abs': abs,
                'min': min,
                'max': max,
                'round': round
            }
            
            return eval(eval_condition, allowed_names)
            
        except Exception as e:
            self.logger.error(f"评估告警条件失败 '{condition}': {e}")
            return False
    
    def add_notification_handler(self, channel: str, 
                               handler: Callable[[Alert], None]) -> None:
        """
        添加通知处理器
        
        Args:
            channel: 通知渠道
            handler: 处理器函数
        """
        self.notification_handlers[channel] = handler
        self.logger.info(f"通知处理器已添加: {channel}")
    
    def _send_notifications(self, alert: Alert) -> None:
        """
        发送通知
        
        Args:
            alert: 告警
        """
        # 查找相关规则的通知渠道
        channels = set()
        for rule in self.alert_rules.values():
            if rule.name in alert.source or alert.source in rule.name:
                channels.update(rule.notification_channels)
        
        # 发送通知
        for channel in channels:
            handler = self.notification_handlers.get(channel)
            if handler:
                try:
                    handler(alert)
                except Exception as e:
                    self.logger.error(f"发送通知失败 {channel}: {e}")
    
    def get_active_alerts(self) -> List[Alert]:
        """
        获取活跃告警
        
        Returns:
            List[Alert]: 活跃告警列表
        """
        with self.lock:
            return [alert for alert in self.alerts.values() if not alert.resolved]
    
    def get_all_alerts(self) -> List[Alert]:
        """
        获取所有告警
        
        Returns:
            List[Alert]: 所有告警列表
        """
        with self.lock:
            return list(self.alerts.values())


class ToolMonitor:
    """工具监控器
    
    提供工具执行监控、性能分析、健康检查和告警功能
    """
    
    def __init__(self, config: MonitorConfig):
        """
        初始化工具监控器
        
        Args:
            config: 监控配置
        """
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
        self.logger.setLevel(getattr(logging, config.log_level.upper()))
        
        # 组件
        self.metrics_collector = MetricsCollector(config.metrics_retention)
        self.alert_manager = AlertManager()
        
        # 工具组件
        self.registry = get_global_registry()
        self.executor = get_global_executor()
        
        # 性能指标
        self.performance_metrics: Dict[str, PerformanceMetrics] = defaultdict(PerformanceMetrics)
        
        # 健康检查
        self.health_checks: Dict[str, HealthCheck] = {}
        
        # 监控状态
        self.monitoring_active = False
        self.monitoring_thread: Optional[threading.Thread] = None
        
        # 锁
        self.lock = threading.RLock()
        
        # 初始化默认健康检查
        self._init_default_health_checks()
        
        # 初始化默认告警规则
        self._init_default_alert_rules()
    
    def _init_default_health_checks(self) -> None:
        """
        初始化默认健康检查
        """
        if not self.config.enable_health_checks:
            return
        
        # 注册器健康检查
        if self.registry:
            self.add_health_check(HealthCheck(
                name="registry_health",
                description="工具注册器健康检查",
                check_function=lambda: self.registry is not None,
                interval=60
            ))
        
        # 执行器健康检查
        if self.executor:
            self.add_health_check(HealthCheck(
                name="executor_health",
                description="工具执行器健康检查",
                check_function=lambda: self.executor is not None,
                interval=60
            ))
    
    def _init_default_alert_rules(self) -> None:
        """
        初始化默认告警规则
        """
        if not self.config.enable_alerts:
            return
        
        # 错误率告警
        self.alert_manager.add_alert_rule(AlertRule(
            name="high_error_rate",
            description="工具执行错误率过高",
            condition="error_rate > 0.1",
            level=AlertLevel.WARNING,
            cooldown=300
        ))
        
        # 响应时间告警
        self.alert_manager.add_alert_rule(AlertRule(
            name="slow_response",
            description="工具执行响应时间过长",
            condition="average_execution_time > 10",
            level=AlertLevel.WARNING,
            cooldown=300
        ))
        
        # 并发数告警
        self.alert_manager.add_alert_rule(AlertRule(
            name="high_concurrency",
            description="并发执行数过高",
            condition="current_concurrent_executions > 50",
            level=AlertLevel.ERROR,
            cooldown=180
        ))
    
    def start_monitoring(self) -> None:
        """
        启动监控
        """
        if self.monitoring_active:
            self.logger.warning("监控已经在运行")
            return
        
        self.monitoring_active = True
        self.monitoring_thread = threading.Thread(
            target=self._monitoring_loop,
            daemon=True
        )
        self.monitoring_thread.start()
        
        self.logger.info("工具监控已启动")
    
    def stop_monitoring(self) -> None:
        """
        停止监控
        """
        self.monitoring_active = False
        
        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=5)
        
        self.logger.info("工具监控已停止")
    
    def _monitoring_loop(self) -> None:
        """
        监控循环
        """
        last_metrics_collection = 0
        last_health_check = 0
        last_alert_evaluation = 0
        
        while self.monitoring_active:
            try:
                current_time = time.time()
                
                # 指标收集
                if (self.config.enable_metrics and 
                    current_time - last_metrics_collection >= self.config.metrics_interval):
                    self._collect_metrics()
                    last_metrics_collection = current_time
                
                # 健康检查
                if (self.config.enable_health_checks and 
                    current_time - last_health_check >= self.config.health_check_interval):
                    self._run_health_checks()
                    last_health_check = current_time
                
                # 告警评估
                if (self.config.enable_alerts and 
                    current_time - last_alert_evaluation >= self.config.alert_evaluation_interval):
                    self._evaluate_alerts()
                    last_alert_evaluation = current_time
                
                time.sleep(1)  # 避免过度占用CPU
                
            except Exception as e:
                self.logger.error(f"监控循环异常: {e}")
                time.sleep(5)  # 异常时等待更长时间
    
    def _collect_metrics(self) -> None:
        """
        收集指标
        """
        try:
            # 收集执行器指标
            if self.executor:
                executor_stats = self.executor.get_stats()
                
                # 记录基础指标
                self.record_metric(MetricValue(
                    name="total_executions",
                    value=executor_stats.get('total_executions', 0),
                    metric_type=MetricType.COUNTER
                ))
                
                self.record_metric(MetricValue(
                    name="active_executions",
                    value=executor_stats.get('active_executions', 0),
                    metric_type=MetricType.GAUGE
                ))
                
                self.record_metric(MetricValue(
                    name="queue_size",
                    value=executor_stats.get('queue_size', 0),
                    metric_type=MetricType.GAUGE
                ))
            
            # 收集注册器指标
            if self.registry:
                registry_stats = self.registry.get_stats()
                
                self.record_metric(MetricValue(
                    name="registered_tools",
                    value=registry_stats.total_tools,
                    metric_type=MetricType.GAUGE
                ))
                
                self.record_metric(MetricValue(
                    name="active_tools",
                    value=registry_stats.active_tools,
                    metric_type=MetricType.GAUGE
                ))
            
            # 收集系统资源指标
            self._collect_system_metrics()
            
        except Exception as e:
            self.logger.error(f"收集指标失败: {e}")
    
    def _collect_system_metrics(self) -> None:
        """
        收集系统资源指标
        """
        try:
            import psutil
            
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            self.record_metric(MetricValue(
                name="cpu_usage",
                value=cpu_percent,
                metric_type=MetricType.GAUGE
            ))
            
            # 内存使用率
            memory = psutil.virtual_memory()
            self.record_metric(MetricValue(
                name="memory_usage",
                value=memory.percent,
                metric_type=MetricType.GAUGE
            ))
            
            # 磁盘使用率
            disk = psutil.disk_usage('/')
            self.record_metric(MetricValue(
                name="disk_usage",
                value=disk.percent,
                metric_type=MetricType.GAUGE
            ))
            
        except ImportError:
            # psutil未安装，跳过系统指标收集
            pass
        except Exception as e:
            self.logger.error(f"收集系统指标失败: {e}")
    
    def _run_health_checks(self) -> None:
        """
        运行健康检查
        """
        for check in self.health_checks.values():
            if not check.enabled:
                continue
            
            # 检查间隔
            if check.last_check:
                time_since_last = (datetime.now() - check.last_check).seconds
                if time_since_last < check.interval:
                    continue
            
            try:
                # 执行健康检查
                result = check.check_function()
                check.last_check = datetime.now()
                check.last_result = result
                
                if result:
                    check.consecutive_failures = 0
                else:
                    check.consecutive_failures += 1
                    
                    # 触发告警
                    if check.consecutive_failures >= check.max_failures:
                        alert = Alert(
                            id=f"health_check_{check.name}_{int(time.time())}",
                            level=AlertLevel.ERROR,
                            title=f"健康检查失败: {check.name}",
                            message=f"健康检查 '{check.description}' 连续失败 {check.consecutive_failures} 次",
                            source=f"health_check:{check.name}"
                        )
                        self.alert_manager.trigger_alert(alert)
                
                # 记录健康检查指标
                self.record_metric(MetricValue(
                    name=f"health_check_{check.name}",
                    value=1 if result else 0,
                    metric_type=MetricType.GAUGE,
                    tags={'check_name': check.name}
                ))
                
            except Exception as e:
                self.logger.error(f"健康检查异常 {check.name}: {e}")
                check.consecutive_failures += 1
    
    def _evaluate_alerts(self) -> None:
        """
        评估告警
        """
        try:
            # 收集当前指标值
            metrics = {}
            
            # 获取最新的性能指标
            for tool_name, perf_metrics in self.performance_metrics.items():
                metrics.update({
                    f"{tool_name}_error_rate": perf_metrics.error_rate,
                    f"{tool_name}_average_execution_time": perf_metrics.average_execution_time,
                    f"{tool_name}_current_concurrent_executions": perf_metrics.current_concurrent_executions
                })
            
            # 获取系统指标
            cpu_metric = self.metrics_collector.get_latest_metric("cpu_usage")
            if cpu_metric:
                metrics['cpu_usage'] = cpu_metric.value
            
            memory_metric = self.metrics_collector.get_latest_metric("memory_usage")
            if memory_metric:
                metrics['memory_usage'] = memory_metric.value
            
            # 评估告警规则
            self.alert_manager.evaluate_rules(metrics)
            
        except Exception as e:
            self.logger.error(f"评估告警失败: {e}")
    
    def record_metric(self, metric: MetricValue) -> None:
        """
        记录指标
        
        Args:
            metric: 指标值
        """
        if self.config.enable_metrics:
            self.metrics_collector.record_metric(metric)
    
    def record_tool_execution(self, tool_name: str, result: ExecutionResult) -> None:
        """
        记录工具执行
        
        Args:
            tool_name: 工具名称
            result: 执行结果
        """
        if not self.config.enable_performance_monitoring:
            return
        
        # 采样控制
        if self.config.performance_sample_rate < 1.0:
            import random
            if random.random() > self.config.performance_sample_rate:
                return
        
        with self.lock:
            metrics = self.performance_metrics[tool_name]
            
            # 更新执行计数
            metrics.total_executions += 1
            
            if result.state == ExecutionState.COMPLETED:
                metrics.successful_executions += 1
            else:
                metrics.failed_executions += 1
            
            # 更新时间指标
            if result.duration:
                metrics.total_execution_time += result.duration
                metrics.average_execution_time = (
                    metrics.total_execution_time / metrics.total_executions
                )
                metrics.min_execution_time = min(
                    metrics.min_execution_time, result.duration
                )
                metrics.max_execution_time = max(
                    metrics.max_execution_time, result.duration
                )
            
            # 更新错误率
            if metrics.total_executions > 0:
                metrics.error_rate = (
                    metrics.failed_executions / metrics.total_executions
                )
            
            # 更新重试计数
            if result.attempt_count > 1:
                metrics.retry_count += result.attempt_count - 1
            
            # 更新缓存指标
            if result.from_cache:
                metrics.cache_hits += 1
            else:
                metrics.cache_misses += 1
            
            total_cache_requests = metrics.cache_hits + metrics.cache_misses
            if total_cache_requests > 0:
                metrics.cache_hit_rate = metrics.cache_hits / total_cache_requests
            
            metrics.last_updated = datetime.now()
            
            # 记录指标到收集器
            self.record_metric(MetricValue(
                name=f"{tool_name}_execution_time",
                value=result.duration or 0,
                metric_type=MetricType.TIMER,
                tags={'tool_name': tool_name, 'state': result.state.value}
            ))
            
            self.record_metric(MetricValue(
                name=f"{tool_name}_execution_count",
                value=1,
                metric_type=MetricType.COUNTER,
                tags={'tool_name': tool_name, 'state': result.state.value}
            ))
    
    def add_health_check(self, health_check: HealthCheck) -> None:
        """
        添加健康检查
        
        Args:
            health_check: 健康检查
        """
        self.health_checks[health_check.name] = health_check
        self.logger.info(f"健康检查已添加: {health_check.name}")
    
    def remove_health_check(self, name: str) -> None:
        """
        移除健康检查
        
        Args:
            name: 健康检查名称
        """
        if name in self.health_checks:
            del self.health_checks[name]
            self.logger.info(f"健康检查已移除: {name}")
    
    def get_health_status(self) -> Dict[str, Any]:
        """
        获取健康状态
        
        Returns:
            Dict[str, Any]: 健康状态
        """
        overall_status = HealthStatus.HEALTHY
        check_results = {}
        
        for name, check in self.health_checks.items():
            if check.last_result is None:
                status = HealthStatus.UNKNOWN
            elif check.last_result:
                status = HealthStatus.HEALTHY
            elif check.consecutive_failures < check.max_failures:
                status = HealthStatus.DEGRADED
            else:
                status = HealthStatus.UNHEALTHY
            
            check_results[name] = {
                'status': status.value,
                'last_check': check.last_check.isoformat() if check.last_check else None,
                'consecutive_failures': check.consecutive_failures,
                'description': check.description
            }
            
            # 更新整体状态
            if status == HealthStatus.UNHEALTHY:
                overall_status = HealthStatus.UNHEALTHY
            elif status == HealthStatus.DEGRADED and overall_status == HealthStatus.HEALTHY:
                overall_status = HealthStatus.DEGRADED
        
        return {
            'overall_status': overall_status.value,
            'checks': check_results,
            'timestamp': datetime.now().isoformat()
        }
    
    def get_performance_metrics(self, tool_name: Optional[str] = None) -> Dict[str, Any]:
        """
        获取性能指标
        
        Args:
            tool_name: 工具名称（可选）
        
        Returns:
            Dict[str, Any]: 性能指标
        """
        with self.lock:
            if tool_name:
                metrics = self.performance_metrics.get(tool_name)
                return asdict(metrics) if metrics else {}
            else:
                return {
                    name: asdict(metrics)
                    for name, metrics in self.performance_metrics.items()
                }
    
    def get_metrics_summary(self, duration_hours: int = 1) -> Dict[str, Any]:
        """
        获取指标摘要
        
        Args:
            duration_hours: 统计时长（小时）
        
        Returns:
            Dict[str, Any]: 指标摘要
        """
        duration_seconds = duration_hours * 3600
        summary = {}
        
        for metric_name in self.metrics_collector.get_all_metric_names():
            summary[metric_name] = self.metrics_collector.get_metric_summary(
                metric_name, duration_seconds
            )
        
        return summary
    
    def get_alerts(self, active_only: bool = True) -> List[Dict[str, Any]]:
        """
        获取告警
        
        Args:
            active_only: 是否只返回活跃告警
        
        Returns:
            List[Dict[str, Any]]: 告警列表
        """
        if active_only:
            alerts = self.alert_manager.get_active_alerts()
        else:
            alerts = self.alert_manager.get_all_alerts()
        
        return [asdict(alert) for alert in alerts]
    
    def add_notification_handler(self, channel: str, 
                               handler: Callable[[Alert], None]) -> None:
        """
        添加通知处理器
        
        Args:
            channel: 通知渠道
            handler: 处理器函数
        """
        self.alert_manager.add_notification_handler(channel, handler)
    
    def get_monitoring_stats(self) -> Dict[str, Any]:
        """
        获取监控统计信息
        
        Returns:
            Dict[str, Any]: 监控统计
        """
        return {
            'monitoring_active': self.monitoring_active,
            'config': asdict(self.config),
            'metrics_count': len(self.metrics_collector.get_all_metric_names()),
            'health_checks_count': len(self.health_checks),
            'alert_rules_count': len(self.alert_manager.alert_rules),
            'active_alerts_count': len(self.alert_manager.get_active_alerts()),
            'performance_metrics_count': len(self.performance_metrics)
        }


# 全局监控器实例
_global_monitor: Optional[ToolMonitor] = None


def get_global_monitor() -> Optional[ToolMonitor]:
    """
    获取全局监控器实例
    
    Returns:
        Optional[ToolMonitor]: 监控器实例
    """
    return _global_monitor


def set_global_monitor(monitor: ToolMonitor) -> None:
    """
    设置全局监控器实例
    
    Args:
        monitor: 监控器实例
    """
    global _global_monitor
    _global_monitor = monitor