# -*- coding: utf-8 -*-
"""
A2A多智能体系统消息API接口

提供消息发送、接收、管理和流式处理功能
"""

from datetime import datetime
from typing import Dict, Any, Optional, List
from fastapi import APIRouter, Depends, HTTPException, status, Request, Query, WebSocket, WebSocketDisconnect
from fastapi.security import HTT<PERSON><PERSON>earer, HTTPAuthorizationCredentials
from fastapi.responses import StreamingResponse
from pydantic import BaseModel, Field, validator
from loguru import logger
import json
import asyncio

from app.services.message_service import MessageService
from app.services.auth_service import AuthService
from app.core.config import get_settings
from app.core.rate_limiter import RateLimiter
from app.core.security import get_client_ip


# 创建路由器
router = APIRouter(prefix="/messages", tags=["消息管理"])

# 安全依赖
security = HTTPBearer()

# 服务实例
message_service = MessageService()
auth_service = AuthService()
settings = get_settings()
rate_limiter = RateLimiter()


# 请求模型
class MessageCreateRequest(BaseModel):
    """消息创建请求模型"""
    session_id: int = Field(..., description="会话ID")
    content: str = Field(..., min_length=1, description="消息内容")
    message_type: str = Field("text", description="消息类型")
    metadata: Optional[Dict[str, Any]] = Field(None, description="消息元数据")
    reply_to_id: Optional[int] = Field(None, description="回复的消息ID")
    agent_id: Optional[int] = Field(None, description="发送消息的智能体ID")
    
    @validator('message_type')
    def validate_message_type(cls, v):
        """验证消息类型"""
        allowed_types = ['text', 'image', 'file', 'code', 'system', 'error', 'audio', 'video']
        if v not in allowed_types:
            raise ValueError(f'消息类型必须是: {", ".join(allowed_types)}')
        return v


class MessageUpdateRequest(BaseModel):
    """消息更新请求模型"""
    content: Optional[str] = Field(None, min_length=1, description="消息内容")
    metadata: Optional[Dict[str, Any]] = Field(None, description="消息元数据")
    is_edited: bool = Field(True, description="是否标记为已编辑")


class MessageReactionRequest(BaseModel):
    """消息反应请求模型"""
    reaction_type: str = Field(..., description="反应类型")
    
    @validator('reaction_type')
    def validate_reaction_type(cls, v):
        """验证反应类型"""
        allowed_reactions = ['like', 'dislike', 'love', 'laugh', 'angry', 'sad', 'wow']
        if v not in allowed_reactions:
            raise ValueError(f'反应类型必须是: {", ".join(allowed_reactions)}')
        return v


class MessageSearchRequest(BaseModel):
    """消息搜索请求模型"""
    query: str = Field(..., min_length=1, description="搜索关键词")
    session_ids: Optional[List[int]] = Field(None, description="会话ID列表")
    message_types: Optional[List[str]] = Field(None, description="消息类型列表")
    sender_ids: Optional[List[int]] = Field(None, description="发送者ID列表")
    agent_ids: Optional[List[int]] = Field(None, description="智能体ID列表")
    start_date: Optional[datetime] = Field(None, description="开始日期")
    end_date: Optional[datetime] = Field(None, description="结束日期")
    include_metadata: bool = Field(False, description="是否包含元数据搜索")


# 响应模型
class MessageResponse(BaseModel):
    """消息响应模型"""
    message_id: int = Field(..., description="消息ID")
    session_id: int = Field(..., description="会话ID")
    sender_id: Optional[int] = Field(None, description="发送者用户ID")
    agent_id: Optional[int] = Field(None, description="发送者智能体ID")
    content: str = Field(..., description="消息内容")
    message_type: str = Field(..., description="消息类型")
    metadata: Optional[Dict[str, Any]] = Field(None, description="消息元数据")
    reply_to_id: Optional[int] = Field(None, description="回复的消息ID")
    is_edited: bool = Field(..., description="是否已编辑")
    edit_count: int = Field(..., description="编辑次数")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: Optional[datetime] = Field(None, description="更新时间")
    reactions: Optional[List[Dict[str, Any]]] = Field(None, description="反应列表")
    reply_count: int = Field(0, description="回复数量")


class MessageStatisticsResponse(BaseModel):
    """消息统计响应模型"""
    total_messages: int = Field(..., description="总消息数")
    messages_by_type: Dict[str, int] = Field(..., description="按类型分组的消息数")
    messages_by_date: Dict[str, int] = Field(..., description="按日期分组的消息数")
    top_senders: List[Dict[str, Any]] = Field(..., description="活跃发送者")
    average_message_length: float = Field(..., description="平均消息长度")
    reaction_statistics: Dict[str, int] = Field(..., description="反应统计")


class ApiResponse(BaseModel):
    """API响应模型"""
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="响应消息")
    data: Optional[Dict[str, Any]] = Field(None, description="响应数据")


# 依赖函数
async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """获取当前用户"""
    try:
        token = credentials.credentials
        user_info = await auth_service.verify_token(token)
        return user_info
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的访问令牌",
            headers={"WWW-Authenticate": "Bearer"}
        )


async def check_message_permission(message_id: int, user_id: int, permission: str = "read"):
    """检查消息权限"""
    has_permission = await auth_service.check_resource_permission(
        user_id, "message", str(message_id), permission
    )
    if not has_permission:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=f"没有对消息{message_id}的{permission}权限"
        )


async def check_session_permission(session_id: int, user_id: int, permission: str = "read"):
    """检查会话权限"""
    has_permission = await auth_service.check_resource_permission(
        user_id, "session", str(session_id), permission
    )
    if not has_permission:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=f"没有对会话{session_id}的{permission}权限"
        )


# API接口
@router.post("/", response_model=MessageResponse, summary="发送消息")
async def send_message(
    request: MessageCreateRequest,
    http_request: Request,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    发送消息
    
    - **session_id**: 会话ID
    - **content**: 消息内容
    - **message_type**: 消息类型（text/image/file/code/system/error/audio/video）
    - **metadata**: 消息元数据（可选）
    - **reply_to_id**: 回复的消息ID（可选）
    - **agent_id**: 发送消息的智能体ID（可选）
    
    需要对会话有写入权限
    """
    try:
        # 获取客户端IP
        client_ip = get_client_ip(http_request)
        
        # 检查会话权限
        await check_session_permission(request.session_id, current_user["user_id"], "write")
        
        # 速率限制检查
        await rate_limiter.check_rate_limit(
            key=f"message_send:{current_user['user_id']}",
            limit=300,  # 每小时最多发送300条消息
            window=3600
        )
        
        # 准备消息数据
        message_data = {
            "session_id": request.session_id,
            "sender_id": current_user["user_id"],
            "agent_id": request.agent_id,
            "content": request.content,
            "message_type": request.message_type,
            "metadata": request.metadata or {},
            "reply_to_id": request.reply_to_id
        }
        
        # 发送消息
        result = await message_service.send_message(
            message_data, current_user["user_id"], client_ip
        )
        
        if result["success"]:
            message = result["data"]
            
            logger.info(f"消息发送成功", extra={
                "message_id": message["message_id"],
                "session_id": request.session_id,
                "message_type": message["message_type"],
                "sender_id": current_user["user_id"],
                "client_ip": client_ip
            })
            
            return MessageResponse(**message)
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result["message"]
            )
            
    except ValueError as e:
        logger.warning(f"消息发送失败: {str(e)}", extra={
            "session_id": request.session_id,
            "user_id": current_user["user_id"]
        })
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"消息发送异常: {str(e)}", extra={
            "session_id": request.session_id,
            "user_id": current_user["user_id"]
        })
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="消息发送失败，请稍后重试"
        )


@router.get("/{message_id}", response_model=MessageResponse, summary="获取消息")
async def get_message(
    message_id: int,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    获取指定消息的详细信息
    
    - **message_id**: 消息ID
    
    需要对消息有读取权限
    """
    try:
        # 检查权限
        await check_message_permission(message_id, current_user["user_id"], "read")
        
        # 获取消息信息
        message = await message_service.get_message(message_id, current_user["user_id"])
        
        if message:
            return MessageResponse(**message)
        else:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="消息不存在"
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取消息信息异常: {str(e)}", extra={
            "message_id": message_id,
            "user_id": current_user["user_id"]
        })
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取消息信息失败"
        )


@router.put("/{message_id}", response_model=MessageResponse, summary="更新消息")
async def update_message(
    message_id: int,
    request: MessageUpdateRequest,
    http_request: Request,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    更新消息内容
    
    - **message_id**: 消息ID
    - **content**: 消息内容（可选）
    - **metadata**: 消息元数据（可选）
    - **is_edited**: 是否标记为已编辑（默认true）
    
    需要对消息有写入权限
    """
    try:
        # 获取客户端IP
        client_ip = get_client_ip(http_request)
        
        # 检查权限
        await check_message_permission(message_id, current_user["user_id"], "write")
        
        # 准备更新数据
        update_data = {}
        if request.content is not None:
            update_data["content"] = request.content
        if request.metadata is not None:
            update_data["metadata"] = request.metadata
        if request.is_edited is not None:
            update_data["is_edited"] = request.is_edited
        
        # 更新消息
        result = await message_service.update_message(
            message_id, update_data, current_user["user_id"], client_ip
        )
        
        if result["success"]:
            message = result["data"]
            
            logger.info(f"消息更新成功", extra={
                "message_id": message_id,
                "updated_fields": list(update_data.keys()),
                "user_id": current_user["user_id"],
                "client_ip": client_ip
            })
            
            return MessageResponse(**message)
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result["message"]
            )
            
    except ValueError as e:
        logger.warning(f"消息更新失败: {str(e)}", extra={
            "message_id": message_id,
            "user_id": current_user["user_id"]
        })
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"消息更新异常: {str(e)}", extra={
            "message_id": message_id,
            "user_id": current_user["user_id"]
        })
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="消息更新失败，请稍后重试"
        )


@router.delete("/{message_id}", response_model=ApiResponse, summary="删除消息")
async def delete_message(
    message_id: int,
    hard_delete: bool = Query(False, description="是否硬删除"),
    http_request: Request = None,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    删除消息
    
    - **message_id**: 消息ID
    - **hard_delete**: 是否硬删除（默认false，软删除）
    
    需要对消息有删除权限
    """
    try:
        # 获取客户端IP
        client_ip = get_client_ip(http_request)
        
        # 检查权限
        await check_message_permission(message_id, current_user["user_id"], "delete")
        
        # 删除消息
        result = await message_service.delete_message(
            message_id, hard_delete, current_user["user_id"], client_ip
        )
        
        if result["success"]:
            logger.info(f"消息删除成功", extra={
                "message_id": message_id,
                "hard_delete": hard_delete,
                "user_id": current_user["user_id"],
                "client_ip": client_ip
            })
            
            return ApiResponse(
                success=True,
                message=result["message"],
                data={
                    "message_id": message_id,
                    "hard_delete": hard_delete,
                    "deleted_at": datetime.utcnow().isoformat()
                }
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result["message"]
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"消息删除异常: {str(e)}", extra={
            "message_id": message_id,
            "user_id": current_user["user_id"]
        })
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="消息删除失败，请稍后重试"
        )


@router.post("/{message_id}/reactions", response_model=ApiResponse, summary="添加消息反应")
async def add_reaction(
    message_id: int,
    request: MessageReactionRequest,
    http_request: Request,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    为消息添加反应
    
    - **message_id**: 消息ID
    - **reaction_type**: 反应类型（like/dislike/love/laugh/angry/sad/wow）
    
    需要对消息有读取权限
    """
    try:
        # 获取客户端IP
        client_ip = get_client_ip(http_request)
        
        # 检查权限
        await check_message_permission(message_id, current_user["user_id"], "read")
        
        # 添加反应
        result = await message_service.add_reaction(
            message_id, current_user["user_id"], request.reaction_type, client_ip
        )
        
        if result["success"]:
            logger.info(f"消息反应添加成功", extra={
                "message_id": message_id,
                "reaction_type": request.reaction_type,
                "user_id": current_user["user_id"],
                "client_ip": client_ip
            })
            
            return ApiResponse(
                success=True,
                message="反应添加成功",
                data={
                    "message_id": message_id,
                    "reaction_type": request.reaction_type,
                    "user_id": current_user["user_id"],
                    "added_at": datetime.utcnow().isoformat()
                }
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result["message"]
            )
            
    except ValueError as e:
        logger.warning(f"消息反应添加失败: {str(e)}", extra={
            "message_id": message_id,
            "user_id": current_user["user_id"]
        })
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"消息反应添加异常: {str(e)}", extra={
            "message_id": message_id,
            "user_id": current_user["user_id"]
        })
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="反应添加失败，请稍后重试"
        )


@router.delete("/{message_id}/reactions/{reaction_type}", response_model=ApiResponse, summary="移除消息反应")
async def remove_reaction(
    message_id: int,
    reaction_type: str,
    http_request: Request,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    移除消息反应
    
    - **message_id**: 消息ID
    - **reaction_type**: 反应类型
    
    需要对消息有读取权限
    """
    try:
        # 获取客户端IP
        client_ip = get_client_ip(http_request)
        
        # 检查权限
        await check_message_permission(message_id, current_user["user_id"], "read")
        
        # 移除反应
        result = await message_service.remove_reaction(
            message_id, current_user["user_id"], reaction_type, client_ip
        )
        
        if result["success"]:
            logger.info(f"消息反应移除成功", extra={
                "message_id": message_id,
                "reaction_type": reaction_type,
                "user_id": current_user["user_id"],
                "client_ip": client_ip
            })
            
            return ApiResponse(
                success=True,
                message="反应移除成功",
                data={
                    "message_id": message_id,
                    "reaction_type": reaction_type,
                    "user_id": current_user["user_id"],
                    "removed_at": datetime.utcnow().isoformat()
                }
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result["message"]
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"消息反应移除异常: {str(e)}", extra={
            "message_id": message_id,
            "user_id": current_user["user_id"]
        })
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="反应移除失败，请稍后重试"
        )


@router.post("/search", response_model=Dict[str, Any], summary="搜索消息")
async def search_messages(
    request: MessageSearchRequest,
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="页面大小"),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    搜索消息
    
    - **query**: 搜索关键词
    - **session_ids**: 会话ID列表（可选）
    - **message_types**: 消息类型列表（可选）
    - **sender_ids**: 发送者ID列表（可选）
    - **agent_ids**: 智能体ID列表（可选）
    - **start_date**: 开始日期（可选）
    - **end_date**: 结束日期（可选）
    - **include_metadata**: 是否包含元数据搜索（可选）
    - **page**: 页码（默认1）
    - **page_size**: 页面大小（默认20，最大100）
    
    返回用户有权限访问的消息搜索结果
    """
    try:
        # 构建搜索条件
        search_params = {
            "query": request.query,
            "session_ids": request.session_ids,
            "message_types": request.message_types,
            "sender_ids": request.sender_ids,
            "agent_ids": request.agent_ids,
            "start_date": request.start_date.isoformat() if request.start_date else None,
            "end_date": request.end_date.isoformat() if request.end_date else None,
            "include_metadata": request.include_metadata
        }
        
        # 搜索消息
        result = await message_service.search_messages(
            search_params, current_user["user_id"], page, page_size
        )
        
        return {
            "success": True,
            "message": "消息搜索成功",
            "data": result
        }
        
    except Exception as e:
        logger.error(f"消息搜索异常: {str(e)}", extra={
            "user_id": current_user["user_id"],
            "query": request.query
        })
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="消息搜索失败"
        )


@router.get("/statistics", response_model=MessageStatisticsResponse, summary="获取消息统计")
async def get_message_statistics(
    session_ids: Optional[str] = Query(None, description="会话ID列表（逗号分隔）"),
    start_date: Optional[datetime] = Query(None, description="开始日期"),
    end_date: Optional[datetime] = Query(None, description="结束日期"),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    获取消息统计信息
    
    - **session_ids**: 会话ID列表（逗号分隔，可选）
    - **start_date**: 开始日期（可选）
    - **end_date**: 结束日期（可选）
    
    返回用户有权限访问的消息统计数据
    """
    try:
        # 解析会话ID列表
        session_id_list = None
        if session_ids:
            session_id_list = [int(sid.strip()) for sid in session_ids.split(",")]
        
        # 构建过滤条件
        filters = {
            "session_ids": session_id_list,
            "start_date": start_date.isoformat() if start_date else None,
            "end_date": end_date.isoformat() if end_date else None
        }
        
        # 获取统计信息
        statistics = await message_service.get_message_statistics(
            current_user["user_id"], filters
        )
        
        return MessageStatisticsResponse(**statistics)
        
    except Exception as e:
        logger.error(f"获取消息统计异常: {str(e)}", extra={
            "user_id": current_user["user_id"]
        })
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取消息统计失败"
        )


@router.get("/stream/{session_id}", summary="流式获取消息")
async def stream_messages(
    session_id: int,
    since: Optional[datetime] = Query(None, description="起始时间"),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    流式获取会话消息
    
    - **session_id**: 会话ID
    - **since**: 起始时间（可选，默认获取所有消息）
    
    返回Server-Sent Events格式的消息流
    """
    try:
        # 检查会话权限
        await check_session_permission(session_id, current_user["user_id"], "read")
        
        async def generate_messages():
            """生成消息流"""
            try:
                async for message_data in message_service.stream_messages(
                    session_id, current_user["user_id"], since
                ):
                    yield f"data: {json.dumps(message_data)}\n\n"
                    await asyncio.sleep(0.1)  # 避免过于频繁的推送
            except Exception as e:
                logger.error(f"消息流生成异常: {str(e)}", extra={
                    "session_id": session_id,
                    "user_id": current_user["user_id"]
                })
                yield f"event: error\ndata: {{\"error\": \"消息流异常\", \"message\": \"{str(e)}\"}}\n\n"
        
        return StreamingResponse(
            generate_messages(),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Headers": "Cache-Control"
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"消息流异常: {str(e)}", extra={
            "session_id": session_id,
            "user_id": current_user["user_id"]
        })
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="消息流获取失败"
        )


# WebSocket接口
@router.websocket("/ws/{session_id}")
async def websocket_endpoint(
    websocket: WebSocket,
    session_id: int,
    token: str = Query(..., description="访问令牌")
):
    """
    消息WebSocket连接
    
    - **session_id**: 会话ID
    - **token**: 访问令牌
    
    提供实时消息推送和接收
    """
    try:
        # 验证令牌
        user_info = await auth_service.verify_token(token)
        
        # 检查会话权限
        await check_session_permission(session_id, user_info["user_id"], "read")
        
        # 建立连接
        await websocket.accept()
        
        logger.info(f"消息WebSocket连接建立", extra={
            "session_id": session_id,
            "user_id": user_info["user_id"]
        })
        
        try:
            while True:
                # 接收客户端消息
                data = await websocket.receive_text()
                message_data = json.loads(data)
                
                # 处理不同类型的消息
                if message_data.get("type") == "ping":
                    await websocket.send_text(json.dumps({
                        "type": "pong",
                        "timestamp": datetime.utcnow().isoformat()
                    }))
                elif message_data.get("type") == "message":
                    # 处理新消息
                    content = message_data.get("content")
                    if content:
                        # 发送消息到服务
                        msg_data = {
                            "session_id": session_id,
                            "sender_id": user_info["user_id"],
                            "content": content,
                            "message_type": message_data.get("message_type", "text"),
                            "metadata": message_data.get("metadata", {})
                        }
                        
                        result = await message_service.send_message(
                            msg_data, user_info["user_id"], "websocket"
                        )
                        
                        if result["success"]:
                            # 广播消息给所有连接的客户端
                            await websocket.send_text(json.dumps({
                                "type": "message_sent",
                                "message": result["data"],
                                "timestamp": datetime.utcnow().isoformat()
                            }))
                        else:
                            await websocket.send_text(json.dumps({
                                "type": "error",
                                "message": result["message"],
                                "timestamp": datetime.utcnow().isoformat()
                            }))
                
        except WebSocketDisconnect:
            logger.info(f"消息WebSocket连接断开", extra={
                "session_id": session_id,
                "user_id": user_info["user_id"]
            })
            
    except Exception as e:
        logger.error(f"消息WebSocket连接异常: {str(e)}", extra={
            "session_id": session_id
        })
        await websocket.close(code=1000)