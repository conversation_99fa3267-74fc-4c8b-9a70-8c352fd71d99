# A2A 多智能体系统 API 文档

## 概述

本文档详细描述了 A2A 多智能体系统的所有 API 接口，包括路径、参数、描述和返回结果格式。系统基于 FastAPI 框架构建，支持 RESTful API 和 WebSocket 实时通信。

## 基础信息

- **基础URL**: `http://localhost:8000`
- **API版本**: v1
- **认证方式**: JWT Bearer Token
- **数据格式**: JSON
- **字符编码**: UTF-8

## 认证说明

大部分API需要在请求头中包含认证令牌：
```
Authorization: Bearer <your_jwt_token>
```

## API 接口列表

### 1. 认证管理 API (`/api/v1/auth`)

#### 1.1 用户注册
- **路径**: `POST /api/v1/auth/register`
- **描述**: 注册新用户账户
- **认证**: 无需认证
- **请求参数**:
```json
{
  "username": "string",
  "email": "string",
  "password": "string",
  "full_name": "string"
}
```
- **返回结果**:
```json
{
  "success": true,
  "message": "用户注册成功",
  "data": {
    "user_id": "uuid",
    "username": "string",
    "email": "string",
    "created_at": "datetime"
  }
}
```

#### 1.2 用户登录
- **路径**: `POST /api/v1/auth/login`
- **描述**: 用户登录获取访问令牌
- **认证**: 无需认证
- **请求参数**:
```json
{
  "username": "string",
  "password": "string"
}
```
- **返回结果**:
```json
{
  "success": true,
  "message": "登录成功",
  "data": {
    "access_token": "string",
    "refresh_token": "string",
    "token_type": "bearer",
    "expires_in": 3600,
    "user_info": {
      "user_id": "uuid",
      "username": "string",
      "email": "string",
      "role": "string"
    }
  }
}
```

#### 1.3 刷新令牌
- **路径**: `POST /api/v1/auth/refresh`
- **描述**: 使用刷新令牌获取新的访问令牌
- **认证**: 需要刷新令牌
- **请求参数**:
```json
{
  "refresh_token": "string"
}
```
- **返回结果**:
```json
{
  "success": true,
  "message": "令牌刷新成功",
  "data": {
    "access_token": "string",
    "expires_in": 3600
  }
}
```

#### 1.4 用户登出
- **路径**: `POST /api/v1/auth/logout`
- **描述**: 用户登出，使令牌失效
- **认证**: 需要认证
- **请求参数**: 无
- **返回结果**:
```json
{
  "success": true,
  "message": "登出成功"
}
```

#### 1.5 密码重置
- **路径**: `POST /api/v1/auth/reset-password`
- **描述**: 重置用户密码
- **认证**: 无需认证
- **请求参数**:
```json
{
  "email": "string",
  "new_password": "string",
  "reset_token": "string"
}
```
- **返回结果**:
```json
{
  "success": true,
  "message": "密码重置成功"
}
```

### 2. 用户管理 API (`/api/v1/users`)

#### 2.1 获取用户信息
- **路径**: `GET /api/v1/users/me`
- **描述**: 获取当前用户信息
- **认证**: 需要认证
- **请求参数**: 无
- **返回结果**:
```json
{
  "success": true,
  "data": {
    "user_id": "uuid",
    "username": "string",
    "email": "string",
    "full_name": "string",
    "role": "string",
    "status": "active",
    "created_at": "datetime",
    "updated_at": "datetime"
  }
}
```

#### 2.2 更新用户信息
- **路径**: `PUT /api/v1/users/me`
- **描述**: 更新当前用户信息
- **认证**: 需要认证
- **请求参数**:
```json
{
  "full_name": "string",
  "email": "string"
}
```
- **返回结果**:
```json
{
  "success": true,
  "message": "用户信息更新成功",
  "data": {
    "user_id": "uuid",
    "username": "string",
    "email": "string",
    "full_name": "string",
    "updated_at": "datetime"
  }
}
```

#### 2.3 获取用户权限
- **路径**: `GET /api/v1/users/me/permissions`
- **描述**: 获取当前用户权限列表
- **认证**: 需要认证
- **请求参数**: 无
- **返回结果**:
```json
{
  "success": true,
  "data": {
    "permissions": [
      {
        "permission_id": "uuid",
        "permission_name": "string",
        "resource_type": "string",
        "actions": ["read", "write", "delete"]
      }
    ]
  }
}
```

#### 2.4 获取用户操作日志
- **路径**: `GET /api/v1/users/me/logs`
- **描述**: 获取当前用户操作日志
- **认证**: 需要认证
- **查询参数**:
  - `page`: 页码 (默认: 1)
  - `size`: 每页数量 (默认: 20)
  - `start_date`: 开始日期
  - `end_date`: 结束日期
- **返回结果**:
```json
{
  "success": true,
  "data": {
    "logs": [
      {
        "log_id": "uuid",
        "action": "string",
        "resource_type": "string",
        "resource_id": "string",
        "details": {},
        "timestamp": "datetime"
      }
    ],
    "pagination": {
      "page": 1,
      "size": 20,
      "total": 100,
      "pages": 5
    }
  }
}
```

### 3. 智能体管理 API (`/api/v1/agents`)

#### 3.1 获取智能体列表
- **路径**: `GET /api/v1/agents`
- **描述**: 获取用户可访问的智能体列表
- **认证**: 需要认证
- **查询参数**:
  - `page`: 页码 (默认: 1)
  - `size`: 每页数量 (默认: 20)
  - `status`: 状态过滤 (active/inactive)
  - `type`: 类型过滤
- **返回结果**:
```json
{
  "success": true,
  "data": {
    "agents": [
      {
        "agent_id": "uuid",
        "name": "string",
  "description": "string",
  "tags": ["string"],
  "metadata": {}
}
```
- **返回结果**:
```json
{
  "success": true,
  "message": "工件信息更新成功",
  "data": {
    "artifact_id": "uuid",
    "name": "string",
    "updated_at": "datetime"
  }
}
```

#### 10.6 删除工件
- **路径**: `DELETE /api/v1/artifacts/{artifact_id}`
- **描述**: 删除指定工件
- **认证**: 需要认证
- **路径参数**:
  - `artifact_id`: 工件ID
- **返回结果**:
```json
{
  "success": true,
  "message": "工件删除成功"
}
```

#### 10.7 获取工件版本列表
- **路径**: `GET /api/v1/artifacts/{artifact_id}/versions`
- **描述**: 获取工件的版本列表
- **认证**: 需要认证
- **路径参数**:
  - `artifact_id`: 工件ID
- **返回结果**:
```json
{
  "success": true,
  "data": {
    "versions": [
      {
        "version_id": "uuid",
        "version": "1.0.0",
        "file_size": 1024,
        "created_by": "uuid",
        "created_at": "datetime",
        "changes": "string"
      }
    ]
  }
}
```

#### 10.8 创建工件新版本
- **路径**: `POST /api/v1/artifacts/{artifact_id}/versions`
- **描述**: 为工件创建新版本
- **认证**: 需要认证
- **路径参数**:
  - `artifact_id`: 工件ID
- **请求参数**: multipart/form-data
  - `file`: 新版本文件
  - `version`: 版本号
  - `changes`: 变更说明
- **返回结果**:
```json
{
  "success": true,
  "message": "新版本创建成功",
  "data": {
    "version_id": "uuid",
    "artifact_id": "uuid",
    "version": "1.1.0",
    "file_size": 1024,
    "created_at": "datetime"
  }
}
```

### 11. 监控管理 API (`/api/v1/monitoring`)

#### 11.1 获取系统状态
- **路径**: `GET /api/v1/monitoring/system/status`
- **描述**: 获取系统整体状态
- **认证**: 需要认证 (管理员权限)
- **返回结果**:
```json
{
  "success": true,
  "data": {
    "system_status": "healthy",
    "uptime": 86400,
    "version": "1.0.0",
    "environment": "production",
    "database_status": "connected",
    "cache_status": "active",
    "last_check": "datetime"
  }
}
```

#### 11.2 获取性能指标
- **路径**: `GET /api/v1/monitoring/metrics`
- **描述**: 获取系统性能指标
- **认证**: 需要认证
- **查询参数**:
  - `start_time`: 开始时间
  - `end_time`: 结束时间
  - `metrics`: 指标类型 (逗号分隔)
- **返回结果**:
```json
{
  "success": true,
  "data": {
    "metrics": {
      "cpu_usage": 45.5,
      "memory_usage": 68.2,
      "disk_usage": 32.1,
      "api_response_time": 120.5,
      "active_sessions": 25,
      "running_tasks": 8
    },
    "timestamp": "datetime"
  }
}
```

#### 11.3 获取系统健康检查
- **路径**: `GET /api/v1/monitoring/health`
- **描述**: 系统健康检查
- **认证**: 无需认证
- **返回结果**:
```json
{
  "success": true,
  "data": {
    "status": "healthy",
    "checks": {
      "database": "ok",
      "cache": "ok",
      "external_apis": "ok",
      "disk_space": "ok",
      "memory": "warning"
    },
    "timestamp": "datetime"
  }
}
```

#### 11.4 导出监控数据
- **路径**: `POST /api/v1/monitoring/export`
- **描述**: 导出监控数据
- **认证**: 需要认证
- **请求参数**:
```json
{
  "start_time": "datetime",
  "end_time": "datetime",
  "metrics": ["string"],
  "format": "json"
}
```
- **返回结果**:
```json
{
  "success": true,
  "message": "数据导出成功",
  "data": {
    "export_id": "uuid",
    "download_url": "string",
    "expires_at": "datetime"
  }
}
```

### 12. MCP服务配置 API (`/api/v1/mcp`)

#### 12.1 获取MCP服务列表
- **路径**: `GET /api/v1/mcp/services`
- **描述**: 获取MCP服务列表
- **认证**: 需要认证
- **查询参数**:
  - `page`: 页码 (默认: 1)
  - `size`: 每页数量 (默认: 20)
  - `status`: 状态过滤
- **返回结果**:
```json
{
  "success": true,
  "data": {
    "services": [
      {
        "service_id": "uuid",
        "name": "string",
        "description": "string",
        "connection_mode": "stdio",
        "status": "active",
        "config": {},
        "created_at": "datetime",
        "last_connected": "datetime"
      }
    ],
    "pagination": {
      "page": 1,
      "size": 20,
      "total": 15,
      "pages": 1
    }
  }
}
```

#### 12.2 添加MCP服务
- **路径**: `POST /api/v1/mcp/services`
- **描述**: 添加新的MCP服务
- **认证**: 需要认证
- **请求参数**:
```json
{
  "name": "string",
  "description": "string",
  "connection_mode": "stdio",
  "config": {
    "command": "string",
    "args": ["string"],
    "env": {}
  }
}
```
- **返回结果**:
```json
{
  "success": true,
  "message": "MCP服务添加成功",
  "data": {
    "service_id": "uuid",
    "name": "string",
    "connection_mode": "stdio",
    "status": "inactive",
    "created_at": "datetime"
  }
}
```
        #### 12.3 更新MCP服务
- **路径**: `PUT /api/v1/mcp/services/{service_id}`
- **描述**: 更新MCP服务配置
- **认证**: 需要认证
- **路径参数**:
  - `service_id`: 服务ID
- **请求参数**:
```json
{
  "name": "string",
  "description": "string",
  "connection_mode": "sse",
  "config": {
    "url": "string",
    "headers": {},
    "timeout": 30
  }
}
```
- **返回结果**:
```json
{
  "success": true,
  "message": "MCP服务更新成功",
  "data": {
    "service_id": "uuid",
    "name": "string",
    "connection_mode": "sse",
    "updated_at": "datetime"
  }
}
```

#### 12.4 删除MCP服务
- **路径**: `DELETE /api/v1/mcp/services/{service_id}`
- **描述**: 删除MCP服务
- **认证**: 需要认证
- **路径参数**:
  - `service_id`: 服务ID
- **返回结果**:
```json
{
  "success": true,
  "message": "MCP服务删除成功"
}
```

#### 12.5 启动/停止MCP服务
- **路径**: `POST /api/v1/mcp/services/{service_id}/toggle`
- **描述**: 启动或停止MCP服务
- **认证**: 需要认证
- **路径参数**:
  - `service_id`: 服务ID
- **请求参数**:
```json
{
  "action": "start"
}
```
- **返回结果**:
```json
{
  "success": true,
  "message": "MCP服务已启动",
  "data": {
    "service_id": "uuid",
    "status": "active",
    "started_at": "datetime"
  }
}
```

#### 12.6 获取MCP服务状态
- **路径**: `GET /api/v1/mcp/services/{service_id}/status`
- **描述**: 获取MCP服务连接状态
- **认证**: 需要认证
- **路径参数**:
  - `service_id`: 服务ID
- **返回结果**:
```json
{
  "success": true,
  "data": {
    "service_id": "uuid",
    "status": "active",
    "connection_status": "connected",
    "last_ping": "datetime",
    "error_count": 0,
    "uptime": 3600
  }
}
```

### 13. WebSocket 实时通信 API

#### 13.1 WebSocket连接
- **路径**: `WS /api/v1/ws/{session_id}`
- **描述**: 建立WebSocket连接进行实时通信
- **认证**: 需要认证 (通过查询参数传递token)
- **路径参数**:
  - `session_id`: 会话ID
- **查询参数**:
  - `token`: 认证令牌
- **连接示例**:
```javascript
const ws = new WebSocket('ws://localhost:8000/api/v1/ws/session123?token=your_jwt_token');
```

#### 13.2 WebSocket消息格式

##### 13.2.1 发送消息
```json
{
  "type": "message",
  "data": {
    "content": "string",
    "message_type": "text",
    "metadata": {}
  }
}
```

##### 13.2.2 接收消息
```json
{
  "type": "message",
  "data": {
    "message_id": "uuid",
    "content": "string",
    "message_type": "text",
    "sender_type": "agent",
    "sender_id": "uuid",
    "timestamp": "datetime",
    "metadata": {}
  }
}
```

##### 13.2.3 任务状态更新
```json
{
  "type": "task_status",
  "data": {
    "task_id": "uuid",
    "status": "running",
    "progress": 45,
    "message": "正在处理数据...",
    "updated_at": "datetime"
  }
}
```

##### 13.2.4 工作流状态更新
```json
{
  "type": "workflow_status",
  "data": {
    "workflow_instance_id": "uuid",
    "status": "running",
    "current_step": "step_2",
    "progress": 60,
    "updated_at": "datetime"
  }
}
```

##### 13.2.5 系统通知
```json
{
  "type": "notification",
  "data": {
    "level": "info",
    "title": "系统通知",
    "message": "您的任务已完成",
    "timestamp": "datetime",
    "actions": [
      {
        "label": "查看结果",
        "action": "view_result",
        "data": {"task_id": "uuid"}
      }
    ]
  }
}
```

##### 13.2.6 错误消息
```json
{
  "type": "error",
  "data": {
    "error_code": "TASK_FAILED",
    "message": "任务执行失败",
    "details": "详细错误信息",
    "timestamp": "datetime"
  }
}
```

### 14. 意图识别和任务分解 API

#### 14.1 意图识别
- **路径**: `POST /api/v1/intent/analyze`
- **描述**: 分析用户输入的意图
- **认证**: 需要认证
- **请求参数**:
```json
{
  "text": "string",
  "context": {
    "session_id": "uuid",
    "previous_messages": ["string"]
  }
}
```
- **返回结果**:
```json
{
  "success": true,
  "data": {
    "intent": "task_creation",
    "confidence": 0.95,
    "entities": [
      {
        "type": "action",
        "value": "create",
        "start": 0,
        "end": 6
      }
    ],
    "suggested_actions": [
      {
        "action": "create_task",
        "parameters": {}
      }
    ]
  }
}
```

#### 14.2 任务分解
- **路径**: `POST /api/v1/intent/decompose`
- **描述**: 将复杂任务分解为子任务
- **认证**: 需要认证
- **请求参数**:
```json
{
  "task_description": "string",
  "context": {
    "available_tools": ["string"],
    "available_agents": ["string"]
  }
}
```
- **返回结果**:
```json
{
  "success": true,
  "data": {
    "subtasks": [
      {
        "id": "subtask_1",
        "description": "string",
        "type": "data_analysis",
        "dependencies": [],
        "estimated_duration": 300,
        "required_tools": ["string"],
        "suggested_agent": "uuid"
      }
    ],
    "execution_plan": {
      "total_steps": 3,
      "estimated_duration": 900,
      "parallel_execution": true
    }
  }
}
```

## 通用响应格式

### 成功响应
```json
{
  "success": true,
  "message": "操作成功",
  "data": {},
  "timestamp": "datetime"
}
```

### 错误响应
```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "错误描述",
    "details": "详细错误信息"
  },
  "timestamp": "datetime"
}
```

### 分页响应
```json
{
  "success": true,
  "data": {
    "items": [],
    "pagination": {
      "page": 1,
      "size": 20,
      "total": 100,
      "pages": 5,
      "has_next": true,
      "has_prev": false
    }
  }
}
```

## 认证说明

### JWT Token格式
- **Header**: `Authorization: Bearer <jwt_token>`
- **Token包含信息**:
  - `user_id`: 用户ID
  - `username`: 用户名
  - `roles`: 用户角色
  - `permissions`: 用户权限
  - `exp`: 过期时间
  - `iat`: 签发时间

### 权限级别
- **guest**: 游客权限 (只读部分公开接口)
- **user**: 普通用户权限 (基本操作)
- **admin**: 管理员权限 (系统管理)
- **super_admin**: 超级管理员权限 (完全控制)

## 错误代码说明

| 错误代码 | HTTP状态码 | 描述 |
|---------|-----------|------|
| AUTH_REQUIRED | 401 | 需要认证 |
| AUTH_INVALID | 401 | 认证无效 |
| PERMISSION_DENIED | 403 | 权限不足 |
| NOT_FOUND | 404 | 资源不存在 |
| VALIDATION_ERROR | 400 | 参数验证失败 |
| RATE_LIMIT_EXCEEDED | 429 | 请求频率超限 |
| INTERNAL_ERROR | 500 | 内部服务器错误 |
| SERVICE_UNAVAILABLE | 503 | 服务不可用 |

## 使用示例

### 1. 用户登录并创建智能体
```bash
# 1. 用户登录
curl -X POST http://localhost:8000/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username": "<EMAIL>", "password": "password123"}'

# 2. 创建智能体
curl -X POST http://localhost:8000/api/v1/agents \
  -H "Authorization: Bearer <jwt_token>" \
  -H "Content-Type: application/json" \
  -d '{"name": "数据分析助手", "description": "专门用于数据分析的智能体", "type": "data_analyst"}'
```

### 2. 创建会话并发送消息
```bash
# 1. 创建会话
curl -X POST http://localhost:8000/api/v1/sessions \
  -H "Authorization: Bearer <jwt_token>" \
  -H "Content-Type: application/json" \
  -d '{"agent_id": "agent_uuid", "title": "数据分析会话"}'

# 2. 发送消息
curl -X POST http://localhost:8000/api/v1/sessions/session_uuid/messages \
  -H "Authorization: Bearer <jwt_token>" \
  -H "Content-Type: application/json" \
  -d '{"content": "请分析这个数据集", "message_type": "text"}'
```

### 3. 创建和执行工作流
```bash
# 1. 创建工作流
curl -X POST http://localhost:8000/api/v1/workflows \
  -H "Authorization: Bearer <jwt_token>" \
  -H "Content-Type: application/json" \
  -d '{"name": "数据处理流程", "description": "自动化数据处理工作流", "definition": {...}}'

# 2. 执行工作流
curl -X POST http://localhost:8000/api/v1/workflows/workflow_uuid/execute \
  -H "Authorization: Bearer <jwt_token>" \
  -H "Content-Type: application/json" \
  -d '{"input_data": {...}, "parameters": {...}}'
```

---

**注意**: 本文档基于 A2A 多智能体系统的开发计划编写，实际API实现可能会根据开发进度和需求变更进行调整。建议在使用前查看最新的API文档和版本说明。
        "status": "active",
        "config": {},
        "owner_id": "uuid",
        "created_at": "datetime",
        "updated_at": "datetime"
      }
    ],
    "pagination": {
      "page": 1,
      "size": 20,
      "total": 50,
      "pages": 3
    }
  }
}
```

#### 3.2 创建智能体
- **路径**: `POST /api/v1/agents`
- **描述**: 创建新的智能体
- **认证**: 需要认证
- **请求参数**:
```json
{
  "name": "string",
  "description": "string",
  "type": "string",
  "config": {},
  "capabilities": ["string"]
}
```
- **返回结果**:
```json
{
  "success": true,
  "message": "智能体创建成功",
  "data": {
    "agent_id": "uuid",
    "name": "string",
    "description": "string",
    "type": "string",
    "status": "active",
    "config": {},
    "owner_id": "uuid",
    "created_at": "datetime"
  }
}
```

#### 3.3 获取智能体详情
- **路径**: `GET /api/v1/agents/{agent_id}`
- **描述**: 获取指定智能体的详细信息
- **认证**: 需要认证
- **路径参数**:
  - `agent_id`: 智能体ID
- **返回结果**:
```json
{
  "success": true,
  "data": {
    "agent_id": "uuid",
    "name": "string",
    "description": "string",
    "type": "string",
    "status": "active",
    "config": {},
    "capabilities": ["string"],
    "owner_id": "uuid",
    "created_at": "datetime",
    "updated_at": "datetime",
    "statistics": {
      "total_executions": 100,
      "success_rate": 95.5,
      "average_response_time": 1.2
    }
  }
}
```

#### 3.4 更新智能体配置
- **路径**: `PUT /api/v1/agents/{agent_id}`
- **描述**: 更新智能体配置
- **认证**: 需要认证
- **路径参数**:
  - `agent_id`: 智能体ID
- **请求参数**:
```json
{
  "name": "string",
  "description": "string",
  "config": {},
  "capabilities": ["string"]
}
```
- **返回结果**:
```json
{
  "success": true,
  "message": "智能体更新成功",
  "data": {
    "agent_id": "uuid",
    "name": "string",
    "description": "string",
    "config": {},
    "updated_at": "datetime"
  }
}
```

#### 3.5 删除智能体
- **路径**: `DELETE /api/v1/agents/{agent_id}`
- **描述**: 删除指定智能体
- **认证**: 需要认证
- **路径参数**:
  - `agent_id`: 智能体ID
- **返回结果**:
```json
{
  "success": true,
  "message": "智能体删除成功"
}
```

#### 3.6 启用智能体
- **路径**: `POST /api/v1/agents/{agent_id}/enable`
- **描述**: 启用指定智能体
- **认证**: 需要认证
- **路径参数**:
  - `agent_id`: 智能体ID
- **返回结果**:
```json
{
  "success": true,
  "message": "智能体启用成功"
}
```

#### 3.7 禁用智能体
- **路径**: `POST /api/v1/agents/{agent_id}/disable`
- **描述**: 禁用指定智能体
- **认证**: 需要认证
- **路径参数**:
  - `agent_id`: 智能体ID
- **返回结果**:
```json
{
  "success": true,
  "message": "智能体禁用成功"
}
```

#### 3.8 获取智能体层次关系
- **路径**: `GET /api/v1/agents/hierarchy`
- **描述**: 获取智能体层次关系
- **认证**: 需要认证
- **返回结果**:
```json
{
  "success": true,
  "data": {
    "hierarchy": [
      {
        "agent_id": "uuid",
        "name": "string",
        "level": 1,
        "parent_id": null,
        "children": [
          {
            "agent_id": "uuid",
            "name": "string",
            "level": 2,
            "parent_id": "uuid"
          }
        ]
      }
    ]
  }
}
```

#### 3.9 设置智能体层次关系
- **路径**: `POST /api/v1/agents/hierarchy`
- **描述**: 设置智能体层次关系
- **认证**: 需要认证
- **请求参数**:
```json
{
  "relationships": [
    {
      "child_id": "uuid",
      "parent_id": "uuid"
    }
  ]
}
```
- **返回结果**:
```json
{
  "success": true,
  "message": "智能体层次关系设置成功"
}
```

### 4. 会话管理 API (`/api/v1/sessions`)

#### 4.1 获取会话列表
- **路径**: `GET /api/v1/sessions`
- **描述**: 获取用户的会话列表
- **认证**: 需要认证
- **查询参数**:
  - `page`: 页码 (默认: 1)
  - `size`: 每页数量 (默认: 20)
  - `status`: 状态过滤 (active/completed/terminated)
- **返回结果**:
```json
{
  "success": true,
  "data": {
    "sessions": [
      {
        "session_id": "uuid",
        "title": "string",
        "status": "active",
        "agent_id": "uuid",
        "user_id": "uuid",
        "created_at": "datetime",
        "updated_at": "datetime",
        "message_count": 10
      }
    ],
    "pagination": {
      "page": 1,
      "size": 20,
      "total": 30,
      "pages": 2
    }
  }
}
```

#### 4.2 创建新会话
- **路径**: `POST /api/v1/sessions`
- **描述**: 创建新的会话
- **认证**: 需要认证
- **请求参数**:
```json
{
  "title": "string",
  "agent_id": "uuid",
  "context": {}
}
```
- **返回结果**:
```json
{
  "success": true,
  "message": "会话创建成功",
  "data": {
    "session_id": "uuid",
    "title": "string",
    "status": "active",
    "agent_id": "uuid",
    "user_id": "uuid",
    "created_at": "datetime"
  }
}
```

#### 4.3 获取会话详情
- **路径**: `GET /api/v1/sessions/{session_id}`
- **描述**: 获取指定会话的详细信息
- **认证**: 需要认证
- **路径参数**:
  - `session_id`: 会话ID
- **返回结果**:
```json
{
  "success": true,
  "data": {
    "session_id": "uuid",
    "title": "string",
    "status": "active",
    "agent_id": "uuid",
    "user_id": "uuid",
    "context": {},
    "created_at": "datetime",
    "updated_at": "datetime",
    "statistics": {
      "message_count": 15,
      "duration": 3600,
      "last_activity": "datetime"
    }
  }
}
```

#### 4.4 更新会话信息
- **路径**: `PUT /api/v1/sessions/{session_id}`
- **描述**: 更新会话信息
- **认证**: 需要认证
- **路径参数**:
  - `session_id`: 会话ID
- **请求参数**:
```json
{
  "title": "string",
  "context": {}
}
```
- **返回结果**:
```json
{
  "success": true,
  "message": "会话更新成功",
  "data": {
    "session_id": "uuid",
    "title": "string",
    "updated_at": "datetime"
  }
}
```

#### 4.5 删除会话
- **路径**: `DELETE /api/v1/sessions/{session_id}`
- **描述**: 删除指定会话
- **认证**: 需要认证
- **路径参数**:
  - `session_id`: 会话ID
- **返回结果**:
```json
{
  "success": true,
  "message": "会话删除成功"
}
```

#### 4.6 发送消息
- **路径**: `POST /api/v1/sessions/{session_id}/messages`
- **描述**: 向会话发送消息
- **认证**: 需要认证
- **路径参数**:
  - `session_id`: 会话ID
- **请求参数**:
```json
{
  "content": "string",
  "message_type": "text",
  "metadata": {}
}
```
- **返回结果**:
```json
{
  "success": true,
  "message": "消息发送成功",
  "data": {
    "message_id": "uuid",
    "content": "string",
    "message_type": "text",
    "sender_id": "uuid",
    "session_id": "uuid",
    "timestamp": "datetime"
  }
}
```

#### 4.7 获取会话消息历史
- **路径**: `GET /api/v1/sessions/{session_id}/messages`
- **描述**: 获取会话的消息历史
- **认证**: 需要认证
- **路径参数**:
  - `session_id`: 会话ID
- **查询参数**:
  - `page`: 页码 (默认: 1)
  - `size`: 每页数量 (默认: 50)
  - `before`: 获取指定时间之前的消息
  - `after`: 获取指定时间之后的消息
- **返回结果**:
```json
{
  "success": true,
  "data": {
    "messages": [
      {
        "message_id": "uuid",
        "content": "string",
        "message_type": "text",
        "sender_id": "uuid",
        "sender_type": "user",
        "timestamp": "datetime",
        "metadata": {}
      }
    ],
    "pagination": {
      "page": 1,
      "size": 50,
      "total": 100,
      "pages": 2
    }
  }
}
```

#### 4.8 终止会话
- **路径**: `POST /api/v1/sessions/{session_id}/terminate`
- **描述**: 终止指定会话
- **认证**: 需要认证
- **路径参数**:
  - `session_id`: 会话ID
- **返回结果**:
```json
{
  "success": true,
  "message": "会话终止成功"
}
```

### 5. 工作流管理 API (`/api/v1/workflows`)

#### 5.1 获取工作流列表
- **路径**: `GET /api/v1/workflows`
- **描述**: 获取用户可访问的工作流列表
- **认证**: 需要认证
- **查询参数**:
  - `page`: 页码 (默认: 1)
  - `size`: 每页数量 (默认: 20)
  - `status`: 状态过滤 (active/archived/deleted)
- **返回结果**:
```json
{
  "success": true,
  "data": {
    "workflows": [
      {
        "workflow_id": "uuid",
        "name": "string",
        "description": "string",
        "version": "1.0.0",
        "status": "active",
        "owner_id": "uuid",
        "created_at": "datetime",
        "updated_at": "datetime",
        "step_count": 5
      }
    ],
    "pagination": {
      "page": 1,
      "size": 20,
      "total": 25,
      "pages": 2
    }
  }
}
```

#### 5.2 创建新工作流
- **路径**: `POST /api/v1/workflows`
- **描述**: 创建新的工作流
- **认证**: 需要认证
- **请求参数**:
```json
{
  "name": "string",
  "description": "string",
  "definition": {},
  "input_schema": {},
  "output_schema": {}
}
```
- **返回结果**:
```json
{
  "success": true,
  "message": "工作流创建成功",
  "data": {
    "workflow_id": "uuid",
    "name": "string",
    "description": "string",
    "version": "1.0.0",
    "status": "active",
    "owner_id": "uuid",
    "created_at": "datetime"
  }
}
```

#### 5.3 获取工作流详情
- **路径**: `GET /api/v1/workflows/{workflow_id}`
- **描述**: 获取指定工作流的详细信息
- **认证**: 需要认证
- **路径参数**:
  - `workflow_id`: 工作流ID
- **返回结果**:
```json
{
  "success": true,
  "data": {
    "workflow_id": "uuid",
    "name": "string",
    "description": "string",
    "version": "1.0.0",
    "status": "active",
    "definition": {},
    "input_schema": {},
    "output_schema": {},
    "owner_id": "uuid",
    "created_at": "datetime",
    "updated_at": "datetime",
    "statistics": {
      "total_executions": 50,
      "success_rate": 92.0,
      "average_duration": 120.5
    }
  }
}
```

#### 5.4 更新工作流配置
- **路径**: `PUT /api/v1/workflows/{workflow_id}`
- **描述**: 更新工作流配置
- **认证**: 需要认证
- **路径参数**:
  - `workflow_id`: 工作流ID
- **请求参数**:
```json
{
  "name": "string",
  "description": "string",
  "definition": {},
  "input_schema": {},
  "output_schema": {}
}
```
- **返回结果**:
```json
{
  "success": true,
  "message": "工作流更新成功",
  "data": {
    "workflow_id": "uuid",
    "name": "string",
    "version": "1.1.0",
    "updated_at": "datetime"
  }
}
```

#### 5.5 删除工作流
- **路径**: `DELETE /api/v1/workflows/{workflow_id}`
- **描述**: 删除指定工作流
- **认证**: 需要认证
- **路径参数**:
  - `workflow_id`: 工作流ID
- **返回结果**:
```json
{
  "success": true,
  "message": "工作流删除成功"
}
```

#### 5.6 获取工作流步骤定义
- **路径**: `GET /api/v1/workflows/{workflow_id}/steps`
- **描述**: 获取工作流的步骤定义
- **认证**: 需要认证
- **路径参数**:
  - `workflow_id`: 工作流ID
- **返回结果**:
```json
{
  "success": true,
  "data": {
    "steps": [
      {
        "step_id": "uuid",
        "name": "string",
        "type": "agent",
        "agent_id": "uuid",
        "order": 1,
        "config": {},
        "input_mapping": {},
        "output_mapping": {},
        "conditions": {}
      }
    ]
  }
}
```

#### 5.7 添加工作流步骤
- **路径**: `POST /api/v1/workflows/{workflow_id}/steps`
- **描述**: 向工作流添加新步骤
- **认证**: 需要认证
- **路径参数**:
  - `workflow_id`: 工作流ID
- **请求参数**:
```json
{
  "name": "string",
  "type": "agent",
  "agent_id": "uuid",
  "order": 1,
  "config": {},
  "input_mapping": {},
  "output_mapping": {},
  "conditions": {}
}
```
- **返回结果**:
```json
{
  "success": true,
  "message": "工作流步骤添加成功",
  "data": {
    "step_id": "uuid",
    "name": "string",
    "order": 1,
    "created_at": "datetime"
  }
}
```

#### 5.8 更新步骤配置
- **路径**: `PUT /api/v1/workflows/{workflow_id}/steps/{step_id}`
- **描述**: 更新工作流步骤配置
- **认证**: 需要认证
- **路径参数**:
  - `workflow_id`: 工作流ID
  - `step_id`: 步骤ID
- **请求参数**:
```json
{
  "name": "string",
  "config": {},
  "input_mapping": {},
  "output_mapping": {},
  "conditions": {}
}
```
- **返回结果**:
```json
{
  "success": true,
  "message": "步骤配置更新成功",
  "data": {
    "step_id": "uuid",
    "name": "string",
    "updated_at": "datetime"
  }
}
```

#### 5.9 删除步骤
- **路径**: `DELETE /api/v1/workflows/{workflow_id}/steps/{step_id}`
- **描述**: 删除工作流步骤
- **认证**: 需要认证
- **路径参数**:
  - `workflow_id`: 工作流ID
  - `step_id`: 步骤ID
- **返回结果**:
```json
{
  "success": true,
  "message": "步骤删除成功"
}
```

#### 5.10 验证工作流配置
- **路径**: `POST /api/v1/workflows/{workflow_id}/validate`
- **描述**: 验证工作流配置的正确性
- **认证**: 需要认证
- **路径参数**:
  - `workflow_id`: 工作流ID
- **返回结果**:
```json
{
  "success": true,
  "data": {
    "valid": true,
    "errors": [],
    "warnings": [],
    "suggestions": []
  }
}
```

### 6. 任务执行 API (`/api/v1/tasks`)

#### 6.1 创建并启动任务
- **路径**: `POST /api/v1/tasks`
- **描述**: 创建并启动新任务
- **认证**: 需要认证
- **请求参数**:
```json
{
  "workflow_id": "uuid",
  "input_data": {},
  "priority": "normal",
  "timeout": 3600
}
```
- **返回结果**:
```json
{
  "success": true,
  "message": "任务创建成功",
  "data": {
    "task_id": "uuid",
    "workflow_id": "uuid",
    "status": "pending",
    "priority": "normal",
    "created_at": "datetime",
    "estimated_duration": 300
  }
}
```

#### 6.2 获取任务列表
- **路径**: `GET /api/v1/tasks`
- **描述**: 获取用户的任务列表
- **认证**: 需要认证
- **查询参数**:
  - `page`: 页码 (默认: 1)
  - `size`: 每页数量 (默认: 20)
  - `status`: 状态过滤 (pending/running/completed/failed/cancelled)
  - `workflow_id`: 工作流ID过滤
- **返回结果**:
```json
{
  "success": true,
  "data": {
    "tasks": [
      {
        "task_id": "uuid",
        "workflow_id": "uuid",
        "workflow_name": "string",
        "status": "running",
        "priority": "normal",
        "progress_percentage": 45.5,
        "created_at": "datetime",
        "started_at": "datetime",
        "estimated_completion": "datetime"
      }
    ],
    "pagination": {
      "page": 1,
      "size": 20,
      "total": 40,
      "pages": 2
    }
  }
}
```

#### 6.3 获取任务详情
- **路径**: `GET /api/v1/tasks/{task_id}`
- **描述**: 获取指定任务的详细信息
- **认证**: 需要认证
- **路径参数**:
  - `task_id`: 任务ID
- **返回结果**:
```json
{
  "success": true,
  "data": {
    "task_id": "uuid",
    "workflow_id": "uuid",
    "workflow_name": "string",
    "status": "running",
    "priority": "normal",
    "progress_percentage": 45.5,
    "input_data": {},
    "output_data": {},
    "error_message": null,
    "created_at": "datetime",
    "started_at": "datetime",
    "completed_at": null,
    "duration": 120,
    "current_step": {
      "step_id": "uuid",
      "step_name": "string",
      "status": "running",
      "started_at": "datetime"
    }
  }
}
```

#### 6.4 暂停任务
- **路径**: `POST /api/v1/tasks/{task_id}/pause`
- **描述**: 暂停正在运行的任务
- **认证**: 需要认证
- **路径参数**:
  - `task_id`: 任务ID
- **返回结果**:
```json
{
  "success": true,
  "message": "任务暂停成功"
}
```

#### 6.5 恢复任务
- **路径**: `POST /api/v1/tasks/{task_id}/resume`
- **描述**: 恢复暂停的任务
- **认证**: 需要认证
- **路径参数**:
  - `task_id`: 任务ID
- **返回结果**:
```json
{
  "success": true,
  "message": "任务恢复成功"
}
```

#### 6.6 取消任务
- **路径**: `POST /api/v1/tasks/{task_id}/cancel`
- **描述**: 取消任务执行
- **认证**: 需要认证
- **路径参数**:
  - `task_id`: 任务ID
- **返回结果**:
```json
{
  "success": true,
  "message": "任务取消成功"
}
```

#### 6.7 获取任务进度
- **路径**: `GET /api/v1/tasks/{task_id}/progress`
- **描述**: 获取任务执行进度
- **认证**: 需要认证
- **路径参数**:
  - `task_id`: 任务ID
- **返回结果**:
```json
{
  "success": true,
  "data": {
    "task_id": "uuid",
    "status": "running",
    "progress_percentage": 65.5,
    "current_step": "string",
    "completed_steps": 3,
    "total_steps": 5,
    "estimated_remaining_time": 180,
    "last_updated": "datetime"
  }
}
```

#### 6.8 获取任务步骤执行状态
- **路径**: `GET /api/v1/tasks/{task_id}/steps`
- **描述**: 获取任务中各步骤的执行状态
- **认证**: 需要认证
- **路径参数**:
  - `task_id`: 任务ID
- **返回结果**:
```json
{
  "success": true,
  "data": {
    "steps": [
      {
        "step_id": "uuid",
        "step_name": "string",
        "status": "completed",
        "agent_id": "uuid",
        "agent_name": "string",
        "started_at": "datetime",
        "completed_at": "datetime",
        "duration": 30,
        "input_data": {},
        "output_data": {},
        "error_message": null
      }
    ]
  }
}
```

#### 6.9 重试失败步骤
- **路径**: `POST /api/v1/tasks/{task_id}/steps/{step_id}/retry`
- **描述**: 重试失败的任务步骤
- **认证**: 需要认证
- **路径参数**:
  - `task_id`: 任务ID
  - `step_id`: 步骤ID
- **返回结果**:
```json
{
  "success": true,
  "message": "步骤重试成功",
  "data": {
    "step_id": "uuid",
    "status": "running",
    "retry_count": 1,
    "started_at": "datetime"
  }
}
```

### 7. 工具管理 API (`/api/v1/tools`)

#### 7.1 获取工具列表
- **路径**: `GET /api/v1/tools`
- **描述**: 获取可用工具列表
- **认证**: 需要认证
- **查询参数**:
  - `page`: 页码 (默认: 1)
  - `size`: 每页数量 (默认: 20)
  - `type`: 工具类型过滤 (builtin/mcp/custom)
  - `category`: 工具分类过滤
- **返回结果**:
```json
{
  "success": true,
  "data": {
    "tools": [
      {
        "tool_id": "uuid",
        "name": "string",
        "description": "string",
        "type": "builtin",
        "category": "string",
        "version": "1.0.0",
        "status": "active",
        "owner_id": "uuid",
        "created_at": "datetime",
        "capabilities": ["string"]
      }
    ],
    "pagination": {
      "page": 1,
      "size": 20,
      "total": 35,
      "pages": 2
    }
  }
}
```

#### 7.2 获取工具详情
- **路径**: `GET /api/v1/tools/{tool_id}`
- **描述**: 获取指定工具的详细信息
- **认证**: 需要认证
- **路径参数**:
  - `tool_id`: 工具ID
- **返回结果**:
```json
{
  "success": true,
  "data": {
    "tool_id": "uuid",
    "name": "string",
    "description": "string",
    "type": "builtin",
    "category": "string",
    "version": "1.0.0",
    "status": "active",
    "config": {},
    "input_schema": {},
    "output_schema": {},
    "owner_id": "uuid",
    "created_at": "datetime",
    "updated_at": "datetime",
    "capabilities": ["string"],
    "usage_statistics": {
      "total_executions": 100,
      "success_rate": 98.5,
      "average_execution_time": 2.3
    }
  }
}
```

#### 7.3 执行工具
- **路径**: `POST /api/v1/tools/{tool_id}/execute`
- **描述**: 执行指定工具
- **认证**: 需要认证
- **路径参数**:
  - `tool_id`: 工具ID
- **请求参数**:
```json
{
  "input_data": {},
  "config": {},
  "timeout": 30
}
```
- **返回结果**:
```json
{
  "success": true,
  "message": "工具执行成功",
  "data": {
    "execution_id": "uuid",
    "tool_id": "uuid",
    "status": "completed",
    "input_data": {},
    "output_data": {},
    "execution_time": 2.5,
    "started_at": "datetime",
    "completed_at": "datetime"
  }
}
```

#### 7.4 获取工具配置
- **路径**: `GET /api/v1/tools/{tool_id}/config`
- **描述**: 获取工具配置
- **认证**: 需要认证
- **路径参数**:
  - `tool_id`: 工具ID
- **返回结果**:
```json
{
  "success": true,
  "data": {
    "tool_id": "uuid",
    "config": {},
    "user_config": {},
    "default_config": {},
    "config_schema": {}
  }
}
```

#### 7.5 更新工具配置
- **路径**: `PUT /api/v1/tools/{tool_id}/config`
- **描述**: 更新工具配置
- **认证**: 需要认证
- **路径参数**:
  - `tool_id`: 工具ID
- **请求参数**:
```json
{
  "config": {}
}
```
- **返回结果**:
```json
{
  "success": true,
  "message": "工具配置更新成功",
  "data": {
    "tool_id": "uuid",
    "config": {},
    "updated_at": "datetime"
  }
}
```

#### 7.6 获取工具执行历史
- **路径**: `GET /api/v1/tools/{tool_id}/executions`
- **描述**: 获取工具执行历史
- **认证**: 需要认证
- **路径参数**:
  - `tool_id`: 工具ID
- **查询参数**:
  - `page`: 页码 (默认: 1)
  - `size`: 每页数量 (默认: 20)
  - `status`: 状态过滤
  - `start_date`: 开始日期
  - `end_date`: 结束日期
- **返回结果**:
```json
{
  "success": true,
  "data": {
    "executions": [
      {
        "execution_id": "uuid",
        "tool_id": "uuid",
        "status": "completed",
        "input_data": {},
        "output_data": {},
        "execution_time": 2.5,
        "user_id": "uuid",
        "started_at": "datetime",
        "completed_at": "datetime"
      }
    ],
    "pagination": {
      "page": 1,
      "size": 20,
      "total": 50,
      "pages": 3
    }
  }
}
```

### 8. 流式输出 API (`/api/v1/stream`)

#### 8.1 建立SSE连接
- **路径**: `GET /api/v1/stream/sse/{session_id}`
- **描述**: 建立Server-Sent Events连接
- **认证**: 需要认证
- **路径参数**:
  - `session_id`: 会话ID
- **查询参数**:
  - `types`: 订阅的事件类型 (逗号分隔)
- **返回结果**: SSE流式数据
```
data: {"type":"message","data":{"content":"Hello"}}

data: {"type":"status","data":{"status":"processing"}}

```

#### 8.2 获取流式状态
- **路径**: `GET /api/v1/stream/status/{session_id}`
- **描述**: 获取流式连接状态
- **认证**: 需要认证
- **路径参数**:
  - `session_id`: 会话ID
- **返回结果**:
```json
{
  "success": true,
  "data": {
    "session_id": "uuid",
    "connected": true,
    "connection_count": 2,
    "last_activity": "datetime",
    "subscribed_events": ["message", "status"]
  }
}
```

#### 8.3 发送流式消息
- **路径**: `POST /api/v1/stream/send/{session_id}`
- **描述**: 向流式连接发送消息
- **认证**: 需要认证
- **路径参数**:
  - `session_id`: 会话ID
- **请求参数**:
```json
{
  "type": "message",
  "data": {},
  "target_clients": ["all"]
}
```
- **返回结果**:
```json
{
  "success": true,
  "message": "消息发送成功",
  "data": {
    "message_id": "uuid",
    "sent_to_clients": 2,
    "timestamp": "datetime"
  }
}
```

### 9. 配置管理 API (`/api/v1/configs`)

#### 9.1 获取系统配置
- **路径**: `GET /api/v1/configs`
- **描述**: 获取系统配置列表
- **认证**: 需要认证 (管理员权限)
- **查询参数**:
  - `category`: 配置分类过滤
  - `key`: 配置键过滤
- **返回结果**:
```json
{
  "success": true,
  "data": {
    "configs": [
      {
        "config_id": "uuid",
        "config_key": "string",
        "config_value": {},
        "category": "system",
        "description": "string",
        "is_public": false,
        "updated_at": "datetime"
      }
    ]
  }
}
```

#### 9.2 更新系统配置
- **路径**: `PUT /api/v1/configs`
- **描述**: 批量更新系统配置
- **认证**: 需要认证 (管理员权限)
- **请求参数**:
```json
{
  "configs": [
    {
      "config_key": "string",
      "config_value": {}
    }
  ]
}
```
- **返回结果**:
```json
{
  "success": true,
  "message": "配置更新成功",
  "data": {
    "updated_count": 3,
    "updated_at": "datetime"
  }
}
```

#### 9.3 获取特定类型配置
- **路径**: `GET /api/v1/configs/{config_type}`
- **描述**: 获取特定类型的配置
- **认证**: 需要认证
- **路径参数**:
  - `config_type`: 配置类型 (system/llm/tool/user)
- **返回结果**:
```json
{
  "success": true,
  "data": {
    "config_type": "llm",
    "configs": {
      "openai_api_key": "string",
      "model_settings": {},
      "rate_limits": {}
    }
  }
}
```

#### 9.4 更新特定类型配置
- **路径**: `PUT /api/v1/configs/{config_type}`
- **描述**: 更新特定类型的配置
- **认证**: 需要认证
- **路径参数**:
  - `config_type`: 配置类型
- **请求参数**:
```json
{
  "configs": {
    "key1": "value1",
    "key2": {}
  }
}
```
- **返回结果**:
```json
{
  "success": true,
  "message": "配置更新成功",
  "data": {
    "config_type": "llm",
    "updated_keys": ["key1", "key2"],
    "updated_at": "datetime"
  }
}
```

#### 9.5 重置配置为默认值
- **路径**: `POST /api/v1/configs/reset`
- **描述**: 重置配置为默认值
- **认证**: 需要认证 (管理员权限)
- **请求参数**:
```json
{
  "config_keys": ["string"],
  "reset_all": false
}
```
- **返回结果**:
```json
{
  "success": true,
  "message": "配置重置成功",
  "data": {
    "reset_count": 5,
    "reset_at": "datetime"
  }
}
```

#### 9.6 获取配置模式定义
- **路径**: `GET /api/v1/configs/schema`
- **描述**: 获取配置的JSON Schema定义
- **认证**: 需要认证
- **查询参数**:
  - `config_type`: 配置类型
- **返回结果**:
```json
{
  "success": true,
  "data": {
    "schema": {
      "type": "object",
      "properties": {},
      "required": []
    }
  }
}
```

### 10. 工件管理 API (`/api/v1/artifacts`)

#### 10.1 上传工件
- **路径**: `POST /api/v1/artifacts/upload`
- **描述**: 上传文件工件
- **认证**: 需要认证
- **请求参数**: multipart/form-data
  - `file`: 文件
  - `name`: 工件名称
  - `description`: 描述
  - `tags`: 标签 (JSON数组)
- **返回结果**:
```json
{
  "success": true,
  "message": "工件上传成功",
  "data": {
    "artifact_id": "uuid",
    "name": "string",
    "file_name": "string",
    "file_size": 1024,
    "mime_type": "string",
    "version": "1.0.0",
    "owner_id": "uuid",
    "created_at": "datetime"
  }
}
```

#### 10.2 获取工件列表
- **路径**: `GET /api/v1/artifacts`
- **描述**: 获取用户可访问的工件列表
- **认证**: 需要认证
- **查询参数**:
  - `page`: 页码 (默认: 1)
  - `size`: 每页数量 (默认: 20)
  - `type`: 类型过滤
  - `tags`: 标签过滤
  - `search`: 搜索关键词
- **返回结果**:
```json
{
  "success": true,
  "data": {
    "artifacts": [
      {
        "artifact_id": "uuid",
        "name": "string",
        "description": "string",
        "file_name": "string",
        "file_size": 1024,
        "mime_type": "string",
        "version": "1.0.0",
        "status": "active",
        "owner_id": "uuid",
        "created_at": "datetime",
        "tags": ["string"]
      }
    ],
    "pagination": {
      "page": 1,
      "size": 20,
      "total": 30,
      "pages": 2
    }
  }
}
```

#### 10.3 获取工件详情
- **路径**: `GET /api/v1/artifacts/{artifact_id}`
- **描述**: 获取指定工件的详细信息
- **认证**: 需要认证
- **路径参数**:
  - `artifact_id`: 工件ID
- **返回结果**:
```json
{
  "success": true,
  "data": {
    "artifact_id": "uuid",
    "name": "string",
    "description": "string",
    "file_name": "string",
    "file_size": 1024,
    "mime_type": "string",
    "version": "1.0.0",
    "status": "active",
    "metadata": {},
    "owner_id": "uuid",
    "created_at": "datetime",
    "updated_at": "datetime",
    "tags": ["string"],
    "access_count": 50,
    "last_accessed": "datetime"
  }
}
```

#### 10.4 下载工件
- **路径**: `GET /api/v1/artifacts/{artifact_id}/download`
- **描述**: 下载工件文件
- **认证**: 需要认证
- **路径参数**:
  - `artifact_id`: 工件ID
- **查询参数**:
  - `version`: 版本号 (可选)
- **返回结果**: 文件流

#### 10.5 更新工件信息
- **路径**: `PUT /api/v1/artifacts/{artifact_id}`
- **描述**: 更新工件信息
- **认证**: 需要认证
- **路径参数**:
  - `artifact_id`: 工件ID
- **请求参数**:
```json
{
  "name": "string",