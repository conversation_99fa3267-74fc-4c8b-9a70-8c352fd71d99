#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A2A工具包使用示例

本文件包含了各种使用场景的示例代码，帮助开发者快速上手。
"""

import asyncio
import json
from typing import Dict, Any

# 导入工具包
from adk.tools import (
    # 基础组件
    create_tool_ecosystem,
    get_tool_ecosystem,
    initialize_tools,
    
    # 工具类
    BaseTool,
    WebTool,
    CalculationTool,
    ToolConfig,
    ToolResult,
    ToolError,
    
    # 管理组件
    ToolRegistry,
    ToolExecutor,
    SecurityManager,
    ToolCache,
    ToolMonitor,
    PluginManager,
    ConfigManager,
    
    # API组件
    ToolAPI,
    
    # MCP组件
    MCPManager,
    MCPServer,
    MCPClient,
    
    # 配置类
    SecurityConfig,
    CacheConfig,
    MonitorConfig,
    ExecutionMode
)


class ExampleCustomTool(BaseTool):
    """自定义工具示例"""
    
    def __init__(self):
        config = ToolConfig(
            name="example_tool",
            description="这是一个示例工具，用于演示如何创建自定义工具",
            version="1.0.0",
            category="example",
            tags=["demo", "example"],
            author="A2A Team"
        )
        super().__init__(config)
    
    async def _execute(self, **kwargs) -> ToolResult:
        """执行工具逻辑"""
        try:
            # 获取输入参数
            message = kwargs.get('message', 'Hello, World!')
            repeat = kwargs.get('repeat', 1)
            
            # 执行业务逻辑
            result = []
            for i in range(repeat):
                result.append(f"[{i+1}] {message}")
            
            return ToolResult(
                success=True,
                data={
                    'messages': result,
                    'count': len(result)
                },
                message="执行成功",
                execution_time=0.1
            )
        
        except Exception as e:
            return ToolResult(
                success=False,
                error=str(e),
                message="执行失败"
            )
    
    def validate_input(self, **kwargs) -> bool:
        """验证输入参数"""
        repeat = kwargs.get('repeat', 1)
        if not isinstance(repeat, int) or repeat < 1 or repeat > 10:
            return False
        return True
    
    def get_schema(self) -> Dict[str, Any]:
        """获取工具模式定义"""
        return {
            "type": "object",
            "properties": {
                "message": {
                    "type": "string",
                    "description": "要重复的消息",
                    "default": "Hello, World!"
                },
                "repeat": {
                    "type": "integer",
                    "description": "重复次数",
                    "minimum": 1,
                    "maximum": 10,
                    "default": 1
                }
            },
            "required": []
        }


async def example_basic_usage():
    """基础使用示例"""
    print("\n=== 基础使用示例 ===")
    
    # 创建工具生态系统
    ecosystem = create_tool_ecosystem()
    
    # 获取组件
    registry = ecosystem['tool_registry']
    executor = ecosystem['executor']
    
    # 创建和注册工具
    custom_tool = ExampleCustomTool()
    web_tool = WebTool()
    calc_tool = CalculationTool()
    
    await registry.register_tool(custom_tool)
    await registry.register_tool(web_tool)
    await registry.register_tool(calc_tool)
    
    print(f"已注册工具数量: {len(await registry.list_tools())}")
    
    # 执行自定义工具
    result = await executor.execute_tool(
        tool_name="example_tool",
        message="Hello, A2A!",
        repeat=3
    )
    print(f"自定义工具执行结果: {result.data}")
    
    # 执行计算工具
    calc_result = await executor.execute_tool(
        tool_name="calculation_tool",
        expression="2 + 2 * 3",
        format="JSON"
    )
    print(f"计算结果: {calc_result.data}")


async def example_web_tool():
    """Web工具使用示例"""
    print("\n=== Web工具使用示例 ===")
    
    ecosystem = create_tool_ecosystem()
    executor = ecosystem['executor']
    registry = ecosystem['tool_registry']
    
    # 注册Web工具
    web_tool = WebTool()
    await registry.register_tool(web_tool)
    
    # GET请求示例
    try:
        result = await executor.execute_tool(
            tool_name="web_tool",
            method="GET",
            url="https://httpbin.org/get",
            headers={"User-Agent": "A2A-Tools/1.0.0"}
        )
        print(f"GET请求成功: {result.success}")
        if result.success:
            data = result.data
            print(f"响应状态: {data.get('status_code')}")
            print(f"响应头数量: {len(data.get('headers', {}))}")
    except Exception as e:
        print(f"GET请求失败: {e}")
    
    # POST请求示例
    try:
        post_data = {"key": "value", "number": 42}
        result = await executor.execute_tool(
            tool_name="web_tool",
            method="POST",
            url="https://httpbin.org/post",
            json=post_data
        )
        print(f"POST请求成功: {result.success}")
        if result.success:
            response_data = result.data.get('data', {})
            json_data = response_data.get('json', {})
            print(f"发送的数据: {json_data}")
    except Exception as e:
        print(f"POST请求失败: {e}")


async def example_batch_execution():
    """批量执行示例"""
    print("\n=== 批量执行示例 ===")
    
    ecosystem = create_tool_ecosystem()
    executor = ecosystem['executor']
    registry = ecosystem['registry']
    
    # 注册工具
    custom_tool = ExampleCustomTool()
    calc_tool = CalculationTool()
    await registry.register_tool(custom_tool)
    await registry.register_tool(calc_tool)
    
    # 准备批量执行请求
    batch_requests = [
        {
            'tool_name': 'example_tool',
            'arguments': {'message': 'Batch 1', 'repeat': 2}
        },
        {
            'tool_name': 'calculation_tool',
            'arguments': {'expression': '10 + 20'}
        },
        {
            'tool_name': 'example_tool',
            'arguments': {'message': 'Batch 2', 'repeat': 1}
        },
        {
            'tool_name': 'calculation_tool',
            'arguments': {'expression': 'sqrt(16) + 2^3'}
        }
    ]
    
    # 执行批量请求
    batch_result = await executor.execute_batch(batch_requests)
    
    print(f"批量执行完成，成功: {batch_result.success}")
    print(f"总请求数: {len(batch_requests)}")
    print(f"成功数: {len([r for r in batch_result.results if r.success])}")
    print(f"失败数: {len([r for r in batch_result.results if not r.success])}")
    
    # 显示结果
    for i, result in enumerate(batch_result.results):
        print(f"请求 {i+1}: {'成功' if result.success else '失败'}")
        if result.success:
            print(f"  结果: {result.data}")
        else:
            print(f"  错误: {result.error}")


async def example_caching():
    """缓存使用示例"""
    print("\n=== 缓存使用示例 ===")
    
    # 创建带缓存配置的生态系统
    cache_config = CacheConfig(
        strategy="LRU",
        max_size=100,
        ttl=60,  # 60秒过期
        enable_compression=True
    )
    
    ecosystem = create_tool_ecosystem({
        'cache': cache_config
    })
    
    executor = ecosystem['executor']
    registry = ecosystem['tool_registry']
    cache = ecosystem['cache']
    
    # 注册工具
    calc_tool = CalculationTool()
    await registry.register_tool(calc_tool)
    
    # 第一次执行（会缓存结果）
    print("第一次执行（无缓存）...")
    start_time = asyncio.get_event_loop().time()
    result1 = await executor.execute_tool(
        tool_name="calculation_tool",
        expression="2^10 + 3^5",
        enable_cache=True
    )
    end_time = asyncio.get_event_loop().time()
    print(f"执行时间: {(end_time - start_time)*1000:.2f}ms")
    print(f"结果: {result1.data}")
    
    # 第二次执行（使用缓存）
    print("\n第二次执行（使用缓存）...")
    start_time = asyncio.get_event_loop().time()
    result2 = await executor.execute_tool(
        tool_name="calculation_tool",
        expression="2^10 + 3^5",
        enable_cache=True
    )
    end_time = asyncio.get_event_loop().time()
    print(f"执行时间: {(end_time - start_time)*1000:.2f}ms")
    print(f"结果: {result2.data}")
    
    # 显示缓存统计
    stats = await cache.get_stats()
    print(f"\n缓存统计:")
    print(f"  命中次数: {stats.hits}")
    print(f"  未命中次数: {stats.misses}")
    print(f"  命中率: {stats.hit_rate:.2%}")
    print(f"  缓存大小: {stats.size}")


async def example_security():
    """安全控制示例"""
    print("\n=== 安全控制示例 ===")
    
    # 创建带安全配置的生态系统
    security_config = SecurityConfig(
        default_level="HIGH",
        enable_rate_limiting=True,
        rate_limit_requests=5,  # 每分钟5次请求
        rate_limit_window=60,
        require_authentication=True
    )
    
    ecosystem = create_tool_ecosystem({
        'security': security_config
    })
    
    security_manager = ecosystem['security_manager']
    executor = ecosystem['executor']
    registry = ecosystem['tool_registry']
    
    # 注册工具
    custom_tool = ExampleCustomTool()
    await registry.register_tool(custom_tool)
    
    # 创建用户上下文
    user_context = {
        'user_id': 'user123',
        'roles': ['user'],
        'permissions': ['tool:execute'],
        'ip_address': '127.0.0.1'
    }
    
    # 验证权限
    has_permission = await security_manager.check_permission(
        user_context,
        'tool:execute',
        'example_tool'
    )
    print(f"用户权限检查: {'通过' if has_permission else '拒绝'}")
    
    if has_permission:
        # 执行工具（带安全检查）
        try:
            result = await executor.execute_tool(
                tool_name="example_tool",
                message="安全执行测试",
                repeat=2,
                user_context=user_context
            )
            print(f"安全执行成功: {result.data}")
        except Exception as e:
            print(f"安全执行失败: {e}")
    
    # 显示安全统计
    stats = await security_manager.get_stats()
    print(f"\n安全统计:")
    print(f"  认证尝试: {stats.get('auth_attempts', 0)}")
    print(f"  权限检查: {stats.get('permission_checks', 0)}")
    print(f"  安全事件: {stats.get('security_incidents', 0)}")


async def example_monitoring():
    """监控示例"""
    print("\n=== 监控示例 ===")
    
    # 创建带监控配置的生态系统
    monitor_config = MonitorConfig(
        level="INFO",
        enable_metrics=True,
        enable_health_checks=True,
        metrics_interval=10
    )
    
    ecosystem = create_tool_ecosystem({
        'monitor': monitor_config
    })
    
    monitor = ecosystem['monitor']
    executor = ecosystem['executor']
    registry = ecosystem['tool_registry']
    
    # 注册工具
    calc_tool = CalculationTool()
    await registry.register_tool(calc_tool)
    
    # 执行一些工具来生成监控数据
    for i in range(5):
        await executor.execute_tool(
            tool_name="calculation_tool",
            expression=f"2^{i} + {i}"
        )
    
    # 获取监控指标
    metrics = await monitor.get_metrics()
    print(f"监控指标:")
    for metric_name, metric_value in metrics.items():
        print(f"  {metric_name}: {metric_value}")
    
    # 执行健康检查
    health_status = await monitor.check_health()
    print(f"\n系统健康状态: {health_status}")
    
    # 获取性能统计
    performance = await monitor.get_performance_metrics()
    print(f"\n性能统计:")
    print(f"  平均执行时间: {performance.avg_execution_time:.2f}ms")
    print(f"  最大执行时间: {performance.max_execution_time:.2f}ms")
    print(f"  总执行次数: {performance.total_executions}")
    print(f"  成功率: {performance.success_rate:.2%}")


async def example_mcp_integration():
    """MCP集成示例"""
    print("\n=== MCP集成示例 ===")
    
    try:
        # 创建MCP管理器
        mcp_manager = MCPManager()
        
        # 启动MCP服务器
        server_config = {
            'name': 'example-server',
            'version': '1.0.0',
            'transport': 'websocket',
            'host': 'localhost',
            'port': 8001,
            'capabilities': {
                'tools': True,
                'resources': False,
                'prompts': False
            }
        }
        
        print("启动MCP服务器...")
        await mcp_manager.start_server('example-server', server_config)
        
        # 等待服务器启动
        await asyncio.sleep(1)
        
        # 连接MCP客户端
        client_config = {
            'server_url': 'ws://localhost:8001',
            'transport': 'websocket',
            'retry_attempts': 3
        }
        
        print("连接MCP客户端...")
        await mcp_manager.start_client('example-client', client_config)
        
        # 等待连接建立
        await asyncio.sleep(1)
        
        # 列出可用工具
        tools = await mcp_manager.list_tools('example-client')
        print(f"可用工具: {[tool.name for tool in tools]}")
        
        # 调用远程工具
        if tools:
            tool_name = tools[0].name
            result = await mcp_manager.call_tool(
                endpoint_name='example-client',
                tool_name=tool_name,
                arguments={'test': 'value'}
            )
            print(f"远程工具调用结果: {result}")
        
        # 获取连接状态
        status = await mcp_manager.get_endpoint_status('example-client')
        print(f"客户端状态: {status}")
        
        # 清理资源
        await mcp_manager.stop_client('example-client')
        await mcp_manager.stop_server('example-server')
        
    except Exception as e:
        print(f"MCP集成示例失败: {e}")


async def example_api_server():
    """API服务器示例"""
    print("\n=== API服务器示例 ===")
    
    # 创建工具生态系统
    ecosystem = create_tool_ecosystem()
    
    # 注册一些工具
    registry = ecosystem['tool_registry']
    custom_tool = ExampleCustomTool()
    calc_tool = CalculationTool()
    
    await registry.register_tool(custom_tool)
    await registry.register_tool(calc_tool)
    
    # 创建API服务器
    api = ToolAPI()
    
    print("API服务器已创建")
    print("可用端点:")
    print("  GET /tools - 获取工具列表")
    print("  GET /tools/{tool_name} - 获取工具信息")
    print("  POST /tools/{tool_name}/execute - 执行工具")
    print("  POST /tools/batch - 批量执行工具")
    print("  GET /health - 健康检查")
    print("  GET /metrics - 获取指标")
    print("  WebSocket /ws - 实时通信")
    
    # 注意：在实际使用中，你需要使用 uvicorn 来运行服务器
    # uvicorn.run(api.app, host="0.0.0.0", port=8000)
    
    print("\n要启动API服务器，请运行:")
    print("uvicorn examples:api.app --host 0.0.0.0 --port 8000 --reload")


async def example_configuration():
    """配置管理示例"""
    print("\n=== 配置管理示例 ===")
    
    # 创建配置管理器
    config_manager = ConfigManager()
    
    # 加载配置文件
    try:
        config = await config_manager.load_config(
            "config_example.yaml",
            scope="global"
        )
        print(f"配置加载成功，包含 {len(config)} 个配置项")
        
        # 获取特定配置
        security_config = await config_manager.get_config(
            "security",
            scope="global"
        )
        print(f"安全配置: {security_config}")
        
        # 更新配置
        await config_manager.update_config(
            "security.rate_limit_requests",
            200,
            scope="global"
        )
        print("配置更新成功")
        
        # 验证配置
        validation_result = await config_manager.validate_config(
            security_config,
            "security"
        )
        print(f"配置验证: {'通过' if validation_result.is_valid else '失败'}")
        
    except Exception as e:
        print(f"配置管理示例失败: {e}")


async def main():
    """主函数，运行所有示例"""
    print("A2A工具包使用示例")
    print("=" * 50)
    
    try:
        # 运行各种示例
        await example_basic_usage()
        await example_web_tool()
        await example_batch_execution()
        await example_caching()
        await example_security()
        await example_monitoring()
        await example_mcp_integration()
        await example_api_server()
        await example_configuration()
        
        print("\n=== 所有示例执行完成 ===")
        
    except Exception as e:
        print(f"示例执行失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    # 运行示例
    asyncio.run(main())