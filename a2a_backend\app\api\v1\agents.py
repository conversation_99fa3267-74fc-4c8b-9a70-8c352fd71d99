# -*- coding: utf-8 -*-
"""
A2A多智能体系统智能体API接口

提供智能体的CRUD操作、执行、状态查询和性能统计功能
"""

from datetime import datetime
from typing import Dict, Any, Optional, List
from fastapi import APIRouter, Depends, HTTPException, status, Request, Query, BackgroundTasks
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from pydantic import BaseModel, Field, validator
from loguru import logger

from app.services.agent_service import AgentService
from app.services.auth_service import AuthService
from app.core.config import get_settings
from app.core.rate_limiter import RateLimiter
from app.core.security import get_client_ip


# 创建路由器
router = APIRouter(prefix="/agents", tags=["智能体管理"])

# 安全依赖
security = HTTPBearer()

# 服务实例
agent_service = AgentService()
auth_service = AuthService()
settings = get_settings()
rate_limiter = RateLimiter()


# 请求模型
class AgentCreateRequest(BaseModel):
    """智能体创建请求模型"""
    name: str = Field(..., min_length=1, max_length=100, description="智能体名称")
    description: Optional[str] = Field(None, max_length=500, description="智能体描述")
    agent_type: str = Field(..., description="智能体类型")
    config: Dict[str, Any] = Field(..., description="智能体配置")
    parent_id: Optional[int] = Field(None, description="父智能体ID")
    is_public: bool = Field(False, description="是否公开")
    tags: Optional[List[str]] = Field(None, description="标签列表")
    
    @validator('agent_type')
    def validate_agent_type(cls, v):
        """验证智能体类型"""
        allowed_types = ['chat', 'task', 'workflow', 'tool', 'composite']
        if v not in allowed_types:
            raise ValueError(f'智能体类型必须是: {", ".join(allowed_types)}')
        return v
    
    @validator('config')
    def validate_config(cls, v):
        """验证智能体配置"""
        required_fields = ['model', 'temperature']
        for field in required_fields:
            if field not in v:
                raise ValueError(f'配置中缺少必需字段: {field}')
        
        # 验证温度值
        if not 0 <= v.get('temperature', 0) <= 2:
            raise ValueError('temperature必须在0-2之间')
        
        return v


class AgentUpdateRequest(BaseModel):
    """智能体更新请求模型"""
    name: Optional[str] = Field(None, min_length=1, max_length=100, description="智能体名称")
    description: Optional[str] = Field(None, max_length=500, description="智能体描述")
    config: Optional[Dict[str, Any]] = Field(None, description="智能体配置")
    is_public: Optional[bool] = Field(None, description="是否公开")
    tags: Optional[List[str]] = Field(None, description="标签列表")
    status: Optional[str] = Field(None, description="智能体状态")
    
    @validator('status')
    def validate_status(cls, v):
        """验证智能体状态"""
        if v is not None:
            allowed_statuses = ['active', 'inactive', 'archived']
            if v not in allowed_statuses:
                raise ValueError(f'状态必须是: {", ".join(allowed_statuses)}')
        return v
    
    @validator('config')
    def validate_config(cls, v):
        """验证智能体配置"""
        if v is not None:
            # 验证温度值
            if 'temperature' in v and not 0 <= v['temperature'] <= 2:
                raise ValueError('temperature必须在0-2之间')
        return v


class AgentExecuteRequest(BaseModel):
    """智能体执行请求模型"""
    input_data: Dict[str, Any] = Field(..., description="输入数据")
    context: Optional[Dict[str, Any]] = Field(None, description="执行上下文")
    session_id: Optional[int] = Field(None, description="会话ID")
    async_execution: bool = Field(False, description="是否异步执行")
    timeout: Optional[int] = Field(None, ge=1, le=3600, description="超时时间（秒）")
    
    @validator('input_data')
    def validate_input_data(cls, v):
        """验证输入数据"""
        if not v:
            raise ValueError('输入数据不能为空')
        return v


# 响应模型
class AgentResponse(BaseModel):
    """智能体响应模型"""
    agent_id: int = Field(..., description="智能体ID")
    name: str = Field(..., description="智能体名称")
    description: Optional[str] = Field(None, description="智能体描述")
    agent_type: str = Field(..., description="智能体类型")
    config: Dict[str, Any] = Field(..., description="智能体配置")
    status: str = Field(..., description="智能体状态")
    is_public: bool = Field(..., description="是否公开")
    tags: Optional[List[str]] = Field(None, description="标签列表")
    parent_id: Optional[int] = Field(None, description="父智能体ID")
    owner_id: int = Field(..., description="所有者ID")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    last_executed_at: Optional[datetime] = Field(None, description="最后执行时间")
    execution_count: int = Field(..., description="执行次数")
    success_rate: float = Field(..., description="成功率")


class AgentExecutionResponse(BaseModel):
    """智能体执行响应模型"""
    execution_id: str = Field(..., description="执行ID")
    agent_id: int = Field(..., description="智能体ID")
    status: str = Field(..., description="执行状态")
    result: Optional[Dict[str, Any]] = Field(None, description="执行结果")
    error: Optional[str] = Field(None, description="错误信息")
    started_at: datetime = Field(..., description="开始时间")
    completed_at: Optional[datetime] = Field(None, description="完成时间")
    duration: Optional[float] = Field(None, description="执行时长（秒）")
    async_execution: bool = Field(..., description="是否异步执行")


class MessageResponse(BaseModel):
    """消息响应模型"""
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="响应消息")
    data: Optional[Dict[str, Any]] = Field(None, description="响应数据")


# 依赖函数
async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """获取当前用户"""
    try:
        token = credentials.credentials
        user_info = await auth_service.verify_token(token)
        return user_info
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的访问令牌",
            headers={"WWW-Authenticate": "Bearer"}
        )


async def check_agent_permission(agent_id: int, user_id: int, permission: str = "read"):
    """检查智能体权限"""
    has_permission = await auth_service.check_resource_permission(
        user_id, "agent", str(agent_id), permission
    )
    if not has_permission:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=f"没有对智能体{agent_id}的{permission}权限"
        )


# API接口
@router.post("/", response_model=AgentResponse, summary="创建智能体")
async def create_agent(
    request: AgentCreateRequest,
    background_tasks: BackgroundTasks,
    http_request: Request,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    创建新的智能体
    
    - **name**: 智能体名称
    - **description**: 智能体描述（可选）
    - **agent_type**: 智能体类型（chat/task/workflow/tool/composite）
    - **config**: 智能体配置
    - **parent_id**: 父智能体ID（可选）
    - **is_public**: 是否公开（默认false）
    - **tags**: 标签列表（可选）
    
    需要提供有效的访问令牌
    """
    try:
        # 获取客户端IP
        client_ip = get_client_ip(http_request)
        
        # 速率限制检查
        await rate_limiter.check_rate_limit(
            key=f"agent_create:{current_user['user_id']}",
            limit=20,  # 每小时最多创建20个智能体
            window=3600
        )
        
        # 如果指定了父智能体，检查权限
        if request.parent_id:
            await check_agent_permission(
                request.parent_id, current_user["user_id"], "read"
            )
        
        # 创建智能体
        agent_data = {
            "name": request.name,
            "description": request.description,
            "agent_type": request.agent_type,
            "config": request.config,
            "parent_id": request.parent_id,
            "is_public": request.is_public,
            "tags": request.tags or [],
            "owner_id": current_user["user_id"]
        }
        
        result = await agent_service.create_agent(
            agent_data, current_user["user_id"], client_ip
        )
        
        if result["success"]:
            agent = result["data"]
            
            logger.info(f"智能体创建成功", extra={
                "agent_id": agent["agent_id"],
                "agent_name": agent["name"],
                "agent_type": agent["agent_type"],
                "owner_id": current_user["user_id"],
                "client_ip": client_ip
            })
            
            return AgentResponse(**agent)
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result["message"]
            )
            
    except ValueError as e:
        logger.warning(f"智能体创建失败: {str(e)}", extra={
            "user_id": current_user["user_id"],
            "client_ip": get_client_ip(http_request)
        })
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"智能体创建异常: {str(e)}", extra={
            "user_id": current_user["user_id"],
            "client_ip": get_client_ip(http_request)
        })
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="智能体创建失败，请稍后重试"
        )


@router.get("/{agent_id}", response_model=AgentResponse, summary="获取智能体信息")
async def get_agent(
    agent_id: int,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    获取指定智能体的详细信息
    
    - **agent_id**: 智能体ID
    
    需要对智能体有读取权限
    """
    try:
        # 检查权限
        await check_agent_permission(agent_id, current_user["user_id"], "read")
        
        # 获取智能体信息
        agent = await agent_service.get_agent(agent_id, current_user["user_id"])
        
        if agent:
            return AgentResponse(**agent)
        else:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="智能体不存在"
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取智能体信息异常: {str(e)}", extra={
            "agent_id": agent_id,
            "user_id": current_user["user_id"]
        })
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取智能体信息失败"
        )


@router.put("/{agent_id}", response_model=AgentResponse, summary="更新智能体")
async def update_agent(
    agent_id: int,
    request: AgentUpdateRequest,
    http_request: Request,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    更新智能体信息
    
    - **agent_id**: 智能体ID
    - **name**: 智能体名称（可选）
    - **description**: 智能体描述（可选）
    - **config**: 智能体配置（可选）
    - **is_public**: 是否公开（可选）
    - **tags**: 标签列表（可选）
    - **status**: 智能体状态（可选）
    
    需要对智能体有写入权限
    """
    try:
        # 获取客户端IP
        client_ip = get_client_ip(http_request)
        
        # 检查权限
        await check_agent_permission(agent_id, current_user["user_id"], "write")
        
        # 准备更新数据
        update_data = {}
        if request.name is not None:
            update_data["name"] = request.name
        if request.description is not None:
            update_data["description"] = request.description
        if request.config is not None:
            update_data["config"] = request.config
        if request.is_public is not None:
            update_data["is_public"] = request.is_public
        if request.tags is not None:
            update_data["tags"] = request.tags
        if request.status is not None:
            update_data["status"] = request.status
        
        # 更新智能体
        result = await agent_service.update_agent(
            agent_id, update_data, current_user["user_id"], client_ip
        )
        
        if result["success"]:
            agent = result["data"]
            
            logger.info(f"智能体更新成功", extra={
                "agent_id": agent_id,
                "updated_fields": list(update_data.keys()),
                "user_id": current_user["user_id"],
                "client_ip": client_ip
            })
            
            return AgentResponse(**agent)
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result["message"]
            )
            
    except ValueError as e:
        logger.warning(f"智能体更新失败: {str(e)}", extra={
            "agent_id": agent_id,
            "user_id": current_user["user_id"]
        })
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"智能体更新异常: {str(e)}", extra={
            "agent_id": agent_id,
            "user_id": current_user["user_id"]
        })
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="智能体更新失败，请稍后重试"
        )


@router.delete("/{agent_id}", response_model=MessageResponse, summary="删除智能体")
async def delete_agent(
    agent_id: int,
    hard_delete: bool = Query(False, description="是否硬删除"),
    http_request: Request = None,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    删除智能体
    
    - **agent_id**: 智能体ID
    - **hard_delete**: 是否硬删除（默认false，软删除）
    
    需要对智能体有删除权限
    """
    try:
        # 获取客户端IP
        client_ip = get_client_ip(http_request)
        
        # 检查权限
        await check_agent_permission(agent_id, current_user["user_id"], "delete")
        
        # 删除智能体
        result = await agent_service.delete_agent(
            agent_id, hard_delete, current_user["user_id"], client_ip
        )
        
        if result["success"]:
            logger.info(f"智能体删除成功", extra={
                "agent_id": agent_id,
                "hard_delete": hard_delete,
                "user_id": current_user["user_id"],
                "client_ip": client_ip
            })
            
            return MessageResponse(
                success=True,
                message=result["message"],
                data={
                    "agent_id": agent_id,
                    "hard_delete": hard_delete,
                    "deleted_at": datetime.utcnow().isoformat()
                }
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result["message"]
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"智能体删除异常: {str(e)}", extra={
            "agent_id": agent_id,
            "user_id": current_user["user_id"]
        })
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="智能体删除失败，请稍后重试"
        )


@router.get("/", response_model=Dict[str, Any], summary="获取智能体列表")
async def list_agents(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="页面大小"),
    agent_type: Optional[str] = Query(None, description="智能体类型过滤"),
    status: Optional[str] = Query(None, description="状态过滤"),
    is_public: Optional[bool] = Query(None, description="公开状态过滤"),
    owner_id: Optional[int] = Query(None, description="所有者ID过滤"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    tags: Optional[str] = Query(None, description="标签过滤（逗号分隔）"),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    获取智能体列表
    
    - **page**: 页码（默认1）
    - **page_size**: 页面大小（默认20，最大100）
    - **agent_type**: 智能体类型过滤
    - **status**: 状态过滤
    - **is_public**: 公开状态过滤
    - **owner_id**: 所有者ID过滤
    - **search**: 搜索关键词（名称、描述）
    - **tags**: 标签过滤（逗号分隔）
    
    返回用户有权限访问的智能体列表
    """
    try:
        # 构建过滤条件
        filters = {}
        if agent_type:
            filters["agent_type"] = agent_type
        if status:
            filters["status"] = status
        if is_public is not None:
            filters["is_public"] = is_public
        if owner_id:
            filters["owner_id"] = owner_id
        if search:
            filters["search"] = search
        if tags:
            filters["tags"] = [tag.strip() for tag in tags.split(",")]
        
        # 获取智能体列表
        result = await agent_service.list_agents(
            current_user["user_id"], filters, page, page_size
        )
        
        return {
            "success": True,
            "message": "获取智能体列表成功",
            "data": result
        }
        
    except Exception as e:
        logger.error(f"获取智能体列表异常: {str(e)}", extra={
            "user_id": current_user["user_id"]
        })
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取智能体列表失败"
        )


@router.post("/{agent_id}/execute", response_model=AgentExecutionResponse, summary="执行智能体")
async def execute_agent(
    agent_id: int,
    request: AgentExecuteRequest,
    background_tasks: BackgroundTasks,
    http_request: Request,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    执行智能体
    
    - **agent_id**: 智能体ID
    - **input_data**: 输入数据
    - **context**: 执行上下文（可选）
    - **session_id**: 会话ID（可选）
    - **async_execution**: 是否异步执行（默认false）
    - **timeout**: 超时时间（秒，可选）
    
    需要对智能体有执行权限
    """
    try:
        # 获取客户端IP
        client_ip = get_client_ip(http_request)
        
        # 检查权限
        await check_agent_permission(agent_id, current_user["user_id"], "execute")
        
        # 速率限制检查
        await rate_limiter.check_rate_limit(
            key=f"agent_execute:{current_user['user_id']}",
            limit=100,  # 每小时最多执行100次
            window=3600
        )
        
        # 执行智能体
        execution_data = {
            "input_data": request.input_data,
            "context": request.context or {},
            "session_id": request.session_id,
            "async_execution": request.async_execution,
            "timeout": request.timeout,
            "user_id": current_user["user_id"],
            "client_ip": client_ip
        }
        
        result = await agent_service.execute_agent(
            agent_id, execution_data, background_tasks
        )
        
        if result["success"]:
            execution = result["data"]
            
            logger.info(f"智能体执行启动", extra={
                "agent_id": agent_id,
                "execution_id": execution["execution_id"],
                "async_execution": request.async_execution,
                "user_id": current_user["user_id"],
                "client_ip": client_ip
            })
            
            return AgentExecutionResponse(**execution)
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result["message"]
            )
            
    except ValueError as e:
        logger.warning(f"智能体执行失败: {str(e)}", extra={
            "agent_id": agent_id,
            "user_id": current_user["user_id"]
        })
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"智能体执行异常: {str(e)}", extra={
            "agent_id": agent_id,
            "user_id": current_user["user_id"]
        })
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="智能体执行失败，请稍后重试"
        )


@router.get("/{agent_id}/executions/{execution_id}", response_model=AgentExecutionResponse, summary="获取执行结果")
async def get_execution_result(
    agent_id: int,
    execution_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    获取智能体执行结果
    
    - **agent_id**: 智能体ID
    - **execution_id**: 执行ID
    
    需要对智能体有读取权限
    """
    try:
        # 检查权限
        await check_agent_permission(agent_id, current_user["user_id"], "read")
        
        # 获取执行结果
        execution = await agent_service.get_execution_result(
            execution_id, current_user["user_id"]
        )
        
        if execution:
            return AgentExecutionResponse(**execution)
        else:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="执行记录不存在"
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取执行结果异常: {str(e)}", extra={
            "agent_id": agent_id,
            "execution_id": execution_id,
            "user_id": current_user["user_id"]
        })
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取执行结果失败"
        )


@router.get("/{agent_id}/executions", response_model=Dict[str, Any], summary="获取执行历史")
async def get_execution_history(
    agent_id: int,
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="页面大小"),
    status: Optional[str] = Query(None, description="执行状态过滤"),
    start_date: Optional[datetime] = Query(None, description="开始日期"),
    end_date: Optional[datetime] = Query(None, description="结束日期"),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    获取智能体执行历史
    
    - **agent_id**: 智能体ID
    - **page**: 页码（默认1）
    - **page_size**: 页面大小（默认20，最大100）
    - **status**: 执行状态过滤
    - **start_date**: 开始日期
    - **end_date**: 结束日期
    
    需要对智能体有读取权限
    """
    try:
        # 检查权限
        await check_agent_permission(agent_id, current_user["user_id"], "read")
        
        # 构建过滤条件
        filters = {}
        if status:
            filters["status"] = status
        if start_date:
            filters["start_date"] = start_date.isoformat()
        if end_date:
            filters["end_date"] = end_date.isoformat()
        
        # 获取执行历史
        result = await agent_service.get_execution_history(
            agent_id, current_user["user_id"], filters, page, page_size
        )
        
        return {
            "success": True,
            "message": "获取执行历史成功",
            "data": result
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取执行历史异常: {str(e)}", extra={
            "agent_id": agent_id,
            "user_id": current_user["user_id"]
        })
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取执行历史失败"
        )


@router.get("/{agent_id}/metrics", response_model=Dict[str, Any], summary="获取智能体指标")
async def get_agent_metrics(
    agent_id: int,
    period: str = Query("7d", description="统计周期（1d/7d/30d）"),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    获取智能体性能指标
    
    - **agent_id**: 智能体ID
    - **period**: 统计周期（1d/7d/30d）
    
    需要对智能体有读取权限
    """
    try:
        # 检查权限
        await check_agent_permission(agent_id, current_user["user_id"], "read")
        
        # 验证周期参数
        if period not in ["1d", "7d", "30d"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="统计周期必须是: 1d, 7d, 30d"
            )
        
        # 获取智能体指标
        metrics = await agent_service.get_agent_metrics(
            agent_id, current_user["user_id"], period
        )
        
        return {
            "success": True,
            "message": "获取智能体指标成功",
            "data": {
                "agent_id": agent_id,
                "period": period,
                "metrics": metrics
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取智能体指标异常: {str(e)}", extra={
            "agent_id": agent_id,
            "user_id": current_user["user_id"]
        })
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取智能体指标失败"
        )


@router.post("/{agent_id}/clone", response_model=AgentResponse, summary="克隆智能体")
async def clone_agent(
    agent_id: int,
    name: str = Query(..., description="新智能体名称"),
    description: Optional[str] = Query(None, description="新智能体描述"),
    http_request: Request = None,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    克隆智能体
    
    - **agent_id**: 源智能体ID
    - **name**: 新智能体名称
    - **description**: 新智能体描述（可选）
    
    需要对源智能体有读取权限
    """
    try:
        # 获取客户端IP
        client_ip = get_client_ip(http_request)
        
        # 检查权限
        await check_agent_permission(agent_id, current_user["user_id"], "read")
        
        # 速率限制检查
        await rate_limiter.check_rate_limit(
            key=f"agent_clone:{current_user['user_id']}",
            limit=10,  # 每小时最多克隆10个智能体
            window=3600
        )
        
        # 克隆智能体
        result = await agent_service.clone_agent(
            agent_id, name, description, current_user["user_id"], client_ip
        )
        
        if result["success"]:
            agent = result["data"]
            
            logger.info(f"智能体克隆成功", extra={
                "source_agent_id": agent_id,
                "new_agent_id": agent["agent_id"],
                "new_agent_name": name,
                "user_id": current_user["user_id"],
                "client_ip": client_ip
            })
            
            return AgentResponse(**agent)
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result["message"]
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"智能体克隆异常: {str(e)}", extra={
            "agent_id": agent_id,
            "user_id": current_user["user_id"]
        })
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="智能体克隆失败，请稍后重试"
        )


@router.get("/public", response_model=Dict[str, Any], summary="获取公开智能体列表")
async def list_public_agents(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="页面大小"),
    agent_type: Optional[str] = Query(None, description="智能体类型过滤"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    tags: Optional[str] = Query(None, description="标签过滤（逗号分隔）"),
    sort_by: str = Query("created_at", description="排序字段"),
    sort_order: str = Query("desc", description="排序方向")
):
    """
    获取公开智能体列表（无需认证）
    
    - **page**: 页码（默认1）
    - **page_size**: 页面大小（默认20，最大100）
    - **agent_type**: 智能体类型过滤
    - **search**: 搜索关键词（名称、描述）
    - **tags**: 标签过滤（逗号分隔）
    - **sort_by**: 排序字段（created_at/execution_count/success_rate）
    - **sort_order**: 排序方向（asc/desc）
    
    返回所有公开的智能体
    """
    try:
        # 验证排序参数
        if sort_by not in ["created_at", "execution_count", "success_rate", "name"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="排序字段必须是: created_at, execution_count, success_rate, name"
            )
        
        if sort_order not in ["asc", "desc"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="排序方向必须是: asc, desc"
            )
        
        # 构建过滤条件
        filters = {"is_public": True}
        if agent_type:
            filters["agent_type"] = agent_type
        if search:
            filters["search"] = search
        if tags:
            filters["tags"] = [tag.strip() for tag in tags.split(",")]
        
        # 获取公开智能体列表
        result = await agent_service.list_public_agents(
            filters, page, page_size, sort_by, sort_order
        )
        
        return {
            "success": True,
            "message": "获取公开智能体列表成功",
            "data": result
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取公开智能体列表异常: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取公开智能体列表失败"
        )