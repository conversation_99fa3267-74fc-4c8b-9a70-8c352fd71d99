#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A2A多智能体系统 LLM工厂

根据配置创建LLM实例的工厂类
"""

import asyncio
import logging
from typing import Dict, Type, Optional, Any, List
from datetime import datetime, timedelta

from app.services.llm_service import LLMConfig, LLMProvider, LLMService
from .base_llm import BaseLLM
from .google_llm import GoogleLLM
from .openai_llm import OpenAILLM
from .anthropic_llm import AnthropicLLM
from .dashscope_llm import DashScopeLLM
from .zhipu_llm import ZhipuLLM


class LLMFactory:
    """
    LLM工厂类
    
    负责根据配置创建和管理LLM实例
    """
    
    # LLM提供商映射
    _PROVIDER_MAPPING: Dict[LLMProvider, Type[BaseLLM]] = {
        LLMProvider.GOOGLE: GoogleLLM,
        LLMProvider.OPENAI: OpenAILLM,
        LLMProvider.ANTHROPIC: AnthropicLLM,
        LLMProvider.DASHSCOPE: DashScopeLLM,
        LLMProvider.ZHIPU: ZhipuLLM,
    }
    
    def __init__(self, llm_service: LLMService):
        """
        初始化LLM工厂
        
        Args:
            llm_service: LLM服务实例
        """
        self.llm_service = llm_service
        self.logger = logging.getLogger(__name__)
        
        # LLM实例缓存
        self._instance_cache: Dict[str, BaseLLM] = {}
        
        # 实例创建时间记录
        self._instance_created_at: Dict[str, datetime] = {}
        
        # 缓存过期时间（默认1小时）
        self._cache_ttl = timedelta(hours=1)
        
        # 最大缓存实例数
        self._max_cache_size = 100
        
        self.logger.info("LLM工厂初始化完成")
    
    async def create_llm(
        self,
        provider: LLMProvider,
        model_name: str,
        user_id: int,
        use_cache: bool = True,
        **kwargs
    ) -> BaseLLM:
        """
        创建LLM实例
        
        Args:
            provider: LLM提供商
            model_name: 模型名称
            user_id: 用户ID
            use_cache: 是否使用缓存
            **kwargs: 其他参数
        
        Returns:
            BaseLLM: LLM实例
        
        Raises:
            ValueError: 不支持的LLM提供商
            Exception: 创建LLM实例失败
        """
        try:
            # 生成缓存键
            cache_key = self._generate_cache_key(provider, model_name, user_id)
            
            # 检查缓存
            if use_cache and cache_key in self._instance_cache:
                cached_instance = self._instance_cache[cache_key]
                created_at = self._instance_created_at.get(cache_key)
                
                # 检查缓存是否过期
                if created_at and datetime.now() - created_at < self._cache_ttl:
                    # 验证实例是否仍然有效
                    if await self._validate_cached_instance(cached_instance):
                        self.logger.info(f"使用缓存的LLM实例: {cache_key}")
                        return cached_instance
                    else:
                        # 缓存实例无效，清理缓存
                        await self._remove_from_cache(cache_key)
            
            # 获取LLM配置
            config = await self.llm_service.get_llm_config(
                provider=provider,
                model_name=model_name,
                user_id=user_id
            )
            
            if not config:
                raise ValueError(f"未找到LLM配置: {provider.value}/{model_name}")
            
            # 检查提供商是否支持
            if provider not in self._PROVIDER_MAPPING:
                raise ValueError(f"不支持的LLM提供商: {provider.value}")
            
            # 获取LLM类
            llm_class = self._PROVIDER_MAPPING[provider]
            
            # 创建LLM实例
            self.logger.info(f"创建LLM实例: {provider.value}/{model_name}, 用户: {user_id}")
            llm_instance = await llm_class.create(config, user_id, **kwargs)
            
            # 添加到缓存
            if use_cache:
                await self._add_to_cache(cache_key, llm_instance)
            
            self.logger.info(f"LLM实例创建成功: {cache_key}")
            return llm_instance
            
        except Exception as e:
            self.logger.error(f"创建LLM实例失败: {e}")
            raise
    
    async def create_llm_by_config(
        self,
        config: LLMConfig,
        user_id: int,
        use_cache: bool = True,
        **kwargs
    ) -> BaseLLM:
        """
        根据配置创建LLM实例
        
        Args:
            config: LLM配置
            user_id: 用户ID
            use_cache: 是否使用缓存
            **kwargs: 其他参数
        
        Returns:
            BaseLLM: LLM实例
        """
        try:
            return await self.create_llm(
                provider=config.provider,
                model_name=config.model_name,
                user_id=user_id,
                use_cache=use_cache,
                **kwargs
            )
            
        except Exception as e:
            self.logger.error(f"根据配置创建LLM实例失败: {e}")
            raise
    
    async def get_available_providers(self, user_id: int) -> List[LLMProvider]:
        """
        获取用户可用的LLM提供商
        
        Args:
            user_id: 用户ID
        
        Returns:
            List[LLMProvider]: 可用的LLM提供商列表
        """
        try:
            available_providers = []
            
            for provider in LLMProvider:
                # 检查用户是否有权限使用该提供商
                if await self.llm_service.check_user_permission(user_id, provider):
                    available_providers.append(provider)
            
            return available_providers
            
        except Exception as e:
            self.logger.error(f"获取可用提供商失败: {e}")
            return []
    
    async def get_available_models(
        self,
        provider: LLMProvider,
        user_id: int
    ) -> List[str]:
        """
        获取用户可用的模型列表
        
        Args:
            provider: LLM提供商
            user_id: 用户ID
        
        Returns:
            List[str]: 可用的模型列表
        """
        try:
            return await self.llm_service.get_available_models(provider, user_id)
            
        except Exception as e:
            self.logger.error(f"获取可用模型失败: {e}")
            return []
    
    async def get_default_llm(
        self,
        user_id: int,
        task_type: Optional[str] = None
    ) -> Optional[BaseLLM]:
        """
        获取用户的默认LLM实例
        
        Args:
            user_id: 用户ID
            task_type: 任务类型（可选）
        
        Returns:
            Optional[BaseLLM]: 默认LLM实例
        """
        try:
            # 获取默认配置
            default_config = await self.llm_service.get_default_config(user_id, task_type)
            
            if not default_config:
                self.logger.warning(f"用户 {user_id} 没有默认LLM配置")
                return None
            
            # 创建默认LLM实例
            return await self.create_llm_by_config(default_config, user_id)
            
        except Exception as e:
            self.logger.error(f"获取默认LLM实例失败: {e}")
            return None
    
    async def health_check_all(self) -> Dict[str, bool]:
        """
        对所有缓存的LLM实例进行健康检查
        
        Returns:
            Dict[str, bool]: 健康检查结果
        """
        try:
            health_results = {}
            
            for cache_key, instance in self._instance_cache.items():
                try:
                    is_healthy = await instance.health_check()
                    health_results[cache_key] = is_healthy
                    
                    if not is_healthy:
                        self.logger.warning(f"LLM实例健康检查失败: {cache_key}")
                        # 从缓存中移除不健康的实例
                        await self._remove_from_cache(cache_key)
                        
                except Exception as e:
                    self.logger.error(f"LLM实例健康检查异常: {cache_key}, {e}")
                    health_results[cache_key] = False
                    await self._remove_from_cache(cache_key)
            
            return health_results
            
        except Exception as e:
            self.logger.error(f"批量健康检查失败: {e}")
            return {}
    
    async def cleanup_expired_instances(self) -> int:
        """
        清理过期的LLM实例
        
        Returns:
            int: 清理的实例数量
        """
        try:
            cleaned_count = 0
            current_time = datetime.now()
            expired_keys = []
            
            # 找出过期的实例
            for cache_key, created_at in self._instance_created_at.items():
                if current_time - created_at >= self._cache_ttl:
                    expired_keys.append(cache_key)
            
            # 清理过期实例
            for cache_key in expired_keys:
                await self._remove_from_cache(cache_key)
                cleaned_count += 1
            
            if cleaned_count > 0:
                self.logger.info(f"清理了 {cleaned_count} 个过期的LLM实例")
            
            return cleaned_count
            
        except Exception as e:
            self.logger.error(f"清理过期实例失败: {e}")
            return 0
    
    async def cleanup_all_instances(self) -> None:
        """
        清理所有LLM实例
        """
        try:
            cache_keys = list(self._instance_cache.keys())
            
            for cache_key in cache_keys:
                await self._remove_from_cache(cache_key)
            
            self.logger.info(f"清理了所有 {len(cache_keys)} 个LLM实例")
            
        except Exception as e:
            self.logger.error(f"清理所有实例失败: {e}")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """
        获取缓存统计信息
        
        Returns:
            Dict[str, Any]: 缓存统计信息
        """
        try:
            current_time = datetime.now()
            active_count = 0
            expired_count = 0
            
            for cache_key, created_at in self._instance_created_at.items():
                if current_time - created_at < self._cache_ttl:
                    active_count += 1
                else:
                    expired_count += 1
            
            return {
                "total_instances": len(self._instance_cache),
                "active_instances": active_count,
                "expired_instances": expired_count,
                "cache_ttl_hours": self._cache_ttl.total_seconds() / 3600,
                "max_cache_size": self._max_cache_size
            }
            
        except Exception as e:
            self.logger.error(f"获取缓存统计失败: {e}")
            return {}
    
    def _generate_cache_key(
        self,
        provider: LLMProvider,
        model_name: str,
        user_id: int
    ) -> str:
        """
        生成缓存键
        
        Args:
            provider: LLM提供商
            model_name: 模型名称
            user_id: 用户ID
        
        Returns:
            str: 缓存键
        """
        return f"{provider.value}:{model_name}:{user_id}"
    
    async def _validate_cached_instance(self, instance: BaseLLM) -> bool:
        """
        验证缓存的LLM实例是否仍然有效
        
        Args:
            instance: LLM实例
        
        Returns:
            bool: 是否有效
        """
        try:
            # 进行简单的健康检查
            return await instance.health_check()
            
        except Exception as e:
            self.logger.error(f"验证缓存实例失败: {e}")
            return False
    
    async def _add_to_cache(self, cache_key: str, instance: BaseLLM) -> None:
        """
        添加实例到缓存
        
        Args:
            cache_key: 缓存键
            instance: LLM实例
        """
        try:
            # 检查缓存大小限制
            if len(self._instance_cache) >= self._max_cache_size:
                # 清理最旧的实例
                await self._cleanup_oldest_instance()
            
            # 添加到缓存
            self._instance_cache[cache_key] = instance
            self._instance_created_at[cache_key] = datetime.now()
            
            self.logger.debug(f"LLM实例已添加到缓存: {cache_key}")
            
        except Exception as e:
            self.logger.error(f"添加实例到缓存失败: {e}")
    
    async def _remove_from_cache(self, cache_key: str) -> None:
        """
        从缓存中移除实例
        
        Args:
            cache_key: 缓存键
        """
        try:
            if cache_key in self._instance_cache:
                instance = self._instance_cache[cache_key]
                
                # 清理实例资源
                try:
                    await instance.cleanup()
                except Exception as cleanup_error:
                    self.logger.error(f"清理实例资源失败: {cleanup_error}")
                
                # 从缓存中移除
                del self._instance_cache[cache_key]
                
                if cache_key in self._instance_created_at:
                    del self._instance_created_at[cache_key]
                
                self.logger.debug(f"LLM实例已从缓存移除: {cache_key}")
            
        except Exception as e:
            self.logger.error(f"从缓存移除实例失败: {e}")
    
    async def _cleanup_oldest_instance(self) -> None:
        """
        清理最旧的实例
        """
        try:
            if not self._instance_created_at:
                return
            
            # 找到最旧的实例
            oldest_key = min(
                self._instance_created_at.keys(),
                key=lambda k: self._instance_created_at[k]
            )
            
            # 移除最旧的实例
            await self._remove_from_cache(oldest_key)
            
            self.logger.info(f"清理了最旧的LLM实例: {oldest_key}")
            
        except Exception as e:
            self.logger.error(f"清理最旧实例失败: {e}")
    
    @classmethod
    def register_provider(
        cls,
        provider: LLMProvider,
        llm_class: Type[BaseLLM]
    ) -> None:
        """
        注册新的LLM提供商
        
        Args:
            provider: LLM提供商
            llm_class: LLM类
        """
        cls._PROVIDER_MAPPING[provider] = llm_class
        logging.getLogger(__name__).info(f"注册LLM提供商: {provider.value}")
    
    @classmethod
    def get_supported_providers(cls) -> List[LLMProvider]:
        """
        获取支持的LLM提供商列表
        
        Returns:
            List[LLMProvider]: 支持的LLM提供商列表
        """
        return list(cls._PROVIDER_MAPPING.keys())