# -*- coding: utf-8 -*-
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""Simplified invocation context implementation."""

from __future__ import annotations

from typing import Optional, Dict, Any
import uuid

from pydantic import BaseModel
from pydantic import ConfigDict


class LlmCallsLimitExceededError(Exception):
    """Error thrown when the number of LLM calls exceed the limit."""


class InvocationContext(BaseModel):
    """A simplified invocation context for agent execution.
    
    This is a simplified version that contains only the essential
    functionality needed for the A2A backend system.
    """
    
    model_config = ConfigDict(
        arbitrary_types_allowed=True,
        extra="allow",  # Allow extra fields for flexibility
    )
    
    invocation_id: str
    """The id of this invocation context."""
    
    branch: Optional[str] = None
    """The branch of the invocation context.
    
    The format is like agent_1.agent_2.agent_3, where agent_1 is the parent of
    agent_2, and agent_2 is the parent of agent_3.
    """
    
    data: Dict[str, Any] = {}
    """Context data for the invocation."""
    
    end_invocation: bool = False
    """Whether to end this invocation."""
    
    user_id: Optional[str] = None
    """The user ID associated with this invocation."""
    
    session_id: Optional[str] = None
    """The session ID associated with this invocation."""
    
    _llm_call_count: int = 0
    """Counter for LLM calls made during this invocation."""
    
    max_llm_calls: Optional[int] = None
    """Maximum number of LLM calls allowed."""
    
    def __init__(self, **data):
        """Initialize the invocation context."""
        if 'invocation_id' not in data:
            data['invocation_id'] = new_invocation_context_id()
        super().__init__(**data)
    
    def increment_llm_call_count(self):
        """Tracks number of llm calls made.
        
        Raises:
            LlmCallsLimitExceededError: If number of llm calls made exceed the set threshold.
        """
        self._llm_call_count += 1
        
        if (
            self.max_llm_calls is not None
            and self.max_llm_calls > 0
            and self._llm_call_count > self.max_llm_calls
        ):
            raise LlmCallsLimitExceededError(
                f"Max number of llm calls limit of {self.max_llm_calls} exceeded"
            )
    
    def get(self, key: str, default: Any = None) -> Any:
        """Get a value from the context data.
        
        Args:
            key: The key to retrieve
            default: Default value if key not found
            
        Returns:
            The value associated with the key, or default if not found
        """
        return self.data.get(key, default)
    
    def set(self, key: str, value: Any) -> None:
        """Set a value in the context data.
        
        Args:
            key: The key to set
            value: The value to set
        """
        self.data[key] = value
    
    def update(self, data: Dict[str, Any]) -> None:
        """Update the context data with new values.
        
        Args:
            data: Dictionary of key-value pairs to update
        """
        self.data.update(data)


def new_invocation_context_id() -> str:
    """Generate a new invocation context ID.
    
    Returns:
        A new unique invocation context ID
    """
    return "e-" + str(uuid.uuid4())