#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A2A多智能体系统任务相关Pydantic模式

定义任务相关的请求和响应模式
"""

from typing import Dict, List, Optional, Any, Union
from datetime import datetime
from pydantic import Field, validator
from enum import Enum

from .base import BaseSchema


class TaskType(str, Enum):
    """任务类型枚举"""
    CHAT = "chat"
    ANALYSIS = "analysis"
    GENERATION = "generation"
    AUTOMATION = "automation"
    RESEARCH = "research"
    CUSTOM = "custom"


class TaskStatus(str, Enum):
    """任务状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    PAUSED = "paused"


class TaskPriority(str, Enum):
    """任务优先级枚举"""
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    URGENT = "urgent"


class TaskStepType(str, Enum):
    """任务步骤类型枚举"""
    ANALYSIS = "analysis"
    GENERATION = "generation"
    TOOL_CALL = "tool_call"
    VALIDATION = "validation"
    DECISION = "decision"
    CUSTOM = "custom"


class TaskStepStatus(str, Enum):
    """任务步骤状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"


class TaskExecutionStatus(str, Enum):
    """任务执行状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class TaskCreate(BaseSchema):
    """
    任务创建请求模式
    """
    
    title: str = Field(
        min_length=1,
        max_length=200,
        description="任务标题"
    )
    
    description: Optional[str] = Field(
        default=None,
        max_length=2000,
        description="任务描述"
    )
    
    task_type: TaskType = Field(
        default=TaskType.CUSTOM,
        description="任务类型"
    )
    
    priority: TaskPriority = Field(
        default=TaskPriority.NORMAL,
        description="任务优先级"
    )
    
    agent_id: Optional[int] = Field(
        default=None,
        description="指定执行的智能体ID"
    )
    
    config: Optional[Dict[str, Any]] = Field(
        default=None,
        description="任务配置"
    )
    
    input_data: Optional[Dict[str, Any]] = Field(
        default=None,
        description="输入数据"
    )
    
    expected_output: Optional[str] = Field(
        default=None,
        max_length=1000,
        description="期望输出描述"
    )
    
    deadline: Optional[datetime] = Field(
        default=None,
        description="截止时间"
    )
    
    tags: Optional[List[str]] = Field(
        default=None,
        description="任务标签"
    )


class TaskUpdate(BaseSchema):
    """
    任务更新请求模式
    """
    
    title: Optional[str] = Field(
        default=None,
        min_length=1,
        max_length=200,
        description="任务标题"
    )
    
    description: Optional[str] = Field(
        default=None,
        max_length=2000,
        description="任务描述"
    )
    
    priority: Optional[TaskPriority] = Field(
        default=None,
        description="任务优先级"
    )
    
    status: Optional[TaskStatus] = Field(
        default=None,
        description="任务状态"
    )
    
    config: Optional[Dict[str, Any]] = Field(
        default=None,
        description="任务配置"
    )
    
    deadline: Optional[datetime] = Field(
        default=None,
        description="截止时间"
    )
    
    tags: Optional[List[str]] = Field(
        default=None,
        description="任务标签"
    )


class TaskResponse(BaseSchema):
    """
    任务响应模式
    """
    
    id: int = Field(
        description="任务ID"
    )
    
    task_id: str = Field(
        description="任务唯一标识"
    )
    
    title: str = Field(
        description="任务标题"
    )
    
    description: Optional[str] = Field(
        description="任务描述"
    )
    
    task_type: TaskType = Field(
        description="任务类型"
    )
    
    priority: TaskPriority = Field(
        description="任务优先级"
    )
    
    status: TaskStatus = Field(
        description="任务状态"
    )
    
    progress: float = Field(
        description="任务进度（0-100）"
    )
    
    user_id: int = Field(
        description="创建者用户ID"
    )
    
    owner_id: int = Field(
        description="拥有者用户ID"
    )
    
    agent_id: Optional[int] = Field(
        description="执行智能体ID"
    )
    
    config: Optional[Dict[str, Any]] = Field(
        description="任务配置"
    )
    
    input_data: Optional[Dict[str, Any]] = Field(
        description="输入数据"
    )
    
    output_data: Optional[Dict[str, Any]] = Field(
        description="输出数据"
    )
    
    execution_log: Optional[List[Dict[str, Any]]] = Field(
        description="执行日志"
    )
    
    expected_output: Optional[str] = Field(
        description="期望输出描述"
    )
    
    actual_output: Optional[str] = Field(
        description="实际输出"
    )
    
    error_message: Optional[str] = Field(
        description="错误信息"
    )
    
    start_time: Optional[datetime] = Field(
        description="开始时间"
    )
    
    end_time: Optional[datetime] = Field(
        description="结束时间"
    )
    
    deadline: Optional[datetime] = Field(
        description="截止时间"
    )
    
    duration: Optional[float] = Field(
        description="执行时长（秒）"
    )
    
    cost: float = Field(
        description="执行成本"
    )
    
    tags: Optional[List[str]] = Field(
        description="任务标签"
    )
    
    created_at: datetime = Field(
        description="创建时间"
    )
    
    updated_at: datetime = Field(
        description="更新时间"
    )


class TaskStepCreate(BaseSchema):
    """
    任务步骤创建请求模式
    """
    
    step_name: str = Field(
        min_length=1,
        max_length=200,
        description="步骤名称"
    )
    
    description: Optional[str] = Field(
        default=None,
        max_length=1000,
        description="步骤描述"
    )
    
    step_type: TaskStepType = Field(
        description="步骤类型"
    )
    
    step_order: int = Field(
        ge=0,
        description="步骤顺序"
    )
    
    parent_step_id: Optional[int] = Field(
        default=None,
        description="父步骤ID"
    )
    
    level: int = Field(
        default=0,
        ge=0,
        description="步骤层级"
    )
    
    agent_id: Optional[int] = Field(
        default=None,
        description="执行智能体ID"
    )
    
    tool_name: Optional[str] = Field(
        default=None,
        max_length=100,
        description="使用的工具名称"
    )
    
    input_data: Optional[Dict[str, Any]] = Field(
        default=None,
        description="输入数据"
    )
    
    config: Optional[Dict[str, Any]] = Field(
        default=None,
        description="步骤配置"
    )
    
    dependencies: Optional[List[str]] = Field(
        default=None,
        description="依赖步骤"
    )
    
    max_retries: int = Field(
        default=3,
        ge=0,
        le=10,
        description="最大重试次数"
    )


class TaskStepUpdate(BaseSchema):
    """
    任务步骤更新请求模式
    """
    
    step_name: Optional[str] = Field(
        default=None,
        min_length=1,
        max_length=200,
        description="步骤名称"
    )
    
    description: Optional[str] = Field(
        default=None,
        max_length=1000,
        description="步骤描述"
    )
    
    status: Optional[TaskStepStatus] = Field(
        default=None,
        description="步骤状态"
    )
    
    output_data: Optional[Dict[str, Any]] = Field(
        default=None,
        description="输出数据"
    )
    
    result: Optional[Dict[str, Any]] = Field(
        default=None,
        description="执行结果"
    )
    
    error_message: Optional[str] = Field(
        default=None,
        description="错误信息"
    )


class TaskStepResponse(BaseSchema):
    """
    任务步骤响应模式
    """
    
    id: int = Field(
        description="步骤ID"
    )
    
    step_id: str = Field(
        description="步骤唯一标识"
    )
    
    task_id: int = Field(
        description="任务ID"
    )
    
    step_name: str = Field(
        description="步骤名称"
    )
    
    description: Optional[str] = Field(
        description="步骤描述"
    )
    
    step_type: TaskStepType = Field(
        description="步骤类型"
    )
    
    status: TaskStepStatus = Field(
        description="步骤状态"
    )
    
    step_order: int = Field(
        description="步骤顺序"
    )
    
    parent_step_id: Optional[int] = Field(
        description="父步骤ID"
    )
    
    level: int = Field(
        description="步骤层级"
    )
    
    user_id: int = Field(
        description="用户ID"
    )
    
    owner_id: int = Field(
        description="拥有者用户ID"
    )
    
    execution_id: Optional[int] = Field(
        description="执行ID"
    )
    
    agent_id: Optional[int] = Field(
        description="执行智能体ID"
    )
    
    tool_name: Optional[str] = Field(
        description="使用的工具名称"
    )
    
    input_data: Optional[Dict[str, Any]] = Field(
        description="输入数据"
    )
    
    output_data: Optional[Dict[str, Any]] = Field(
        description="输出数据"
    )
    
    config: Optional[Dict[str, Any]] = Field(
        description="步骤配置"
    )
    
    result: Optional[Dict[str, Any]] = Field(
        description="执行结果"
    )
    
    error_message: Optional[str] = Field(
        description="错误信息"
    )
    
    error_details: Optional[Dict[str, Any]] = Field(
        description="错误详情"
    )
    
    retry_count: int = Field(
        description="重试次数"
    )
    
    max_retries: int = Field(
        description="最大重试次数"
    )
    
    dependencies: Optional[List[str]] = Field(
        description="依赖步骤"
    )
    
    metadata: Optional[Dict[str, Any]] = Field(
        description="步骤元数据"
    )
    
    start_time: Optional[datetime] = Field(
        description="开始时间"
    )
    
    end_time: Optional[datetime] = Field(
        description="结束时间"
    )
    
    duration: Optional[float] = Field(
        description="执行时长（秒）"
    )
    
    created_at: datetime = Field(
        description="创建时间"
    )
    
    updated_at: datetime = Field(
        description="更新时间"
    )


class TaskExecutionCreate(BaseSchema):
    """
    任务执行创建请求模式
    """
    
    execution_config: Optional[Dict[str, Any]] = Field(
        default=None,
        description="执行配置"
    )
    
    environment: Optional[Dict[str, Any]] = Field(
        default=None,
        description="执行环境"
    )


class TaskExecutionResponse(BaseSchema):
    """
    任务执行响应模式
    """
    
    id: int = Field(
        description="执行ID"
    )
    
    execution_id: str = Field(
        description="执行唯一标识"
    )
    
    task_id: int = Field(
        description="任务ID"
    )
    
    user_id: int = Field(
        description="创建者用户ID"
    )
    
    owner_id: int = Field(
        description="拥有者用户ID"
    )
    
    status: TaskExecutionStatus = Field(
        description="执行状态"
    )
    
    start_time: Optional[datetime] = Field(
        description="开始时间"
    )
    
    end_time: Optional[datetime] = Field(
        description="结束时间"
    )
    
    duration: Optional[float] = Field(
        description="执行时长（秒）"
    )
    
    result: Optional[Dict[str, Any]] = Field(
        description="执行结果"
    )
    
    error_message: Optional[str] = Field(
        description="错误信息"
    )
    
    error_details: Optional[Dict[str, Any]] = Field(
        description="错误详情"
    )
    
    execution_config: Optional[Dict[str, Any]] = Field(
        description="执行配置"
    )
    
    environment: Optional[Dict[str, Any]] = Field(
        description="执行环境"
    )
    
    created_at: datetime = Field(
        description="创建时间"
    )
    
    updated_at: datetime = Field(
        description="更新时间"
    )


class TaskQuery(BaseSchema):
    """
    任务查询参数模式
    """
    
    task_type: Optional[TaskType] = Field(
        default=None,
        description="任务类型过滤"
    )
    
    status: Optional[TaskStatus] = Field(
        default=None,
        description="任务状态过滤"
    )
    
    priority: Optional[TaskPriority] = Field(
        default=None,
        description="任务优先级过滤"
    )
    
    agent_id: Optional[int] = Field(
        default=None,
        description="智能体ID过滤"
    )
    
    user_id: Optional[int] = Field(
        default=None,
        description="用户ID过滤"
    )
    
    tags: Optional[List[str]] = Field(
        default=None,
        description="标签过滤"
    )
    
    start_date: Optional[datetime] = Field(
        default=None,
        description="开始日期过滤"
    )
    
    end_date: Optional[datetime] = Field(
        default=None,
        description="结束日期过滤"
    )
    
    search: Optional[str] = Field(
        default=None,
        max_length=200,
        description="搜索关键词"
    )


class TaskStepQuery(BaseSchema):
    """
    任务步骤查询参数模式
    """
    
    task_id: Optional[int] = Field(
        default=None,
        description="任务ID过滤"
    )
    
    step_type: Optional[TaskStepType] = Field(
        default=None,
        description="步骤类型过滤"
    )
    
    status: Optional[TaskStepStatus] = Field(
        default=None,
        description="步骤状态过滤"
    )
    
    agent_id: Optional[int] = Field(
        default=None,
        description="智能体ID过滤"
    )
    
    level: Optional[int] = Field(
        default=None,
        description="步骤层级过滤"
    )


class TaskStatistics(BaseSchema):
    """
    任务统计信息模式
    """
    
    total_tasks: int = Field(
        description="总任务数"
    )
    
    pending_tasks: int = Field(
        description="待执行任务数"
    )
    
    running_tasks: int = Field(
        description="执行中任务数"
    )
    
    completed_tasks: int = Field(
        description="已完成任务数"
    )
    
    failed_tasks: int = Field(
        description="失败任务数"
    )
    
    cancelled_tasks: int = Field(
        description="已取消任务数"
    )
    
    success_rate: float = Field(
        description="成功率"
    )
    
    average_duration: Optional[float] = Field(
        description="平均执行时长（秒）"
    )
    
    total_cost: float = Field(
        description="总成本"
    )


class TaskBatchOperation(BaseSchema):
    """
    任务批量操作请求模式
    """
    
    task_ids: List[int] = Field(
        min_items=1,
        max_items=100,
        description="任务ID列表"
    )
    
    operation: str = Field(
        description="操作类型（start/pause/cancel/delete）"
    )
    
    @validator('operation')
    def validate_operation(cls, v):
        """验证操作类型"""
        allowed_operations = ['start', 'pause', 'cancel', 'delete']
        if v not in allowed_operations:
            raise ValueError(f'操作类型必须是以下之一: {", ".join(allowed_operations)}')
        return v