# -*- coding: utf-8 -*-
"""
A2A多智能体系统工作流上下文管理器

提供工作流上下文的传递和管理、智能体间数据的共享和隔离
"""

import logging
import json
import uuid
import asyncio
from typing import Dict, List, Optional, Any, Union, Tuple, Set
from datetime import datetime, timedelta
from enum import Enum
from dataclasses import dataclass, field
from collections import defaultdict
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc, func

from app.models.workflow import Workflow, WorkflowExecution
from app.models.user import User
from app.core.database import get_db
from app.core.logging import get_logger
from app.core.config import get_settings


class ContextScope(Enum):
    """上下文作用域"""
    GLOBAL = "global"  # 全局作用域
    WORKFLOW = "workflow"  # 工作流作用域
    NODE = "node"  # 节点作用域
    AGENT = "agent"  # 智能体作用域
    SESSION = "session"  # 会话作用域


class ContextType(Enum):
    """上下文类型"""
    INPUT = "input"  # 输入数据
    OUTPUT = "output"  # 输出数据
    INTERMEDIATE = "intermediate"  # 中间数据
    METADATA = "metadata"  # 元数据
    CONFIG = "config"  # 配置数据
    STATE = "state"  # 状态数据
    CACHE = "cache"  # 缓存数据


class ContextAccessLevel(Enum):
    """上下文访问级别"""
    READ_ONLY = "read_only"  # 只读
    READ_WRITE = "read_write"  # 读写
    WRITE_ONLY = "write_only"  # 只写
    PRIVATE = "private"  # 私有


@dataclass
class ContextEntry:
    """上下文条目"""
    key: str
    value: Any
    context_type: ContextType
    scope: ContextScope
    access_level: ContextAccessLevel
    owner_id: str  # 所有者ID（用户、智能体、节点等）
    created_at: datetime
    updated_at: datetime
    expires_at: Optional[datetime] = None
    version: int = 1
    metadata: Dict[str, Any] = field(default_factory=dict)
    tags: Set[str] = field(default_factory=set)
    
    def is_expired(self) -> bool:
        """检查是否过期"""
        if self.expires_at is None:
            return False
        return datetime.utcnow() > self.expires_at
    
    def can_read(self, accessor_id: str) -> bool:
        """检查是否可读"""
        if self.access_level == ContextAccessLevel.WRITE_ONLY:
            return False
        if self.access_level == ContextAccessLevel.PRIVATE and self.owner_id != accessor_id:
            return False
        return True
    
    def can_write(self, accessor_id: str) -> bool:
        """检查是否可写"""
        if self.access_level == ContextAccessLevel.READ_ONLY:
            return False
        if self.access_level == ContextAccessLevel.PRIVATE and self.owner_id != accessor_id:
            return False
        return True


@dataclass
class ContextSnapshot:
    """上下文快照"""
    snapshot_id: str
    execution_id: str
    node_id: Optional[str]
    context_data: Dict[str, Any]
    created_at: datetime
    description: str = ""
    metadata: Dict[str, Any] = field(default_factory=dict)


class WorkflowContextManager:
    """
    工作流上下文管理器类
    
    提供以下功能：
    1. 工作流上下文的传递和管理
    2. 智能体间数据的共享和隔离
    3. 上下文的持久化和恢复
    4. 上下文的版本管理和回滚
    5. 上下文的访问控制和权限管理
    6. 上下文的生命周期管理
    """
    
    def __init__(self, db: Session, logger: Optional[logging.Logger] = None):
        """
        初始化工作流上下文管理器
        
        Args:
            db: 数据库会话
            logger: 日志记录器
        """
        self.db = db
        self.logger = logger or get_logger("workflow_context_manager")
        self.settings = get_settings()
        
        # 上下文存储（内存中的快速访问）
        self.contexts: Dict[str, Dict[str, ContextEntry]] = defaultdict(dict)
        
        # 执行上下文映射
        self.execution_contexts: Dict[str, str] = {}  # execution_id -> context_id
        
        # 上下文快照
        self.snapshots: Dict[str, List[ContextSnapshot]] = defaultdict(list)
        
        # 上下文锁（用于并发控制）
        self.context_locks: Dict[str, asyncio.Lock] = defaultdict(asyncio.Lock)
        
        # 配置
        self.max_context_size = self.settings.max_context_size or 10 * 1024 * 1024  # 10MB
        self.context_ttl = self.settings.context_ttl or 3600  # 1小时
        self.max_snapshots_per_context = 100
        self.cleanup_interval = 300  # 5分钟
        
        # 清理任务
        self.cleanup_task: Optional[asyncio.Task] = None
        self.is_running = False
        
        self.logger.info("WorkflowContextManager已初始化")
    
    async def start(self) -> None:
        """
        启动上下文管理器
        """
        if self.is_running:
            return
        
        self.is_running = True
        self.cleanup_task = asyncio.create_task(self._cleanup_loop())
        
        self.logger.info("工作流上下文管理器已启动")
    
    async def stop(self) -> None:
        """
        停止上下文管理器
        """
        if not self.is_running:
            return
        
        self.is_running = False
        
        if self.cleanup_task:
            self.cleanup_task.cancel()
            try:
                await self.cleanup_task
            except asyncio.CancelledError:
                pass
        
        self.logger.info("工作流上下文管理器已停止")
    
    async def create_context(self, execution_id: str, initial_data: Optional[Dict[str, Any]] = None) -> str:
        """
        创建工作流上下文
        
        Args:
            execution_id: 执行ID
            initial_data: 初始数据
            
        Returns:
            str: 上下文ID
        """
        try:
            context_id = f"ctx_{execution_id}_{uuid.uuid4().hex[:8]}"
            
            # 创建上下文存储
            self.contexts[context_id] = {}
            self.execution_contexts[execution_id] = context_id
            
            # 添加初始数据
            if initial_data:
                for key, value in initial_data.items():
                    await self.set_context(
                        context_id=context_id,
                        key=key,
                        value=value,
                        context_type=ContextType.INPUT,
                        scope=ContextScope.WORKFLOW,
                        owner_id=execution_id
                    )
            
            # 持久化到数据库
            await self._persist_context(context_id)
            
            self.logger.info(f"工作流上下文已创建: {context_id}")
            return context_id
        except Exception as e:
            self.logger.error(f"创建工作流上下文错误: {str(e)}")
            raise
    
    async def get_context_by_execution(self, execution_id: str) -> Optional[str]:
        """
        根据执行ID获取上下文ID
        
        Args:
            execution_id: 执行ID
            
        Returns:
            Optional[str]: 上下文ID
        """
        return self.execution_contexts.get(execution_id)
    
    async def set_context(
        self,
        context_id: str,
        key: str,
        value: Any,
        context_type: ContextType = ContextType.INTERMEDIATE,
        scope: ContextScope = ContextScope.NODE,
        access_level: ContextAccessLevel = ContextAccessLevel.READ_WRITE,
        owner_id: Optional[str] = None,
        expires_in: Optional[int] = None,
        tags: Optional[Set[str]] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        设置上下文值
        
        Args:
            context_id: 上下文ID
            key: 键
            value: 值
            context_type: 上下文类型
            scope: 作用域
            access_level: 访问级别
            owner_id: 所有者ID
            expires_in: 过期时间（秒）
            tags: 标签
            metadata: 元数据
            
        Returns:
            bool: 是否设置成功
        """
        try:
            async with self.context_locks[context_id]:
                if context_id not in self.contexts:
                    self.logger.error(f"上下文不存在: {context_id}")
                    return False
                
                # 检查上下文大小
                if await self._check_context_size(context_id, key, value):
                    self.logger.warning(f"上下文大小超限: {context_id}")
                    return False
                
                current_time = datetime.utcnow()
                expires_at = None
                if expires_in:
                    expires_at = current_time + timedelta(seconds=expires_in)
                
                # 检查是否已存在
                existing_entry = self.contexts[context_id].get(key)
                if existing_entry:
                    # 检查写权限
                    if not existing_entry.can_write(owner_id or ""):
                        self.logger.warning(f"无写权限: {context_id}.{key}")
                        return False
                    
                    # 更新现有条目
                    existing_entry.value = value
                    existing_entry.updated_at = current_time
                    existing_entry.version += 1
                    if expires_at:
                        existing_entry.expires_at = expires_at
                    if tags:
                        existing_entry.tags.update(tags)
                    if metadata:
                        existing_entry.metadata.update(metadata)
                else:
                    # 创建新条目
                    entry = ContextEntry(
                        key=key,
                        value=value,
                        context_type=context_type,
                        scope=scope,
                        access_level=access_level,
                        owner_id=owner_id or "",
                        created_at=current_time,
                        updated_at=current_time,
                        expires_at=expires_at,
                        tags=tags or set(),
                        metadata=metadata or {}
                    )
                    
                    self.contexts[context_id][key] = entry
                
                # 异步持久化
                asyncio.create_task(self._persist_context_entry(context_id, key))
                
                return True
        except Exception as e:
            self.logger.error(f"设置上下文错误: {str(e)}")
            return False
    
    async def get_context(
        self,
        context_id: str,
        key: str,
        accessor_id: Optional[str] = None,
        default: Any = None
    ) -> Any:
        """
        获取上下文值
        
        Args:
            context_id: 上下文ID
            key: 键
            accessor_id: 访问者ID
            default: 默认值
            
        Returns:
            Any: 上下文值
        """
        try:
            async with self.context_locks[context_id]:
                if context_id not in self.contexts:
                    return default
                
                entry = self.contexts[context_id].get(key)
                if not entry:
                    return default
                
                # 检查是否过期
                if entry.is_expired():
                    await self._remove_context_entry(context_id, key)
                    return default
                
                # 检查读权限
                if not entry.can_read(accessor_id or ""):
                    self.logger.warning(f"无读权限: {context_id}.{key}")
                    return default
                
                return entry.value
        except Exception as e:
            self.logger.error(f"获取上下文错误: {str(e)}")
            return default
    
    async def get_context_info(
        self,
        context_id: str,
        key: str,
        accessor_id: Optional[str] = None
    ) -> Optional[Dict[str, Any]]:
        """
        获取上下文条目信息
        
        Args:
            context_id: 上下文ID
            key: 键
            accessor_id: 访问者ID
            
        Returns:
            Optional[Dict[str, Any]]: 上下文条目信息
        """
        try:
            async with self.context_locks[context_id]:
                if context_id not in self.contexts:
                    return None
                
                entry = self.contexts[context_id].get(key)
                if not entry:
                    return None
                
                # 检查是否过期
                if entry.is_expired():
                    await self._remove_context_entry(context_id, key)
                    return None
                
                # 检查读权限
                if not entry.can_read(accessor_id or ""):
                    return None
                
                return {
                    "key": entry.key,
                    "context_type": entry.context_type.value,
                    "scope": entry.scope.value,
                    "access_level": entry.access_level.value,
                    "owner_id": entry.owner_id,
                    "created_at": entry.created_at.isoformat(),
                    "updated_at": entry.updated_at.isoformat(),
                    "expires_at": entry.expires_at.isoformat() if entry.expires_at else None,
                    "version": entry.version,
                    "metadata": entry.metadata,
                    "tags": list(entry.tags)
                }
        except Exception as e:
            self.logger.error(f"获取上下文信息错误: {str(e)}")
            return None
    
    async def remove_context(self, context_id: str, key: str, accessor_id: Optional[str] = None) -> bool:
        """
        删除上下文值
        
        Args:
            context_id: 上下文ID
            key: 键
            accessor_id: 访问者ID
            
        Returns:
            bool: 是否删除成功
        """
        try:
            async with self.context_locks[context_id]:
                if context_id not in self.contexts:
                    return False
                
                entry = self.contexts[context_id].get(key)
                if not entry:
                    return False
                
                # 检查写权限
                if not entry.can_write(accessor_id or ""):
                    self.logger.warning(f"无删除权限: {context_id}.{key}")
                    return False
                
                await self._remove_context_entry(context_id, key)
                return True
        except Exception as e:
            self.logger.error(f"删除上下文错误: {str(e)}")
            return False
    
    async def list_context_keys(
        self,
        context_id: str,
        scope: Optional[ContextScope] = None,
        context_type: Optional[ContextType] = None,
        accessor_id: Optional[str] = None,
        tags: Optional[Set[str]] = None
    ) -> List[str]:
        """
        列出上下文键
        
        Args:
            context_id: 上下文ID
            scope: 作用域过滤
            context_type: 类型过滤
            accessor_id: 访问者ID
            tags: 标签过滤
            
        Returns:
            List[str]: 键列表
        """
        try:
            async with self.context_locks[context_id]:
                if context_id not in self.contexts:
                    return []
                
                keys = []
                for key, entry in self.contexts[context_id].items():
                    # 检查是否过期
                    if entry.is_expired():
                        continue
                    
                    # 检查读权限
                    if not entry.can_read(accessor_id or ""):
                        continue
                    
                    # 应用过滤条件
                    if scope and entry.scope != scope:
                        continue
                    
                    if context_type and entry.context_type != context_type:
                        continue
                    
                    if tags and not tags.intersection(entry.tags):
                        continue
                    
                    keys.append(key)
                
                return keys
        except Exception as e:
            self.logger.error(f"列出上下文键错误: {str(e)}")
            return []
    
    async def create_snapshot(
        self,
        context_id: str,
        node_id: Optional[str] = None,
        description: str = "",
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        创建上下文快照
        
        Args:
            context_id: 上下文ID
            node_id: 节点ID
            description: 描述
            metadata: 元数据
            
        Returns:
            str: 快照ID
        """
        try:
            async with self.context_locks[context_id]:
                if context_id not in self.contexts:
                    raise ValueError(f"上下文不存在: {context_id}")
                
                # 获取执行ID
                execution_id = None
                for exec_id, ctx_id in self.execution_contexts.items():
                    if ctx_id == context_id:
                        execution_id = exec_id
                        break
                
                if not execution_id:
                    raise ValueError(f"找不到执行ID: {context_id}")
                
                # 序列化上下文数据
                context_data = {}
                for key, entry in self.contexts[context_id].items():
                    if not entry.is_expired():
                        try:
                            # 尝试序列化值
                            json.dumps(entry.value)
                            context_data[key] = {
                                "value": entry.value,
                                "context_type": entry.context_type.value,
                                "scope": entry.scope.value,
                                "access_level": entry.access_level.value,
                                "owner_id": entry.owner_id,
                                "version": entry.version,
                                "metadata": entry.metadata,
                                "tags": list(entry.tags)
                            }
                        except (TypeError, ValueError):
                            # 无法序列化的值跳过
                            self.logger.warning(f"跳过无法序列化的值: {key}")
                            continue
                
                # 创建快照
                snapshot_id = f"snap_{uuid.uuid4().hex[:8]}"
                snapshot = ContextSnapshot(
                    snapshot_id=snapshot_id,
                    execution_id=execution_id,
                    node_id=node_id,
                    context_data=context_data,
                    created_at=datetime.utcnow(),
                    description=description,
                    metadata=metadata or {}
                )
                
                # 添加到快照列表
                self.snapshots[context_id].append(snapshot)
                
                # 限制快照数量
                if len(self.snapshots[context_id]) > self.max_snapshots_per_context:
                    self.snapshots[context_id].pop(0)
                
                # 持久化快照
                await self._persist_snapshot(snapshot)
                
                self.logger.info(f"上下文快照已创建: {snapshot_id}")
                return snapshot_id
        except Exception as e:
            self.logger.error(f"创建上下文快照错误: {str(e)}")
            raise
    
    async def restore_snapshot(
        self,
        context_id: str,
        snapshot_id: str,
        selective_keys: Optional[List[str]] = None
    ) -> bool:
        """
        恢复上下文快照
        
        Args:
            context_id: 上下文ID
            snapshot_id: 快照ID
            selective_keys: 选择性恢复的键列表
            
        Returns:
            bool: 是否恢复成功
        """
        try:
            async with self.context_locks[context_id]:
                # 查找快照
                snapshot = None
                for snap in self.snapshots[context_id]:
                    if snap.snapshot_id == snapshot_id:
                        snapshot = snap
                        break
                
                if not snapshot:
                    # 尝试从数据库加载
                    snapshot = await self._load_snapshot(snapshot_id)
                    if not snapshot:
                        self.logger.error(f"快照不存在: {snapshot_id}")
                        return False
                
                # 恢复上下文数据
                for key, entry_data in snapshot.context_data.items():
                    if selective_keys and key not in selective_keys:
                        continue
                    
                    # 重建上下文条目
                    entry = ContextEntry(
                        key=key,
                        value=entry_data["value"],
                        context_type=ContextType(entry_data["context_type"]),
                        scope=ContextScope(entry_data["scope"]),
                        access_level=ContextAccessLevel(entry_data["access_level"]),
                        owner_id=entry_data["owner_id"],
                        created_at=datetime.utcnow(),
                        updated_at=datetime.utcnow(),
                        version=entry_data["version"],
                        metadata=entry_data["metadata"],
                        tags=set(entry_data["tags"])
                    )
                    
                    self.contexts[context_id][key] = entry
                
                # 持久化恢复的上下文
                await self._persist_context(context_id)
                
                self.logger.info(f"上下文快照已恢复: {snapshot_id}")
                return True
        except Exception as e:
            self.logger.error(f"恢复上下文快照错误: {str(e)}")
            return False
    
    async def list_snapshots(self, context_id: str) -> List[Dict[str, Any]]:
        """
        列出上下文快照
        
        Args:
            context_id: 上下文ID
            
        Returns:
            List[Dict[str, Any]]: 快照信息列表
        """
        try:
            snapshots = []
            for snapshot in self.snapshots[context_id]:
                snapshots.append({
                    "snapshot_id": snapshot.snapshot_id,
                    "execution_id": snapshot.execution_id,
                    "node_id": snapshot.node_id,
                    "created_at": snapshot.created_at.isoformat(),
                    "description": snapshot.description,
                    "metadata": snapshot.metadata,
                    "data_keys": list(snapshot.context_data.keys())
                })
            
            return snapshots
        except Exception as e:
            self.logger.error(f"列出快照错误: {str(e)}")
            return []
    
    async def clear_context(self, context_id: str, scope: Optional[ContextScope] = None) -> bool:
        """
        清空上下文
        
        Args:
            context_id: 上下文ID
            scope: 作用域（可选，仅清空指定作用域）
            
        Returns:
            bool: 是否清空成功
        """
        try:
            async with self.context_locks[context_id]:
                if context_id not in self.contexts:
                    return False
                
                if scope:
                    # 清空指定作用域
                    keys_to_remove = []
                    for key, entry in self.contexts[context_id].items():
                        if entry.scope == scope:
                            keys_to_remove.append(key)
                    
                    for key in keys_to_remove:
                        await self._remove_context_entry(context_id, key)
                else:
                    # 清空所有
                    self.contexts[context_id].clear()
                
                # 持久化
                await self._persist_context(context_id)
                
                self.logger.info(f"上下文已清空: {context_id}")
                return True
        except Exception as e:
            self.logger.error(f"清空上下文错误: {str(e)}")
            return False
    
    async def destroy_context(self, context_id: str) -> bool:
        """
        销毁上下文
        
        Args:
            context_id: 上下文ID
            
        Returns:
            bool: 是否销毁成功
        """
        try:
            # 清理内存中的数据
            if context_id in self.contexts:
                del self.contexts[context_id]
            
            if context_id in self.snapshots:
                del self.snapshots[context_id]
            
            if context_id in self.context_locks:
                del self.context_locks[context_id]
            
            # 清理执行映射
            execution_id_to_remove = None
            for exec_id, ctx_id in self.execution_contexts.items():
                if ctx_id == context_id:
                    execution_id_to_remove = exec_id
                    break
            
            if execution_id_to_remove:
                del self.execution_contexts[execution_id_to_remove]
            
            # 从数据库删除
            await self._delete_context_from_db(context_id)
            
            self.logger.info(f"上下文已销毁: {context_id}")
            return True
        except Exception as e:
            self.logger.error(f"销毁上下文错误: {str(e)}")
            return False
    
    async def _check_context_size(self, context_id: str, key: str, value: Any) -> bool:
        """
        检查上下文大小是否超限
        
        Args:
            context_id: 上下文ID
            key: 键
            value: 值
            
        Returns:
            bool: 是否超限
        """
        try:
            # 估算值的大小
            value_size = len(json.dumps(value, ensure_ascii=False).encode('utf-8'))
            
            # 计算当前上下文大小
            current_size = 0
            for existing_key, entry in self.contexts[context_id].items():
                if existing_key != key:  # 排除要更新的键
                    try:
                        entry_size = len(json.dumps(entry.value, ensure_ascii=False).encode('utf-8'))
                        current_size += entry_size
                    except (TypeError, ValueError):
                        # 无法序列化的值估算为1KB
                        current_size += 1024
            
            return (current_size + value_size) > self.max_context_size
        except Exception:
            # 出错时保守估计
            return False
    
    async def _remove_context_entry(self, context_id: str, key: str) -> None:
        """
        删除上下文条目
        
        Args:
            context_id: 上下文ID
            key: 键
        """
        if context_id in self.contexts and key in self.contexts[context_id]:
            del self.contexts[context_id][key]
            
            # 异步持久化
            asyncio.create_task(self._delete_context_entry_from_db(context_id, key))
    
    async def _cleanup_loop(self) -> None:
        """
        清理循环
        """
        while self.is_running:
            try:
                await self._cleanup_expired_contexts()
                await asyncio.sleep(self.cleanup_interval)
            except Exception as e:
                self.logger.error(f"清理循环错误: {str(e)}")
                await asyncio.sleep(60)
    
    async def _cleanup_expired_contexts(self) -> None:
        """
        清理过期的上下文
        """
        try:
            current_time = datetime.utcnow()
            expired_entries = []
            
            for context_id, context in self.contexts.items():
                for key, entry in context.items():
                    if entry.is_expired():
                        expired_entries.append((context_id, key))
            
            # 删除过期条目
            for context_id, key in expired_entries:
                await self._remove_context_entry(context_id, key)
            
            if expired_entries:
                self.logger.info(f"清理过期上下文条目: {len(expired_entries)}")
        except Exception as e:
            self.logger.error(f"清理过期上下文错误: {str(e)}")
    
    async def _persist_context(self, context_id: str) -> None:
        """
        持久化上下文到数据库
        
        Args:
            context_id: 上下文ID
        """
        try:
            # 这里应该实现数据库持久化逻辑
            # 由于篇幅限制，这里只是占位符
            pass
        except Exception as e:
            self.logger.error(f"持久化上下文错误: {str(e)}")
    
    async def _persist_context_entry(self, context_id: str, key: str) -> None:
        """
        持久化上下文条目到数据库
        
        Args:
            context_id: 上下文ID
            key: 键
        """
        try:
            # 这里应该实现数据库持久化逻辑
            pass
        except Exception as e:
            self.logger.error(f"持久化上下文条目错误: {str(e)}")
    
    async def _persist_snapshot(self, snapshot: ContextSnapshot) -> None:
        """
        持久化快照到数据库
        
        Args:
            snapshot: 上下文快照
        """
        try:
            # 这里应该实现数据库持久化逻辑
            pass
        except Exception as e:
            self.logger.error(f"持久化快照错误: {str(e)}")
    
    async def _load_snapshot(self, snapshot_id: str) -> Optional[ContextSnapshot]:
        """
        从数据库加载快照
        
        Args:
            snapshot_id: 快照ID
            
        Returns:
            Optional[ContextSnapshot]: 快照对象
        """
        try:
            # 这里应该实现数据库加载逻辑
            return None
        except Exception as e:
            self.logger.error(f"加载快照错误: {str(e)}")
            return None
    
    async def _delete_context_from_db(self, context_id: str) -> None:
        """
        从数据库删除上下文
        
        Args:
            context_id: 上下文ID
        """
        try:
            # 这里应该实现数据库删除逻辑
            pass
        except Exception as e:
            self.logger.error(f"删除上下文错误: {str(e)}")
    
    async def _delete_context_entry_from_db(self, context_id: str, key: str) -> None:
        """
        从数据库删除上下文条目
        
        Args:
            context_id: 上下文ID
            key: 键
        """
        try:
            # 这里应该实现数据库删除逻辑
            pass
        except Exception as e:
            self.logger.error(f"删除上下文条目错误: {str(e)}")
    
    async def get_context_statistics(self, context_id: str) -> Dict[str, Any]:
        """
        获取上下文统计信息
        
        Args:
            context_id: 上下文ID
            
        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            if context_id not in self.contexts:
                return {}
            
            context = self.contexts[context_id]
            
            # 统计信息
            total_entries = len(context)
            expired_entries = sum(1 for entry in context.values() if entry.is_expired())
            
            # 按类型统计
            type_stats = defaultdict(int)
            for entry in context.values():
                if not entry.is_expired():
                    type_stats[entry.context_type.value] += 1
            
            # 按作用域统计
            scope_stats = defaultdict(int)
            for entry in context.values():
                if not entry.is_expired():
                    scope_stats[entry.scope.value] += 1
            
            # 计算总大小
            total_size = 0
            for entry in context.values():
                if not entry.is_expired():
                    try:
                        entry_size = len(json.dumps(entry.value, ensure_ascii=False).encode('utf-8'))
                        total_size += entry_size
                    except (TypeError, ValueError):
                        total_size += 1024  # 估算
            
            return {
                "context_id": context_id,
                "total_entries": total_entries,
                "active_entries": total_entries - expired_entries,
                "expired_entries": expired_entries,
                "total_size_bytes": total_size,
                "type_distribution": dict(type_stats),
                "scope_distribution": dict(scope_stats),
                "snapshots_count": len(self.snapshots.get(context_id, [])),
                "max_size_bytes": self.max_context_size
            }
        except Exception as e:
            self.logger.error(f"获取上下文统计错误: {str(e)}")
            return {}