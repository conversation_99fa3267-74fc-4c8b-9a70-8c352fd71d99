# -*- coding: utf-8 -*-
"""
A2A多智能体系统智能体服务

基于Google ADK的智能体管理和执行服务
"""

import json
import uuid
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Union
from sqlalchemy import and_, or_, desc, func
from sqlalchemy.orm import Session, selectinload
from loguru import logger

from app.core.database import get_database_manager
from app.models.agent import Agent
from app.models.config import AgentConfig
from app.models.user import User
from app.services.auth_service import AuthService
from google.adk.agents.base_agent import BaseAgent

from adk.agents.agent_factory import AgentFactory

from app.core.config import get_settings


class AgentService:
    """
    智能体服务
    
    提供智能体的创建、管理、执行和监控功能
    """
    
    def __init__(self):
        self.db_manager = get_database_manager()
        self.auth_service = AuthService()

        self.agent_factory = AgentFactory()

        self.settings = get_settings()
    
    async def create_agent(self, user_id: int, agent_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        创建智能体
        
        Args:
            user_id: 用户ID
            agent_data: 智能体数据
            
        Returns:
            Dict[str, Any]: 创建结果
            
        Raises:
            ValueError: 参数无效
            Exception: 创建失败
        """
        async with self.db_manager.get_session() as session:
            try:
                # 验证用户权限
                has_permission = await self.auth_service.check_permission(
                    user_id, "agent", None, "create"
                )
                if not has_permission:
                    raise ValueError("没有创建智能体的权限")
                
                # 验证必需字段
                required_fields = ["name", "type", "description"]
                for field in required_fields:
                    if field not in agent_data:
                        raise ValueError(f"缺少必需字段: {field}")
                
                # 检查智能体名称是否重复
                existing_result = await session.execute(
                    session.query(Agent).filter(
                        and_(
                            Agent.user_id == user_id,
                            Agent.name == agent_data["name"],
                            Agent.deleted_at.is_(None)
                        )
                    )
                )
                if existing_result.scalar_one_or_none():
                    raise ValueError("智能体名称已存在")
                
                # 验证智能体配置
                config_data = agent_data.get("config", {})
                validated_config = await self._validate_agent_config(
                    agent_data["type"], config_data
                )
                
                # 创建智能体记录
                agent = Agent(
                    id=str(uuid.uuid4()),
                    user_id=user_id,
                    name=agent_data["name"],
                    type=agent_data["type"],
                    description=agent_data["description"],
                    version=agent_data.get("version", "1.0.0"),
                    status="inactive",
                    is_public=agent_data.get("is_public", False),
                    tags=json.dumps(agent_data.get("tags", [])),
                    metadata=json.dumps(agent_data.get("metadata", {})),
                    created_at=datetime.utcnow(),
                    updated_at=datetime.utcnow()
                )
                
                session.add(agent)
                await session.flush()  # 获取agent.id
                
                # 创建智能体配置
                agent_config = AgentConfig(
                    id=str(uuid.uuid4()),
                    agent_id=agent.id,
                    config_data=json.dumps(validated_config),
                    is_active=True,
                    created_at=datetime.utcnow()
                )
                
                session.add(agent_config)
                
                # 初始化智能体指标
                agent_metrics = AgentMetrics(
                    id=str(uuid.uuid4()),
                    agent_id=agent.id,
                    total_executions=0,
                    successful_executions=0,
                    failed_executions=0,
                    average_execution_time=0.0,
                    total_tokens_used=0,
                    total_cost=0.0,
                    last_execution_at=None,
                    created_at=datetime.utcnow(),
                    updated_at=datetime.utcnow()
                )
                
                session.add(agent_metrics)
                
                await session.commit()
                
                logger.info(f"智能体创建成功: {agent.name}", extra={
                    "user_id": user_id,
                    "agent_id": agent.id,
                    "agent_type": agent.type
                })
                
                return {
                    "success": True,
                    "message": "智能体创建成功",
                    "data": {
                        "agent_id": agent.id,
                        "name": agent.name,
                        "type": agent.type,
                        "status": agent.status,
                        "created_at": agent.created_at.isoformat()
                    }
                }
                
            except ValueError as e:
                await session.rollback()
                logger.warning(f"智能体创建失败: {str(e)}")
                raise
            except Exception as e:
                await session.rollback()
                logger.error(f"智能体创建异常: {str(e)}")
                raise Exception(f"智能体创建失败: {str(e)}")
    
    async def get_agent(self, user_id: int, agent_id: str) -> Dict[str, Any]:
        """
        获取智能体信息
        
        Args:
            user_id: 用户ID
            agent_id: 智能体ID
            
        Returns:
            Dict[str, Any]: 智能体信息
            
        Raises:
            ValueError: 智能体不存在或无权限
        """
        async with self.db_manager.get_session() as session:
            try:
                # 查询智能体
                result = await session.execute(
                    session.query(Agent)
                    .options(
                        selectinload(Agent.configs),
                        selectinload(Agent.metrics),
                        selectinload(Agent.executions)
                    )
                    .filter(
                        and_(
                            Agent.id == agent_id,
                            Agent.deleted_at.is_(None)
                        )
                    )
                )
                agent = result.scalar_one_or_none()
                
                if not agent:
                    raise ValueError("智能体不存在")
                
                # 检查访问权限
                if agent.user_id != user_id:
                    # 检查是否有读取权限或智能体是否公开
                    has_permission = await self.auth_service.check_permission(
                        user_id, "agent", agent_id, "read"
                    )
                    if not has_permission and not agent.is_public:
                        raise ValueError("没有访问权限")
                
                # 获取最新配置
                latest_config = None
                if agent.configs:
                    latest_config = max(agent.configs, key=lambda c: c.created_at)
                
                # 获取指标
                metrics = agent.metrics[0] if agent.metrics else None
                
                return {
                    "agent_id": agent.id,
                    "name": agent.name,
                    "type": agent.type,
                    "description": agent.description,
                    "version": agent.version,
                    "status": agent.status,
                    "is_public": agent.is_public,
                    "tags": json.loads(agent.tags) if agent.tags else [],
                    "metadata": json.loads(agent.metadata) if agent.metadata else {},
                    "config": json.loads(latest_config.config_data) if latest_config else {},
                    "metrics": {
                        "total_executions": metrics.total_executions if metrics else 0,
                        "successful_executions": metrics.successful_executions if metrics else 0,
                        "failed_executions": metrics.failed_executions if metrics else 0,
                        "average_execution_time": metrics.average_execution_time if metrics else 0.0,
                        "total_tokens_used": metrics.total_tokens_used if metrics else 0,
                        "total_cost": metrics.total_cost if metrics else 0.0,
                        "last_execution_at": metrics.last_execution_at.isoformat() if metrics and metrics.last_execution_at else None
                    },
                    "created_at": agent.created_at.isoformat(),
                    "updated_at": agent.updated_at.isoformat()
                }
                
            except ValueError as e:
                logger.warning(f"获取智能体失败: {str(e)}")
                raise
            except Exception as e:
                logger.error(f"获取智能体异常: {str(e)}")
                raise Exception(f"获取智能体失败: {str(e)}")
    
    async def update_agent(self, user_id: int, agent_id: str, update_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        更新智能体
        
        Args:
            user_id: 用户ID
            agent_id: 智能体ID
            update_data: 更新数据
            
        Returns:
            Dict[str, Any]: 更新结果
        """
        async with self.db_manager.get_session() as session:
            try:
                # 检查所有权
                is_owner = await self.auth_service.check_resource_ownership(
                    user_id, "agent", agent_id
                )
                if not is_owner:
                    raise ValueError("没有更新权限")
                
                # 查询智能体
                result = await session.execute(
                    session.query(Agent).filter(
                        and_(
                            Agent.id == agent_id,
                            Agent.user_id == user_id,
                            Agent.deleted_at.is_(None)
                        )
                    )
                )
                agent = result.scalar_one_or_none()
                
                if not agent:
                    raise ValueError("智能体不存在")
                
                # 更新基本信息
                if "name" in update_data:
                    # 检查名称是否重复
                    existing_result = await session.execute(
                        session.query(Agent).filter(
                            and_(
                                Agent.user_id == user_id,
                                Agent.name == update_data["name"],
                                Agent.id != agent_id,
                                Agent.deleted_at.is_(None)
                            )
                        )
                    )
                    if existing_result.scalar_one_or_none():
                        raise ValueError("智能体名称已存在")
                    agent.name = update_data["name"]
                
                if "description" in update_data:
                    agent.description = update_data["description"]
                
                if "version" in update_data:
                    agent.version = update_data["version"]
                
                if "is_public" in update_data:
                    agent.is_public = update_data["is_public"]
                
                if "tags" in update_data:
                    agent.tags = json.dumps(update_data["tags"])
                
                if "metadata" in update_data:
                    agent.metadata = json.dumps(update_data["metadata"])
                
                # 更新配置
                if "config" in update_data:
                    validated_config = await self._validate_agent_config(
                        agent.type, update_data["config"]
                    )
                    
                    # 创建新的配置版本
                    new_config = AgentConfig(
                        id=str(uuid.uuid4()),
                        agent_id=agent.id,
                        config_data=json.dumps(validated_config),
                        is_active=True,
                        created_at=datetime.utcnow()
                    )
                    
                    # 停用旧配置
                    await session.execute(
                        session.query(AgentConfig)
                        .filter(
                            and_(
                                AgentConfig.agent_id == agent.id,
                                AgentConfig.is_active == True
                            )
                        )
                        .update({"is_active": False})
                    )
                    
                    session.add(new_config)
                
                agent.updated_at = datetime.utcnow()
                await session.commit()
                
                logger.info(f"智能体更新成功: {agent.name}", extra={
                    "user_id": user_id,
                    "agent_id": agent.id
                })
                
                return {
                    "success": True,
                    "message": "智能体更新成功",
                    "data": {
                        "agent_id": agent.id,
                        "updated_at": agent.updated_at.isoformat()
                    }
                }
                
            except ValueError as e:
                await session.rollback()
                logger.warning(f"智能体更新失败: {str(e)}")
                raise
            except Exception as e:
                await session.rollback()
                logger.error(f"智能体更新异常: {str(e)}")
                raise Exception(f"智能体更新失败: {str(e)}")
    
    async def delete_agent(self, user_id: int, agent_id: str, hard_delete: bool = False) -> Dict[str, Any]:
        """
        删除智能体
        
        Args:
            user_id: 用户ID
            agent_id: 智能体ID
            hard_delete: 是否硬删除
            
        Returns:
            Dict[str, Any]: 删除结果
        """
        async with self.db_manager.get_session() as session:
            try:
                # 检查所有权
                is_owner = await self.auth_service.check_resource_ownership(
                    user_id, "agent", agent_id
                )
                if not is_owner:
                    raise ValueError("没有删除权限")
                
                # 查询智能体
                result = await session.execute(
                    session.query(Agent).filter(
                        and_(
                            Agent.id == agent_id,
                            Agent.user_id == user_id,
                            Agent.deleted_at.is_(None) if not hard_delete else True
                        )
                    )
                )
                agent = result.scalar_one_or_none()
                
                if not agent:
                    raise ValueError("智能体不存在")
                
                if hard_delete:
                    # 硬删除：删除所有相关记录
                    await session.execute(
                        session.query(AgentConfig).filter(
                            AgentConfig.agent_id == agent_id
                        ).delete()
                    )
                    
                    await session.execute(
                        session.query(AgentExecution).filter(
                            AgentExecution.agent_id == agent_id
                        ).delete()
                    )
                    
                    await session.execute(
                        session.query(AgentMetrics).filter(
                            AgentMetrics.agent_id == agent_id
                        ).delete()
                    )
                    
                    await session.delete(agent)
                    
                    logger.info(f"智能体硬删除成功: {agent.name}", extra={
                        "user_id": user_id,
                        "agent_id": agent.id
                    })
                    
                    message = "智能体已永久删除"
                else:
                    # 软删除：标记删除时间
                    agent.deleted_at = datetime.utcnow()
                    agent.status = "deleted"
                    agent.updated_at = datetime.utcnow()
                    
                    logger.info(f"智能体软删除成功: {agent.name}", extra={
                        "user_id": user_id,
                        "agent_id": agent.id
                    })
                    
                    message = "智能体已删除"
                
                await session.commit()
                
                return {
                    "success": True,
                    "message": message
                }
                
            except ValueError as e:
                await session.rollback()
                logger.warning(f"智能体删除失败: {str(e)}")
                raise
            except Exception as e:
                await session.rollback()
                logger.error(f"智能体删除异常: {str(e)}")
                raise Exception(f"智能体删除失败: {str(e)}")
    
    async def list_agents(self, user_id: int, filters: Optional[Dict[str, Any]] = None, 
                         page: int = 1, page_size: int = 20) -> Dict[str, Any]:
        """
        获取智能体列表
        
        Args:
            user_id: 用户ID
            filters: 过滤条件
            page: 页码
            page_size: 页面大小
            
        Returns:
            Dict[str, Any]: 智能体列表
        """
        async with self.db_manager.get_session() as session:
            try:
                # 构建查询条件
                query_conditions = [
                    Agent.deleted_at.is_(None)
                ]
                
                # 权限过滤：只显示用户自己的智能体或公开的智能体
                query_conditions.append(
                    or_(
                        Agent.user_id == user_id,
                        Agent.is_public == True
                    )
                )
                
                # 应用过滤条件
                if filters:
                    if "type" in filters:
                        query_conditions.append(Agent.type == filters["type"])
                    
                    if "status" in filters:
                        query_conditions.append(Agent.status == filters["status"])
                    
                    if "name" in filters:
                        query_conditions.append(
                            Agent.name.ilike(f"%{filters['name']}%")
                        )
                    
                    if "tags" in filters:
                        for tag in filters["tags"]:
                            query_conditions.append(
                                Agent.tags.like(f'%"{tag}"%')
                            )
                    
                    if "is_public" in filters:
                        query_conditions.append(Agent.is_public == filters["is_public"])
                
                # 计算总数
                count_result = await session.execute(
                    session.query(func.count(Agent.id)).filter(
                        and_(*query_conditions)
                    )
                )
                total = count_result.scalar()
                
                # 分页查询
                offset = (page - 1) * page_size
                result = await session.execute(
                    session.query(Agent)
                    .options(selectinload(Agent.metrics))
                    .filter(and_(*query_conditions))
                    .order_by(desc(Agent.updated_at))
                    .offset(offset)
                    .limit(page_size)
                )
                agents = result.scalars().all()
                
                # 格式化结果
                agent_list = []
                for agent in agents:
                    metrics = agent.metrics[0] if agent.metrics else None
                    agent_list.append({
                        "agent_id": agent.id,
                        "name": agent.name,
                        "type": agent.type,
                        "description": agent.description,
                        "version": agent.version,
                        "status": agent.status,
                        "is_public": agent.is_public,
                        "is_owner": agent.user_id == user_id,
                        "tags": json.loads(agent.tags) if agent.tags else [],
                        "metrics": {
                            "total_executions": metrics.total_executions if metrics else 0,
                            "success_rate": (
                                metrics.successful_executions / metrics.total_executions * 100
                                if metrics and metrics.total_executions > 0 else 0
                            ),
                            "last_execution_at": metrics.last_execution_at.isoformat() if metrics and metrics.last_execution_at else None
                        },
                        "created_at": agent.created_at.isoformat(),
                        "updated_at": agent.updated_at.isoformat()
                    })
                
                return {
                    "agents": agent_list,
                    "pagination": {
                        "page": page,
                        "page_size": page_size,
                        "total": total,
                        "pages": (total + page_size - 1) // page_size
                    }
                }
                
            except Exception as e:
                logger.error(f"获取智能体列表异常: {str(e)}")
                raise Exception(f"获取智能体列表失败: {str(e)}")
    
    async def execute_agent(self, user_id: int, agent_id: str, 
                           execution_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行智能体
        
        Args:
            user_id: 用户ID
            agent_id: 智能体ID
            execution_data: 执行数据
            
        Returns:
            Dict[str, Any]: 执行结果
        """
        execution_id = str(uuid.uuid4())
        start_time = datetime.utcnow()
        
        async with self.db_manager.get_session() as session:
            try:
                # 检查执行权限
                is_owner = await self.auth_service.check_resource_ownership(
                    user_id, "agent", agent_id
                )
                has_execute_permission = await self.auth_service.check_permission(
                    user_id, "agent", agent_id, "execute"
                )
                
                if not is_owner and not has_execute_permission:
                    raise ValueError("没有执行权限")
                
                # 获取智能体和配置
                agent_result = await session.execute(
                    session.query(Agent)
                    .options(selectinload(Agent.configs))
                    .filter(
                        and_(
                            Agent.id == agent_id,
                            Agent.deleted_at.is_(None),
                            Agent.status.in_(["active", "inactive"])
                        )
                    )
                )
                agent = agent_result.scalar_one_or_none()
                
                if not agent:
                    raise ValueError("智能体不存在或不可用")
                
                # 获取最新配置
                active_config = None
                for config in agent.configs:
                    if config.is_active:
                        active_config = config
                        break
                
                if not active_config:
                    raise ValueError("智能体配置不存在")
                
                # 创建执行记录
                execution = AgentExecution(
                    id=execution_id,
                    agent_id=agent_id,
                    user_id=user_id,
                    input_data=json.dumps(execution_data.get("input", {})),
                    status="running",
                    started_at=start_time,
                    created_at=start_time
                )
                
                session.add(execution)
                await session.commit()
                
                # 更新智能体状态
                agent.status = "running"
                agent.updated_at = datetime.utcnow()
                await session.commit()
                
                logger.info(f"开始执行智能体: {agent.name}", extra={
                    "user_id": user_id,
                    "agent_id": agent_id,
                    "execution_id": execution_id
                })
                
                # 执行智能体
                try:
                    # 创建智能体实例
                    agent_instance = await self.agent_factory.create_agent(
                        agent.type,
                        json.loads(active_config.config_data)
                    )
                    
                    # 执行智能体
                    result = await agent_instance.execute(
                        execution_data.get("input", {}),
                        context={
                            "user_id": user_id,
                            "agent_id": agent_id,
                            "execution_id": execution_id
                        }
                    )
                    
                    end_time = datetime.utcnow()
                    execution_time = (end_time - start_time).total_seconds()
                    
                    # 更新执行记录
                    execution.status = "completed"
                    execution.output_data = json.dumps(result)
                    execution.completed_at = end_time
                    execution.execution_time = execution_time
                    execution.tokens_used = result.get("tokens_used", 0)
                    execution.cost = result.get("cost", 0.0)
                    
                    # 更新智能体状态和指标
                    agent.status = "active"
                    await self._update_agent_metrics(
                        session, agent_id, execution_time, 
                        execution.tokens_used, execution.cost, True
                    )
                    
                    await session.commit()
                    
                    logger.info(f"智能体执行成功: {agent.name}", extra={
                        "user_id": user_id,
                        "agent_id": agent_id,
                        "execution_id": execution_id,
                        "execution_time": execution_time
                    })
                    
                    return {
                        "success": True,
                        "message": "智能体执行成功",
                        "data": {
                            "execution_id": execution_id,
                            "result": result,
                            "execution_time": execution_time,
                            "tokens_used": execution.tokens_used,
                            "cost": execution.cost
                        }
                    }
                    
                except Exception as exec_error:
                    # 执行失败
                    end_time = datetime.utcnow()
                    execution_time = (end_time - start_time).total_seconds()
                    
                    execution.status = "failed"
                    execution.error_message = str(exec_error)
                    execution.completed_at = end_time
                    execution.execution_time = execution_time
                    
                    agent.status = "error"
                    await self._update_agent_metrics(
                        session, agent_id, execution_time, 0, 0.0, False
                    )
                    
                    await session.commit()
                    
                    logger.error(f"智能体执行失败: {agent.name}", extra={
                        "user_id": user_id,
                        "agent_id": agent_id,
                        "execution_id": execution_id,
                        "error": str(exec_error)
                    })
                    
                    raise Exception(f"智能体执行失败: {str(exec_error)}")
                
            except ValueError as e:
                await session.rollback()
                logger.warning(f"智能体执行失败: {str(e)}")
                raise
            except Exception as e:
                await session.rollback()
                logger.error(f"智能体执行异常: {str(e)}")
                raise Exception(f"智能体执行失败: {str(e)}")
    
    async def get_agent_executions(self, user_id: int, agent_id: str, 
                                  page: int = 1, page_size: int = 20) -> Dict[str, Any]:
        """
        获取智能体执行历史
        
        Args:
            user_id: 用户ID
            agent_id: 智能体ID
            page: 页码
            page_size: 页面大小
            
        Returns:
            Dict[str, Any]: 执行历史
        """
        async with self.db_manager.get_session() as session:
            try:
                # 检查访问权限
                is_owner = await self.auth_service.check_resource_ownership(
                    user_id, "agent", agent_id
                )
                has_read_permission = await self.auth_service.check_permission(
                    user_id, "agent", agent_id, "read"
                )
                
                if not is_owner and not has_read_permission:
                    raise ValueError("没有访问权限")
                
                # 计算总数
                count_result = await session.execute(
                    session.query(func.count(AgentExecution.id)).filter(
                        AgentExecution.agent_id == agent_id
                    )
                )
                total = count_result.scalar()
                
                # 分页查询
                offset = (page - 1) * page_size
                result = await session.execute(
                    session.query(AgentExecution)
                    .filter(AgentExecution.agent_id == agent_id)
                    .order_by(desc(AgentExecution.started_at))
                    .offset(offset)
                    .limit(page_size)
                )
                executions = result.scalars().all()
                
                # 格式化结果
                execution_list = []
                for execution in executions:
                    execution_list.append({
                        "execution_id": execution.id,
                        "status": execution.status,
                        "input_data": json.loads(execution.input_data) if execution.input_data else {},
                        "output_data": json.loads(execution.output_data) if execution.output_data else {},
                        "error_message": execution.error_message,
                        "execution_time": execution.execution_time,
                        "tokens_used": execution.tokens_used,
                        "cost": execution.cost,
                        "started_at": execution.started_at.isoformat(),
                        "completed_at": execution.completed_at.isoformat() if execution.completed_at else None
                    })
                
                return {
                    "executions": execution_list,
                    "pagination": {
                        "page": page,
                        "page_size": page_size,
                        "total": total,
                        "pages": (total + page_size - 1) // page_size
                    }
                }
                
            except ValueError as e:
                logger.warning(f"获取执行历史失败: {str(e)}")
                raise
            except Exception as e:
                logger.error(f"获取执行历史异常: {str(e)}")
                raise Exception(f"获取执行历史失败: {str(e)}")
    
    async def get_agent_metrics(self, user_id: int, agent_id: str) -> Dict[str, Any]:
        """
        获取智能体指标
        
        Args:
            user_id: 用户ID
            agent_id: 智能体ID
            
        Returns:
            Dict[str, Any]: 智能体指标
        """
        async with self.db_manager.get_session() as session:
            try:
                # 检查访问权限
                is_owner = await self.auth_service.check_resource_ownership(
                    user_id, "agent", agent_id
                )
                has_read_permission = await self.auth_service.check_permission(
                    user_id, "agent", agent_id, "read"
                )
                
                if not is_owner and not has_read_permission:
                    raise ValueError("没有访问权限")
                
                # 获取指标
                result = await session.execute(
                    session.query(AgentMetrics).filter(
                        AgentMetrics.agent_id == agent_id
                    )
                )
                metrics = result.scalar_one_or_none()
                
                if not metrics:
                    return {
                        "total_executions": 0,
                        "successful_executions": 0,
                        "failed_executions": 0,
                        "success_rate": 0.0,
                        "average_execution_time": 0.0,
                        "total_tokens_used": 0,
                        "total_cost": 0.0,
                        "last_execution_at": None
                    }
                
                success_rate = (
                    metrics.successful_executions / metrics.total_executions * 100
                    if metrics.total_executions > 0 else 0
                )
                
                return {
                    "total_executions": metrics.total_executions,
                    "successful_executions": metrics.successful_executions,
                    "failed_executions": metrics.failed_executions,
                    "success_rate": round(success_rate, 2),
                    "average_execution_time": round(metrics.average_execution_time, 3),
                    "total_tokens_used": metrics.total_tokens_used,
                    "total_cost": round(metrics.total_cost, 4),
                    "last_execution_at": metrics.last_execution_at.isoformat() if metrics.last_execution_at else None,
                    "updated_at": metrics.updated_at.isoformat()
                }
                
            except ValueError as e:
                logger.warning(f"获取智能体指标失败: {str(e)}")
                raise
            except Exception as e:
                logger.error(f"获取智能体指标异常: {str(e)}")
                raise Exception(f"获取智能体指标失败: {str(e)}")
    
    async def _validate_agent_config(self, agent_type: str, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        验证智能体配置
        
        Args:
            agent_type: 智能体类型
            config: 配置数据
            
        Returns:
            Dict[str, Any]: 验证后的配置
            
        Raises:
            ValueError: 配置无效
        """
        try:
            # 根据智能体类型验证配置
            if agent_type == "chat":
                required_fields = ["model", "temperature"]
                for field in required_fields:
                    if field not in config:
                        raise ValueError(f"聊天智能体缺少必需配置: {field}")
                
                if not 0 <= config["temperature"] <= 2:
                    raise ValueError("temperature 必须在 0-2 之间")
            
            elif agent_type == "task":
                required_fields = ["model", "max_steps"]
                for field in required_fields:
                    if field not in config:
                        raise ValueError(f"任务智能体缺少必需配置: {field}")
                
                if config["max_steps"] <= 0:
                    raise ValueError("max_steps 必须大于 0")
            
            elif agent_type == "workflow":
                required_fields = ["steps"]
                for field in required_fields:
                    if field not in config:
                        raise ValueError(f"工作流智能体缺少必需配置: {field}")
                
                if not isinstance(config["steps"], list) or len(config["steps"]) == 0:
                    raise ValueError("工作流必须包含至少一个步骤")
            
            # 验证通用配置
            if "max_tokens" in config and config["max_tokens"] <= 0:
                raise ValueError("max_tokens 必须大于 0")
            
            if "timeout" in config and config["timeout"] <= 0:
                raise ValueError("timeout 必须大于 0")
            
            return config
            
        except Exception as e:
            logger.error(f"智能体配置验证异常: {str(e)}")
            raise ValueError(f"配置验证失败: {str(e)}")
    
    async def _update_agent_metrics(self, session: Session, agent_id: str, 
                                   execution_time: float, tokens_used: int, 
                                   cost: float, success: bool):
        """
        更新智能体指标
        
        Args:
            session: 数据库会话
            agent_id: 智能体ID
            execution_time: 执行时间
            tokens_used: 使用的令牌数
            cost: 成本
            success: 是否成功
        """
        try:
            # 获取现有指标
            result = await session.execute(
                session.query(AgentMetrics).filter(
                    AgentMetrics.agent_id == agent_id
                )
            )
            metrics = result.scalar_one_or_none()
            
            if metrics:
                # 更新指标
                metrics.total_executions += 1
                if success:
                    metrics.successful_executions += 1
                else:
                    metrics.failed_executions += 1
                
                # 计算平均执行时间
                total_time = metrics.average_execution_time * (metrics.total_executions - 1) + execution_time
                metrics.average_execution_time = total_time / metrics.total_executions
                
                metrics.total_tokens_used += tokens_used
                metrics.total_cost += cost
                metrics.last_execution_at = datetime.utcnow()
                metrics.updated_at = datetime.utcnow()
            
        except Exception as e:
            logger.error(f"更新智能体指标异常: {str(e)}")