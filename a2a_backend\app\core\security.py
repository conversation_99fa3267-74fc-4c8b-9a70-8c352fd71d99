# -*- coding: utf-8 -*-
"""
A2A多智能体系统安全工具模块

提供安全相关的工具函数
"""

from typing import Optional
from fastapi import Request
from app.core.logging import get_logger


logger = get_logger("security")


def get_client_ip(request: Request) -> str:
    """
    获取客户端真实IP地址
    
    Args:
        request: FastAPI请求对象
        
    Returns:
        str: 客户端IP地址
    """
    # 尝试从各种头部获取真实IP
    forwarded_for = request.headers.get("X-Forwarded-For")
    if forwarded_for:
        # X-Forwarded-For可能包含多个IP，取第一个
        ip = forwarded_for.split(",")[0].strip()
        if ip:
            return ip
    
    # 尝试从X-Real-IP获取
    real_ip = request.headers.get("X-Real-IP")
    if real_ip:
        return real_ip.strip()
    
    # 尝试从X-Forwarded-Host获取
    forwarded_host = request.headers.get("X-Forwarded-Host")
    if forwarded_host:
        return forwarded_host.strip()
    
    # 最后使用客户端连接IP
    client_host = getattr(request.client, "host", "unknown")
    return client_host


def get_user_agent(request: Request) -> str:
    """
    获取客户端User-Agent
    
    Args:
        request: FastAPI请求对象
        
    Returns:
        str: User-Agent字符串
    """
    return request.headers.get("User-Agent", "unknown")


def is_safe_url(url: str, allowed_hosts: Optional[list] = None) -> bool:
    """
    检查URL是否安全（防止开放重定向攻击）
    
    Args:
        url: 要检查的URL
        allowed_hosts: 允许的主机列表
        
    Returns:
        bool: URL是否安全
    """
    if not url:
        return False
    
    # 简单的安全检查
    if url.startswith("//") or url.startswith("http://") or url.startswith("https://"):
        if allowed_hosts:
            from urllib.parse import urlparse
            parsed = urlparse(url)
            return parsed.netloc in allowed_hosts
        return False
    
    # 相对URL被认为是安全的
    return url.startswith("/")


def sanitize_filename(filename: str) -> str:
    """
    清理文件名，移除危险字符
    
    Args:
        filename: 原始文件名
        
    Returns:
        str: 清理后的文件名
    """
    import re
    
    # 移除路径分隔符和其他危险字符
    filename = re.sub(r'[<>:"/\\|?*]', '', filename)
    
    # 移除控制字符
    filename = re.sub(r'[\x00-\x1f\x7f-\x9f]', '', filename)
    
    # 限制长度
    if len(filename) > 255:
        name, ext = filename.rsplit('.', 1) if '.' in filename else (filename, '')
        max_name_len = 255 - len(ext) - 1 if ext else 255
        filename = name[:max_name_len] + ('.' + ext if ext else '')
    
    return filename.strip()


def validate_email(email: str) -> bool:
    """
    验证邮箱地址格式
    
    Args:
        email: 邮箱地址
        
    Returns:
        bool: 邮箱格式是否有效
    """
    import re
    
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return bool(re.match(pattern, email))


def generate_csrf_token() -> str:
    """
    生成CSRF令牌
    
    Returns:
        str: CSRF令牌
    """
    import secrets
    return secrets.token_urlsafe(32)


def verify_csrf_token(token: str, expected_token: str) -> bool:
    """
    验证CSRF令牌
    
    Args:
        token: 提交的令牌
        expected_token: 期望的令牌
        
    Returns:
        bool: 令牌是否有效
    """
    import hmac
    return hmac.compare_digest(token, expected_token)