#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A2A多智能体系统 ADK智能体Runner

支持单智能体执行，包含用户权限检查
"""

import logging
from typing import Dict, List, Optional, Any, Union, Callable, Awaitable
from datetime import datetime

from google.ai.generativelanguage import Content
from google.genai import types
from google.adk.agents.llm_agent import Agent

from app.models.agent import Agent as AgentModel
from app.models.task import Task, TaskExecution
from app.core.logging import get_logger

from .base_runner import BaseRunner
from ..services.database_session_service import DatabaseSessionService
from ..services.database_memory_service import DatabaseMemoryService
from ..services.database_artifact_service import DatabaseArtifactService

class AgentRunner(BaseRunner):
    """
    智能体Runner，支持单智能体执行，包含用户权限检查
    
    提供以下功能：
    1. 单智能体执行
    2. 智能体状态管理
    3. 智能体执行历史记录
    4. 智能体执行指标收集
    """
    
    def __init__(
        self,
        user_id: int,
        owner_id: int,
        agent_instance: Agent,
        agent_id: Optional[int] = None,
        agent_config: Optional[Dict[str, Any]] = None,
        session: Optional[DatabaseSessionService] = None,
        memory_service: Optional[DatabaseMemoryService] = None,
        artifact_service: Optional[DatabaseArtifactService] = None,
        task_id: Optional[int] = None,
        execution_id: Optional[str] = None,
        config: Optional[Dict[str, Any]] = None,
        event_handlers: Optional[Dict[str, List[Callable[[Any], Awaitable[None]]]]] = None,
        logger: Optional[logging.Logger] = None
    ):
        """
        初始化智能体Runner
        
        Args:
            user_id: 用户ID
            owner_id: 拥有者ID
            agent_instance: ADK Agent实例
            agent_id: 智能体ID
            agent_config: 智能体配置
            session: 数据库会话服务
            memory_service: 数据库内存服务
            artifact_service: 数据库工件服务
            task_id: 关联的任务ID
            execution_id: 执行ID
            config: Runner配置
            event_handlers: 事件处理器字典
            logger: 日志记录器
        """
        super().__init__(
            user_id=user_id,
            owner_id=owner_id,
            agent_instance=agent_instance,
            session=session,
            task_id=task_id,
            execution_id=execution_id,
            config=config,
            event_handlers=event_handlers,
            logger=logger
        )
        
        self.agent_id = agent_id
        self.agent_config = agent_config or {}
        
        # 创建内存服务和工件服务
        self.memory_service = memory_service or DatabaseMemoryService(user_id=user_id, owner_id=owner_id)
        self.artifact_service = artifact_service or DatabaseArtifactService(user_id=user_id, owner_id=owner_id)
        
        # 执行指标
        self.metrics = {
            "token_count": 0,
            "prompt_tokens": 0,
            "completion_tokens": 0,
            "total_tokens": 0,
            "latency": 0,
            "tool_calls": 0,
            "steps": 0
        }
        
        self.logger.info(f"AgentRunner {self.execution_id} 已初始化，智能体ID: {agent_id}")
    
    async def _load_agent_model(self) -> Optional[AgentModel]:
        """
        加载智能体模型
        
        Returns:
            Optional[AgentModel]: 智能体模型
        """
        if not self.agent_id:
            return None
        
        try:
            agent_model = await AgentModel.get_by_id(self.agent_id)
            if not agent_model:
                self.logger.warning(f"智能体不存在: {self.agent_id}")
                return None
            
            # 检查用户是否有权限访问此智能体
            if agent_model.user_id != self.user_id and agent_model.owner_id != self.owner_id and not agent_model.is_public:
                self.logger.warning(f"用户 {self.user_id} 没有权限访问智能体 {self.agent_id}")
                return None
            
            return agent_model
        except Exception as e:
            self.logger.error(f"加载智能体模型错误: {str(e)}")
            return None
    
    async def _create_task_execution(self) -> Optional[TaskExecution]:
        """
        创建任务执行记录
        
        Returns:
            Optional[TaskExecution]: 任务执行记录
        """
        if not self.task_id:
            return None
        
        try:
            # 检查任务是否存在
            task = await Task.get_by_id(self.task_id)
            if not task:
                self.logger.warning(f"任务不存在: {self.task_id}")
                return None
            
            # 创建任务执行记录
            execution = TaskExecution(
                task_id=self.task_id,
                user_id=self.user_id,
                owner_id=self.owner_id,
                execution_id=self.execution_id,
                agent_id=self.agent_id,
                status="running",
                config=self.config,
                metrics={}
            )
            
            await execution.save()
            return execution
        except Exception as e:
            self.logger.error(f"创建任务执行记录错误: {str(e)}")
            return None
    
    async def _update_task_execution(self, status: str) -> None:
        """
        更新任务执行记录
        
        Args:
            status: 执行状态
        """
        if not self.task_id:
            return
        
        try:
            # 查找任务执行记录
            execution = await TaskExecution.get_by_execution_id(self.execution_id)
            if not execution:
                self.logger.warning(f"任务执行记录不存在: {self.execution_id}")
                return
            
            # 更新状态和指标
            execution.status = status
            execution.metrics = self.metrics
            execution.end_time = datetime.now() if status in ["completed", "failed", "stopped"] else None
            
            await execution.save()
        except Exception as e:
            self.logger.error(f"更新任务执行记录错误: {str(e)}")
    
    async def _update_metrics(self, response: Any) -> None:
        """
        更新执行指标
        
        Args:
            response: 执行响应
        """
        try:
            # 更新令牌计数
            if hasattr(response, "usage") and response.usage:
                self.metrics["prompt_tokens"] += getattr(response.usage, "prompt_tokens", 0)
                self.metrics["completion_tokens"] += getattr(response.usage, "completion_tokens", 0)
                self.metrics["total_tokens"] += getattr(response.usage, "total_tokens", 0)
            
            # 更新工具调用计数
            if hasattr(response, "tool_calls") and response.tool_calls:
                self.metrics["tool_calls"] += len(response.tool_calls)
            
            # 更新步骤计数
            self.metrics["steps"] += 1
        except Exception as e:
            self.logger.error(f"更新指标错误: {str(e)}")
    
    async def run(
        self, 
        input_content: Union[str, Content, types.Content],
        stream: bool = False
    ) -> Any:
        """
        运行智能体Runner
        
        Args:
            input_content: 输入内容
            stream: 是否使用流式输出
            
        Returns:
            Any: 运行结果
        """
        # 加载智能体模型
        agent_model = await self._load_agent_model()
        if self.agent_id and not agent_model:
            raise ValueError(f"无法加载智能体: {self.agent_id}")
        
        # 创建任务执行记录
        execution = await self._create_task_execution()
        
        # 记录开始时间
        start_time = datetime.now()
        
        try:
            # 调用基类的run方法
            result = await super().run(input_content, stream)
            
            # 更新指标
            if not stream:
                await self._update_metrics(result)
            
            # 计算延迟
            end_time = datetime.now()
            self.metrics["latency"] = (end_time - start_time).total_seconds() * 1000  # 毫秒
            
            # 更新任务执行记录
            await self._update_task_execution("completed")
            
            return result
        except Exception as e:
            # 更新任务执行记录为失败
            await self._update_task_execution("failed")
            raise e
    
    async def stop(self) -> None:
        """
        停止智能体Runner
        """
        await super().stop()
        
        # 更新任务执行记录
        await self._update_task_execution("stopped")
    
    def get_state(self) -> Dict[str, Any]:
        """
        获取智能体Runner状态
        
        Returns:
            Dict[str, Any]: Runner状态信息
        """
        state = super().get_state()
        
        # 添加智能体特定信息
        state.update({
            "agent_id": self.agent_id,
            "metrics": self.metrics
        })
        
        return state