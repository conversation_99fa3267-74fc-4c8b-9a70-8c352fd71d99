# -*- coding: utf-8 -*-
"""
A2A多智能体系统事件服务

基于Google ADK的事件驱动架构服务
"""

import asyncio
import json
import time
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable, Union, Set
from dataclasses import dataclass, field
from enum import Enum
import logging
from collections import defaultdict, deque
import weakref
from concurrent.futures import ThreadPoolExecutor

from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc
from cachetools import TTLCache
from loguru import logger
import pickle
import gzip

from ..models import User, Session as SessionModel, Message, Agent
from ..core.database import get_db
from ..auth.permissions import check_user_permission
from .user_service import UserService


class EventType(str, Enum):
    """事件类型枚举"""
    SYSTEM = "system"
    USER = "user"
    AGENT = "agent"
    SESSION = "session"
    MESSAGE = "message"
    WORKFLOW = "workflow"
    TASK = "task"
    STREAM = "stream"
    WEBSOCKET = "websocket"
    ERROR = "error"
    NOTIFICATION = "notification"
    CUSTOM = "custom"


class EventPriority(int, Enum):
    """事件优先级枚举"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    CRITICAL = 4
    EMERGENCY = 5


class EventStatus(str, Enum):
    """事件状态枚举"""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    RETRYING = "retrying"


class DeliveryMode(str, Enum):
    """投递模式枚举"""
    IMMEDIATE = "immediate"
    DELAYED = "delayed"
    SCHEDULED = "scheduled"
    BATCH = "batch"


@dataclass
class EventFilter:
    """事件过滤器"""
    event_types: Optional[Set[EventType]] = None
    sources: Optional[Set[str]] = None
    targets: Optional[Set[str]] = None
    priorities: Optional[Set[EventPriority]] = None
    tags: Optional[Set[str]] = None
    user_ids: Optional[Set[int]] = None
    session_ids: Optional[Set[str]] = None
    agent_ids: Optional[Set[str]] = None
    time_range: Optional[tuple] = None
    metadata_filters: Optional[Dict[str, Any]] = None
    
    def matches(self, event: 'Event') -> bool:
        """检查事件是否匹配过滤器"""
        if self.event_types and event.event_type not in self.event_types:
            return False
        
        if self.sources and event.source not in self.sources:
            return False
        
        if self.targets and event.target and event.target not in self.targets:
            return False
        
        if self.priorities and event.priority not in self.priorities:
            return False
        
        if self.tags and not (set(event.tags) & self.tags):
            return False
        
        if self.user_ids and event.user_id not in self.user_ids:
            return False
        
        if self.session_ids and event.session_id not in self.session_ids:
            return False
        
        if self.agent_ids and event.agent_id not in self.agent_ids:
            return False
        
        if self.time_range:
            start_time, end_time = self.time_range
            if event.timestamp < start_time or event.timestamp > end_time:
                return False
        
        if self.metadata_filters:
            for key, value in self.metadata_filters.items():
                if key not in event.metadata or event.metadata[key] != value:
                    return False
        
        return True


@dataclass
class Event:
    """事件数据类"""
    event_id: str
    event_type: EventType
    source: str
    data: Any
    timestamp: datetime = field(default_factory=datetime.utcnow)
    target: Optional[str] = None
    user_id: Optional[int] = None
    session_id: Optional[str] = None
    agent_id: Optional[str] = None
    priority: EventPriority = EventPriority.NORMAL
    tags: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    correlation_id: Optional[str] = None
    parent_event_id: Optional[str] = None
    retry_count: int = 0
    max_retries: int = 3
    retry_delay: float = 1.0
    ttl: Optional[int] = None  # 生存时间（秒）
    delivery_mode: DeliveryMode = DeliveryMode.IMMEDIATE
    scheduled_time: Optional[datetime] = None
    
    def __post_init__(self):
        if not self.event_id:
            self.event_id = str(uuid.uuid4())
    
    def is_expired(self) -> bool:
        """检查事件是否过期"""
        if not self.ttl:
            return False
        return (datetime.utcnow() - self.timestamp).total_seconds() > self.ttl
    
    def should_retry(self) -> bool:
        """检查是否应该重试"""
        return self.retry_count < self.max_retries
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "event_id": self.event_id,
            "event_type": self.event_type.value,
            "source": self.source,
            "target": self.target,
            "user_id": self.user_id,
            "session_id": self.session_id,
            "agent_id": self.agent_id,
            "priority": self.priority.value,
            "tags": self.tags,
            "data": self.data,
            "metadata": self.metadata,
            "correlation_id": self.correlation_id,
            "parent_event_id": self.parent_event_id,
            "timestamp": self.timestamp.isoformat(),
            "retry_count": self.retry_count,
            "max_retries": self.max_retries,
            "retry_delay": self.retry_delay,
            "ttl": self.ttl,
            "delivery_mode": self.delivery_mode.value,
            "scheduled_time": self.scheduled_time.isoformat() if self.scheduled_time else None
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Event':
        """从字典创建事件"""
        event = cls(
            event_id=data["event_id"],
            event_type=EventType(data["event_type"]),
            source=data["source"],
            data=data["data"],
            timestamp=datetime.fromisoformat(data["timestamp"]),
            target=data.get("target"),
            user_id=data.get("user_id"),
            session_id=data.get("session_id"),
            agent_id=data.get("agent_id"),
            priority=EventPriority(data.get("priority", EventPriority.NORMAL.value)),
            tags=data.get("tags", []),
            metadata=data.get("metadata", {}),
            correlation_id=data.get("correlation_id"),
            parent_event_id=data.get("parent_event_id"),
            retry_count=data.get("retry_count", 0),
            max_retries=data.get("max_retries", 3),
            retry_delay=data.get("retry_delay", 1.0),
            ttl=data.get("ttl"),
            delivery_mode=DeliveryMode(data.get("delivery_mode", DeliveryMode.IMMEDIATE.value))
        )
        
        if data.get("scheduled_time"):
            event.scheduled_time = datetime.fromisoformat(data["scheduled_time"])
        
        return event


@dataclass
class EventSubscription:
    """事件订阅"""
    subscription_id: str
    subscriber_id: str
    event_filter: EventFilter
    callback: Callable[[Event], None]
    created_at: datetime = field(default_factory=datetime.utcnow)
    is_active: bool = True
    delivery_count: int = 0
    last_delivery: Optional[datetime] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        if not self.subscription_id:
            self.subscription_id = str(uuid.uuid4())


@dataclass
class EventDeliveryRecord:
    """事件投递记录"""
    delivery_id: str
    event_id: str
    subscription_id: str
    status: EventStatus
    delivered_at: datetime
    error_message: Optional[str] = None
    retry_count: int = 0
    processing_time: Optional[float] = None
    
    def __post_init__(self):
        if not self.delivery_id:
            self.delivery_id = str(uuid.uuid4())


class EventQueue:
    """事件队列"""
    
    def __init__(self, max_size: int = 10000):
        self.max_size = max_size
        self.queues = {
            priority: deque() for priority in EventPriority
        }
        self.total_size = 0
        self._lock = asyncio.Lock()
    
    async def put(self, event: Event) -> bool:
        """添加事件到队列"""
        async with self._lock:
            if self.total_size >= self.max_size:
                # 移除最低优先级的事件
                for priority in [EventPriority.LOW, EventPriority.NORMAL, EventPriority.HIGH]:
                    if self.queues[priority]:
                        self.queues[priority].popleft()
                        self.total_size -= 1
                        break
                else:
                    return False  # 队列已满且无法移除事件
            
            self.queues[event.priority].append(event)
            self.total_size += 1
            return True
    
    async def get(self) -> Optional[Event]:
        """从队列获取事件（按优先级）"""
        async with self._lock:
            # 按优先级顺序获取事件
            for priority in [EventPriority.EMERGENCY, EventPriority.CRITICAL, 
                           EventPriority.HIGH, EventPriority.NORMAL, EventPriority.LOW]:
                if self.queues[priority]:
                    event = self.queues[priority].popleft()
                    self.total_size -= 1
                    return event
            return None
    
    async def peek(self) -> Optional[Event]:
        """查看下一个事件（不移除）"""
        async with self._lock:
            for priority in [EventPriority.EMERGENCY, EventPriority.CRITICAL, 
                           EventPriority.HIGH, EventPriority.NORMAL, EventPriority.LOW]:
                if self.queues[priority]:
                    return self.queues[priority][0]
            return None
    
    def size(self) -> int:
        """获取队列大小"""
        return self.total_size
    
    def is_empty(self) -> bool:
        """检查队列是否为空"""
        return self.total_size == 0
    
    def is_full(self) -> bool:
        """检查队列是否已满"""
        return self.total_size >= self.max_size


class EventStore:
    """事件存储"""
    
    def __init__(self, max_events: int = 100000, compression: bool = True):
        self.max_events = max_events
        self.compression = compression
        self.events: Dict[str, Event] = {}
        self.event_index: Dict[str, Set[str]] = defaultdict(set)  # 索引：类型->事件ID集合
        self.user_events: Dict[int, Set[str]] = defaultdict(set)
        self.session_events: Dict[str, Set[str]] = defaultdict(set)
        self.agent_events: Dict[str, Set[str]] = defaultdict(set)
        self.time_index: List[tuple] = []  # (时间戳, 事件ID)
        self._lock = asyncio.Lock()
    
    async def store_event(self, event: Event) -> bool:
        """存储事件"""
        async with self._lock:
            # 检查存储限制
            if len(self.events) >= self.max_events:
                await self._cleanup_old_events()
            
            # 存储事件
            self.events[event.event_id] = event
            
            # 更新索引
            self.event_index[event.event_type.value].add(event.event_id)
            
            if event.user_id:
                self.user_events[event.user_id].add(event.event_id)
            
            if event.session_id:
                self.session_events[event.session_id].add(event.event_id)
            
            if event.agent_id:
                self.agent_events[event.agent_id].add(event.event_id)
            
            # 更新时间索引
            self.time_index.append((event.timestamp, event.event_id))
            self.time_index.sort(key=lambda x: x[0])
            
            return True
    
    async def get_event(self, event_id: str) -> Optional[Event]:
        """获取事件"""
        return self.events.get(event_id)
    
    async def get_events(
        self,
        event_filter: Optional[EventFilter] = None,
        limit: int = 100,
        offset: int = 0
    ) -> List[Event]:
        """获取事件列表"""
        async with self._lock:
            # 获取候选事件
            candidate_events = []
            
            if event_filter:
                # 使用索引优化查询
                if event_filter.event_types:
                    event_ids = set()
                    for event_type in event_filter.event_types:
                        event_ids.update(self.event_index.get(event_type.value, set()))
                    candidate_events = [self.events[eid] for eid in event_ids if eid in self.events]
                
                elif event_filter.user_ids:
                    event_ids = set()
                    for user_id in event_filter.user_ids:
                        event_ids.update(self.user_events.get(user_id, set()))
                    candidate_events = [self.events[eid] for eid in event_ids if eid in self.events]
                
                elif event_filter.session_ids:
                    event_ids = set()
                    for session_id in event_filter.session_ids:
                        event_ids.update(self.session_events.get(session_id, set()))
                    candidate_events = [self.events[eid] for eid in event_ids if eid in self.events]
                
                elif event_filter.agent_ids:
                    event_ids = set()
                    for agent_id in event_filter.agent_ids:
                        event_ids.update(self.agent_events.get(agent_id, set()))
                    candidate_events = [self.events[eid] for eid in event_ids if eid in self.events]
                
                else:
                    candidate_events = list(self.events.values())
                
                # 应用过滤器
                filtered_events = [event for event in candidate_events if event_filter.matches(event)]
            else:
                filtered_events = list(self.events.values())
            
            # 按时间排序
            filtered_events.sort(key=lambda x: x.timestamp, reverse=True)
            
            # 应用分页
            return filtered_events[offset:offset + limit]
    
    async def delete_event(self, event_id: str) -> bool:
        """删除事件"""
        async with self._lock:
            event = self.events.get(event_id)
            if not event:
                return False
            
            # 删除事件
            del self.events[event_id]
            
            # 清理索引
            self.event_index[event.event_type.value].discard(event_id)
            
            if event.user_id:
                self.user_events[event.user_id].discard(event_id)
            
            if event.session_id:
                self.session_events[event.session_id].discard(event_id)
            
            if event.agent_id:
                self.agent_events[event.agent_id].discard(event_id)
            
            # 清理时间索引
            self.time_index = [(ts, eid) for ts, eid in self.time_index if eid != event_id]
            
            return True
    
    async def _cleanup_old_events(self):
        """清理旧事件"""
        # 删除最旧的10%事件
        cleanup_count = max(1, len(self.events) // 10)
        
        # 按时间排序，删除最旧的事件
        old_events = sorted(self.time_index, key=lambda x: x[0])[:cleanup_count]
        
        for _, event_id in old_events:
            await self.delete_event(event_id)
    
    def get_stats(self) -> Dict[str, Any]:
        """获取存储统计"""
        return {
            "total_events": len(self.events),
            "events_by_type": {k: len(v) for k, v in self.event_index.items()},
            "events_by_user_count": len(self.user_events),
            "events_by_session_count": len(self.session_events),
            "events_by_agent_count": len(self.agent_events),
            "oldest_event": self.time_index[0][0].isoformat() if self.time_index else None,
            "newest_event": self.time_index[-1][0].isoformat() if self.time_index else None
        }


class EventService:
    """
    事件服务
    
    基于Google ADK的事件驱动架构服务
    """
    
    def __init__(self):
        """初始化事件服务"""
        self.logger = logger
        
        # 事件队列和存储
        self.event_queue = EventQueue(max_size=50000)
        self.event_store = EventStore(max_events=100000)
        self.scheduled_events: Dict[str, Event] = {}
        
        # 订阅管理
        self.subscriptions: Dict[str, EventSubscription] = {}
        self.subscriber_subscriptions: Dict[str, Set[str]] = defaultdict(set)
        self.type_subscriptions: Dict[EventType, Set[str]] = defaultdict(set)
        
        # 投递记录
        self.delivery_records: Dict[str, EventDeliveryRecord] = {}
        self.failed_deliveries: deque = deque(maxlen=10000)
        
        # 性能监控
        self.metrics = {
            "total_events": 0,
            "processed_events": 0,
            "failed_events": 0,
            "total_subscriptions": 0,
            "active_subscriptions": 0,
            "total_deliveries": 0,
            "failed_deliveries": 0,
            "average_processing_time": 0.0
        }
        
        # 配置
        self.max_concurrent_processors = 10
        self.batch_size = 100
        self.processing_timeout = 30.0
        
        # 线程池
        self.thread_pool = ThreadPoolExecutor(max_workers=self.max_concurrent_processors)
        
        # 后台任务
        self._processor_task = None
        self._scheduler_task = None
        self._cleanup_task = None
        self._monitoring_task = None
        
        # 服务依赖
        self.user_service = UserService()
        
        self.logger.info("事件服务初始化完成")
    
    async def start(self):
        """启动事件服务"""
        try:
            # 启动后台任务
            self._processor_task = asyncio.create_task(self._process_events())
            self._scheduler_task = asyncio.create_task(self._process_scheduled_events())
            self._cleanup_task = asyncio.create_task(self._cleanup_expired_events())
            self._monitoring_task = asyncio.create_task(self._monitor_performance())
            
            self.logger.info("事件服务启动成功")
            
        except Exception as e:
            self.logger.error(f"事件服务启动失败: {e}")
            raise
    
    async def stop(self):
        """停止事件服务"""
        try:
            # 取消后台任务
            if self._processor_task:
                self._processor_task.cancel()
            if self._scheduler_task:
                self._scheduler_task.cancel()
            if self._cleanup_task:
                self._cleanup_task.cancel()
            if self._monitoring_task:
                self._monitoring_task.cancel()
            
            # 关闭线程池
            self.thread_pool.shutdown(wait=True)
            
            self.logger.info("事件服务停止成功")
            
        except Exception as e:
            self.logger.error(f"事件服务停止失败: {e}")
    
    async def publish_event(
        self,
        event_type: EventType,
        source: str,
        data: Any,
        target: Optional[str] = None,
        user_id: Optional[int] = None,
        session_id: Optional[str] = None,
        agent_id: Optional[str] = None,
        priority: EventPriority = EventPriority.NORMAL,
        tags: Optional[List[str]] = None,
        metadata: Optional[Dict[str, Any]] = None,
        correlation_id: Optional[str] = None,
        parent_event_id: Optional[str] = None,
        ttl: Optional[int] = None,
        delivery_mode: DeliveryMode = DeliveryMode.IMMEDIATE,
        scheduled_time: Optional[datetime] = None
    ) -> str:
        """
        发布事件
        
        Args:
            event_type: 事件类型
            source: 事件源
            data: 事件数据
            target: 目标
            user_id: 用户ID
            session_id: 会话ID
            agent_id: 智能体ID
            priority: 优先级
            tags: 标签
            metadata: 元数据
            correlation_id: 关联ID
            parent_event_id: 父事件ID
            ttl: 生存时间
            delivery_mode: 投递模式
            scheduled_time: 计划时间
        
        Returns:
            str: 事件ID
        """
        try:
            # 创建事件
            event = Event(
                event_id=str(uuid.uuid4()),
                event_type=event_type,
                source=source,
                data=data,
                target=target,
                user_id=user_id,
                session_id=session_id,
                agent_id=agent_id,
                priority=priority,
                tags=tags or [],
                metadata=metadata or {},
                correlation_id=correlation_id,
                parent_event_id=parent_event_id,
                ttl=ttl,
                delivery_mode=delivery_mode,
                scheduled_time=scheduled_time
            )
            
            # 存储事件
            await self.event_store.store_event(event)
            
            # 根据投递模式处理事件
            if delivery_mode == DeliveryMode.IMMEDIATE:
                # 立即投递
                await self.event_queue.put(event)
            elif delivery_mode == DeliveryMode.SCHEDULED and scheduled_time:
                # 计划投递
                self.scheduled_events[event.event_id] = event
            elif delivery_mode == DeliveryMode.DELAYED:
                # 延迟投递（使用默认延迟）
                event.scheduled_time = datetime.utcnow() + timedelta(seconds=event.retry_delay)
                self.scheduled_events[event.event_id] = event
            else:
                # 批量投递（暂时放入队列）
                await self.event_queue.put(event)
            
            # 更新指标
            self.metrics["total_events"] += 1
            
            self.logger.debug(f"事件发布成功: {event.event_id} ({event_type.value})")
            return event.event_id
            
        except Exception as e:
            self.logger.error(f"事件发布失败: {e}")
            raise
    
    async def subscribe(
        self,
        subscriber_id: str,
        event_filter: EventFilter,
        callback: Callable[[Event], None],
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        订阅事件
        
        Args:
            subscriber_id: 订阅者ID
            event_filter: 事件过滤器
            callback: 回调函数
            metadata: 元数据
        
        Returns:
            str: 订阅ID
        """
        try:
            # 创建订阅
            subscription = EventSubscription(
                subscription_id=str(uuid.uuid4()),
                subscriber_id=subscriber_id,
                event_filter=event_filter,
                callback=callback,
                metadata=metadata or {}
            )
            
            # 存储订阅
            self.subscriptions[subscription.subscription_id] = subscription
            
            # 更新索引
            self.subscriber_subscriptions[subscriber_id].add(subscription.subscription_id)
            
            if event_filter.event_types:
                for event_type in event_filter.event_types:
                    self.type_subscriptions[event_type].add(subscription.subscription_id)
            
            # 更新指标
            self.metrics["total_subscriptions"] += 1
            self.metrics["active_subscriptions"] += 1
            
            self.logger.info(f"事件订阅成功: {subscription.subscription_id} (订阅者: {subscriber_id})")
            return subscription.subscription_id
            
        except Exception as e:
            self.logger.error(f"事件订阅失败: {e}")
            raise
    
    async def unsubscribe(self, subscription_id: str) -> bool:
        """
        取消订阅
        
        Args:
            subscription_id: 订阅ID
        
        Returns:
            bool: 是否成功
        """
        try:
            subscription = self.subscriptions.get(subscription_id)
            if not subscription:
                return False
            
            # 删除订阅
            del self.subscriptions[subscription_id]
            
            # 清理索引
            self.subscriber_subscriptions[subscription.subscriber_id].discard(subscription_id)
            if not self.subscriber_subscriptions[subscription.subscriber_id]:
                del self.subscriber_subscriptions[subscription.subscriber_id]
            
            if subscription.event_filter.event_types:
                for event_type in subscription.event_filter.event_types:
                    self.type_subscriptions[event_type].discard(subscription_id)
                    if not self.type_subscriptions[event_type]:
                        del self.type_subscriptions[event_type]
            
            # 更新指标
            self.metrics["active_subscriptions"] -= 1
            
            self.logger.info(f"取消事件订阅成功: {subscription_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"取消事件订阅失败: {e}")
            return False
    
    async def get_events(
        self,
        event_filter: Optional[EventFilter] = None,
        limit: int = 100,
        offset: int = 0
    ) -> List[Event]:
        """
        获取事件列表
        
        Args:
            event_filter: 事件过滤器
            limit: 限制数量
            offset: 偏移量
        
        Returns:
            List[Event]: 事件列表
        """
        try:
            return await self.event_store.get_events(event_filter, limit, offset)
            
        except Exception as e:
            self.logger.error(f"获取事件列表失败: {e}")
            return []
    
    async def get_event(self, event_id: str) -> Optional[Event]:
        """
        获取事件
        
        Args:
            event_id: 事件ID
        
        Returns:
            Optional[Event]: 事件
        """
        try:
            return await self.event_store.get_event(event_id)
            
        except Exception as e:
            self.logger.error(f"获取事件失败: {e}")
            return None
    
    async def replay_events(
        self,
        event_filter: EventFilter,
        subscriber_id: str,
        callback: Callable[[Event], None]
    ) -> int:
        """
        重放事件
        
        Args:
            event_filter: 事件过滤器
            subscriber_id: 订阅者ID
            callback: 回调函数
        
        Returns:
            int: 重放事件数量
        """
        try:
            # 获取匹配的事件
            events = await self.event_store.get_events(event_filter, limit=10000)
            
            # 重放事件
            replayed_count = 0
            for event in events:
                try:
                    if asyncio.iscoroutinefunction(callback):
                        await callback(event)
                    else:
                        callback(event)
                    replayed_count += 1
                except Exception as e:
                    self.logger.error(f"重放事件失败: {event.event_id} - {e}")
            
            self.logger.info(f"事件重放完成: {replayed_count} 个事件 (订阅者: {subscriber_id})")
            return replayed_count
            
        except Exception as e:
            self.logger.error(f"事件重放失败: {e}")
            return 0
    
    async def get_metrics(self) -> Dict[str, Any]:
        """
        获取性能指标
        
        Returns:
            Dict[str, Any]: 性能指标
        """
        try:
            # 获取队列和存储统计
            queue_stats = {
                "queue_size": self.event_queue.size(),
                "queue_is_full": self.event_queue.is_full(),
                "scheduled_events": len(self.scheduled_events)
            }
            
            store_stats = self.event_store.get_stats()
            
            subscription_stats = {
                "total_subscriptions": len(self.subscriptions),
                "active_subscriptions": sum(1 for s in self.subscriptions.values() if s.is_active),
                "subscribers_count": len(self.subscriber_subscriptions)
            }
            
            delivery_stats = {
                "total_deliveries": len(self.delivery_records),
                "failed_deliveries": len(self.failed_deliveries),
                "success_rate": self._calculate_success_rate()
            }
            
            return {
                **self.metrics,
                "queue_stats": queue_stats,
                "store_stats": store_stats,
                "subscription_stats": subscription_stats,
                "delivery_stats": delivery_stats,
                "timestamp": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"获取事件服务指标失败: {e}")
            return {}
    
    # 私有方法
    
    async def _process_events(self):
        """
        处理事件（后台任务）
        """
        while True:
            try:
                # 获取事件
                event = await self.event_queue.get()
                if not event:
                    await asyncio.sleep(0.1)
                    continue
                
                # 检查事件是否过期
                if event.is_expired():
                    self.logger.debug(f"事件已过期: {event.event_id}")
                    continue
                
                # 处理事件
                await self._deliver_event(event)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"处理事件失败: {e}")
                await asyncio.sleep(1)
    
    async def _deliver_event(self, event: Event):
        """
        投递事件
        
        Args:
            event: 事件
        """
        try:
            start_time = time.time()
            
            # 查找匹配的订阅
            matching_subscriptions = []
            
            # 使用类型索引优化查询
            if event.event_type in self.type_subscriptions:
                candidate_subscription_ids = self.type_subscriptions[event.event_type]
            else:
                candidate_subscription_ids = self.subscriptions.keys()
            
            for subscription_id in candidate_subscription_ids:
                subscription = self.subscriptions.get(subscription_id)
                if subscription and subscription.is_active and subscription.event_filter.matches(event):
                    matching_subscriptions.append(subscription)
            
            # 投递到匹配的订阅
            delivery_tasks = []
            for subscription in matching_subscriptions:
                task = asyncio.create_task(
                    self._deliver_to_subscription(event, subscription)
                )
                delivery_tasks.append(task)
            
            # 等待所有投递完成
            if delivery_tasks:
                await asyncio.gather(*delivery_tasks, return_exceptions=True)
            
            # 更新指标
            processing_time = time.time() - start_time
            self.metrics["processed_events"] += 1
            self.metrics["average_processing_time"] = (
                (self.metrics["average_processing_time"] * (self.metrics["processed_events"] - 1) + processing_time) /
                self.metrics["processed_events"]
            )
            
            self.logger.debug(f"事件投递完成: {event.event_id} -> {len(matching_subscriptions)} 个订阅")
            
        except Exception as e:
            self.logger.error(f"投递事件失败: {e}")
            self.metrics["failed_events"] += 1
    
    async def _deliver_to_subscription(self, event: Event, subscription: EventSubscription):
        """
        投递事件到订阅
        
        Args:
            event: 事件
            subscription: 订阅
        """
        delivery_record = EventDeliveryRecord(
            delivery_id=str(uuid.uuid4()),
            event_id=event.event_id,
            subscription_id=subscription.subscription_id,
            status=EventStatus.PROCESSING,
            delivered_at=datetime.utcnow()
        )
        
        try:
            start_time = time.time()
            
            # 执行回调
            if asyncio.iscoroutinefunction(subscription.callback):
                await asyncio.wait_for(
                    subscription.callback(event),
                    timeout=self.processing_timeout
                )
            else:
                # 在线程池中执行同步回调
                await asyncio.get_event_loop().run_in_executor(
                    self.thread_pool,
                    subscription.callback,
                    event
                )
            
            # 更新投递记录
            delivery_record.status = EventStatus.COMPLETED
            delivery_record.processing_time = time.time() - start_time
            
            # 更新订阅统计
            subscription.delivery_count += 1
            subscription.last_delivery = datetime.utcnow()
            
            # 更新指标
            self.metrics["total_deliveries"] += 1
            
        except asyncio.TimeoutError:
            delivery_record.status = EventStatus.FAILED
            delivery_record.error_message = "投递超时"
            self.failed_deliveries.append(delivery_record)
            self.metrics["failed_deliveries"] += 1
            self.logger.error(f"事件投递超时: {event.event_id} -> {subscription.subscription_id}")
            
        except Exception as e:
            delivery_record.status = EventStatus.FAILED
            delivery_record.error_message = str(e)
            self.failed_deliveries.append(delivery_record)
            self.metrics["failed_deliveries"] += 1
            self.logger.error(f"事件投递失败: {event.event_id} -> {subscription.subscription_id} - {e}")
            
            # 检查是否需要重试
            if event.should_retry():
                event.retry_count += 1
                event.scheduled_time = datetime.utcnow() + timedelta(seconds=event.retry_delay)
                self.scheduled_events[event.event_id] = event
                delivery_record.status = EventStatus.RETRYING
        
        finally:
            # 存储投递记录
            self.delivery_records[delivery_record.delivery_id] = delivery_record
    
    async def _process_scheduled_events(self):
        """
        处理计划事件（后台任务）
        """
        while True:
            try:
                current_time = datetime.utcnow()
                ready_events = []
                
                # 查找准备执行的事件
                for event_id, event in list(self.scheduled_events.items()):
                    if event.scheduled_time and event.scheduled_time <= current_time:
                        ready_events.append(event)
                        del self.scheduled_events[event_id]
                
                # 将准备好的事件加入队列
                for event in ready_events:
                    await self.event_queue.put(event)
                
                # 等待下次检查
                await asyncio.sleep(1)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"处理计划事件失败: {e}")
                await asyncio.sleep(1)
    
    async def _cleanup_expired_events(self):
        """
        清理过期事件（后台任务）
        """
        while True:
            try:
                current_time = datetime.utcnow()
                
                # 清理过期的计划事件
                expired_events = []
                for event_id, event in list(self.scheduled_events.items()):
                    if event.is_expired():
                        expired_events.append(event_id)
                
                for event_id in expired_events:
                    del self.scheduled_events[event_id]
                    self.logger.debug(f"清理过期计划事件: {event_id}")
                
                # 清理旧的投递记录
                cutoff_time = current_time - timedelta(hours=24)
                old_delivery_ids = []
                for delivery_id, record in self.delivery_records.items():
                    if record.delivered_at < cutoff_time:
                        old_delivery_ids.append(delivery_id)
                
                for delivery_id in old_delivery_ids:
                    del self.delivery_records[delivery_id]
                
                # 等待下次清理
                await asyncio.sleep(3600)  # 每小时清理一次
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"清理过期事件失败: {e}")
                await asyncio.sleep(3600)
    
    async def _monitor_performance(self):
        """
        性能监控（后台任务）
        """
        while True:
            try:
                # 记录性能指标
                metrics = await self.get_metrics()
                self.logger.debug(f"事件服务性能指标: {metrics}")
                
                # 检查队列状态
                if self.event_queue.is_full():
                    self.logger.warning("事件队列已满")
                
                # 检查失败率
                success_rate = self._calculate_success_rate()
                if success_rate < 0.9:  # 成功率低于90%
                    self.logger.warning(f"事件投递成功率较低: {success_rate:.2%}")
                
                # 等待下次监控
                await asyncio.sleep(300)  # 每5分钟监控一次
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"性能监控失败: {e}")
                await asyncio.sleep(300)
    
    def _calculate_success_rate(self) -> float:
        """
        计算投递成功率
        
        Returns:
            float: 成功率
        """
        total_deliveries = self.metrics["total_deliveries"]
        failed_deliveries = self.metrics["failed_deliveries"]
        
        if total_deliveries == 0:
            return 1.0
        
        return (total_deliveries - failed_deliveries) / total_deliveries


# 全局事件服务实例
event_service = EventService()