#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A2A多智能体系统消息模型

定义消息相关的数据模型
"""

from datetime import datetime
from typing import Optional, List, Dict, Any
from sqlalchemy import Column, Integer, String, Text, Boolean, DateTime, ForeignKey, Index, Float
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from .base import BaseModel


class Message(BaseModel):
    """
    消息模型
    
    存储会话中的消息信息
    """
    
    __tablename__ = "messages"
    
    # 会话关联
    session_id = Column(
        Integer,
        ForeignKey("sessions.id", ondelete="CASCADE"),
        nullable=False,
        comment="会话ID"
    )
    
    # 用户关联
    user_id = Column(
        Integer,
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        comment="用户ID"
    )
    
    # 消息基本信息
    message_id = Column(
        String(36),
        nullable=False,
        unique=True,
        comment="消息唯一标识"
    )
    
    # 发送者信息
    sender_type = Column(
        String(20),
        nullable=False,
        comment="发送者类型（user/agent/system）"
    )
    
    sender_id = Column(
        Integer,
        nullable=True,
        comment="发送者ID"
    )
    
    sender_name = Column(
        String(100),
        nullable=True,
        comment="发送者名称"
    )
    
    # 消息内容
    content = Column(
        Text,
        nullable=False,
        comment="消息内容"
    )
    
    content_type = Column(
        String(50),
        nullable=False,
        default="text",
        comment="内容类型"
    )
    
    # 消息类型和状态
    message_type = Column(
        String(50),
        nullable=False,
        default="chat",
        comment="消息类型"
    )
    
    status = Column(
        String(20),
        nullable=False,
        default="sent",
        comment="消息状态"
    )
    
    # 回复关联
    parent_message_id = Column(
        Integer,
        ForeignKey("messages.id", ondelete="SET NULL"),
        nullable=True,
        comment="父消息ID（回复）"
    )
    
    # 元数据
    meta_data = Column(
        "metadata",
        Text,
        nullable=True,
        comment="消息元数据（JSON格式）"
    )
    
    # 附件信息
    attachments = Column(
        Text,
        nullable=True,
        comment="附件信息（JSON格式）"
    )
    
    # 模型相关信息
    model_name = Column(
        String(100),
        nullable=True,
        comment="使用的模型名称"
    )
    
    # Token统计
    input_tokens = Column(
        Integer,
        nullable=False,
        default=0,
        comment="输入token数"
    )
    
    output_tokens = Column(
        Integer,
        nullable=False,
        default=0,
        comment="输出token数"
    )
    
    total_tokens = Column(
        Integer,
        nullable=False,
        default=0,
        comment="总token数"
    )
    
    # 费用信息
    cost = Column(
        Float,
        nullable=False,
        default=0.0,
        comment="费用"
    )
    
    # 性能指标
    response_time = Column(
        Float,
        nullable=True,
        comment="响应时间（秒）"
    )
    
    # 时间信息
    sent_at = Column(
        DateTime,
        nullable=False,
        default=func.now(),
        comment="发送时间"
    )
    
    received_at = Column(
        DateTime,
        nullable=True,
        comment="接收时间"
    )
    
    processed_at = Column(
        DateTime,
        nullable=True,
        comment="处理时间"
    )
    
    # 评分和反馈
    rating = Column(
        Integer,
        nullable=True,
        comment="用户评分（1-5）"
    )
    
    feedback = Column(
        Text,
        nullable=True,
        comment="用户反馈"
    )
    
    # 关联关系
    session = relationship("Session", back_populates="messages")
    parent_message = relationship("Message", remote_side="Message.id")
    replies = relationship("Message", back_populates="parent_message")
    attachments = relationship("MessageAttachment", back_populates="message", cascade="all, delete-orphan")
    
    # 索引
    __table_args__ = (
        Index("idx_messages_session_id", "session_id"),
        Index("idx_messages_message_id", "message_id"),
        Index("idx_messages_sender", "sender_type", "sender_id"),
        Index("idx_messages_type", "message_type"),
        Index("idx_messages_status", "status"),
        Index("idx_messages_sent_at", "sent_at"),
        Index("idx_messages_parent", "parent_message_id"),
        Index("idx_messages_is_active", "is_active"),
    )
    
    def set_metadata(self, metadata: Dict[str, Any]) -> None:
        """
        设置消息元数据
        
        Args:
            metadata: 元数据字典
        """
        import json
        self.meta_data = json.dumps(metadata, ensure_ascii=False)
    
    def get_metadata(self) -> Dict[str, Any]:
        """
        获取消息元数据
        
        Returns:
            Dict[str, Any]: 元数据字典
        """
        if not self.meta_data:
            return {}
        
        try:
            import json
            return json.loads(self.meta_data)
        except (json.JSONDecodeError, TypeError):
            return {}
    
    def set_attachments(self, attachments: List[Dict[str, Any]]) -> None:
        """
        设置附件信息
        
        Args:
            attachments: 附件列表
        """
        import json
        self.attachments = json.dumps(attachments, ensure_ascii=False)
    
    def get_attachments(self) -> List[Dict[str, Any]]:
        """
        获取附件信息
        
        Returns:
            List[Dict[str, Any]]: 附件列表
        """
        if not self.attachments:
            return []
        
        try:
            import json
            return json.loads(self.attachments)
        except (json.JSONDecodeError, TypeError):
            return []
    
    def mark_as_received(self) -> None:
        """
        标记为已接收
        """
        self.status = "received"
        self.received_at = datetime.now()
    
    def mark_as_processed(self) -> None:
        """
        标记为已处理
        """
        self.status = "processed"
        self.processed_at = datetime.now()
    
    def mark_as_failed(self, error_message: str = None) -> None:
        """
        标记为失败
        
        Args:
            error_message: 错误信息
        """
        self.status = "failed"
        if error_message:
            metadata = self.get_metadata()
            metadata["error"] = error_message
            self.set_metadata(metadata)
    
    def set_rating(self, rating: int, feedback: str = None) -> None:
        """
        设置评分和反馈
        
        Args:
            rating: 评分（1-5）
            feedback: 反馈内容
        """
        if 1 <= rating <= 5:
            self.rating = rating
        if feedback:
            self.feedback = feedback
    
    def calculate_tokens(self) -> None:
        """
        计算token数量
        """
        # 简单的token计算，实际应该使用tokenizer
        content_length = len(self.content)
        self.total_tokens = max(1, content_length // 4)  # 粗略估算
        
        if self.sender_type == "user":
            self.input_tokens = self.total_tokens
            self.output_tokens = 0
        else:
            self.input_tokens = 0
            self.output_tokens = self.total_tokens
    
    @property
    def is_from_user(self) -> bool:
        """
        检查是否来自用户
        
        Returns:
            bool: 是否来自用户
        """
        return self.sender_type == "user"
    
    @property
    def is_from_agent(self) -> bool:
        """
        检查是否来自智能体
        
        Returns:
            bool: 是否来自智能体
        """
        return self.sender_type == "agent"
    
    @property
    def is_system_message(self) -> bool:
        """
        检查是否为系统消息
        
        Returns:
            bool: 是否为系统消息
        """
        return self.sender_type == "system"
    
    @property
    def has_attachments(self) -> bool:
        """
        检查是否有附件
        
        Returns:
            bool: 是否有附件
        """
        return bool(self.get_attachments())
    
    def __repr__(self) -> str:
        return f"<Message(id={self.id}, message_id='{self.message_id}', sender='{self.sender_type}')>"


class MessageReaction(BaseModel):
    """
    消息反应模型
    
    存储用户对消息的反应（点赞、点踩等）
    """
    
    __tablename__ = "message_reactions"
    
    # 消息和用户关联
    message_id = Column(
        Integer,
        ForeignKey("messages.id", ondelete="CASCADE"),
        nullable=False,
        comment="消息ID"
    )
    
    user_id = Column(
        Integer,
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        comment="用户ID"
    )
    
    # 反应信息
    reaction_type = Column(
        String(20),
        nullable=False,
        comment="反应类型"
    )
    
    # 关联关系
    message = relationship("Message")
    user = relationship("User")
    
    # 索引
    __table_args__ = (
        Index("idx_message_reactions_message_id", "message_id"),
        Index("idx_message_reactions_user_id", "user_id"),
        Index("idx_message_reactions_type", "reaction_type"),
        # 复合唯一索引
        Index("idx_message_reactions_unique", "message_id", "user_id", "reaction_type", unique=True),
    )
    
    def __repr__(self) -> str:
        return f"<MessageReaction(id={self.id}, message_id={self.message_id}, type='{self.reaction_type}')>"


class MessageEdit(BaseModel):
    """
    消息编辑历史模型
    
    记录消息的编辑历史
    """
    
    __tablename__ = "message_edits"
    
    # 消息关联
    message_id = Column(
        Integer,
        ForeignKey("messages.id", ondelete="CASCADE"),
        nullable=False,
        comment="消息ID"
    )
    
    # 编辑信息
    original_content = Column(
        Text,
        nullable=False,
        comment="原始内容"
    )
    
    new_content = Column(
        Text,
        nullable=False,
        comment="新内容"
    )
    
    edit_reason = Column(
        String(200),
        nullable=True,
        comment="编辑原因"
    )
    
    # 编辑者信息
    edited_by = Column(
        Integer,
        nullable=False,
        comment="编辑者ID"
    )
    
    # 时间信息
    edited_at = Column(
        DateTime,
        nullable=False,
        default=func.now(),
        comment="编辑时间"
    )
    
    # 关联关系
    message = relationship("Message")
    
    # 索引
    __table_args__ = (
        Index("idx_message_edits_message_id", "message_id"),
        Index("idx_message_edits_edited_by", "edited_by"),
        Index("idx_message_edits_edited_at", "edited_at"),
    )
    
    def __repr__(self) -> str:
        return f"<MessageEdit(id={self.id}, message_id={self.message_id}, edited_by={self.edited_by})>"


class MessageTemplate(BaseModel):
    """
    消息模板模型
    
    存储常用的消息模板
    """
    
    __tablename__ = "message_templates"
    
    # 模板信息
    name = Column(
        String(100),
        nullable=False,
        comment="模板名称"
    )
    
    description = Column(
        Text,
        nullable=True,
        comment="模板描述"
    )
    
    category = Column(
        String(50),
        nullable=False,
        default="general",
        comment="模板分类"
    )
    
    # 模板内容
    template_content = Column(
        Text,
        nullable=False,
        comment="模板内容"
    )
    
    # 变量定义
    variables = Column(
        Text,
        nullable=True,
        comment="模板变量（JSON格式）"
    )
    
    # 状态信息
    is_public = Column(
        Boolean,
        nullable=False,
        default=False,
        comment="是否公开"
    )
    
    is_system = Column(
        Boolean,
        nullable=False,
        default=False,
        comment="是否为系统模板"
    )
    
    # 统计信息
    usage_count = Column(
        Integer,
        nullable=False,
        default=0,
        comment="使用次数"
    )
    
    # 创建者信息
    created_by = Column(
        Integer,
        nullable=True,
        comment="创建者ID"
    )
    
    # 索引
    __table_args__ = (
        Index("idx_message_templates_name", "name"),
        Index("idx_message_templates_category", "category"),
        Index("idx_message_templates_is_public", "is_public"),
        Index("idx_message_templates_is_system", "is_system"),
        Index("idx_message_templates_created_by", "created_by"),
    )
    
    def set_variables(self, variables: Dict[str, Any]) -> None:
        """
        设置模板变量
        
        Args:
            variables: 变量字典
        """
        import json
        self.variables = json.dumps(variables, ensure_ascii=False)
    
    def get_variables(self) -> Dict[str, Any]:
        """
        获取模板变量
        
        Returns:
            Dict[str, Any]: 变量字典
        """
        if not self.variables:
            return {}
        
        try:
            import json
            return json.loads(self.variables)
        except (json.JSONDecodeError, TypeError):
            return {}
    
    def render(self, context: Dict[str, Any]) -> str:
        """
        渲染模板
        
        Args:
            context: 上下文变量
            
        Returns:
            str: 渲染后的内容
        """
        content = self.template_content
        
        # 简单的变量替换
        for key, value in context.items():
            placeholder = f"{{{key}}}"
            content = content.replace(placeholder, str(value))
        
        return content
    
    def increment_usage(self) -> None:
        """
        增加使用次数
        """
        self.usage_count += 1
    
    def __repr__(self) -> str:
        return f"<MessageTemplate(id={self.id}, name='{self.name}', category='{self.category}')>"


class MessageAttachment(BaseModel):
    """
    消息附件模型
    
    存储消息的附件信息
    """
    
    __tablename__ = "message_attachments"
    
    # 关联关系
    message_id = Column(
        Integer,
        ForeignKey("messages.id", ondelete="CASCADE"),
        nullable=False,
        comment="消息ID"
    )
    
    user_id = Column(
        Integer,
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        comment="用户ID"
    )
    
    # 附件基本信息
    attachment_id = Column(
        String(36),
        nullable=False,
        unique=True,
        comment="附件唯一标识"
    )
    
    filename = Column(
        String(255),
        nullable=False,
        comment="文件名"
    )
    
    original_filename = Column(
        String(255),
        nullable=False,
        comment="原始文件名"
    )
    
    file_path = Column(
        String(500),
        nullable=True,
        comment="文件路径"
    )
    
    file_url = Column(
        String(500),
        nullable=True,
        comment="文件URL"
    )
    
    file_size = Column(
        Integer,
        nullable=False,
        default=0,
        comment="文件大小（字节）"
    )
    
    content_type = Column(
        String(100),
        nullable=False,
        comment="内容类型（MIME类型）"
    )
    
    file_extension = Column(
        String(20),
        nullable=True,
        comment="文件扩展名"
    )
    
    file_hash = Column(
        String(64),
        nullable=True,
        comment="文件哈希值"
    )
    
    # 附件状态
    status = Column(
        String(20),
        nullable=False,
        default="uploaded",
        comment="附件状态"
    )
    
    is_image = Column(
        Boolean,
        nullable=False,
        default=False,
        comment="是否为图片"
    )
    
    is_document = Column(
        Boolean,
        nullable=False,
        default=False,
        comment="是否为文档"
    )
    
    # 元数据
    meta_data = Column(
        "metadata",
        Text,
        nullable=True,
        comment="附件元数据"
    )
    
    # 访问统计
    download_count = Column(
        Integer,
        nullable=False,
        default=0,
        comment="下载次数"
    )
    
    last_accessed_at = Column(
        DateTime,
        nullable=True,
        comment="最后访问时间"
    )
    
    # 关联关系
    message = relationship("Message", back_populates="attachments")
    user = relationship("User", back_populates="message_attachments")
    
    # 索引
    __table_args__ = (
        Index("idx_message_attachments_message_id", "message_id"),
        Index("idx_message_attachments_user_id", "user_id"),
        Index("idx_message_attachments_attachment_id", "attachment_id"),
        Index("idx_message_attachments_content_type", "content_type"),
        Index("idx_message_attachments_status", "status"),
        Index("idx_message_attachments_created_at", "created_at"),
    )
    
    def get_metadata(self) -> Dict[str, Any]:
        """获取元数据"""
        if not self.meta_data:
            return {}
        
        try:
            import json
            return json.loads(self.meta_data)
        except (json.JSONDecodeError, TypeError):
            return {}
    
    def set_metadata(self, metadata: Dict[str, Any]):
        """设置元数据"""
        import json
        self.meta_data = json.dumps(metadata, ensure_ascii=False) if metadata else None
    
    def is_valid_image(self) -> bool:
        """检查是否为有效图片格式"""
        image_types = ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml']
        return self.content_type in image_types
    
    def is_valid_document(self) -> bool:
        """检查是否为有效文档格式"""
        document_types = [
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'text/plain',
            'text/csv'
        ]
        return self.content_type in document_types
    
    def get_file_size_formatted(self) -> str:
        """获取格式化的文件大小"""
        if self.file_size < 1024:
            return f"{self.file_size} B"
        elif self.file_size < 1024 * 1024:
            return f"{self.file_size / 1024:.1f} KB"
        elif self.file_size < 1024 * 1024 * 1024:
            return f"{self.file_size / (1024 * 1024):.1f} MB"
        else:
            return f"{self.file_size / (1024 * 1024 * 1024):.1f} GB"
    
    def increment_download_count(self):
        """增加下载次数"""
        self.download_count += 1
        self.last_accessed_at = datetime.now()
    
    def __repr__(self):
        return f"<MessageAttachment(id={self.id}, filename='{self.filename}', content_type='{self.content_type}')>"