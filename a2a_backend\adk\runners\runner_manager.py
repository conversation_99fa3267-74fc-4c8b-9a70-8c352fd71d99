#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A2A多智能体系统 ADK Runner管理器

负责Runner生命周期，支持用户隔离
"""

import logging
import asyncio
from typing import Dict, List, Optional, Any, Union, Type, TypeVar
from datetime import datetime, timedelta
import uuid

from app.models.user import User
from app.core.logging import get_logger
from app.auth.permissions import check_user_permission

from .base_runner import BaseRunner
from .agent_runner import AgentRunner
from .workflow_runner import WorkflowRunner

# 创建类型变量用于子类继承
T = TypeVar('T', bound=BaseRunner)

class RunnerManager:
    """
    Runner管理器，负责Runner生命周期，支持用户隔离
    
    提供以下功能：
    1. Runner创建和销毁
    2. Runner状态管理
    3. Runner资源限制
    4. 用户隔离
    5. 超时和自动清理
    """
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        """
        初始化Runner管理器
        
        Args:
            logger: 日志记录器
        """
        self.logger = logger or setup_logger("runner_manager")
        
        # 按用户ID存储Runner实例
        self.runners: Dict[int, Dict[str, BaseRunner]] = {}
        
        # Runner资源限制
        self.max_runners_per_user = 10
        self.max_total_runners = 100
        
        # 超时设置（秒）
        self.runner_timeout = 3600  # 1小时
        
        # 清理任务
        self.cleanup_task = None
        
        self.logger.info("Runner管理器已初始化")
    
    async def start(self) -> None:
        """
        启动Runner管理器
        """
        # 启动定期清理任务
        self.cleanup_task = asyncio.create_task(self._cleanup_loop())
        self.logger.info("Runner管理器已启动")
    
    async def stop(self) -> None:
        """
        停止Runner管理器
        """
        # 停止清理任务
        if self.cleanup_task:
            self.cleanup_task.cancel()
            try:
                await self.cleanup_task
            except asyncio.CancelledError:
                pass
        
        # 停止所有Runner
        for user_id, user_runners in self.runners.items():
            for execution_id, runner in user_runners.items():
                try:
                    await runner.stop()
                except Exception as e:
                    self.logger.error(f"停止Runner错误: {str(e)}")
        
        self.logger.info("Runner管理器已停止")
    
    async def _cleanup_loop(self) -> None:
        """
        定期清理过期Runner的循环
        """
        while True:
            try:
                await self._cleanup_expired_runners()
                await asyncio.sleep(300)  # 每5分钟清理一次
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"清理Runner错误: {str(e)}")
                await asyncio.sleep(60)  # 出错后等待1分钟再试
    
    async def _cleanup_expired_runners(self) -> None:
        """
        清理过期的Runner
        """
        now = datetime.now()
        expired_count = 0
        
        for user_id in list(self.runners.keys()):
            user_runners = self.runners[user_id]
            for execution_id in list(user_runners.keys()):
                runner = user_runners[execution_id]
                
                # 检查Runner是否已过期
                if runner.start_time and (now - runner.start_time) > timedelta(seconds=self.runner_timeout):
                    try:
                        # 停止并移除Runner
                        await runner.stop()
                        del user_runners[execution_id]
                        expired_count += 1
                    except Exception as e:
                        self.logger.error(f"清理过期Runner错误: {str(e)}")
            
            # 如果用户没有Runner了，移除用户条目
            if not user_runners:
                del self.runners[user_id]
        
        if expired_count > 0:
            self.logger.info(f"已清理 {expired_count} 个过期Runner")
    
    async def _check_user_permission(self, user_id: int, action: str) -> bool:
        """
        检查用户权限
        
        Args:
            user_id: 用户ID
            action: 操作类型
            
        Returns:
            bool: 是否有权限
        """
        try:
            # 检查用户是否存在
            user = await User.get_by_id(user_id)
            if not user:
                self.logger.error(f"用户不存在: {user_id}")
                return False
            
            # 检查用户是否有权限执行此操作
            has_permission = await check_user_permission(
                user_id=user_id,
                owner_id=user_id,  # 用户自己的资源
                resource_type="runner_manager",
                action=action
            )
            
            if not has_permission:
                self.logger.error(f"用户 {user_id} 没有权限执行操作 {action}")
            
            return has_permission
        except Exception as e:
            self.logger.error(f"权限检查错误: {str(e)}")
            return False
    
    async def _check_resource_limits(self, user_id: int) -> bool:
        """
        检查资源限制
        
        Args:
            user_id: 用户ID
            
        Returns:
            bool: 是否在资源限制内
        """
        # 检查总Runner数量
        total_runners = sum(len(user_runners) for user_runners in self.runners.values())
        if total_runners >= self.max_total_runners:
            self.logger.warning(f"已达到总Runner数量限制: {self.max_total_runners}")
            return False
        
        # 检查用户Runner数量
        user_runners = self.runners.get(user_id, {})
        if len(user_runners) >= self.max_runners_per_user:
            self.logger.warning(f"用户 {user_id} 已达到Runner数量限制: {self.max_runners_per_user}")
            return False
        
        return True
    
    async def create_runner(
        self, 
        runner_class: Type[T], 
        user_id: int, 
        owner_id: int, 
        **kwargs
    ) -> Optional[T]:
        """
        创建Runner实例
        
        Args:
            runner_class: Runner类
            user_id: 用户ID
            owner_id: 拥有者ID
            **kwargs: 其他参数
            
        Returns:
            Optional[T]: Runner实例
        """
        try:
            # 检查用户权限
            has_permission = await self._check_user_permission(user_id, "create")
            if not has_permission:
                raise PermissionError(f"用户 {user_id} 没有权限创建Runner")
            
            # 检查资源限制
            within_limits = await self._check_resource_limits(user_id)
            if not within_limits:
                raise ResourceWarning(f"用户 {user_id} 已达到Runner资源限制")
            
            # 创建Runner实例
            execution_id = kwargs.get("execution_id") or str(uuid.uuid4())
            kwargs["execution_id"] = execution_id
            
            # 使用工厂方法创建Runner
            runner = await runner_class.create(user_id=user_id, owner_id=owner_id, **kwargs)
            
            # 存储Runner实例
            if user_id not in self.runners:
                self.runners[user_id] = {}
            self.runners[user_id][execution_id] = runner
            
            self.logger.info(f"已创建Runner: {execution_id}, 类型: {runner_class.__name__}, 用户ID: {user_id}")
            return runner
        except Exception as e:
            self.logger.error(f"创建Runner错误: {str(e)}")
            raise e
    
    async def get_runner(
        self, 
        user_id: int, 
        execution_id: str
    ) -> Optional[BaseRunner]:
        """
        获取Runner实例
        
        Args:
            user_id: 用户ID
            execution_id: 执行ID
            
        Returns:
            Optional[BaseRunner]: Runner实例
        """
        try:
            # 检查用户权限
            has_permission = await self._check_user_permission(user_id, "get")
            if not has_permission:
                raise PermissionError(f"用户 {user_id} 没有权限获取Runner")
            
            # 获取Runner实例
            user_runners = self.runners.get(user_id, {})
            runner = user_runners.get(execution_id)
            
            if not runner:
                self.logger.warning(f"Runner不存在: {execution_id}, 用户ID: {user_id}")
                return None
            
            return runner
        except Exception as e:
            self.logger.error(f"获取Runner错误: {str(e)}")
            return None
    
    async def stop_runner(
        self, 
        user_id: int, 
        execution_id: str
    ) -> bool:
        """
        停止Runner
        
        Args:
            user_id: 用户ID
            execution_id: 执行ID
            
        Returns:
            bool: 是否成功停止
        """
        try:
            # 检查用户权限
            has_permission = await self._check_user_permission(user_id, "stop")
            if not has_permission:
                raise PermissionError(f"用户 {user_id} 没有权限停止Runner")
            
            # 获取Runner实例
            user_runners = self.runners.get(user_id, {})
            runner = user_runners.get(execution_id)
            
            if not runner:
                self.logger.warning(f"Runner不存在: {execution_id}, 用户ID: {user_id}")
                return False
            
            # 停止Runner
            await runner.stop()
            
            # 移除Runner
            del user_runners[execution_id]
            if not user_runners:
                del self.runners[user_id]
            
            self.logger.info(f"已停止Runner: {execution_id}, 用户ID: {user_id}")
            return True
        except Exception as e:
            self.logger.error(f"停止Runner错误: {str(e)}")
            return False
    
    async def list_runners(
        self, 
        user_id: int
    ) -> List[Dict[str, Any]]:
        """
        列出用户的所有Runner
        
        Args:
            user_id: 用户ID
            
        Returns:
            List[Dict[str, Any]]: Runner状态列表
        """
        try:
            # 检查用户权限
            has_permission = await self._check_user_permission(user_id, "list")
            if not has_permission:
                raise PermissionError(f"用户 {user_id} 没有权限列出Runner")
            
            # 获取用户的所有Runner
            user_runners = self.runners.get(user_id, {})
            
            # 获取Runner状态
            runner_states = [runner.get_state() for runner in user_runners.values()]
            
            return runner_states
        except Exception as e:
            self.logger.error(f"列出Runner错误: {str(e)}")
            return []
    
    async def get_runner_stats(
        self, 
        user_id: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        获取Runner统计信息
        
        Args:
            user_id: 用户ID，如果为None则获取所有用户的统计信息
            
        Returns:
            Dict[str, Any]: Runner统计信息
        """
        try:
            if user_id is not None:
                # 检查用户权限
                has_permission = await self._check_user_permission(user_id, "stats")
                if not has_permission:
                    raise PermissionError(f"用户 {user_id} 没有权限获取Runner统计信息")
            
            # 计算统计信息
            total_runners = 0
            running_runners = 0
            completed_runners = 0
            failed_runners = 0
            user_count = 0
            
            if user_id is not None:
                # 获取特定用户的统计信息
                user_runners = self.runners.get(user_id, {})
                total_runners = len(user_runners)
                user_count = 1 if total_runners > 0 else 0
                
                for runner in user_runners.values():
                    if runner.state == "running":
                        running_runners += 1
                    elif runner.state == "completed":
                        completed_runners += 1
                    elif runner.state == "failed":
                        failed_runners += 1
            else:
                # 获取所有用户的统计信息
                user_count = len(self.runners)
                
                for user_runners in self.runners.values():
                    total_runners += len(user_runners)
                    
                    for runner in user_runners.values():
                        if runner.state == "running":
                            running_runners += 1
                        elif runner.state == "completed":
                            completed_runners += 1
                        elif runner.state == "failed":
                            failed_runners += 1
            
            return {
                "total_runners": total_runners,
                "running_runners": running_runners,
                "completed_runners": completed_runners,
                "failed_runners": failed_runners,
                "user_count": user_count,
                "max_runners_per_user": self.max_runners_per_user,
                "max_total_runners": self.max_total_runners
            }
        except Exception as e:
            self.logger.error(f"获取Runner统计信息错误: {str(e)}")
            return {}
    
    async def create_agent_runner(
        self, 
        user_id: int, 
        owner_id: int, 
        agent_id: int, 
        **kwargs
    ) -> Optional[AgentRunner]:
        """
        创建智能体Runner
        
        Args:
            user_id: 用户ID
            owner_id: 拥有者ID
            agent_id: 智能体ID
            **kwargs: 其他参数
            
        Returns:
            Optional[AgentRunner]: 智能体Runner实例
        """
        # 这里需要创建ADK Agent实例
        # 暂时使用None，在实际使用时需要实现
        agent_instance = None
        
        kwargs["agent_id"] = agent_id
        kwargs["agent_instance"] = agent_instance
        
        return await self.create_runner(AgentRunner, user_id, owner_id, **kwargs)
    
    async def create_workflow_runner(
        self, 
        user_id: int, 
        owner_id: int, 
        workflow_id: int, 
        **kwargs
    ) -> Optional[WorkflowRunner]:
        """
        创建工作流Runner
        
        Args:
            user_id: 用户ID
            owner_id: 拥有者ID
            workflow_id: 工作流ID
            **kwargs: 其他参数
            
        Returns:
            Optional[WorkflowRunner]: 工作流Runner实例
        """
        # 这里需要创建ADK Agent实例
        # 暂时使用None，在实际使用时需要实现
        workflow_instance = None
        
        kwargs["workflow_id"] = workflow_id
        kwargs["workflow_instance"] = workflow_instance
        
        return await self.create_runner(WorkflowRunner, user_id, owner_id, **kwargs)