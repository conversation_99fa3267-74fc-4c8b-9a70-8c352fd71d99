#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A2A多智能体系统日志模块

使用loguru配置结构化日志和数据库日志存储
"""

import sys
import json
import asyncio
from datetime import datetime
from typing import Dict, Any, Optional
from pathlib import Path
from loguru import logger
from sqlalchemy import text

from app.core.config import get_settings
from app.core.database import get_database_manager


class DatabaseLogHandler:
    """
    数据库日志处理器
    
    将日志写入数据库的system_logs表
    """
    
    def __init__(self):
        self._queue = asyncio.Queue(maxsize=1000)
        self._task: Optional[asyncio.Task] = None
        self._running = False
    
    async def start(self) -> None:
        """
        启动数据库日志处理器
        """
        if self._running:
            return
        
        self._running = True
        self._task = asyncio.create_task(self._process_logs())
        logger.info("数据库日志处理器已启动")
    
    async def stop(self) -> None:
        """
        停止数据库日志处理器
        """
        if not self._running:
            return
        
        self._running = False
        
        if self._task:
            self._task.cancel()
            try:
                await self._task
            except asyncio.CancelledError:
                pass
        
        logger.info("数据库日志处理器已停止")
    
    async def write_log(self, record: Dict[str, Any]) -> None:
        """
        写入日志记录
        
        Args:
            record: 日志记录
        """
        try:
            await self._queue.put(record)
        except asyncio.QueueFull:
            # 队列满时丢弃最旧的日志
            try:
                self._queue.get_nowait()
                await self._queue.put(record)
            except asyncio.QueueEmpty:
                pass
    
    async def _process_logs(self) -> None:
        """
        处理日志队列
        """
        while self._running:
            try:
                # 批量处理日志
                records = []
                
                # 收集一批日志记录
                try:
                    record = await asyncio.wait_for(self._queue.get(), timeout=1.0)
                    records.append(record)
                    
                    # 尝试获取更多记录（非阻塞）
                    while len(records) < 100:  # 批量大小限制
                        try:
                            record = self._queue.get_nowait()
                            records.append(record)
                        except asyncio.QueueEmpty:
                            break
                
                except asyncio.TimeoutError:
                    continue
                
                if records:
                    await self._batch_insert_logs(records)
                
            except Exception as e:
                logger.error(f"处理数据库日志失败: {e}")
                await asyncio.sleep(1)
    
    async def _batch_insert_logs(self, records: list) -> None:
        """
        批量插入日志记录
        
        Args:
            records: 日志记录列表
        """
        try:
            db_manager = get_database_manager()
            engine = db_manager.get_engine()
            
            # 构建批量插入SQL
            values = []
            for record in records:
                values.append("""(
                    '{}',
                    '{}',
                    '{}',
                    '{}',
                    '{}',
                    {},
                    '{}',
                    {},
                    '{}',
                    '{}',
                    '{}'
                )""".format(
                    record.get('level', 'INFO'),
                    record.get('logger_name', ''),
                    record.get('message', '').replace("'", "\\'")[:1000],
                    record.get('module', ''),
                    record.get('function', ''),
                    record.get('line_number', 'NULL'),
                    json.dumps(record.get('extra_data', {}), ensure_ascii=False).replace("'", "\\'")[:2000],
                    record.get('user_id', 'NULL'),
                    record.get('session_id', ''),
                    record.get('request_id', ''),
                    record.get('timestamp', datetime.now().isoformat())
                ))
            
            sql = f"""
                INSERT INTO system_logs (
                    level, logger_name, message, module, function, line_number,
                    extra_data, user_id, session_id, request_id, timestamp
                ) VALUES {', '.join(values)}
            """
            
            async with engine.begin() as conn:
                await conn.execute(text(sql))
            
        except Exception as e:
            logger.error(f"批量插入日志失败: {e}")


class StructuredFormatter:
    """
    结构化日志格式化器
    """
    
    @staticmethod
    def format_record(record: dict) -> str:
        """
        格式化日志记录
        
        Args:
            record: 日志记录
            
        Returns:
            str: 格式化后的日志
        """
        # 提取基本信息
        timestamp = record["time"].strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
        level = record["level"].name
        logger_name = record["name"]
        message = record["message"]
        
        # 提取位置信息
        file_info = f"{record['file'].name}:{record['line']}"
        function = record["function"]
        
        # 提取额外数据
        extra = record.get("extra", {})
        
        # 构建结构化日志
        log_data = {
            "timestamp": timestamp,
            "level": level,
            "logger": logger_name,
            "message": message,
            "location": file_info,
            "function": function
        }
        
        # 添加额外字段
        if extra:
            log_data["extra"] = extra
        
        # 格式化输出
        if level in ["ERROR", "CRITICAL"]:
            # 错误日志使用红色
            return f"\033[91m{json.dumps(log_data, ensure_ascii=False, indent=2)}\033[0m"
        elif level == "WARNING":
            # 警告日志使用黄色
            return f"\033[93m{json.dumps(log_data, ensure_ascii=False, indent=2)}\033[0m"
        elif level == "DEBUG":
            # 调试日志使用灰色
            return f"\033[90m{json.dumps(log_data, ensure_ascii=False, indent=2)}\033[0m"
        else:
            # 普通日志
            return json.dumps(log_data, ensure_ascii=False, indent=2)


class LoggingManager:
    """
    日志管理器
    
    负责配置和管理整个应用的日志系统
    """
    
    def __init__(self):
        self._db_handler: Optional[DatabaseLogHandler] = None
        self._initialized = False
    
    async def setup(self) -> None:
        """
        设置日志系统
        """
        if self._initialized:
            return
        
        settings = get_settings()
        
        # 移除默认处理器
        logger.remove()
        
        # 配置控制台输出
        logger.add(
            sys.stdout,
            level=settings.log_level,
            format=self._get_console_format(),
            colorize=True,
            backtrace=True,
            diagnose=True
        )
        
        # 配置文件输出（如果指定了文件路径）
        if settings.log_file_path:
            log_file = Path(settings.log_file_path)
            log_file.parent.mkdir(parents=True, exist_ok=True)
            
            logger.add(
                log_file,
                level=settings.log_level,
                format=self._get_file_format(),
                rotation="100 MB",
                retention="30 days",
                compression="zip",
                encoding="utf-8"
            )
        
        # 配置数据库日志（如果启用）
        if settings.log_to_database:
            await self._setup_database_logging()
        
        # 配置异常处理
        logger.add(
            self._handle_exception,
            level="ERROR",
            format="{time} | {level} | {name} | {message}",
            filter=lambda record: record["level"].name in ["ERROR", "CRITICAL"]
        )
        
        self._initialized = True
        logger.info("日志系统初始化完成")
    
    async def _setup_database_logging(self) -> None:
        """
        设置数据库日志
        """
        try:
            self._db_handler = DatabaseLogHandler()
            await self._db_handler.start()
            
            # 添加数据库日志处理器
            logger.add(
                self._database_log_sink,
                level="INFO",
                format="{time} | {level} | {name} | {message}",
                serialize=False
            )
            
            logger.info("数据库日志已启用")
        except Exception as e:
            logger.error(f"数据库日志设置失败: {e}")
    
    def _database_log_sink(self, message) -> None:
        """
        数据库日志接收器
        
        Args:
            message: 日志消息
        """
        if not self._db_handler:
            return
        
        record = message.record
        
        # 构建数据库日志记录
        log_record = {
            "level": record["level"].name,
            "logger_name": record["name"],
            "message": record["message"],
            "module": record["module"],
            "function": record["function"],
            "line_number": record["line"],
            "extra_data": record.get("extra", {}),
            "timestamp": record["time"].isoformat(),
            "user_id": record.get("extra", {}).get("user_id"),
            "session_id": record.get("extra", {}).get("session_id"),
            "request_id": record.get("extra", {}).get("request_id")
        }
        
        # 异步写入数据库
        try:
            asyncio.create_task(self._db_handler.write_log(log_record))
        except Exception as e:
            # 避免日志循环
            print(f"数据库日志写入失败: {e}")
    
    def _handle_exception(self, message) -> None:
        """
        处理异常日志
        
        Args:
            message: 日志消息
        """
        record = message.record
        
        # 这里可以添加异常通知逻辑
        # 例如发送邮件、钉钉通知等
        pass
    
    def _get_console_format(self) -> str:
        """
        获取控制台日志格式
        
        Returns:
            str: 日志格式
        """
        return (
            "<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | "
            "<level>{level: <8}</level> | "
            "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
            "<level>{message}</level>"
        )
    
    def _get_file_format(self) -> str:
        """
        获取文件日志格式
        
        Returns:
            str: 日志格式
        """
        return (
            "{time:YYYY-MM-DD HH:mm:ss.SSS} | "
            "{level: <8} | "
            "{name}:{function}:{line} | "
            "{message} | "
            "{extra}"
        )
    
    async def shutdown(self) -> None:
        """
        关闭日志系统
        """
        if self._db_handler:
            await self._db_handler.stop()
        
        logger.info("日志系统已关闭")


# 全局日志管理器实例
_logging_manager: Optional[LoggingManager] = None


def get_logging_manager() -> LoggingManager:
    """
    获取日志管理器实例（单例模式）
    
    Returns:
        LoggingManager: 日志管理器实例
    """
    global _logging_manager
    if _logging_manager is None:
        _logging_manager = LoggingManager()
    return _logging_manager


async def setup_logging() -> None:
    """
    设置日志系统
    """
    logging_manager = get_logging_manager()
    await logging_manager.setup()


async def shutdown_logging() -> None:
    """
    关闭日志系统
    """
    logging_manager = get_logging_manager()
    await logging_manager.shutdown()


def get_logger(name: str) -> logger:
    """
    获取指定名称的日志器
    
    Args:
        name: 日志器名称
        
    Returns:
        logger: 日志器实例
    """
    return logger.bind(name=name)


def log_with_context(**context) -> logger:
    """
    创建带上下文的日志器
    
    Args:
        **context: 上下文信息
        
    Returns:
        logger: 带上下文的日志器
    """
    return logger.bind(**context)