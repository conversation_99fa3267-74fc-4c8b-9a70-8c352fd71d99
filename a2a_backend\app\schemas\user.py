#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A2A多智能体系统用户相关Pydantic模式

定义用户相关的请求和响应模式
"""

from typing import Dict, List, Optional, Any
from datetime import datetime
from pydantic import Field, EmailStr, validator
from enum import Enum

from .base import BaseSchema


class UserStatus(str, Enum):
    """用户状态枚举"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    SUSPENDED = "suspended"
    DELETED = "deleted"


class UserRole(str, Enum):
    """用户角色枚举"""
    ADMIN = "admin"
    USER = "user"
    GUEST = "guest"


class UserCreate(BaseSchema):
    """
    用户创建请求模式
    """
    
    username: str = Field(
        min_length=3,
        max_length=50,
        pattern=r'^[a-zA-Z0-9_-]+$',
        description="用户名，3-50个字符，只能包含字母、数字、下划线和连字符"
    )
    
    email: EmailStr = Field(
        description="邮箱地址"
    )
    
    password: str = Field(
        min_length=8,
        max_length=128,
        description="密码，至少8个字符"
    )
    
    nickname: Optional[str] = Field(
        default=None,
        max_length=100,
        description="昵称"
    )
    
    avatar_url: Optional[str] = Field(
        default=None,
        max_length=500,
        description="头像URL"
    )
    
    phone: Optional[str] = Field(
        default=None,
        pattern=r'^\+?[1-9]\d{1,14}$',
        description="手机号码"
    )
    
    role: UserRole = Field(
        default=UserRole.USER,
        description="用户角色"
    )
    
    @validator('password')
    def validate_password(cls, v):
        """验证密码强度"""
        if not any(c.isupper() for c in v):
            raise ValueError('密码必须包含至少一个大写字母')
        if not any(c.islower() for c in v):
            raise ValueError('密码必须包含至少一个小写字母')
        if not any(c.isdigit() for c in v):
            raise ValueError('密码必须包含至少一个数字')
        return v


class UserUpdate(BaseSchema):
    """
    用户更新请求模式
    """
    
    nickname: Optional[str] = Field(
        default=None,
        max_length=100,
        description="昵称"
    )
    
    avatar_url: Optional[str] = Field(
        default=None,
        max_length=500,
        description="头像URL"
    )
    
    phone: Optional[str] = Field(
        default=None,
        pattern=r'^\+?[1-9]\d{1,14}$',
        description="手机号码"
    )
    
    bio: Optional[str] = Field(
        default=None,
        max_length=500,
        description="个人简介"
    )
    
    preferences: Optional[Dict[str, Any]] = Field(
        default=None,
        description="用户偏好设置"
    )
    
    timezone: Optional[str] = Field(
        default=None,
        description="时区"
    )
    
    language: Optional[str] = Field(
        default=None,
        description="语言偏好"
    )


class UserPasswordUpdate(BaseSchema):
    """
    用户密码更新请求模式
    """
    
    old_password: str = Field(
        description="当前密码"
    )
    
    new_password: str = Field(
        min_length=8,
        max_length=128,
        description="新密码，至少8个字符"
    )
    
    @validator('new_password')
    def validate_password(cls, v):
        """验证密码强度"""
        if not any(c.isupper() for c in v):
            raise ValueError('密码必须包含至少一个大写字母')
        if not any(c.islower() for c in v):
            raise ValueError('密码必须包含至少一个小写字母')
        if not any(c.isdigit() for c in v):
            raise ValueError('密码必须包含至少一个数字')
        return v


class UserLogin(BaseSchema):
    """
    用户登录请求模式
    """
    
    username: str = Field(
        description="用户名或邮箱"
    )
    
    password: str = Field(
        description="密码"
    )
    
    remember_me: bool = Field(
        default=False,
        description="记住我"
    )


class UserResponse(BaseSchema):
    """
    用户响应模式
    """
    
    id: str = Field(
        description="用户ID"
    )
    
    username: str = Field(
        description="用户名"
    )
    
    email: str = Field(
        description="邮箱地址"
    )
    
    nickname: Optional[str] = Field(
        description="昵称"
    )
    
    avatar_url: Optional[str] = Field(
        description="头像URL"
    )
    
    phone: Optional[str] = Field(
        description="手机号码"
    )
    
    bio: Optional[str] = Field(
        description="个人简介"
    )
    
    role: UserRole = Field(
        description="用户角色"
    )
    
    status: UserStatus = Field(
        description="用户状态"
    )
    
    preferences: Optional[Dict[str, Any]] = Field(
        description="用户偏好设置"
    )
    
    timezone: Optional[str] = Field(
        description="时区"
    )
    
    language: Optional[str] = Field(
        description="语言偏好"
    )
    
    last_login_at: Optional[datetime] = Field(
        description="最后登录时间"
    )
    
    last_login_ip: Optional[str] = Field(
        description="最后登录IP"
    )
    
    login_count: int = Field(
        description="登录次数"
    )
    
    created_at: datetime = Field(
        description="创建时间"
    )
    
    updated_at: datetime = Field(
        description="更新时间"
    )


class UserTokenResponse(BaseSchema):
    """
    用户令牌响应模式
    """
    
    user: UserResponse = Field(
        description="用户信息"
    )
    
    access_token: str = Field(
        description="访问令牌"
    )
    
    token_type: str = Field(
        default="bearer",
        description="令牌类型"
    )
    
    expires_in: int = Field(
        description="过期时间（秒）"
    )
    
    refresh_token: Optional[str] = Field(
        description="刷新令牌"
    )


class UserPermissionResponse(BaseSchema):
    """
    用户权限响应模式
    """
    
    id: str = Field(
        description="权限ID"
    )
    
    user_id: str = Field(
        description="用户ID"
    )
    
    permission: str = Field(
        description="权限标识"
    )
    
    resource_type: Optional[str] = Field(
        description="资源类型"
    )
    
    resource_id: Optional[str] = Field(
        description="资源ID"
    )
    
    granted_by: str = Field(
        description="授权者ID"
    )
    
    granted_at: datetime = Field(
        description="授权时间"
    )
    
    expires_at: Optional[datetime] = Field(
        description="过期时间"
    )


class UserActivityLogResponse(BaseSchema):
    """
    用户活动日志响应模式
    """
    
    id: str = Field(
        description="日志ID"
    )
    
    user_id: str = Field(
        description="用户ID"
    )
    
    action: str = Field(
        description="操作类型"
    )
    
    resource_type: Optional[str] = Field(
        description="资源类型"
    )
    
    resource_id: Optional[str] = Field(
        description="资源ID"
    )
    
    ip_address: Optional[str] = Field(
        description="IP地址"
    )
    
    user_agent: Optional[str] = Field(
        description="用户代理"
    )
    
    details: Optional[Dict[str, Any]] = Field(
        description="操作详情"
    )
    
    created_at: datetime = Field(
        description="创建时间"
    )


class UserStatsResponse(BaseSchema):
    """
    用户统计信息响应模式
    """
    
    total_sessions: int = Field(
        description="总会话数"
    )
    
    total_messages: int = Field(
        description="总消息数"
    )
    
    total_tasks: int = Field(
        description="总任务数"
    )
    
    total_artifacts: int = Field(
        description="总工件数"
    )
    
    active_sessions: int = Field(
        description="活跃会话数"
    )
    
    completed_tasks: int = Field(
        description="已完成任务数"
    )
    
    storage_used: int = Field(
        description="已使用存储空间（字节）"
    )
    
    last_activity_at: Optional[datetime] = Field(
        description="最后活动时间"
    )