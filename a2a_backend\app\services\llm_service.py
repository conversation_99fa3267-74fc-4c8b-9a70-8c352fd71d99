#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A2A多智能体系统LLM配置管理服务

提供LLM配置的管理、缓存、健康检查和故障转移功能
"""

import asyncio
import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union, Tuple
from dataclasses import dataclass
from enum import Enum
import logging

from sqlalchemy.orm import Session
from sqlalchemy import and_, or_
from cachetools import TTLCache
import aiohttp

from ..models import User, UserConfig, SystemConfig, UserPermission
from ..core.database import get_db
from ..auth.permissions import check_user_permission


class LLMProvider(str, Enum):
    """LLM提供商枚举"""
    GOOGLE = "google"
    OPENAI = "openai"
    ANTHROPIC = "anthropic"
    DASHSCOPE = "dashscope"
    ZHIPU = "zhipu"


class LLMStatus(str, Enum):
    """LLM状态枚举"""
    HEALTHY = "healthy"
    UNHEALTHY = "unhealthy"
    UNKNOWN = "unknown"
    DISABLED = "disabled"


@dataclass
class LLMConfig:
    """LLM配置数据类"""
    provider: LLMProvider
    model_name: str
    api_key: str
    api_endpoint: Optional[str] = None
    api_version: Optional[str] = None
    max_tokens: int = 4096
    temperature: float = 0.7
    timeout: int = 30
    retry_count: int = 3
    retry_delay: float = 1.0
    rate_limit: int = 100  # 每分钟请求数
    enabled: bool = True
    priority: int = 1  # 优先级，数字越小优先级越高
    extra_params: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.extra_params is None:
            self.extra_params = {}


@dataclass
class LLMHealthStatus:
    """LLM健康状态数据类"""
    provider: LLMProvider
    model_name: str
    status: LLMStatus
    response_time: float
    last_check: datetime
    error_message: Optional[str] = None
    success_rate: float = 0.0
    total_requests: int = 0
    failed_requests: int = 0


@dataclass
class LLMUsageStats:
    """LLM使用统计数据类"""
    user_id: int
    provider: LLMProvider
    model_name: str
    total_requests: int = 0
    total_tokens: int = 0
    total_cost: float = 0.0
    last_used: Optional[datetime] = None
    daily_requests: int = 0
    daily_tokens: int = 0
    daily_cost: float = 0.0


class LLMService:
    """
    LLM配置管理服务
    
    提供LLM配置的管理、缓存、健康检查和故障转移功能
    """
    
    def __init__(self, cache_ttl: int = 300):
        """
        初始化LLM服务
        
        Args:
            cache_ttl: 缓存TTL时间（秒）
        """
        self.logger = logging.getLogger(__name__)
        
        # 配置缓存
        self._config_cache: TTLCache = TTLCache(maxsize=1000, ttl=cache_ttl)
        self._user_config_cache: TTLCache = TTLCache(maxsize=5000, ttl=cache_ttl)
        
        # 健康状态缓存
        self._health_cache: TTLCache = TTLCache(maxsize=100, ttl=60)
        
        # 使用统计缓存
        self._usage_cache: TTLCache = TTLCache(maxsize=10000, ttl=3600)
        
        # 故障转移配置
        self._failover_enabled = True
        self._health_check_interval = 60  # 秒
        self._last_health_check = {}
        
        # 速率限制
        self._rate_limits = {}  # {(user_id, provider): [(timestamp, count), ...]}
        
        self.logger.info("LLM服务初始化完成")
    
    async def get_user_llm_config(
        self,
        user_id: int,
        provider: LLMProvider,
        model_name: Optional[str] = None,
        db: Optional[Session] = None
    ) -> Optional[LLMConfig]:
        """
        获取用户的LLM配置
        
        Args:
            user_id: 用户ID
            provider: LLM提供商
            model_name: 模型名称
            db: 数据库会话
        
        Returns:
            Optional[LLMConfig]: LLM配置，如果不存在则返回None
        """
        try:
            # 检查缓存
            cache_key = f"user_llm_config:{user_id}:{provider}:{model_name or 'default'}"
            if cache_key in self._user_config_cache:
                return self._user_config_cache[cache_key]
            
            if db is None:
                db = next(get_db())
            
            # 检查用户权限
            if not await self._check_llm_permission(user_id, provider, db):
                self.logger.warning(f"用户 {user_id} 没有使用 {provider} 的权限")
                return None
            
            # 获取用户配置
            config = await self._load_user_llm_config(user_id, provider, model_name, db)
            
            # 如果用户没有配置，使用系统默认配置
            if not config:
                config = await self._load_system_llm_config(provider, model_name, db)
            
            # 缓存配置
            if config:
                self._user_config_cache[cache_key] = config
            
            return config
            
        except Exception as e:
            self.logger.error(f"获取用户LLM配置失败: {e}")
            return None
    
    async def get_available_llm_configs(
        self,
        user_id: int,
        db: Optional[Session] = None
    ) -> List[LLMConfig]:
        """
        获取用户可用的所有LLM配置
        
        Args:
            user_id: 用户ID
            db: 数据库会话
        
        Returns:
            List[LLMConfig]: 可用的LLM配置列表
        """
        try:
            configs = []
            
            for provider in LLMProvider:
                config = await self.get_user_llm_config(user_id, provider, db=db)
                if config and config.enabled:
                    configs.append(config)
            
            # 按优先级排序
            configs.sort(key=lambda x: x.priority)
            
            return configs
            
        except Exception as e:
            self.logger.error(f"获取可用LLM配置失败: {e}")
            return []
    
    async def get_best_llm_config(
        self,
        user_id: int,
        db: Optional[Session] = None
    ) -> Optional[LLMConfig]:
        """
        获取用户最佳的LLM配置（基于健康状态和优先级）
        
        Args:
            user_id: 用户ID
            db: 数据库会话
        
        Returns:
            Optional[LLMConfig]: 最佳LLM配置
        """
        try:
            configs = await self.get_available_llm_configs(user_id, db)
            
            if not configs:
                return None
            
            # 过滤健康的配置
            healthy_configs = []
            for config in configs:
                health = await self.get_llm_health_status(config.provider, config.model_name)
                if health and health.status == LLMStatus.HEALTHY:
                    healthy_configs.append(config)
            
            # 如果有健康的配置，返回优先级最高的
            if healthy_configs:
                return healthy_configs[0]
            
            # 如果没有健康的配置，返回优先级最高的配置
            return configs[0]
            
        except Exception as e:
            self.logger.error(f"获取最佳LLM配置失败: {e}")
            return None
    
    async def update_user_llm_config(
        self,
        user_id: int,
        provider: LLMProvider,
        config_data: Dict[str, Any],
        db: Optional[Session] = None
    ) -> bool:
        """
        更新用户的LLM配置
        
        Args:
            user_id: 用户ID
            provider: LLM提供商
            config_data: 配置数据
            db: 数据库会话
        
        Returns:
            bool: 是否更新成功
        """
        try:
            if db is None:
                db = next(get_db())
            
            # 检查用户权限
            if not await self._check_llm_permission(user_id, provider, db):
                self.logger.warning(f"用户 {user_id} 没有更新 {provider} 配置的权限")
                return False
            
            # 更新配置
            for key, value in config_data.items():
                config_key = f"llm.{provider}.{key}"
                
                # 查找现有配置
                user_config = db.query(UserConfig).filter(
                    and_(
                        UserConfig.user_id == user_id,
                        UserConfig.config_key == config_key
                    )
                ).first()
                
                if user_config:
                    user_config.set_typed_value(value)
                else:
                    # 创建新配置
                    user_config = UserConfig(
                        user_id=user_id,
                        config_key=config_key,
                        value_type=self._get_value_type(value)
                    )
                    user_config.set_typed_value(value)
                    db.add(user_config)
            
            db.commit()
            
            # 清除缓存
            self._clear_user_config_cache(user_id, provider)
            
            self.logger.info(f"用户 {user_id} 的 {provider} 配置更新成功")
            return True
            
        except Exception as e:
            self.logger.error(f"更新用户LLM配置失败: {e}")
            if db:
                db.rollback()
            return False
    
    async def check_llm_health(
        self,
        provider: LLMProvider,
        model_name: str,
        config: LLMConfig
    ) -> LLMHealthStatus:
        """
        检查LLM健康状态
        
        Args:
            provider: LLM提供商
            model_name: 模型名称
            config: LLM配置
        
        Returns:
            LLMHealthStatus: 健康状态
        """
        try:
            start_time = time.time()
            
            # 根据提供商进行健康检查
            if provider == LLMProvider.GOOGLE:
                status = await self._check_google_health(config)
            elif provider == LLMProvider.OPENAI:
                status = await self._check_openai_health(config)
            elif provider == LLMProvider.ANTHROPIC:
                status = await self._check_anthropic_health(config)
            elif provider == LLMProvider.DASHSCOPE:
                status = await self._check_dashscope_health(config)
            elif provider == LLMProvider.ZHIPU:
                status = await self._check_zhipu_health(config)
            else:
                status = LLMStatus.UNKNOWN
            
            response_time = time.time() - start_time
            
            health_status = LLMHealthStatus(
                provider=provider,
                model_name=model_name,
                status=status,
                response_time=response_time,
                last_check=datetime.now()
            )
            
            # 缓存健康状态
            cache_key = f"health:{provider}:{model_name}"
            self._health_cache[cache_key] = health_status
            
            return health_status
            
        except Exception as e:
            self.logger.error(f"检查LLM健康状态失败: {e}")
            return LLMHealthStatus(
                provider=provider,
                model_name=model_name,
                status=LLMStatus.UNHEALTHY,
                response_time=0.0,
                last_check=datetime.now(),
                error_message=str(e)
            )
    
    async def get_llm_health_status(
        self,
        provider: LLMProvider,
        model_name: str
    ) -> Optional[LLMHealthStatus]:
        """
        获取LLM健康状态
        
        Args:
            provider: LLM提供商
            model_name: 模型名称
        
        Returns:
            Optional[LLMHealthStatus]: 健康状态
        """
        cache_key = f"health:{provider}:{model_name}"
        return self._health_cache.get(cache_key)
    
    async def record_llm_usage(
        self,
        user_id: int,
        provider: LLMProvider,
        model_name: str,
        tokens_used: int,
        cost: float = 0.0,
        success: bool = True
    ) -> None:
        """
        记录LLM使用情况
        
        Args:
            user_id: 用户ID
            provider: LLM提供商
            model_name: 模型名称
            tokens_used: 使用的token数
            cost: 成本
            success: 是否成功
        """
        try:
            cache_key = f"usage:{user_id}:{provider}:{model_name}"
            
            # 获取或创建使用统计
            if cache_key in self._usage_cache:
                stats = self._usage_cache[cache_key]
            else:
                stats = LLMUsageStats(
                    user_id=user_id,
                    provider=provider,
                    model_name=model_name
                )
            
            # 更新统计
            stats.total_requests += 1
            stats.total_tokens += tokens_used
            stats.total_cost += cost
            stats.last_used = datetime.now()
            
            # 更新每日统计
            now = datetime.now()
            if stats.last_used and stats.last_used.date() == now.date():
                stats.daily_requests += 1
                stats.daily_tokens += tokens_used
                stats.daily_cost += cost
            else:
                stats.daily_requests = 1
                stats.daily_tokens = tokens_used
                stats.daily_cost = cost
            
            if not success:
                # 记录失败请求
                pass
            
            # 缓存统计
            self._usage_cache[cache_key] = stats
            
        except Exception as e:
            self.logger.error(f"记录LLM使用情况失败: {e}")
    
    async def get_user_usage_stats(
        self,
        user_id: int,
        provider: Optional[LLMProvider] = None
    ) -> List[LLMUsageStats]:
        """
        获取用户使用统计
        
        Args:
            user_id: 用户ID
            provider: LLM提供商（可选）
        
        Returns:
            List[LLMUsageStats]: 使用统计列表
        """
        try:
            stats = []
            
            for key, usage_stats in self._usage_cache.items():
                if key.startswith(f"usage:{user_id}:"):
                    if provider is None or usage_stats.provider == provider:
                        stats.append(usage_stats)
            
            return stats
            
        except Exception as e:
            self.logger.error(f"获取用户使用统计失败: {e}")
            return []
    
    async def check_rate_limit(
        self,
        user_id: int,
        provider: LLMProvider,
        config: LLMConfig
    ) -> bool:
        """
        检查速率限制
        
        Args:
            user_id: 用户ID
            provider: LLM提供商
            config: LLM配置
        
        Returns:
            bool: 是否在限制范围内
        """
        try:
            key = (user_id, provider)
            now = time.time()
            
            # 获取或创建速率限制记录
            if key not in self._rate_limits:
                self._rate_limits[key] = []
            
            requests = self._rate_limits[key]
            
            # 清理过期记录（超过1分钟）
            requests[:] = [req_time for req_time in requests if now - req_time < 60]
            
            # 检查是否超过限制
            if len(requests) >= config.rate_limit:
                return False
            
            # 记录当前请求
            requests.append(now)
            
            return True
            
        except Exception as e:
            self.logger.error(f"检查速率限制失败: {e}")
            return True  # 出错时允许请求
    
    async def _check_llm_permission(
        self,
        user_id: int,
        provider: LLMProvider,
        db: Session
    ) -> bool:
        """
        检查用户LLM权限
        
        Args:
            user_id: 用户ID
            provider: LLM提供商
            db: 数据库会话
        
        Returns:
            bool: 是否有权限
        """
        try:
            # 检查用户是否有使用特定LLM提供商的权限
            permission_name = f"llm.{provider}.use"
            return await check_user_permission(user_id, permission_name, db)
            
        except Exception as e:
            self.logger.error(f"检查LLM权限失败: {e}")
            return False
    
    async def _load_user_llm_config(
        self,
        user_id: int,
        provider: LLMProvider,
        model_name: Optional[str],
        db: Session
    ) -> Optional[LLMConfig]:
        """
        加载用户LLM配置
        
        Args:
            user_id: 用户ID
            provider: LLM提供商
            model_name: 模型名称
            db: 数据库会话
        
        Returns:
            Optional[LLMConfig]: LLM配置
        """
        try:
            # 查询用户配置
            config_prefix = f"llm.{provider}."
            user_configs = db.query(UserConfig).filter(
                and_(
                    UserConfig.user_id == user_id,
                    UserConfig.config_key.like(f"{config_prefix}%")
                )
            ).all()
            
            if not user_configs:
                return None
            
            # 构建配置字典
            config_dict = {}
            for config in user_configs:
                key = config.config_key.replace(config_prefix, "")
                config_dict[key] = config.get_typed_value()
            
            # 检查必需的配置
            if "api_key" not in config_dict:
                return None
            
            # 创建LLM配置
            return LLMConfig(
                provider=provider,
                model_name=model_name or config_dict.get("model_name", "default"),
                api_key=config_dict["api_key"],
                api_endpoint=config_dict.get("api_endpoint"),
                api_version=config_dict.get("api_version"),
                max_tokens=config_dict.get("max_tokens", 4096),
                temperature=config_dict.get("temperature", 0.7),
                timeout=config_dict.get("timeout", 30),
                retry_count=config_dict.get("retry_count", 3),
                retry_delay=config_dict.get("retry_delay", 1.0),
                rate_limit=config_dict.get("rate_limit", 100),
                enabled=config_dict.get("enabled", True),
                priority=config_dict.get("priority", 1),
                extra_params=config_dict.get("extra_params", {})
            )
            
        except Exception as e:
            self.logger.error(f"加载用户LLM配置失败: {e}")
            return None
    
    async def _load_system_llm_config(
        self,
        provider: LLMProvider,
        model_name: Optional[str],
        db: Session
    ) -> Optional[LLMConfig]:
        """
        加载系统LLM配置
        
        Args:
            provider: LLM提供商
            model_name: 模型名称
            db: 数据库会话
        
        Returns:
            Optional[LLMConfig]: LLM配置
        """
        try:
            # 查询系统配置
            config_prefix = f"llm.{provider}."
            system_configs = db.query(SystemConfig).filter(
                SystemConfig.config_key.like(f"{config_prefix}%")
            ).all()
            
            if not system_configs:
                return None
            
            # 构建配置字典
            config_dict = {}
            for config in system_configs:
                key = config.config_key.replace(config_prefix, "")
                config_dict[key] = config.get_typed_value()
            
            # 检查必需的配置
            if "api_key" not in config_dict:
                return None
            
            # 创建LLM配置
            return LLMConfig(
                provider=provider,
                model_name=model_name or config_dict.get("model_name", "default"),
                api_key=config_dict["api_key"],
                api_endpoint=config_dict.get("api_endpoint"),
                api_version=config_dict.get("api_version"),
                max_tokens=config_dict.get("max_tokens", 4096),
                temperature=config_dict.get("temperature", 0.7),
                timeout=config_dict.get("timeout", 30),
                retry_count=config_dict.get("retry_count", 3),
                retry_delay=config_dict.get("retry_delay", 1.0),
                rate_limit=config_dict.get("rate_limit", 100),
                enabled=config_dict.get("enabled", True),
                priority=config_dict.get("priority", 1),
                extra_params=config_dict.get("extra_params", {})
            )
            
        except Exception as e:
            self.logger.error(f"加载系统LLM配置失败: {e}")
            return None
    
    def _clear_user_config_cache(self, user_id: int, provider: LLMProvider) -> None:
        """
        清除用户配置缓存
        
        Args:
            user_id: 用户ID
            provider: LLM提供商
        """
        try:
            # 清除相关缓存
            keys_to_remove = []
            for key in self._user_config_cache.keys():
                if key.startswith(f"user_llm_config:{user_id}:{provider}"):
                    keys_to_remove.append(key)
            
            for key in keys_to_remove:
                del self._user_config_cache[key]
                
        except Exception as e:
            self.logger.error(f"清除用户配置缓存失败: {e}")
    
    def _get_value_type(self, value: Any) -> str:
        """
        获取值类型
        
        Args:
            value: 值
        
        Returns:
            str: 值类型
        """
        if isinstance(value, str):
            return "string"
        elif isinstance(value, int):
            return "integer"
        elif isinstance(value, float):
            return "float"
        elif isinstance(value, bool):
            return "boolean"
        elif isinstance(value, (dict, list)):
            return "json"
        else:
            return "string"
    
    async def _check_google_health(self, config: LLMConfig) -> LLMStatus:
        """
        检查Google LLM健康状态
        
        Args:
            config: LLM配置
        
        Returns:
            LLMStatus: 健康状态
        """
        try:
            # 简单的健康检查，实际实现中应该调用Google API
            if config.api_key and config.enabled:
                return LLMStatus.HEALTHY
            else:
                return LLMStatus.DISABLED
        except Exception:
            return LLMStatus.UNHEALTHY
    
    async def _check_openai_health(self, config: LLMConfig) -> LLMStatus:
        """
        检查OpenAI LLM健康状态
        
        Args:
            config: LLM配置
        
        Returns:
            LLMStatus: 健康状态
        """
        try:
            # 简单的健康检查，实际实现中应该调用OpenAI API
            if config.api_key and config.enabled:
                return LLMStatus.HEALTHY
            else:
                return LLMStatus.DISABLED
        except Exception:
            return LLMStatus.UNHEALTHY
    
    async def _check_anthropic_health(self, config: LLMConfig) -> LLMStatus:
        """
        检查Anthropic LLM健康状态
        
        Args:
            config: LLM配置
        
        Returns:
            LLMStatus: 健康状态
        """
        try:
            # 简单的健康检查，实际实现中应该调用Anthropic API
            if config.api_key and config.enabled:
                return LLMStatus.HEALTHY
            else:
                return LLMStatus.DISABLED
        except Exception:
            return LLMStatus.UNHEALTHY
    
    async def _check_dashscope_health(self, config: LLMConfig) -> LLMStatus:
        """
        检查DashScope LLM健康状态
        
        Args:
            config: LLM配置
        
        Returns:
            LLMStatus: 健康状态
        """
        try:
            # 简单的健康检查，实际实现中应该调用DashScope API
            if config.api_key and config.enabled:
                return LLMStatus.HEALTHY
            else:
                return LLMStatus.DISABLED
        except Exception:
            return LLMStatus.UNHEALTHY
    
    async def _check_zhipu_health(self, config: LLMConfig) -> LLMStatus:
        """
        检查智谱AI LLM健康状态
        
        Args:
            config: LLM配置
        
        Returns:
            LLMStatus: 健康状态
        """
        try:
            # 简单的健康检查，实际实现中应该调用智谱AI API
            if config.api_key and config.enabled:
                return LLMStatus.HEALTHY
            else:
                return LLMStatus.DISABLED
        except Exception:
            return LLMStatus.UNHEALTHY