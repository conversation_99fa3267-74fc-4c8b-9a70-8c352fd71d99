#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A2A多智能体系统 MCP工具集成

支持MCP协议的工具集成，包含用户权限控制和动态工具发现
"""

import asyncio
import json
import logging
from typing import Dict, List, Any, Optional, Union, AsyncGenerator
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime
import aiohttp
import websockets
from urllib.parse import urlparse

# Google ADK imports
from google.ai.generativelanguage import FunctionDeclaration, Schema, Type

from .base_tool import (
    BaseTool, ToolConfig, ToolResult, ToolError, ToolStatus,
    ToolExecutionContext, ToolPermission
)


class MCPTransport(Enum):
    """MCP传输协议枚举"""
    HTTP = "http"
    WEBSOCKET = "websocket"
    STDIO = "stdio"


class MCPMessageType(Enum):
    """MCP消息类型枚举"""
    REQUEST = "request"
    RESPONSE = "response"
    NOTIFICATION = "notification"
    ERROR = "error"


@dataclass
class MCPToolConfig(ToolConfig):
    """MCP工具配置"""
    server_url: str = ""
    transport: MCPTransport = MCPTransport.HTTP
    auth_token: Optional[str] = None
    headers: Dict[str, str] = field(default_factory=dict)
    connection_timeout: float = 10.0
    request_timeout: float = 30.0
    max_connections: int = 10
    retry_attempts: int = 3
    retry_delay: float = 1.0
    ssl_verify: bool = True
    
    def __post_init__(self):
        """配置后处理"""
        if not self.server_url:
            raise ValueError("MCP服务器URL不能为空")
        
        # 验证URL格式
        parsed = urlparse(self.server_url)
        if not parsed.scheme or not parsed.netloc:
            raise ValueError(f"无效的MCP服务器URL: {self.server_url}")
        
        # 根据传输协议验证URL
        if self.transport == MCPTransport.HTTP and parsed.scheme not in ['http', 'https']:
            raise ValueError("HTTP传输协议需要http或https URL")
        elif self.transport == MCPTransport.WEBSOCKET and parsed.scheme not in ['ws', 'wss']:
            raise ValueError("WebSocket传输协议需要ws或wss URL")


@dataclass
class MCPMessage:
    """MCP消息"""
    id: str
    type: MCPMessageType
    method: Optional[str] = None
    params: Optional[Dict[str, Any]] = None
    result: Optional[Any] = None
    error: Optional[Dict[str, Any]] = None
    timestamp: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        message = {
            "id": self.id,
            "type": self.type.value,
            "timestamp": self.timestamp.isoformat()
        }
        
        if self.method:
            message["method"] = self.method
        if self.params:
            message["params"] = self.params
        if self.result is not None:
            message["result"] = self.result
        if self.error:
            message["error"] = self.error
        
        return message
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'MCPMessage':
        """从字典创建消息"""
        return cls(
            id=data["id"],
            type=MCPMessageType(data["type"]),
            method=data.get("method"),
            params=data.get("params"),
            result=data.get("result"),
            error=data.get("error"),
            timestamp=datetime.fromisoformat(data.get("timestamp", datetime.now().isoformat()))
        )


@dataclass
class MCPToolInfo:
    """MCP工具信息"""
    name: str
    description: str
    parameters: Dict[str, Any]
    required: List[str] = field(default_factory=list)
    version: str = "1.0.0"
    category: str = "general"
    permissions: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)


class MCPClient:
    """MCP客户端"""
    
    def __init__(self, config: MCPToolConfig):
        """
        初始化MCP客户端
        
        Args:
            config: MCP工具配置
        """
        self.config = config
        self.logger = logging.getLogger(f"{__name__}.MCPClient")
        self._session: Optional[aiohttp.ClientSession] = None
        self._websocket: Optional[websockets.WebSocketServerProtocol] = None
        self._connected = False
        self._message_id_counter = 0
        
    async def connect(self) -> bool:
        """
        连接到MCP服务器
        
        Returns:
            bool: 连接是否成功
        """
        try:
            if self.config.transport == MCPTransport.HTTP:
                await self._connect_http()
            elif self.config.transport == MCPTransport.WEBSOCKET:
                await self._connect_websocket()
            else:
                raise ValueError(f"不支持的传输协议: {self.config.transport}")
            
            self._connected = True
            self.logger.info(f"成功连接到MCP服务器: {self.config.server_url}")
            return True
            
        except Exception as e:
            self.logger.error(f"连接MCP服务器失败: {e}")
            return False
    
    async def disconnect(self) -> None:
        """
        断开与MCP服务器的连接
        """
        try:
            if self._websocket:
                await self._websocket.close()
                self._websocket = None
            
            if self._session:
                await self._session.close()
                self._session = None
            
            self._connected = False
            self.logger.info("已断开MCP服务器连接")
            
        except Exception as e:
            self.logger.error(f"断开MCP服务器连接失败: {e}")
    
    async def discover_tools(self) -> List[MCPToolInfo]:
        """
        发现可用的MCP工具
        
        Returns:
            List[MCPToolInfo]: 工具信息列表
        """
        try:
            message = MCPMessage(
                id=self._generate_message_id(),
                type=MCPMessageType.REQUEST,
                method="tools/list"
            )
            
            response = await self._send_message(message)
            
            if response.error:
                raise Exception(f"发现工具失败: {response.error}")
            
            tools = []
            for tool_data in response.result.get("tools", []):
                tools.append(MCPToolInfo(
                    name=tool_data["name"],
                    description=tool_data["description"],
                    parameters=tool_data.get("parameters", {}),
                    required=tool_data.get("required", []),
                    version=tool_data.get("version", "1.0.0"),
                    category=tool_data.get("category", "general"),
                    permissions=tool_data.get("permissions", []),
                    metadata=tool_data.get("metadata", {})
                ))
            
            self.logger.info(f"发现 {len(tools)} 个MCP工具")
            return tools
            
        except Exception as e:
            self.logger.error(f"发现MCP工具失败: {e}")
            return []
    
    async def call_tool(
        self,
        tool_name: str,
        parameters: Dict[str, Any],
        user_id: str
    ) -> Dict[str, Any]:
        """
        调用MCP工具
        
        Args:
            tool_name: 工具名称
            parameters: 调用参数
            user_id: 用户ID
        
        Returns:
            Dict[str, Any]: 调用结果
        """
        try:
            message = MCPMessage(
                id=self._generate_message_id(),
                type=MCPMessageType.REQUEST,
                method="tools/call",
                params={
                    "name": tool_name,
                    "arguments": parameters,
                    "user_id": user_id
                }
            )
            
            response = await self._send_message(message)
            
            if response.error:
                raise Exception(f"调用工具失败: {response.error}")
            
            return response.result
            
        except Exception as e:
            self.logger.error(f"调用MCP工具 {tool_name} 失败: {e}")
            raise
    
    async def _connect_http(self) -> None:
        """
        建立HTTP连接
        """
        connector = aiohttp.TCPConnector(
            limit=self.config.max_connections,
            ssl=self.config.ssl_verify
        )
        
        timeout = aiohttp.ClientTimeout(
            total=self.config.connection_timeout,
            connect=self.config.connection_timeout
        )
        
        headers = self.config.headers.copy()
        if self.config.auth_token:
            headers["Authorization"] = f"Bearer {self.config.auth_token}"
        
        self._session = aiohttp.ClientSession(
            connector=connector,
            timeout=timeout,
            headers=headers
        )
    
    async def _connect_websocket(self) -> None:
        """
        建立WebSocket连接
        """
        headers = self.config.headers.copy()
        if self.config.auth_token:
            headers["Authorization"] = f"Bearer {self.config.auth_token}"
        
        self._websocket = await websockets.connect(
            self.config.server_url,
            extra_headers=headers,
            ping_interval=30,
            ping_timeout=10
        )
    
    async def _send_message(self, message: MCPMessage) -> MCPMessage:
        """
        发送消息并等待响应
        
        Args:
            message: 要发送的消息
        
        Returns:
            MCPMessage: 响应消息
        """
        if not self._connected:
            raise Exception("未连接到MCP服务器")
        
        if self.config.transport == MCPTransport.HTTP:
            return await self._send_http_message(message)
        elif self.config.transport == MCPTransport.WEBSOCKET:
            return await self._send_websocket_message(message)
        else:
            raise ValueError(f"不支持的传输协议: {self.config.transport}")
    
    async def _send_http_message(self, message: MCPMessage) -> MCPMessage:
        """
        通过HTTP发送消息
        
        Args:
            message: 要发送的消息
        
        Returns:
            MCPMessage: 响应消息
        """
        try:
            async with self._session.post(
                self.config.server_url,
                json=message.to_dict(),
                timeout=aiohttp.ClientTimeout(total=self.config.request_timeout)
            ) as response:
                response.raise_for_status()
                response_data = await response.json()
                return MCPMessage.from_dict(response_data)
                
        except Exception as e:
            raise Exception(f"HTTP消息发送失败: {e}")
    
    async def _send_websocket_message(self, message: MCPMessage) -> MCPMessage:
        """
        通过WebSocket发送消息
        
        Args:
            message: 要发送的消息
        
        Returns:
            MCPMessage: 响应消息
        """
        try:
            # 发送消息
            await self._websocket.send(json.dumps(message.to_dict()))
            
            # 等待响应
            response_data = await asyncio.wait_for(
                self._websocket.recv(),
                timeout=self.config.request_timeout
            )
            
            response_dict = json.loads(response_data)
            return MCPMessage.from_dict(response_dict)
            
        except Exception as e:
            raise Exception(f"WebSocket消息发送失败: {e}")
    
    def _generate_message_id(self) -> str:
        """
        生成消息ID
        
        Returns:
            str: 消息ID
        """
        self._message_id_counter += 1
        return f"msg_{self._message_id_counter}_{int(datetime.now().timestamp())}"


class MCPTool(BaseTool):
    """MCP工具实现"""
    
    def __init__(self, config: MCPToolConfig, tool_info: MCPToolInfo):
        """
        初始化MCP工具
        
        Args:
            config: MCP工具配置
            tool_info: MCP工具信息
        """
        # 更新配置名称和描述
        config.name = tool_info.name
        config.description = tool_info.description
        
        super().__init__(config)
        
        self.tool_info = tool_info
        self.mcp_client = MCPClient(config)
        self._connected = False
    
    def get_function_declaration(self) -> FunctionDeclaration:
        """
        获取工具的函数声明
        
        Returns:
            FunctionDeclaration: ADK函数声明
        """
        # 转换参数schema
        properties = {}
        for param_name, param_info in self.tool_info.parameters.get("properties", {}).items():
            param_type = param_info.get("type", "string")
            
            # 映射类型
            if param_type == "string":
                schema_type = Type.STRING
            elif param_type == "number":
                schema_type = Type.NUMBER
            elif param_type == "integer":
                schema_type = Type.INTEGER
            elif param_type == "boolean":
                schema_type = Type.BOOLEAN
            elif param_type == "array":
                schema_type = Type.ARRAY
            elif param_type == "object":
                schema_type = Type.OBJECT
            else:
                schema_type = Type.STRING
            
            properties[param_name] = Schema(
                type=schema_type,
                description=param_info.get("description", "")
            )
        
        parameters_schema = Schema(
            type=Type.OBJECT,
            properties=properties,
            required=self.tool_info.required
        )
        
        return FunctionDeclaration(
            name=self.tool_info.name,
            description=self.tool_info.description,
            parameters=parameters_schema
        )
    
    async def execute(
        self,
        context: ToolExecutionContext,
        **kwargs
    ) -> ToolResult:
        """
        执行MCP工具
        
        Args:
            context: 执行上下文
            **kwargs: 额外参数
        
        Returns:
            ToolResult: 执行结果
        """
        async with self._execution_context(context):
            try:
                # 确保连接
                if not self._connected:
                    success = await self.mcp_client.connect()
                    if not success:
                        return ToolResult(
                            tool_name=self.name,
                            execution_id=context.execution_id,
                            status=ToolStatus.FAILED,
                            error=ToolError(
                                code="CONNECTION_FAILED",
                                message="无法连接到MCP服务器"
                            ),
                            user_id=context.user_id
                        )
                    self._connected = True
                
                # 调用MCP工具
                result = await self.mcp_client.call_tool(
                    self.tool_info.name,
                    context.parameters,
                    context.user_id
                )
                
                return ToolResult(
                    tool_name=self.name,
                    execution_id=context.execution_id,
                    status=ToolStatus.SUCCESS,
                    result=result,
                    user_id=context.user_id,
                    metadata={
                        "mcp_server": self.config.server_url,
                        "tool_info": self.tool_info.__dict__
                    }
                )
                
            except Exception as e:
                self.logger.error(f"MCP工具执行失败: {e}")
                
                return ToolResult(
                    tool_name=self.name,
                    execution_id=context.execution_id,
                    status=ToolStatus.FAILED,
                    error=ToolError(
                        code="MCP_EXECUTION_ERROR",
                        message=f"MCP工具执行失败: {str(e)}"
                    ),
                    user_id=context.user_id
                )
    
    async def _validate_specific_parameters(self, parameters: Dict[str, Any]) -> Optional[ToolError]:
        """
        验证MCP工具参数
        
        Args:
            parameters: 参数字典
        
        Returns:
            Optional[ToolError]: 验证错误
        """
        try:
            # 检查必需参数
            for required_param in self.tool_info.required:
                if required_param not in parameters:
                    return ToolError(
                        code="MISSING_REQUIRED_PARAMETER",
                        message=f"缺少必需参数: {required_param}"
                    )
            
            # 检查参数类型
            param_properties = self.tool_info.parameters.get("properties", {})
            for param_name, param_value in parameters.items():
                if param_name in param_properties:
                    expected_type = param_properties[param_name].get("type")
                    if not self._validate_parameter_type(param_value, expected_type):
                        return ToolError(
                            code="INVALID_PARAMETER_TYPE",
                            message=f"参数 {param_name} 类型错误，期望: {expected_type}"
                        )
            
            return None
            
        except Exception as e:
            return ToolError(
                code="PARAMETER_VALIDATION_ERROR",
                message=f"参数验证异常: {str(e)}"
            )
    
    def _validate_parameter_type(self, value: Any, expected_type: str) -> bool:
        """
        验证参数类型
        
        Args:
            value: 参数值
            expected_type: 期望类型
        
        Returns:
            bool: 类型是否匹配
        """
        if expected_type == "string":
            return isinstance(value, str)
        elif expected_type == "number":
            return isinstance(value, (int, float))
        elif expected_type == "integer":
            return isinstance(value, int)
        elif expected_type == "boolean":
            return isinstance(value, bool)
        elif expected_type == "array":
            return isinstance(value, list)
        elif expected_type == "object":
            return isinstance(value, dict)
        else:
            return True  # 未知类型，允许通过
    
    async def cleanup(self) -> None:
        """
        清理资源
        """
        try:
            if self._connected:
                await self.mcp_client.disconnect()
                self._connected = False
            
        except Exception as e:
            self.logger.error(f"清理MCP工具资源失败: {e}")
    
    def __del__(self):
        """析构函数"""
        if self._connected:
            # 注意：在析构函数中不能使用async/await
            # 这里只是标记，实际清理应该在cleanup方法中进行
            self.logger.warning(f"MCP工具 {self.name} 未正确清理连接")