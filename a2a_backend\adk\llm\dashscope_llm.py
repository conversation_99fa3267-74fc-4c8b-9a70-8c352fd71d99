#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A2A多智能体系统 阿里云千问 DashScope LLM集成

自定义Google ADK LLM实现
"""

import asyncio
import logging
import json
from typing import Dict, List, Optional, Any, AsyncIterator, Union
from datetime import datetime

from google.genai.types import Content, Part, GenerateContentResponse
import aiohttp
import dashscope
from dashscope import Generation
from dashscope.api_entities.dashscope_response import GenerationResponse

from app.services.llm_service import LLMConfig, LLMProvider
from .base_llm import BaseLLM, StreamingResponse


class DashScopeLLM(BaseLLM):
    """
    阿里云千问 DashScope LLM集成
    
    自定义Google ADK LLM实现，支持流式输出和自定义配置
    """
    
    def __init__(self, config: LLMConfig, user_id: int):
        """
        初始化DashScope LLM
        
        Args:
            config: LLM配置
            user_id: 用户ID
        """
        super().__init__(config, user_id)
        
        self.logger = logging.getLogger(__name__)
        self._session: Optional[aiohttp.ClientSession] = None
        
        # 设置DashScope API密钥
        dashscope.api_key = self.config.api_key
        
        # API端点
        self.api_endpoint = self.config.api_endpoint or "https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation"
        
        # 初始化HTTP会话
        self._initialize_session()
        
        self.logger.info(f"DashScope LLM初始化完成，用户: {user_id}, 模型: {config.model_name}")
    
    def _initialize_session(self) -> None:
        """
        初始化HTTP会话
        """
        try:
            # 创建HTTP会话
            timeout = aiohttp.ClientTimeout(total=self.config.timeout)
            self._session = aiohttp.ClientSession(
                timeout=timeout,
                headers={
                    "Authorization": f"Bearer {self.config.api_key}",
                    "Content-Type": "application/json",
                    "X-DashScope-SSE": "enable"
                }
            )
            
        except Exception as e:
            self.logger.error(f"初始化HTTP会话失败: {e}")
            raise
    
    async def generate_content(
        self,
        messages: List[Content],
        stream: bool = False,
        **kwargs
    ) -> Union[GenerateContentResponse, AsyncIterator[GenerateContentResponse]]:
        """
        生成内容
        
        Args:
            messages: 消息列表
            stream: 是否流式输出
            **kwargs: 其他参数
        
        Returns:
            Union[GenerateContentResponse, AsyncIterator[GenerateContentResponse]]: 生成的内容
        """
        try:
            # 记录请求开始
            start_time = datetime.now()
            self.logger.info(f"开始生成内容，用户: {self.user_id}, 流式: {stream}")
            
            # 检查速率限制
            if not await self._check_rate_limit():
                raise Exception("超过速率限制")
            
            # 转换消息格式
            dashscope_messages = self._convert_messages_to_dashscope_format(messages)
            
            if stream:
                return self._generate_content_stream(dashscope_messages, **kwargs)
            else:
                return await self._generate_content_sync(dashscope_messages, **kwargs)
                
        except Exception as e:
            self.logger.error(f"生成内容失败: {e}")
            # 记录失败统计
            await self._record_usage(0, 0.0, False)
            raise
    
    async def _generate_content_sync(
        self,
        messages: List[Dict[str, Any]],
        **kwargs
    ) -> GenerateContentResponse:
        """
        同步生成内容
        
        Args:
            messages: DashScope格式的消息列表
            **kwargs: 其他参数
        
        Returns:
            GenerateContentResponse: 生成的内容
        """
        try:
            # 构建请求参数
            request_params = {
                "model": self.config.model_name,
                "input": {
                    "messages": messages
                },
                "parameters": {
                    "temperature": self.config.temperature,
                    "max_tokens": self.config.max_tokens,
                    "top_p": self.config.extra_params.get("top_p", 0.8),
                    "top_k": self.config.extra_params.get("top_k", 50),
                    "repetition_penalty": self.config.extra_params.get("repetition_penalty", 1.1),
                    "result_format": "message"
                }
            }
            
            # 添加工具调用支持
            if "tools" in kwargs:
                request_params["parameters"]["tools"] = kwargs["tools"]
            
            # 使用DashScope SDK调用
            response = Generation.call(**{
                "model": self.config.model_name,
                "messages": messages,
                "temperature": self.config.temperature,
                "max_tokens": self.config.max_tokens,
                "top_p": self.config.extra_params.get("top_p", 0.8),
                "result_format": "message"
            })
            
            # 转换为ADK格式
            adk_response = self._convert_to_adk_response(response)
            
            # 记录使用统计
            tokens_used = self._count_tokens_from_dashscope_response(response)
            cost = self._calculate_cost(tokens_used)
            await self._record_usage(tokens_used, cost, True)
            
            self.logger.info(f"内容生成完成，用户: {self.user_id}, tokens: {tokens_used}")
            return adk_response
            
        except Exception as e:
            self.logger.error(f"同步生成内容失败: {e}")
            raise
    
    async def _generate_content_stream(
        self,
        messages: List[Dict[str, Any]],
        **kwargs
    ) -> AsyncIterator[GenerateContentResponse]:
        """
        流式生成内容
        
        Args:
            messages: DashScope格式的消息列表
            **kwargs: 其他参数
        
        Yields:
            GenerateContentResponse: 生成的内容片段
        """
        try:
            total_tokens = 0
            accumulated_content = ""
            
            # 构建请求参数
            request_data = {
                "model": self.config.model_name,
                "input": {
                    "messages": messages
                },
                "parameters": {
                    "temperature": self.config.temperature,
                    "max_tokens": self.config.max_tokens,
                    "top_p": self.config.extra_params.get("top_p", 0.8),
                    "top_k": self.config.extra_params.get("top_k", 50),
                    "repetition_penalty": self.config.extra_params.get("repetition_penalty", 1.1),
                    "result_format": "message",
                    "incremental_output": True
                }
            }
            
            # 添加工具调用支持
            if "tools" in kwargs:
                request_data["parameters"]["tools"] = kwargs["tools"]
            
            # 发送流式请求
            async with self._session.post(
                self.api_endpoint,
                json=request_data,
                headers={"Accept": "text/event-stream"}
            ) as response:
                if response.status != 200:
                    error_text = await response.text()
                    raise Exception(f"DashScope API错误: {response.status} - {error_text}")
                
                # 处理SSE流
                async for line in response.content:
                    line = line.decode('utf-8').strip()
                    
                    if line.startswith('data:'):
                        data_str = line[5:].strip()
                        
                        if data_str == '[DONE]':
                            # 发送完成标记
                            final_chunk = self._create_adk_chunk("", True)
                            yield final_chunk
                            break
                        
                        try:
                            data = json.loads(data_str)
                            
                            if 'output' in data and 'choices' in data['output']:
                                choices = data['output']['choices']
                                if choices and 'message' in choices[0]:
                                    message = choices[0]['message']
                                    if 'content' in message:
                                        content_delta = message['content']
                                        
                                        # 处理增量内容
                                        if accumulated_content and content_delta.startswith(accumulated_content):
                                            # 提取新增部分
                                            new_content = content_delta[len(accumulated_content):]
                                        else:
                                            new_content = content_delta
                                        
                                        accumulated_content = content_delta
                                        
                                        if new_content:
                                            # 转换为ADK格式
                                            adk_chunk = self._create_adk_chunk(new_content, False)
                                            
                                            # 估算tokens
                                            chunk_tokens = await self.estimate_tokens(new_content)
                                            total_tokens += chunk_tokens
                                            
                                            yield adk_chunk
                            
                            # 检查是否完成
                            if 'output' in data and 'finish_reason' in data['output']:
                                finish_reason = data['output']['finish_reason']
                                if finish_reason and finish_reason != 'null':
                                    # 发送完成标记
                                    final_chunk = self._create_adk_chunk("", True)
                                    yield final_chunk
                                    break
                        
                        except json.JSONDecodeError:
                            # 忽略无效的JSON数据
                            continue
            
            # 记录使用统计
            cost = self._calculate_cost(total_tokens)
            await self._record_usage(total_tokens, cost, True)
            
            self.logger.info(f"流式内容生成完成，用户: {self.user_id}, tokens: {total_tokens}")
            
        except Exception as e:
            self.logger.error(f"流式生成内容失败: {e}")
            raise
    
    def _convert_messages_to_dashscope_format(self, messages: List[Content]) -> List[Dict[str, Any]]:
        """
        转换消息为DashScope格式
        
        Args:
            messages: ADK消息列表
        
        Returns:
            List[Dict[str, Any]]: DashScope格式的消息列表
        """
        try:
            dashscope_messages = []
            
            for message in messages:
                # 确定角色
                role = "user"  # 默认为用户消息
                
                # 处理消息内容
                content_parts = []
                for part in message.parts:
                    if hasattr(part, 'text') and part.text:
                        content_parts.append(part.text)
                    elif hasattr(part, 'inline_data') and part.inline_data:
                        # DashScope支持多模态输入
                        if part.inline_data.mime_type.startswith("image/"):
                            content_parts.append({
                                "image": f"data:{part.inline_data.mime_type};base64,{part.inline_data.data}"
                            })
                
                # 合并文本内容
                text_content = ""
                multimodal_content = []
                
                for part in content_parts:
                    if isinstance(part, str):
                        text_content += part
                    else:
                        multimodal_content.append(part)
                
                # 构建消息
                if multimodal_content:
                    # 多模态消息
                    content = [{"text": text_content}] + multimodal_content
                else:
                    # 纯文本消息
                    content = text_content
                
                dashscope_messages.append({
                    "role": role,
                    "content": content
                })
            
            return dashscope_messages
            
        except Exception as e:
            self.logger.error(f"转换消息为DashScope格式失败: {e}")
            return []
    
    def _convert_to_adk_response(self, response: GenerationResponse) -> GenerateContentResponse:
        """
        转换为ADK响应格式
        
        Args:
            response: DashScope响应
        
        Returns:
            GenerateContentResponse: ADK格式响应
        """
        try:
            # 提取响应内容
            text_content = ""
            tool_calls = None
            
            if response.status_code == 200 and response.output:
                if hasattr(response.output, 'choices') and response.output.choices:
                    choice = response.output.choices[0]
                    if hasattr(choice, 'message') and choice.message:
                        message = choice.message
                        if hasattr(message, 'content') and message.content:
                            text_content = message.content
                        
                        # 处理工具调用
                        if hasattr(message, 'tool_calls') and message.tool_calls:
                            tool_calls = message.tool_calls
            
            # 创建ADK内容
            parts = []
            if text_content:
                parts.append(Part(text=text_content))
            
            # 处理工具调用
            if tool_calls:
                for tool_call in tool_calls:
                    tool_info = {
                        "tool_call": {
                            "id": getattr(tool_call, 'id', ''),
                            "type": getattr(tool_call, 'type', 'function'),
                            "function": {
                                "name": getattr(tool_call.function, 'name', '') if hasattr(tool_call, 'function') else '',
                                "arguments": getattr(tool_call.function, 'arguments', '') if hasattr(tool_call, 'function') else ''
                            }
                        }
                    }
                    parts.append(Part(text=f"[Tool Call: {tool_info}]"))
            
            content = Content(parts=parts)
            
            # 创建ADK响应
            adk_response = type('GenerateContentResponse', (), {
                'candidates': [type('Candidate', (), {
                    'content': content,
                    'finish_reason': getattr(response.output, 'finish_reason', None) if response.output else None,
                    'safety_ratings': []
                })()],
                'usage_metadata': type('UsageMetadata', (), {
                    'prompt_token_count': getattr(response.usage, 'input_tokens', 0) if hasattr(response, 'usage') and response.usage else 0,
                    'candidates_token_count': getattr(response.usage, 'output_tokens', 0) if hasattr(response, 'usage') and response.usage else 0,
                    'total_token_count': getattr(response.usage, 'total_tokens', 0) if hasattr(response, 'usage') and response.usage else 0
                })()
            })()
            
            return adk_response
            
        except Exception as e:
            self.logger.error(f"转换ADK响应格式失败: {e}")
            # 返回一个空的响应
            return type('GenerateContentResponse', (), {
                'candidates': [],
                'usage_metadata': None
            })()
    
    def _create_adk_chunk(self, content: str, is_complete: bool) -> GenerateContentResponse:
        """
        创建ADK格式的流式响应片段
        
        Args:
            content: 内容
            is_complete: 是否完成
        
        Returns:
            GenerateContentResponse: ADK格式响应片段
        """
        try:
            parts = [Part(text=content)] if content else []
            adk_content = Content(parts=parts)
            
            # 创建ADK响应片段
            adk_chunk = type('GenerateContentResponse', (), {
                'candidates': [type('Candidate', (), {
                    'content': adk_content,
                    'finish_reason': 'stop' if is_complete else None,
                    'safety_ratings': []
                })()],
                'usage_metadata': None
            })()
            
            return adk_chunk
            
        except Exception as e:
            self.logger.error(f"创建ADK响应片段失败: {e}")
            return type('GenerateContentResponse', (), {
                'candidates': [],
                'usage_metadata': None
            })()
    
    def _count_tokens_from_dashscope_response(self, response: GenerationResponse) -> int:
        """
        从DashScope响应中统计token数量
        
        Args:
            response: DashScope响应
        
        Returns:
            int: token数量
        """
        try:
            if hasattr(response, 'usage') and response.usage:
                return getattr(response.usage, 'total_tokens', 0)
            
            # 如果没有usage信息，估算token数量
            text_content = ""
            if response.output and hasattr(response.output, 'choices') and response.output.choices:
                choice = response.output.choices[0]
                if hasattr(choice, 'message') and choice.message and hasattr(choice.message, 'content'):
                    text_content = choice.message.content
            
            # 简单估算：1个token约等于1.5个中文字符或4个英文字符
            chinese_chars = sum(1 for char in text_content if '\u4e00' <= char <= '\u9fff')
            other_chars = len(text_content) - chinese_chars
            return int(chinese_chars / 1.5 + other_chars / 4)
            
        except Exception as e:
            self.logger.error(f"统计token数量失败: {e}")
            return 0
    
    def _calculate_cost(self, tokens: int) -> float:
        """
        计算成本
        
        Args:
            tokens: token数量
        
        Returns:
            float: 成本
        """
        try:
            # DashScope的定价（示例，实际价格可能不同）
            # 这里需要根据实际的定价来计算
            cost_per_1k_tokens = self.config.extra_params.get("cost_per_1k_tokens", 0.001)
            return (tokens / 1000) * cost_per_1k_tokens
            
        except Exception as e:
            self.logger.error(f"计算成本失败: {e}")
            return 0.0
    
    async def get_model_info(self) -> Dict[str, Any]:
        """
        获取模型信息
        
        Returns:
            Dict[str, Any]: 模型信息
        """
        try:
            return {
                "provider": LLMProvider.DASHSCOPE,
                "model_name": self.config.model_name,
                "max_tokens": self.config.max_tokens,
                "temperature": self.config.temperature,
                "supports_streaming": True,
                "supports_function_calling": self._supports_function_calling(),
                "supports_vision": self._supports_vision(),
                "context_window": self._get_context_window_for_model()
            }
            
        except Exception as e:
            self.logger.error(f"获取模型信息失败: {e}")
            return {}
    
    def _get_context_window_for_model(self) -> int:
        """
        根据模型名称获取上下文窗口大小
        
        Returns:
            int: 上下文窗口大小
        """
        model_context_windows = {
            "qwen-turbo": 8192,
            "qwen-plus": 32768,
            "qwen-max": 8192,
            "qwen-max-longcontext": 30000,
            "qwen1.5-72b-chat": 32768,
            "qwen1.5-14b-chat": 8192,
            "qwen1.5-7b-chat": 8192,
            "qwen-vl-plus": 8192,
            "qwen-vl-max": 8192,
        }
        
        for model_prefix, context_window in model_context_windows.items():
            if self.config.model_name.startswith(model_prefix):
                return context_window
        
        # 默认值
        return self.config.extra_params.get("context_window", 8192)
    
    def _supports_function_calling(self) -> bool:
        """
        是否支持函数调用
        
        Returns:
            bool: 是否支持函数调用
        """
        # 千问系列模型支持函数调用
        function_calling_models = [
            "qwen-turbo", "qwen-plus", "qwen-max",
            "qwen1.5-72b-chat", "qwen1.5-14b-chat", "qwen1.5-7b-chat"
        ]
        
        return any(model in self.config.model_name for model in function_calling_models)
    
    def supports_function_calling(self) -> bool:
        """
        是否支持函数调用
        
        Returns:
            bool: 是否支持函数调用
        """
        return self._supports_function_calling()
    
    def _supports_vision(self) -> bool:
        """
        是否支持视觉输入
        
        Returns:
            bool: 是否支持视觉输入
        """
        # 千问VL系列支持视觉输入
        return "qwen-vl" in self.config.model_name
    
    def supports_vision(self) -> bool:
        """
        是否支持视觉输入
        
        Returns:
            bool: 是否支持视觉输入
        """
        return self._supports_vision()
    
    async def validate_config(self) -> bool:
        """
        验证配置
        
        Returns:
            bool: 配置是否有效
        """
        try:
            # 检查必需的配置
            if not self.config.api_key:
                self.logger.error("缺少API密钥")
                return False
            
            if not self.config.model_name:
                self.logger.error("缺少模型名称")
                return False
            
            # 尝试进行简单的API调用来验证配置
            test_messages = [{
                "role": "user",
                "content": "Hello"
            }]
            
            response = Generation.call(
                model=self.config.model_name,
                messages=test_messages,
                max_tokens=10
            )
            
            if response.status_code == 200 and response.output:
                self.logger.info("配置验证成功")
                return True
            else:
                self.logger.error(f"配置验证失败：{response.message if hasattr(response, 'message') else '无效响应'}")
                return False
                
        except Exception as e:
            self.logger.error(f"配置验证失败: {e}")
            return False
    
    async def cleanup(self) -> None:
        """
        清理资源
        """
        try:
            if self._session:
                await self._session.close()
            self._session = None
            self.logger.info(f"DashScope LLM资源清理完成，用户: {self.user_id}")
            
        except Exception as e:
            self.logger.error(f"清理资源失败: {e}")
    
    @classmethod
    async def create(
        cls,
        config: LLMConfig,
        user_id: int,
        **kwargs
    ) -> "DashScopeLLM":
        """
        创建DashScope LLM实例
        
        Args:
            config: LLM配置
            user_id: 用户ID
            **kwargs: 其他参数
        
        Returns:
            DashScopeLLM: DashScope LLM实例
        """
        try:
            instance = cls(config, user_id)
            
            # 验证配置
            if not await instance.validate_config():
                raise Exception("DashScope LLM配置验证失败")
            
            return instance
            
        except Exception as e:
            logging.getLogger(__name__).error(f"创建DashScope LLM实例失败: {e}")
            raise