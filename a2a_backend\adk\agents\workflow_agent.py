#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A2A多智能体系统 工作流智能体

支持复杂工作流编排，包含用户权限控制
"""

import logging
import asyncio
from typing import Dict, List, Optional, Any, Union, Callable, Awaitable
from datetime import datetime
import json
import uuid

from google.adk.agents.llm_agent import Agent
from google.ai.generativelanguage import Content, Part
from google.generativeai.types import generation_types

from app.models.user import User
from app.models.workflow import Workflow, WorkflowStep, WorkflowExecution
from app.models.agent import Agent as AgentModel
from app.core.logging import get_logger
from app.auth.permissions import check_user_permission

from .custom_llm_agent import CustomLLMAgent

class WorkflowAgent(Agent):
    """
    工作流智能体，支持复杂工作流编排，包含用户权限控制
    
    提供以下功能：
    1. 工作流节点管理
    2. 多智能体协作
    3. 条件分支和循环
    4. 并行执行
    5. 错误处理和重试
    6. 工作流状态管理
    """
    
    def __init__(
        self,
        user_id: str,
        owner_id: str,
        workflow_model: Workflow,
        logger: Optional[logging.Logger] = None,
        **kwargs
    ):
        """
        初始化工作流智能体
        
        Args:
            user_id: 用户ID
            owner_id: 拥有者ID
            workflow_model: 工作流模型
            options: ADK Agent选项
            logger: 日志记录器
        """
        self.user_id = user_id
        self.owner_id = owner_id
        self.workflow_model = workflow_model
        self.logger = logger or get_logger(f"workflow_agent_{workflow_model.id}")
        
        # 初始化基类
        super().__init__(
            name=workflow_model.name,
            description=workflow_model.description
        )
        
        # 工作流状态
        self.current_execution = None
        self.current_node = None
        self.completed_nodes = []
        self.failed_nodes = []
        self.node_results = {}
        
        # 智能体实例缓存
        self.agent_instances: Dict[int, CustomLLMAgent] = {}
        
        # 执行统计
        self.execution_stats = {
            "total_executions": 0,
            "successful_executions": 0,
            "failed_executions": 0,
            "total_nodes_executed": 0,
            "average_execution_time": 0.0,
            "last_execution": None
        }
        
        self.logger.info(f"WorkflowAgent已初始化，工作流ID: {workflow_model.id}")
    
    async def _check_permission(self) -> bool:
        """
        检查用户权限
        
        Returns:
            bool: 是否有权限
        """
        try:
            # 检查用户是否存在
            user = await User.get_by_id(self.user_id)
            if not user:
                self.logger.error(f"用户不存在: {self.user_id}")
                return False
            
            # 检查用户是否有权限使用此工作流
            has_permission = await check_user_permission(
                user_id=self.user_id,
                owner_id=self.owner_id,
                resource_type="workflow",
                action="execute",
                resource_id=str(self.workflow_model.id)
            )
            
            if not has_permission:
                self.logger.error(f"用户 {self.user_id} 没有权限执行工作流 {self.workflow_model.id}")
            
            return has_permission
        except Exception as e:
            self.logger.error(f"权限检查错误: {str(e)}")
            return False
    
    async def _load_workflow_nodes(self) -> List[WorkflowStep]:
        """
        加载工作流节点
        
        Returns:
            List[WorkflowStep]: 工作流节点列表
        """
        try:
            nodes = await WorkflowStep.get_by_workflow_id(self.workflow_model.id)
            return nodes or []
        except Exception as e:
            self.logger.error(f"加载工作流节点错误: {str(e)}")
            return []
    
    async def _get_agent_instance(self, agent_id: int) -> Optional[CustomLLMAgent]:
        """
        获取智能体实例
        
        Args:
            agent_id: 智能体ID
            
        Returns:
            Optional[CustomLLMAgent]: 智能体实例
        """
        # 检查缓存
        if agent_id in self.agent_instances:
            return self.agent_instances[agent_id]
        
        try:
            # 加载智能体模型
            agent_model = await AgentModel.get_by_id(agent_id)
            if not agent_model:
                self.logger.error(f"智能体不存在: {agent_id}")
                return None
            
            # 检查用户是否有权限访问此智能体
            if agent_model.user_id != self.user_id and agent_model.owner_id != self.owner_id and not agent_model.is_public:
                self.logger.error(f"用户 {self.user_id} 没有权限访问智能体 {agent_id}")
                return None
            
            # 这里需要加载LLM配置并创建CustomLLMAgent实例
            # 暂时返回None，在实际使用时需要实现
            # llm_config = await LLMConfig.get_by_agent_id(agent_id)
            # agent_instance = await CustomLLMAgent.create(
            #     user_id=self.user_id,
            #     owner_id=self.owner_id,
            #     agent_model=agent_model,
            #     llm_config=llm_config
            # )
            agent_instance = None
            
            # 缓存实例
            if agent_instance:
                self.agent_instances[agent_id] = agent_instance
            
            return agent_instance
        except Exception as e:
            self.logger.error(f"获取智能体实例错误: {str(e)}")
            return None
    
    async def _create_execution_record(self) -> Optional[WorkflowExecution]:
        """
        创建工作流执行记录
        
        Returns:
            Optional[WorkflowExecution]: 工作流执行记录
        """
        try:
            execution = WorkflowExecution(
                workflow_id=self.workflow_model.id,
                user_id=self.user_id,
                owner_id=self.owner_id,
                execution_id=str(uuid.uuid4()),
                status="running",
                config={},
                metrics={}
            )
            
            await execution.save()
            self.current_execution = execution
            return execution
        except Exception as e:
            self.logger.error(f"创建工作流执行记录错误: {str(e)}")
            return None
    
    async def _update_execution_record(self, status: str, result: Optional[Any] = None) -> None:
        """
        更新工作流执行记录
        
        Args:
            status: 执行状态
            result: 执行结果
        """
        if not self.current_execution:
            return
        
        try:
            self.current_execution.status = status
            self.current_execution.result = result
            self.current_execution.metrics = self.execution_stats
            self.current_execution.end_time = datetime.now() if status in ["completed", "failed", "stopped"] else None
            
            await self.current_execution.save()
        except Exception as e:
            self.logger.error(f"更新工作流执行记录错误: {str(e)}")
    
    async def _execute_node(self, node: WorkflowStep, input_data: Any) -> Any:
        """
        执行工作流节点
        
        Args:
            node: 工作流节点
            input_data: 输入数据
            
        Returns:
            Any: 节点执行结果
        """
        try:
            self.current_node = node
            self.logger.info(f"开始执行节点: {node.name} (ID: {node.id})")
            
            # 根据节点类型执行不同的逻辑
            if node.type == "agent":
                return await self._execute_agent_node(node, input_data)
            elif node.type == "condition":
                return await self._execute_condition_node(node, input_data)
            elif node.type == "loop":
                return await self._execute_loop_node(node, input_data)
            elif node.type == "parallel":
                return await self._execute_parallel_node(node, input_data)
            elif node.type == "transform":
                return await self._execute_transform_node(node, input_data)
            else:
                raise ValueError(f"不支持的节点类型: {node.type}")
        except Exception as e:
            self.logger.error(f"节点执行失败: {node.name}, 错误: {str(e)}")
            self.failed_nodes.append(node.id)
            raise e
    
    async def _execute_agent_node(self, node: WorkflowStep, input_data: Any) -> Any:
        """
        执行智能体节点
        
        Args:
            node: 工作流节点
            input_data: 输入数据
            
        Returns:
            Any: 智能体执行结果
        """
        # 获取智能体实例
        agent_instance = await self._get_agent_instance(node.agent_id)
        if not agent_instance:
            raise ValueError(f"无法获取智能体实例: {node.agent_id}")
        
        # 准备输入内容
        if isinstance(input_data, str):
            content = input_data
        else:
            content = json.dumps(input_data) if input_data else ""
        
        # 执行智能体
        result = await agent_instance.generate(content)
        
        # 记录结果
        self.node_results[node.id] = result
        self.completed_nodes.append(node.id)
        
        return result
    
    async def _execute_condition_node(self, node: WorkflowStep, input_data: Any) -> Any:
        """
        执行条件节点
        
        Args:
            node: 工作流节点
            input_data: 输入数据
            
        Returns:
            Any: 条件执行结果
        """
        # 获取条件配置
        condition_config = node.config or {}
        condition_expr = condition_config.get("condition", "True")
        
        # 评估条件
        try:
            # 这里需要实现安全的表达式评估
            # 暂时使用简单的字符串比较
            condition_result = self._evaluate_condition(condition_expr, input_data)
        except Exception as e:
            self.logger.error(f"条件评估错误: {str(e)}")
            condition_result = False
        
        # 记录结果
        self.node_results[node.id] = condition_result
        self.completed_nodes.append(node.id)
        
        return condition_result
    
    def _evaluate_condition(self, condition_expr: str, input_data: Any) -> bool:
        """
        评估条件表达式
        
        Args:
            condition_expr: 条件表达式
            input_data: 输入数据
            
        Returns:
            bool: 条件结果
        """
        # 这里需要实现安全的表达式评估
        # 暂时使用简单的实现
        if condition_expr == "True":
            return True
        elif condition_expr == "False":
            return False
        else:
            # 可以实现更复杂的条件评估逻辑
            return True
    
    async def _execute_loop_node(self, node: WorkflowStep, input_data: Any) -> Any:
        """
        执行循环节点
        
        Args:
            node: 工作流节点
            input_data: 输入数据
            
        Returns:
            Any: 循环执行结果
        """
        # 获取循环配置
        loop_config = node.config or {}
        max_iterations = loop_config.get("max_iterations", 10)
        condition_expr = loop_config.get("condition", "False")
        
        results = []
        current_data = input_data
        
        for i in range(max_iterations):
            # 检查循环条件
            if not self._evaluate_condition(condition_expr, current_data):
                break
            
            # 执行循环体（这里需要根据实际需求实现）
            # 暂时直接返回输入数据
            results.append(current_data)
        
        # 记录结果
        self.node_results[node.id] = results
        self.completed_nodes.append(node.id)
        
        return results
    
    async def _execute_parallel_node(self, node: WorkflowStep, input_data: Any) -> Any:
        """
        执行并行节点
        
        Args:
            node: 工作流节点
            input_data: 输入数据
            
        Returns:
            Any: 并行执行结果
        """
        # 获取并行配置
        parallel_config = node.config or {}
        parallel_nodes = parallel_config.get("parallel_nodes", [])
        
        # 创建并行任务
        tasks = []
        for node_id in parallel_nodes:
            # 这里需要根据node_id获取实际的节点并执行
            # 暂时使用占位符实现
            task = asyncio.create_task(self._execute_placeholder_node(node_id, input_data))
            tasks.append(task)
        
        # 等待所有任务完成
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 记录结果
        self.node_results[node.id] = results
        self.completed_nodes.append(node.id)
        
        return results
    
    async def _execute_placeholder_node(self, node_id: int, input_data: Any) -> Any:
        """
        执行占位符节点（用于并行执行）
        
        Args:
            node_id: 节点ID
            input_data: 输入数据
            
        Returns:
            Any: 节点执行结果
        """
        # 这里需要根据node_id获取实际的节点并执行
        # 暂时返回输入数据
        return input_data
    
    async def _execute_transform_node(self, node: WorkflowStep, input_data: Any) -> Any:
        """
        执行转换节点
        
        Args:
            node: 工作流节点
            input_data: 输入数据
            
        Returns:
            Any: 转换结果
        """
        # 获取转换配置
        transform_config = node.config or {}
        transform_type = transform_config.get("type", "identity")
        
        # 根据转换类型执行不同的转换
        if transform_type == "identity":
            result = input_data
        elif transform_type == "json_parse":
            result = json.loads(input_data) if isinstance(input_data, str) else input_data
        elif transform_type == "json_stringify":
            result = json.dumps(input_data)
        else:
            # 可以实现更多的转换类型
            result = input_data
        
        # 记录结果
        self.node_results[node.id] = result
        self.completed_nodes.append(node.id)
        
        return result
    
    async def _get_next_nodes(self, current_node_id: Optional[int], nodes: List[WorkflowStep]) -> List[WorkflowStep]:
        """
        获取下一个要执行的节点
        
        Args:
            current_node_id: 当前节点ID
            nodes: 所有节点列表
            
        Returns:
            List[WorkflowStep]: 下一个要执行的节点列表
        """
        if current_node_id is None:
            # 返回起始节点（没有依赖的节点）
            return [node for node in nodes if not node.dependencies]
        
        # 查找依赖当前节点的节点
        next_nodes = []
        for node in nodes:
            if node.id in self.completed_nodes:
                continue
            
            # 检查节点的所有依赖是否都已完成
            if node.dependencies:
                dependencies = node.dependencies if isinstance(node.dependencies, list) else [node.dependencies]
                if all(dep_id in self.completed_nodes for dep_id in dependencies):
                    next_nodes.append(node)
        
        return next_nodes
    
    async def execute_workflow(
        self, 
        input_data: Any,
        stream: bool = False
    ) -> Any:
        """
        执行工作流
        
        Args:
            input_data: 输入数据
            stream: 是否使用流式输出
            
        Returns:
            Any: 工作流执行结果
        """
        # 检查权限
        has_permission = await self._check_permission()
        if not has_permission:
            raise PermissionError(f"用户 {self.user_id} 没有权限执行工作流 {self.workflow_model.id}")
        
        # 加载工作流节点
        nodes = await self._load_workflow_nodes()
        if not nodes:
            raise ValueError(f"工作流没有节点: {self.workflow_model.id}")
        
        # 创建执行记录
        execution = await self._create_execution_record()
        
        # 重置状态
        self.completed_nodes = []
        self.failed_nodes = []
        self.node_results = {}
        
        start_time = datetime.now()
        
        try:
            current_data = input_data
            current_node_id = None
            
            # 执行工作流
            while True:
                # 获取下一个要执行的节点
                next_nodes = await self._get_next_nodes(current_node_id, nodes)
                
                if not next_nodes:
                    # 没有更多节点要执行
                    break
                
                # 执行节点
                for node in next_nodes:
                    result = await self._execute_node(node, current_data)
                    current_data = result
                    current_node_id = node.id
                    
                    self.execution_stats["total_nodes_executed"] += 1
            
            # 更新统计
            end_time = datetime.now()
            execution_time = (end_time - start_time).total_seconds()
            
            self.execution_stats["total_executions"] += 1
            self.execution_stats["successful_executions"] += 1
            self.execution_stats["last_execution"] = end_time.isoformat()
            
            # 更新平均执行时间
            total_time = self.execution_stats["average_execution_time"] * (self.execution_stats["total_executions"] - 1)
            self.execution_stats["average_execution_time"] = (total_time + execution_time) / self.execution_stats["total_executions"]
            
            # 更新执行记录
            await self._update_execution_record("completed", current_data)
            
            self.logger.info(f"工作流执行完成: {self.workflow_model.id}")
            return current_data
        except Exception as e:
            # 更新统计
            self.execution_stats["total_executions"] += 1
            self.execution_stats["failed_executions"] += 1
            
            # 更新执行记录
            await self._update_execution_record("failed", str(e))
            
            self.logger.error(f"工作流执行失败: {str(e)}")
            raise e
    
    def get_execution_stats(self) -> Dict[str, Any]:
        """
        获取执行统计
        
        Returns:
            Dict[str, Any]: 执行统计信息
        """
        return self.execution_stats.copy()
    
    def get_current_state(self) -> Dict[str, Any]:
        """
        获取当前状态
        
        Returns:
            Dict[str, Any]: 当前状态信息
        """
        return {
            "workflow_id": self.workflow_model.id,
            "current_execution_id": self.current_execution.execution_id if self.current_execution else None,
            "current_node_id": self.current_node.id if self.current_node else None,
            "completed_nodes": self.completed_nodes,
            "failed_nodes": self.failed_nodes,
            "node_results": self.node_results,
            "execution_stats": self.execution_stats
        }
    
    @classmethod
    async def create(
        cls,
        user_id: int,
        owner_id: int,
        workflow_model: Workflow,
        **kwargs
    ) -> "WorkflowAgent":
        """
        创建WorkflowAgent实例的工厂方法
        
        Args:
            user_id: 用户ID
            owner_id: 拥有者ID
            workflow_model: 工作流模型
            **kwargs: 其他参数
            
        Returns:
            WorkflowAgent: WorkflowAgent实例
        """
        # 检查用户权限
        user = await User.get_by_id(user_id)
        if not user:
            raise ValueError(f"用户不存在: {user_id}")
        
        has_permission = await check_user_permission(
            user_id=user_id,
            owner_id=owner_id,
            resource_type="workflow",
            action="create",
            resource_id=str(workflow_model.id)
        )
        if not has_permission:
            raise PermissionError(f"用户 {user_id} 没有权限创建工作流智能体")
        
        # 创建实例
        instance = cls(
            user_id=user_id,
            owner_id=owner_id,
            workflow_model=workflow_model,
            **kwargs
        )
        
        return instance