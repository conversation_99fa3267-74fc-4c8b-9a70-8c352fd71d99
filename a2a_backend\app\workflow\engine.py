# -*- coding: utf-8 -*-
"""
A2A多智能体系统工作流引擎

基于Google ADK的工作流引擎，支持复杂的多智能体协作
"""

import logging
import asyncio
import json
import uuid
from typing import Dict, List, Optional, Any, Union, Tuple, Set
from datetime import datetime, timedelta
from enum import Enum
from dataclasses import dataclass
from sqlalchemy.orm import Session

from adk.agents.workflow_agent import WorkflowAgent
from adk.agents.sequential_agent import SequentialAgent
from adk.agents.parallel_agent import ParallelAgent
from adk.agents.loop_agent import LoopAgent
from adk.agents.branch_agent import BranchAgent
from adk.runners.workflow_runner import WorkflowRunner

from app.models.workflow import Workflow, WorkflowExecution, WorkflowStep
from app.models.agent import Agent
from app.schemas.workflow import WorkflowExecutionStatus, WorkflowNodeStatus
from app.core.database import get_db
from app.core.logging import get_logger
from app.core.config import get_settings
from .context_manager import WorkflowContextManager
from .executor import SequentialExecutor, ParallelExecutor, LoopExecutor, BranchExecutor


class ExecutionPlan:
    """
    工作流执行计划
    """
    
    def __init__(self):
        self.nodes: List[Dict[str, Any]] = []
        self.edges: List[Dict[str, Any]] = []
        self.start_node: Optional[str] = None
        self.end_nodes: List[str] = []
        self.execution_order: List[List[str]] = []  # 按层级组织的执行顺序
        self.dependencies: Dict[str, List[str]] = {}  # 节点依赖关系
        self.parallel_groups: List[List[str]] = []  # 并行执行组
        self.estimated_duration: float = 0.0
        self.resource_requirements: Dict[str, Any] = {}


class WorkflowEngine:
    """
    工作流引擎类
    
    提供以下功能：
    1. 基于ADK的SequentialAgent、ParallelAgent、LoopAgent
    2. 工作流的解析和执行计划生成
    3. 工作流步骤的调度和执行
    4. 工作流异常的处理和恢复
    5. 工作流状态的实时监控和更新
    6. 工作流执行的性能优化
    """
    
    def __init__(self, db: Session, logger: Optional[logging.Logger] = None):
        """
        初始化工作流引擎
        
        Args:
            db: 数据库会话
            logger: 日志记录器
        """
        self.db = db
        self.logger = logger or get_logger("workflow_engine")
        self.settings = get_settings()
        
        # 初始化执行器
        self.sequential_executor = SequentialExecutor(db, logger)
        self.parallel_executor = ParallelExecutor(db, logger)
        self.loop_executor = LoopExecutor(db, logger)
        self.branch_executor = BranchExecutor(db, logger)
        
        # 初始化上下文管理器
        self.context_manager = WorkflowContextManager(db, logger)
        
        # 执行状态跟踪
        self.running_executions: Dict[str, Dict[str, Any]] = {}
        self.execution_locks: Dict[str, asyncio.Lock] = {}
        
        # 性能配置
        self.max_concurrent_nodes = 10
        self.node_timeout = 300  # 5分钟
        self.execution_timeout = 3600  # 1小时
        
        self.logger.info("WorkflowEngine已初始化")
    
    async def parse_workflow_definition(self, definition: Dict[str, Any]) -> ExecutionPlan:
        """
        解析工作流定义并生成执行计划
        
        Args:
            definition: 工作流定义
            
        Returns:
            ExecutionPlan: 执行计划
        """
        try:
            plan = ExecutionPlan()
            
            # 提取基本信息
            plan.nodes = definition.get("nodes", [])
            plan.edges = definition.get("edges", [])
            plan.start_node = definition.get("start_node")
            plan.end_nodes = definition.get("end_nodes", [])
            
            # 构建依赖关系图
            await self._build_dependency_graph(plan)
            
            # 生成执行顺序
            await self._generate_execution_order(plan)
            
            # 识别并行执行组
            await self._identify_parallel_groups(plan)
            
            # 估算执行时间和资源需求
            await self._estimate_execution_metrics(plan)
            
            self.logger.info(f"工作流解析完成，节点数: {len(plan.nodes)}, 边数: {len(plan.edges)}")
            
            return plan
        except Exception as e:
            self.logger.error(f"解析工作流定义错误: {str(e)}")
            raise
    
    async def _build_dependency_graph(self, plan: ExecutionPlan) -> None:
        """
        构建节点依赖关系图
        
        Args:
            plan: 执行计划
        """
        try:
            # 初始化依赖关系
            for node in plan.nodes:
                node_id = node.get("id")
                plan.dependencies[node_id] = []
            
            # 根据边构建依赖关系
            for edge in plan.edges:
                source = edge.get("source")
                target = edge.get("target")
                
                if target in plan.dependencies:
                    plan.dependencies[target].append(source)
            
            # 添加显式依赖
            for node in plan.nodes:
                node_id = node.get("id")
                explicit_deps = node.get("dependencies", [])
                
                for dep in explicit_deps:
                    if dep not in plan.dependencies[node_id]:
                        plan.dependencies[node_id].append(dep)
        except Exception as e:
            self.logger.error(f"构建依赖关系图错误: {str(e)}")
            raise
    
    async def _generate_execution_order(self, plan: ExecutionPlan) -> None:
        """
        生成执行顺序（拓扑排序）
        
        Args:
            plan: 执行计划
        """
        try:
            # 计算入度
            in_degree = {}
            for node in plan.nodes:
                node_id = node.get("id")
                in_degree[node_id] = len(plan.dependencies[node_id])
            
            # 拓扑排序
            queue = [node_id for node_id, degree in in_degree.items() if degree == 0]
            execution_order = []
            
            while queue:
                current_level = queue.copy()
                queue = []
                execution_order.append(current_level)
                
                for node_id in current_level:
                    # 更新后续节点的入度
                    for edge in plan.edges:
                        if edge.get("source") == node_id:
                            target = edge.get("target")
                            in_degree[target] -= 1
                            if in_degree[target] == 0:
                                queue.append(target)
            
            plan.execution_order = execution_order
        except Exception as e:
            self.logger.error(f"生成执行顺序错误: {str(e)}")
            raise
    
    async def _identify_parallel_groups(self, plan: ExecutionPlan) -> None:
        """
        识别可以并行执行的节点组
        
        Args:
            plan: 执行计划
        """
        try:
            parallel_groups = []
            
            for level in plan.execution_order:
                if len(level) > 1:
                    # 检查节点是否可以并行执行
                    parallel_nodes = []
                    for node_id in level:
                        node = next((n for n in plan.nodes if n.get("id") == node_id), None)
                        if node and node.get("config", {}).get("allow_parallel", True):
                            parallel_nodes.append(node_id)
                    
                    if len(parallel_nodes) > 1:
                        parallel_groups.append(parallel_nodes)
            
            plan.parallel_groups = parallel_groups
        except Exception as e:
            self.logger.error(f"识别并行执行组错误: {str(e)}")
            raise
    
    async def _estimate_execution_metrics(self, plan: ExecutionPlan) -> None:
        """
        估算执行时间和资源需求
        
        Args:
            plan: 执行计划
        """
        try:
            total_duration = 0.0
            max_memory = 0
            max_cpu = 0
            
            for level in plan.execution_order:
                level_duration = 0.0
                level_memory = 0
                level_cpu = 0
                
                for node_id in level:
                    node = next((n for n in plan.nodes if n.get("id") == node_id), None)
                    if node:
                        config = node.get("config", {})
                        
                        # 估算节点执行时间
                        node_duration = config.get("estimated_duration", 30.0)  # 默认30秒
                        level_duration = max(level_duration, node_duration)
                        
                        # 估算资源需求
                        level_memory += config.get("memory_mb", 100)
                        level_cpu += config.get("cpu_cores", 0.1)
                
                total_duration += level_duration
                max_memory = max(max_memory, level_memory)
                max_cpu = max(max_cpu, level_cpu)
            
            plan.estimated_duration = total_duration
            plan.resource_requirements = {
                "memory_mb": max_memory,
                "cpu_cores": max_cpu,
                "estimated_duration": total_duration
            }
        except Exception as e:
            self.logger.error(f"估算执行指标错误: {str(e)}")
            raise
    
    async def execute_workflow(
        self,
        execution_id: str,
        workflow_id: int,
        input_data: Optional[Dict[str, Any]] = None,
        config: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        执行工作流
        
        Args:
            execution_id: 执行ID
            workflow_id: 工作流ID
            input_data: 输入数据
            config: 执行配置
            
        Returns:
            bool: 是否启动成功
        """
        try:
            # 获取工作流定义
            workflow = self.db.query(Workflow).filter(Workflow.id == workflow_id).first()
            if not workflow:
                self.logger.error(f"工作流不存在: {workflow_id}")
                return False
            
            # 解析工作流定义
            definition = json.loads(workflow.definition)
            execution_plan = await self.parse_workflow_definition(definition)
            
            # 创建执行锁
            if execution_id not in self.execution_locks:
                self.execution_locks[execution_id] = asyncio.Lock()
            
            # 初始化执行状态
            self.running_executions[execution_id] = {
                "workflow_id": workflow_id,
                "execution_plan": execution_plan,
                "input_data": input_data or {},
                "config": config or {},
                "status": "running",
                "current_level": 0,
                "completed_nodes": set(),
                "failed_nodes": set(),
                "context": await self.context_manager.create_context(execution_id),
                "start_time": datetime.utcnow(),
                "metrics": {
                    "nodes_completed": 0,
                    "nodes_failed": 0,
                    "total_duration": 0.0
                }
            }
            
            # 更新执行状态
            await self._update_execution_status(
                execution_id,
                WorkflowExecutionStatus.RUNNING,
                current_node=execution_plan.start_node
            )
            
            # 启动异步执行
            asyncio.create_task(self._execute_workflow_async(execution_id))
            
            self.logger.info(f"工作流执行已启动: {execution_id}")
            return True
        except Exception as e:
            self.logger.error(f"启动工作流执行错误: {str(e)}")
            await self._handle_execution_error(execution_id, str(e))
            return False
    
    async def _execute_workflow_async(self, execution_id: str) -> None:
        """
        异步执行工作流
        
        Args:
            execution_id: 执行ID
        """
        try:
            async with self.execution_locks[execution_id]:
                execution_state = self.running_executions[execution_id]
                execution_plan = execution_state["execution_plan"]
                
                # 按层级执行节点
                for level_index, level_nodes in enumerate(execution_plan.execution_order):
                    execution_state["current_level"] = level_index
                    
                    # 检查是否可以并行执行
                    if level_nodes in execution_plan.parallel_groups:
                        await self._execute_parallel_nodes(execution_id, level_nodes)
                    else:
                        await self._execute_sequential_nodes(execution_id, level_nodes)
                    
                    # 检查执行状态
                    if execution_state["status"] != "running":
                        break
                
                # 完成执行
                if execution_state["status"] == "running":
                    await self._complete_execution(execution_id)
        except Exception as e:
            self.logger.error(f"异步执行工作流错误: {str(e)}")
            await self._handle_execution_error(execution_id, str(e))
    
    async def _execute_parallel_nodes(self, execution_id: str, node_ids: List[str]) -> None:
        """
        并行执行节点
        
        Args:
            execution_id: 执行ID
            node_ids: 节点ID列表
        """
        try:
            execution_state = self.running_executions[execution_id]
            execution_plan = execution_state["execution_plan"]
            
            # 创建并行任务
            tasks = []
            for node_id in node_ids:
                node = next((n for n in execution_plan.nodes if n.get("id") == node_id), None)
                if node:
                    task = asyncio.create_task(self._execute_single_node(execution_id, node))
                    tasks.append(task)
            
            # 等待所有任务完成
            if tasks:
                await asyncio.gather(*tasks, return_exceptions=True)
        except Exception as e:
            self.logger.error(f"并行执行节点错误: {str(e)}")
            raise
    
    async def _execute_sequential_nodes(self, execution_id: str, node_ids: List[str]) -> None:
        """
        顺序执行节点
        
        Args:
            execution_id: 执行ID
            node_ids: 节点ID列表
        """
        try:
            execution_state = self.running_executions[execution_id]
            execution_plan = execution_state["execution_plan"]
            
            for node_id in node_ids:
                node = next((n for n in execution_plan.nodes if n.get("id") == node_id), None)
                if node:
                    await self._execute_single_node(execution_id, node)
                    
                    # 检查执行状态
                    if execution_state["status"] != "running":
                        break
        except Exception as e:
            self.logger.error(f"顺序执行节点错误: {str(e)}")
            raise
    
    async def _execute_single_node(
        self,
        execution_id: str,
        node: Dict[str, Any]
    ) -> bool:
        """
        执行单个节点
        
        Args:
            execution_id: 执行ID
            node: 节点定义
            
        Returns:
            bool: 是否执行成功
        """
        try:
            execution_state = self.running_executions[execution_id]
            node_id = node.get("id")
            node_type = node.get("type", "task")
            
            self.logger.info(f"开始执行节点: {node_id} (类型: {node_type})")
            
            # 更新当前节点
            await self._update_execution_status(
                execution_id,
                WorkflowExecutionStatus.RUNNING,
                current_node=node_id
            )
            
            # 获取节点上下文
            context = await self.context_manager.get_node_context(execution_id, node_id)
            
            # 根据节点类型选择执行器
            success = False
            if node_type == "sequential":
                success = await self.sequential_executor.execute_node(execution_id, node, context)
            elif node_type == "parallel":
                success = await self.parallel_executor.execute_node(execution_id, node, context)
            elif node_type == "loop":
                success = await self.loop_executor.execute_node(execution_id, node, context)
            elif node_type == "branch":
                success = await self.branch_executor.execute_node(execution_id, node, context)
            else:
                # 默认任务节点
                success = await self._execute_task_node(execution_id, node, context)
            
            # 更新执行状态
            if success:
                execution_state["completed_nodes"].add(node_id)
                execution_state["metrics"]["nodes_completed"] += 1
                self.logger.info(f"节点执行成功: {node_id}")
            else:
                execution_state["failed_nodes"].add(node_id)
                execution_state["metrics"]["nodes_failed"] += 1
                self.logger.error(f"节点执行失败: {node_id}")
                
                # 检查是否需要停止执行
                if not node.get("config", {}).get("continue_on_failure", False):
                    execution_state["status"] = "failed"
            
            return success
        except Exception as e:
            self.logger.error(f"执行节点错误: {str(e)}")
            execution_state = self.running_executions.get(execution_id, {})
            execution_state["failed_nodes"].add(node.get("id"))
            execution_state["status"] = "failed"
            return False
    
    async def _execute_task_node(
        self,
        execution_id: str,
        node: Dict[str, Any],
        context: Dict[str, Any]
    ) -> bool:
        """
        执行任务节点
        
        Args:
            execution_id: 执行ID
            node: 节点定义
            context: 执行上下文
            
        Returns:
            bool: 是否执行成功
        """
        try:
            node_id = node.get("id")
            agent_id = node.get("agent_id")
            tool_id = node.get("tool_id")
            config = node.get("config", {})
            
            # 获取智能体或工具
            if agent_id:
                agent = self.db.query(Agent).filter(Agent.id == agent_id).first()
                if not agent:
                    self.logger.error(f"智能体不存在: {agent_id}")
                    return False
                
                # 创建工作流智能体实例
                workflow_agent = WorkflowAgent(
                    agent_id=agent.agent_id,
                    name=agent.name,
                    description=agent.description,
                    config=json.loads(agent.config or "{}"),
                    user_id=context.get("user_id")
                )
                
                # 执行智能体任务
                result = await workflow_agent.execute(
                    input_data=context.get("input_data", {}),
                    context=context
                )
                
                # 更新上下文
                await self.context_manager.update_node_context(
                    execution_id,
                    node_id,
                    {"output": result, "status": "completed"}
                )
                
                return result.get("success", False)
            elif tool_id:
                # 执行工具（这里需要实现工具执行逻辑）
                # TODO: 实现工具执行
                return True
            else:
                # 简单任务节点
                task_type = config.get("task_type", "noop")
                
                if task_type == "noop":
                    # 空操作
                    await asyncio.sleep(0.1)
                    return True
                elif task_type == "delay":
                    # 延迟操作
                    delay = config.get("delay", 1.0)
                    await asyncio.sleep(delay)
                    return True
                else:
                    self.logger.warning(f"未知任务类型: {task_type}")
                    return False
        except Exception as e:
            self.logger.error(f"执行任务节点错误: {str(e)}")
            return False
    
    async def _update_execution_status(
        self,
        execution_id: str,
        status: WorkflowExecutionStatus,
        current_node: Optional[str] = None,
        error_info: Optional[Dict[str, Any]] = None
    ) -> None:
        """
        更新执行状态
        
        Args:
            execution_id: 执行ID
            status: 执行状态
            current_node: 当前节点
            error_info: 错误信息
        """
        try:
            execution = self.db.query(WorkflowExecution).filter(
                WorkflowExecution.execution_id == execution_id
            ).first()
            
            if execution:
                execution.status = status.value
                execution.updated_at = datetime.utcnow()
                
                if current_node:
                    execution.current_node = current_node
                
                if error_info:
                    execution.error_info = json.dumps(error_info, ensure_ascii=False)
                
                # 更新完成节点和失败节点
                execution_state = self.running_executions.get(execution_id, {})
                if execution_state:
                    execution.completed_nodes = json.dumps(
                        list(execution_state.get("completed_nodes", set())),
                        ensure_ascii=False
                    )
                    execution.failed_nodes = json.dumps(
                        list(execution_state.get("failed_nodes", set())),
                        ensure_ascii=False
                    )
                    execution.metrics = json.dumps(
                        execution_state.get("metrics", {}),
                        ensure_ascii=False
                    )
                
                self.db.commit()
        except Exception as e:
            self.logger.error(f"更新执行状态错误: {str(e)}")
    
    async def _complete_execution(self, execution_id: str) -> None:
        """
        完成执行
        
        Args:
            execution_id: 执行ID
        """
        try:
            execution_state = self.running_executions[execution_id]
            
            # 计算执行时间
            start_time = execution_state.get("start_time")
            if start_time:
                duration = (datetime.utcnow() - start_time).total_seconds()
                execution_state["metrics"]["total_duration"] = duration
            
            # 更新执行状态
            await self._update_execution_status(
                execution_id,
                WorkflowExecutionStatus.COMPLETED
            )
            
            # 更新数据库记录
            execution = self.db.query(WorkflowExecution).filter(
                WorkflowExecution.execution_id == execution_id
            ).first()
            
            if execution:
                execution.completed_at = datetime.utcnow()
                execution.output_data = json.dumps(
                    await self.context_manager.get_execution_output(execution_id),
                    ensure_ascii=False
                )
                self.db.commit()
            
            # 清理执行状态
            await self._cleanup_execution(execution_id)
            
            self.logger.info(f"工作流执行完成: {execution_id}")
        except Exception as e:
            self.logger.error(f"完成执行错误: {str(e)}")
    
    async def _handle_execution_error(self, execution_id: str, error_message: str) -> None:
        """
        处理执行错误
        
        Args:
            execution_id: 执行ID
            error_message: 错误消息
        """
        try:
            error_info = {
                "error": error_message,
                "timestamp": datetime.utcnow().isoformat(),
                "execution_id": execution_id
            }
            
            # 更新执行状态
            await self._update_execution_status(
                execution_id,
                WorkflowExecutionStatus.FAILED,
                error_info=error_info
            )
            
            # 更新数据库记录
            execution = self.db.query(WorkflowExecution).filter(
                WorkflowExecution.execution_id == execution_id
            ).first()
            
            if execution:
                execution.completed_at = datetime.utcnow()
                self.db.commit()
            
            # 清理执行状态
            await self._cleanup_execution(execution_id)
            
            self.logger.error(f"工作流执行失败: {execution_id}, 错误: {error_message}")
        except Exception as e:
            self.logger.error(f"处理执行错误失败: {str(e)}")
    
    async def _cleanup_execution(self, execution_id: str) -> None:
        """
        清理执行状态
        
        Args:
            execution_id: 执行ID
        """
        try:
            # 清理运行状态
            if execution_id in self.running_executions:
                del self.running_executions[execution_id]
            
            # 清理执行锁
            if execution_id in self.execution_locks:
                del self.execution_locks[execution_id]
            
            # 清理上下文
            await self.context_manager.cleanup_context(execution_id)
        except Exception as e:
            self.logger.error(f"清理执行状态错误: {str(e)}")
    
    async def cancel_execution(self, execution_id: str) -> bool:
        """
        取消执行
        
        Args:
            execution_id: 执行ID
            
        Returns:
            bool: 是否取消成功
        """
        try:
            if execution_id in self.running_executions:
                execution_state = self.running_executions[execution_id]
                execution_state["status"] = "cancelled"
                
                # 更新执行状态
                await self._update_execution_status(
                    execution_id,
                    WorkflowExecutionStatus.CANCELLED
                )
                
                # 清理执行状态
                await self._cleanup_execution(execution_id)
                
                self.logger.info(f"工作流执行已取消: {execution_id}")
                return True
            
            return False
        except Exception as e:
            self.logger.error(f"取消执行错误: {str(e)}")
            return False
    
    async def pause_execution(self, execution_id: str) -> bool:
        """
        暂停执行
        
        Args:
            execution_id: 执行ID
            
        Returns:
            bool: 是否暂停成功
        """
        try:
            if execution_id in self.running_executions:
                execution_state = self.running_executions[execution_id]
                execution_state["status"] = "paused"
                
                # 更新执行状态
                await self._update_execution_status(
                    execution_id,
                    WorkflowExecutionStatus.PAUSED
                )
                
                self.logger.info(f"工作流执行已暂停: {execution_id}")
                return True
            
            return False
        except Exception as e:
            self.logger.error(f"暂停执行错误: {str(e)}")
            return False
    
    async def resume_execution(self, execution_id: str) -> bool:
        """
        恢复执行
        
        Args:
            execution_id: 执行ID
            
        Returns:
            bool: 是否恢复成功
        """
        try:
            if execution_id in self.running_executions:
                execution_state = self.running_executions[execution_id]
                if execution_state["status"] == "paused":
                    execution_state["status"] = "running"
                    
                    # 更新执行状态
                    await self._update_execution_status(
                        execution_id,
                        WorkflowExecutionStatus.RUNNING
                    )
                    
                    # 继续执行
                    asyncio.create_task(self._execute_workflow_async(execution_id))
                    
                    self.logger.info(f"工作流执行已恢复: {execution_id}")
                    return True
            
            return False
        except Exception as e:
            self.logger.error(f"恢复执行错误: {str(e)}")
            return False
    
    async def get_execution_status(self, execution_id: str) -> Optional[Dict[str, Any]]:
        """
        获取执行状态
        
        Args:
            execution_id: 执行ID
            
        Returns:
            Optional[Dict[str, Any]]: 执行状态信息
        """
        try:
            if execution_id in self.running_executions:
                execution_state = self.running_executions[execution_id]
                return {
                    "execution_id": execution_id,
                    "status": execution_state.get("status"),
                    "current_level": execution_state.get("current_level"),
                    "completed_nodes": list(execution_state.get("completed_nodes", set())),
                    "failed_nodes": list(execution_state.get("failed_nodes", set())),
                    "metrics": execution_state.get("metrics", {}),
                    "start_time": execution_state.get("start_time")
                }
            
            return None
        except Exception as e:
            self.logger.error(f"获取执行状态错误: {str(e)}")
            return None