# -*- coding: utf-8 -*-
"""
A2A多智能体系统工件管理API

提供文件上传下载、工件管理、版本控制、搜索和权限管理等功能
"""

import os
import hashlib
from datetime import datetime
from typing import Optional, List, Dict, Any, Union
from fastapi import APIRouter, Depends, HTTPException, status, Query, Body, UploadFile, File, Form
from fastapi.responses import JSONResponse, StreamingResponse, FileResponse
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc
from pydantic import BaseModel, Field
from io import BytesIO

from app.core.database import get_db
from app.core.logging import get_logger
from app.auth.dependencies import get_current_user
from app.models.user import User
from app.models.artifact import Artifact, ArtifactShare, ArtifactVersion
from app.services.artifact_service import get_artifact_service, ArtifactService
from app.schemas.artifact import (
    ArtifactCreate, ArtifactUpdate, ArtifactResponse,
    ArtifactVersionResponse, ArtifactShareResponse,
    ArtifactSearchRequest, ArtifactStatsResponse
)

logger = get_logger(__name__)
router = APIRouter(prefix="/artifacts", tags=["工件管理"])


# ==================== 请求/响应模型 ====================

class ArtifactUploadRequest(BaseModel):
    """工件上传请求模型"""
    name: str = Field(..., description="工件名称")
    description: Optional[str] = Field(None, description="工件描述")
    artifact_type: str = Field(default="file", description="工件类型")
    tags: Optional[List[str]] = Field(default=[], description="标签列表")
    is_public: bool = Field(default=False, description="是否公开")
    agent_id: Optional[str] = Field(None, description="关联智能体ID")
    session_id: Optional[str] = Field(None, description="关联会话ID")
    message_id: Optional[str] = Field(None, description="关联消息ID")


class ArtifactListResponse(BaseModel):
    """工件列表响应模型"""
    total: int = Field(..., description="总数量")
    items: List[ArtifactResponse] = Field(..., description="工件列表")
    skip: int = Field(..., description="跳过数量")
    limit: int = Field(..., description="限制数量")


class ArtifactSearchResponse(BaseModel):
    """工件搜索响应模型"""
    total: int = Field(..., description="总数量")
    items: List[ArtifactResponse] = Field(..., description="工件列表")
    facets: Dict[str, Dict[str, int]] = Field(..., description="分面统计")
    query: str = Field(..., description="搜索查询")


class ArtifactPermissionRequest(BaseModel):
    """工件权限请求模型"""
    user_id: str = Field(..., description="用户ID")
    permission_type: str = Field(..., description="权限类型: read, write, admin")
    expires_at: Optional[datetime] = Field(None, description="过期时间")


class ArtifactBatchRequest(BaseModel):
    """工件批量操作请求模型"""
    artifact_ids: List[str] = Field(..., description="工件ID列表")
    operation: str = Field(..., description="操作类型: delete, archive, share")
    parameters: Optional[Dict[str, Any]] = Field(default={}, description="操作参数")


# ==================== 文件上传下载接口 ====================

@router.post("/upload", response_model=ArtifactResponse, summary="上传工件")
async def upload_artifact(
    file: UploadFile = File(..., description="上传文件"),
    name: str = Form(..., description="工件名称"),
    description: Optional[str] = Form(None, description="工件描述"),
    artifact_type: str = Form(default="file", description="工件类型"),
    tags: Optional[str] = Form(None, description="标签列表（逗号分隔）"),
    is_public: bool = Form(default=False, description="是否公开"),
    agent_id: Optional[str] = Form(None, description="关联智能体ID"),
    session_id: Optional[str] = Form(None, description="关联会话ID"),
    message_id: Optional[str] = Form(None, description="关联消息ID"),
    current_user: User = Depends(get_current_user),
    artifact_service: ArtifactService = Depends(get_artifact_service)
):
    """
    上传工件文件
    """
    try:
        # 读取文件内容
        file_content = await file.read()
        
        # 解析标签
        tag_list = []
        if tags:
            tag_list = [tag.strip() for tag in tags.split(",") if tag.strip()]
        
        # 上传工件
        artifact = await artifact_service.upload_artifact(
            user_id=current_user.id,
            name=name,
            description=description,
            artifact_type=artifact_type,
            content_type=file.content_type or "application/octet-stream",
            file_extension=os.path.splitext(file.filename or "")[1],
            file_content=file_content,
            tags=tag_list,
            is_public=is_public,
            agent_id=agent_id,
            session_id=session_id,
            message_id=message_id
        )
        
        return ArtifactResponse(
            id=artifact.id,
            user_id=artifact.user_id,
            owner_id=artifact.owner_id,
            agent_id=artifact.agent_id,
            session_id=artifact.session_id,
            message_id=artifact.message_id,
            artifact_id=artifact.artifact_id,
            name=artifact.name,
            description=artifact.description,
            artifact_type=artifact.artifact_type,
            content_type=artifact.content_type,
            file_extension=artifact.file_extension,
            file_size=artifact.file_size,
            file_hash=artifact.file_hash,
            storage_path=artifact.storage_path,
            tags=artifact.tags,
            metadata=artifact.metadata,
            is_public=artifact.is_public,
            is_archived=artifact.is_archived,
            version=artifact.version,
            created_at=artifact.created_at,
            updated_at=artifact.updated_at
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"上传工件失败: {name}, 错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"上传工件失败: {str(e)}"
        )


@router.get("/download/{artifact_id}", summary="下载工件")
async def download_artifact(
    artifact_id: str,
    version: Optional[int] = Query(None, description="版本号"),
    current_user: User = Depends(get_current_user),
    artifact_service: ArtifactService = Depends(get_artifact_service)
):
    """
    下载工件文件
    """
    try:
        # 下载工件
        file_data = await artifact_service.download_artifact(
            artifact_id=artifact_id,
            user_id=current_user.id,
            version=version
        )
        
        if not file_data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="工件不存在或无权限访问"
            )
        
        # 返回文件流
        return StreamingResponse(
            BytesIO(file_data["content"]),
            media_type=file_data["content_type"],
            headers={
                "Content-Disposition": f"attachment; filename={file_data['filename']}",
                "Content-Length": str(len(file_data["content"]))
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"下载工件失败: {artifact_id}, 错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"下载工件失败: {str(e)}"
        )


@router.get("/preview/{artifact_id}", summary="预览工件")
async def preview_artifact(
    artifact_id: str,
    version: Optional[int] = Query(None, description="版本号"),
    current_user: User = Depends(get_current_user),
    artifact_service: ArtifactService = Depends(get_artifact_service)
):
    """
    预览工件内容（适用于文本、图片等可预览的文件）
    """
    try:
        # 获取工件预览
        preview_data = await artifact_service.get_artifact_preview(
            artifact_id=artifact_id,
            user_id=current_user.id,
            version=version
        )
        
        if not preview_data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="工件不存在或无权限访问"
            )
        
        return {
            "artifact_id": artifact_id,
            "name": preview_data["name"],
            "content_type": preview_data["content_type"],
            "preview_type": preview_data["preview_type"],
            "content": preview_data["content"],
            "metadata": preview_data["metadata"]
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"预览工件失败: {artifact_id}, 错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"预览工件失败: {str(e)}"
        )


# ==================== 工件管理接口 ====================

@router.get("/", response_model=ArtifactListResponse, summary="获取工件列表")
async def list_artifacts(
    artifact_type: Optional[str] = Query(None, description="工件类型过滤"),
    tags: Optional[str] = Query(None, description="标签过滤（逗号分隔）"),
    is_public: Optional[bool] = Query(None, description="是否公开"),
    is_archived: Optional[bool] = Query(False, description="是否已归档"),
    agent_id: Optional[str] = Query(None, description="智能体ID过滤"),
    session_id: Optional[str] = Query(None, description="会话ID过滤"),
    skip: int = Query(0, ge=0, description="跳过数量"),
    limit: int = Query(100, ge=1, le=1000, description="限制数量"),
    sort_by: str = Query("created_at", description="排序字段"),
    sort_order: str = Query("desc", description="排序顺序: asc, desc"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    获取工件列表
    """
    try:
        # 构建查询
        query = db.query(Artifact).filter(
            or_(
                Artifact.user_id == current_user.id,  # 用户自己的工件
                Artifact.is_public == True,  # 公开工件
                # TODO: 添加共享权限检查
            )
        )
        
        # 应用过滤条件
        if artifact_type:
            query = query.filter(Artifact.artifact_type == artifact_type)
        
        if tags:
            tag_list = [tag.strip() for tag in tags.split(",") if tag.strip()]
            for tag in tag_list:
                query = query.filter(Artifact.tags.contains([tag]))
        
        if is_public is not None:
            query = query.filter(Artifact.is_public == is_public)
        
        if is_archived is not None:
            query = query.filter(Artifact.is_archived == is_archived)
        
        if agent_id:
            query = query.filter(Artifact.agent_id == agent_id)
        
        if session_id:
            query = query.filter(Artifact.session_id == session_id)
        
        # 排序
        if hasattr(Artifact, sort_by):
            order_column = getattr(Artifact, sort_by)
            if sort_order.lower() == "desc":
                query = query.order_by(desc(order_column))
            else:
                query = query.order_by(order_column)
        
        # 分页
        total = query.count()
        artifacts = query.offset(skip).limit(limit).all()
        
        # 转换为响应格式
        items = [
            ArtifactResponse(
                id=artifact.id,
                user_id=artifact.user_id,
                owner_id=artifact.owner_id,
                agent_id=artifact.agent_id,
                session_id=artifact.session_id,
                message_id=artifact.message_id,
                artifact_id=artifact.artifact_id,
                name=artifact.name,
                description=artifact.description,
                artifact_type=artifact.artifact_type,
                content_type=artifact.content_type,
                file_extension=artifact.file_extension,
                file_size=artifact.file_size,
                file_hash=artifact.file_hash,
                storage_path=artifact.storage_path,
                tags=artifact.tags,
                metadata=artifact.metadata,
                is_public=artifact.is_public,
                is_archived=artifact.is_archived,
                version=artifact.version,
                created_at=artifact.created_at,
                updated_at=artifact.updated_at
            )
            for artifact in artifacts
        ]
        
        return ArtifactListResponse(
            total=total,
            items=items,
            skip=skip,
            limit=limit
        )
        
    except Exception as e:
        logger.error(f"获取工件列表失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取工件列表失败: {str(e)}"
        )


@router.get("/{artifact_id}", response_model=ArtifactResponse, summary="获取工件详情")
async def get_artifact(
    artifact_id: str,
    current_user: User = Depends(get_current_user),
    artifact_service: ArtifactService = Depends(get_artifact_service)
):
    """
    获取工件详情
    """
    try:
        artifact = await artifact_service.get_artifact(
            artifact_id=artifact_id,
            user_id=current_user.id
        )
        
        if not artifact:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="工件不存在或无权限访问"
            )
        
        return ArtifactResponse(
            id=artifact.id,
            user_id=artifact.user_id,
            owner_id=artifact.owner_id,
            agent_id=artifact.agent_id,
            session_id=artifact.session_id,
            message_id=artifact.message_id,
            artifact_id=artifact.artifact_id,
            name=artifact.name,
            description=artifact.description,
            artifact_type=artifact.artifact_type,
            content_type=artifact.content_type,
            file_extension=artifact.file_extension,
            file_size=artifact.file_size,
            file_hash=artifact.file_hash,
            storage_path=artifact.storage_path,
            tags=artifact.tags,
            metadata=artifact.metadata,
            is_public=artifact.is_public,
            is_archived=artifact.is_archived,
            version=artifact.version,
            created_at=artifact.created_at,
            updated_at=artifact.updated_at
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取工件详情失败: {artifact_id}, 错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取工件详情失败: {str(e)}"
        )


@router.put("/{artifact_id}", response_model=ArtifactResponse, summary="更新工件")
async def update_artifact(
    artifact_id: str,
    update_data: ArtifactUpdate,
    current_user: User = Depends(get_current_user),
    artifact_service: ArtifactService = Depends(get_artifact_service)
):
    """
    更新工件信息
    """
    try:
        artifact = await artifact_service.update_artifact(
            artifact_id=artifact_id,
            user_id=current_user.id,
            **update_data.dict(exclude_unset=True)
        )
        
        if not artifact:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="工件不存在或无权限修改"
            )
        
        return ArtifactResponse(
            id=artifact.id,
            user_id=artifact.user_id,
            owner_id=artifact.owner_id,
            agent_id=artifact.agent_id,
            session_id=artifact.session_id,
            message_id=artifact.message_id,
            artifact_id=artifact.artifact_id,
            name=artifact.name,
            description=artifact.description,
            artifact_type=artifact.artifact_type,
            content_type=artifact.content_type,
            file_extension=artifact.file_extension,
            file_size=artifact.file_size,
            file_hash=artifact.file_hash,
            storage_path=artifact.storage_path,
            tags=artifact.tags,
            metadata=artifact.metadata,
            is_public=artifact.is_public,
            is_archived=artifact.is_archived,
            version=artifact.version,
            created_at=artifact.created_at,
            updated_at=artifact.updated_at
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新工件失败: {artifact_id}, 错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新工件失败: {str(e)}"
        )


@router.delete("/{artifact_id}", summary="删除工件")
async def delete_artifact(
    artifact_id: str,
    permanent: bool = Query(default=False, description="是否永久删除"),
    current_user: User = Depends(get_current_user),
    artifact_service: ArtifactService = Depends(get_artifact_service)
):
    """
    删除工件
    """
    try:
        success = await artifact_service.delete_artifact(
            artifact_id=artifact_id,
            user_id=current_user.id,
            permanent=permanent
        )
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="工件不存在或无权限删除"
            )
        
        message = "工件已永久删除" if permanent else "工件已归档"
        return {"message": message}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除工件失败: {artifact_id}, 错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除工件失败: {str(e)}"
        )


# ==================== 工件版本接口 ====================

@router.get("/{artifact_id}/versions", response_model=List[ArtifactVersionResponse], summary="获取工件版本列表")
async def list_artifact_versions(
    artifact_id: str,
    current_user: User = Depends(get_current_user),
    artifact_service: ArtifactService = Depends(get_artifact_service)
):
    """
    获取工件版本列表
    """
    try:
        versions = await artifact_service.list_artifact_versions(
            artifact_id=artifact_id,
            user_id=current_user.id
        )
        
        return [
            ArtifactVersionResponse(
                id=version.id,
                artifact_id=version.artifact_id,
                version_number=version.version_number,
                file_size=version.file_size,
                file_hash=version.file_hash,
                storage_path=version.storage_path,
                changes=version.changes,
                created_by=version.created_by,
                created_at=version.created_at
            )
            for version in versions
        ]
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取工件版本列表失败: {artifact_id}, 错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取工件版本列表失败: {str(e)}"
        )


@router.post("/{artifact_id}/versions", response_model=ArtifactVersionResponse, summary="创建工件版本")
async def create_artifact_version(
    artifact_id: str,
    file: UploadFile = File(..., description="新版本文件"),
    changes: Optional[str] = Form(None, description="版本变更说明"),
    current_user: User = Depends(get_current_user),
    artifact_service: ArtifactService = Depends(get_artifact_service)
):
    """
    创建工件新版本
    """
    try:
        # 读取文件内容
        file_content = await file.read()
        
        # 创建版本
        version = await artifact_service.create_artifact_version(
            artifact_id=artifact_id,
            user_id=current_user.id,
            file_content=file_content,
            changes=changes
        )
        
        if not version:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="工件不存在或无权限创建版本"
            )
        
        return ArtifactVersionResponse(
            id=version.id,
            artifact_id=version.artifact_id,
            version_number=version.version_number,
            file_size=version.file_size,
            file_hash=version.file_hash,
            storage_path=version.storage_path,
            changes=version.changes,
            created_by=version.created_by,
            created_at=version.created_at
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建工件版本失败: {artifact_id}, 错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建工件版本失败: {str(e)}"
        )


@router.post("/{artifact_id}/rollback/{version_number}", response_model=ArtifactResponse, summary="回滚工件版本")
async def rollback_artifact_version(
    artifact_id: str,
    version_number: int,
    current_user: User = Depends(get_current_user),
    artifact_service: ArtifactService = Depends(get_artifact_service)
):
    """
    回滚工件到指定版本
    """
    try:
        artifact = await artifact_service.rollback_artifact_version(
            artifact_id=artifact_id,
            version_number=version_number,
            user_id=current_user.id
        )
        
        if not artifact:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="工件或版本不存在，或无权限回滚"
            )
        
        return ArtifactResponse(
            id=artifact.id,
            user_id=artifact.user_id,
            owner_id=artifact.owner_id,
            agent_id=artifact.agent_id,
            session_id=artifact.session_id,
            message_id=artifact.message_id,
            artifact_id=artifact.artifact_id,
            name=artifact.name,
            description=artifact.description,
            artifact_type=artifact.artifact_type,
            content_type=artifact.content_type,
            file_extension=artifact.file_extension,
            file_size=artifact.file_size,
            file_hash=artifact.file_hash,
            storage_path=artifact.storage_path,
            tags=artifact.tags,
            metadata=artifact.metadata,
            is_public=artifact.is_public,
            is_archived=artifact.is_archived,
            version=artifact.version,
            created_at=artifact.created_at,
            updated_at=artifact.updated_at
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"回滚工件版本失败: {artifact_id}:{version_number}, 错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"回滚工件版本失败: {str(e)}"
        )


# ==================== 工件搜索接口 ====================

@router.post("/search", response_model=ArtifactSearchResponse, summary="搜索工件")
async def search_artifacts(
    search_request: ArtifactSearchRequest,
    current_user: User = Depends(get_current_user),
    artifact_service: ArtifactService = Depends(get_artifact_service)
):
    """
    搜索工件
    """
    try:
        results = await artifact_service.search_artifacts(
            user_id=current_user.id,
            query=search_request.query,
            filters=search_request.filters,
            sort_by=search_request.sort_by,
            sort_order=search_request.sort_order,
            skip=search_request.skip,
            limit=search_request.limit
        )
        
        # 转换为响应格式
        items = [
            ArtifactResponse(
                id=artifact.id,
                user_id=artifact.user_id,
                owner_id=artifact.owner_id,
                agent_id=artifact.agent_id,
                session_id=artifact.session_id,
                message_id=artifact.message_id,
                artifact_id=artifact.artifact_id,
                name=artifact.name,
                description=artifact.description,
                artifact_type=artifact.artifact_type,
                content_type=artifact.content_type,
                file_extension=artifact.file_extension,
                file_size=artifact.file_size,
                file_hash=artifact.file_hash,
                storage_path=artifact.storage_path,
                tags=artifact.tags,
                metadata=artifact.metadata,
                is_public=artifact.is_public,
                is_archived=artifact.is_archived,
                version=artifact.version,
                created_at=artifact.created_at,
                updated_at=artifact.updated_at
            )
            for artifact in results["items"]
        ]
        
        return ArtifactSearchResponse(
            total=results["total"],
            items=items,
            facets=results["facets"],
            query=search_request.query
        )
        
    except Exception as e:
        logger.error(f"搜索工件失败: {search_request.query}, 错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"搜索工件失败: {str(e)}"
        )


# ==================== 工件权限接口 ====================

@router.get("/{artifact_id}/permissions", response_model=List[ArtifactShareResponse], summary="获取工件权限列表")
async def list_artifact_permissions(
    artifact_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    获取工件权限列表
    """
    try:
        # 检查权限
        artifact = db.query(Artifact).filter(
            Artifact.artifact_id == artifact_id
        ).first()
        
        if not artifact:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="工件不存在"
            )
        
        # 只有所有者可以查看权限列表
        if artifact.owner_id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权限查看工件权限列表"
            )
        
        # 获取权限列表
        shares = db.query(ArtifactShare).filter(
            ArtifactShare.artifact_id == artifact_id
        ).all()
        
        return [
            ArtifactShareResponse(
                id=share.id,
                artifact_id=share.artifact_id,
                shared_with_user_id=share.shared_with_user_id,
                shared_by_user_id=share.shared_by_user_id,
                permission_type=share.permission_type,
                expires_at=share.expires_at,
                created_at=share.created_at
            )
            for share in shares
        ]
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取工件权限列表失败: {artifact_id}, 错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取工件权限列表失败: {str(e)}"
        )


@router.post("/{artifact_id}/permissions", response_model=ArtifactShareResponse, summary="添加工件权限")
async def add_artifact_permission(
    artifact_id: str,
    permission_request: ArtifactPermissionRequest,
    current_user: User = Depends(get_current_user),
    artifact_service: ArtifactService = Depends(get_artifact_service)
):
    """
    添加工件权限
    """
    try:
        share = await artifact_service.share_artifact(
            artifact_id=artifact_id,
            owner_user_id=current_user.id,
            shared_with_user_id=permission_request.user_id,
            permission_type=permission_request.permission_type,
            expires_at=permission_request.expires_at
        )
        
        if not share:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="工件不存在或无权限分享"
            )
        
        return ArtifactShareResponse(
            id=share.id,
            artifact_id=share.artifact_id,
            shared_with_user_id=share.shared_with_user_id,
            shared_by_user_id=share.shared_by_user_id,
            permission_type=share.permission_type,
            expires_at=share.expires_at,
            created_at=share.created_at
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"添加工件权限失败: {artifact_id}, 错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"添加工件权限失败: {str(e)}"
        )


@router.delete("/{artifact_id}/permissions/{user_id}", summary="删除工件权限")
async def remove_artifact_permission(
    artifact_id: str,
    user_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    删除工件权限
    """
    try:
        # 检查权限
        artifact = db.query(Artifact).filter(
            Artifact.artifact_id == artifact_id
        ).first()
        
        if not artifact:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="工件不存在"
            )
        
        # 只有所有者可以删除权限
        if artifact.owner_id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权限删除工件权限"
            )
        
        # 删除权限
        share = db.query(ArtifactShare).filter(
            ArtifactShare.artifact_id == artifact_id,
            ArtifactShare.shared_with_user_id == user_id
        ).first()
        
        if not share:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="权限记录不存在"
            )
        
        db.delete(share)
        db.commit()
        
        return {"message": "工件权限已删除"}
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"删除工件权限失败: {artifact_id}:{user_id}, 错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除工件权限失败: {str(e)}"
        )


# ==================== 工件统计接口 ====================

@router.get("/stats/overview", response_model=ArtifactStatsResponse, summary="获取工件统计")
async def get_artifact_stats(
    current_user: User = Depends(get_current_user),
    artifact_service: ArtifactService = Depends(get_artifact_service)
):
    """
    获取工件统计信息
    """
    try:
        stats = await artifact_service.get_user_artifact_stats(current_user.id)
        
        return ArtifactStatsResponse(
            total_artifacts=stats["total_artifacts"],
            total_size=stats["total_size"],
            public_artifacts=stats["public_artifacts"],
            private_artifacts=stats["private_artifacts"],
            archived_artifacts=stats["archived_artifacts"],
            shared_artifacts=stats["shared_artifacts"],
            artifact_types=stats["artifact_types"],
            monthly_uploads=stats["monthly_uploads"],
            storage_usage=stats["storage_usage"]
        )
        
    except Exception as e:
        logger.error(f"获取工件统计失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取工件统计失败: {str(e)}"
        )


# ==================== 批量操作接口 ====================

@router.post("/batch", summary="批量操作工件")
async def batch_artifact_operations(
    batch_request: ArtifactBatchRequest,
    current_user: User = Depends(get_current_user),
    artifact_service: ArtifactService = Depends(get_artifact_service)
):
    """
    批量操作工件
    """
    try:
        results = await artifact_service.batch_artifact_operations(
            artifact_ids=batch_request.artifact_ids,
            operation=batch_request.operation,
            user_id=current_user.id,
            parameters=batch_request.parameters
        )
        
        return {
            "message": f"批量操作完成: {batch_request.operation}",
            "results": results
        }
        
    except Exception as e:
        logger.error(f"批量操作工件失败: {batch_request.operation}, 错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"批量操作工件失败: {str(e)}"
        )


# ==================== 缓存管理接口 ====================

@router.post("/cache/clear", summary="清除工件缓存")
async def clear_artifact_cache(
    pattern: Optional[str] = Body(None, description="缓存键模式"),
    current_user: User = Depends(get_current_user),
    artifact_service: ArtifactService = Depends(get_artifact_service)
):
    """
    清除工件缓存
    
    管理员可以清除所有缓存，普通用户只能清除自己的缓存
    """
    try:
        if current_user.role == "admin":
            # 管理员可以清除所有缓存
            artifact_service.clear_cache(pattern)
            message = "工件缓存已清除"
        else:
            # 普通用户只能清除自己的缓存
            user_pattern = f"artifact:{current_user.id}"
            if pattern:
                user_pattern = f"{user_pattern}:{pattern}"
            artifact_service.clear_cache(user_pattern)
            message = "用户工件缓存已清除"
        
        return {"message": message}
        
    except Exception as e:
        logger.error(f"清除工件缓存失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"清除工件缓存失败: {str(e)}"
        )