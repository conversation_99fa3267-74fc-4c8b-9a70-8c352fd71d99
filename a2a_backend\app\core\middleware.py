#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A2A多智能体系统中间件模块

包含CORS、请求日志记录、全局异常处理、用户认证等中间件
"""

import time
import uuid
import json
from typing import Callable, Optional, Dict, Any
from datetime import datetime

from fastapi import Request, Response, HTTPException, status
from fastapi.middleware.base import BaseHTTPMiddleware
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from starlette.middleware.base import RequestResponseEndpoint
from loguru import logger
from sqlalchemy import text

from app.core.config import get_settings
from app.core.database import get_database_manager
from app.auth.jwt_handler import J<PERSON><PERSON>andler
from app.auth.permissions import PermissionChecker


class RequestLoggingMiddleware(BaseHTTPMiddleware):
    """
    请求日志记录中间件
    
    记录所有HTTP请求的详细信息
    """
    
    def __init__(self, app, exclude_paths: Optional[list] = None):
        super().__init__(app)
        self.exclude_paths = exclude_paths or ["/health", "/metrics", "/docs", "/redoc", "/openapi.json"]
    
    async def dispatch(self, request: Request, call_next: RequestResponseEndpoint) -> Response:
        """
        处理请求日志记录
        
        Args:
            request: HTTP请求
            call_next: 下一个中间件或路由处理器
            
        Returns:
            Response: HTTP响应
        """
        # 生成请求ID
        request_id = str(uuid.uuid4())
        request.state.request_id = request_id
        
        # 记录请求开始时间
        start_time = time.time()
        
        # 获取请求信息
        method = request.method
        url = str(request.url)
        path = request.url.path
        
        # 跳过排除的路径
        if path in self.exclude_paths:
            return await call_next(request)
        
        # 获取客户端信息
        client_ip = self._get_client_ip(request)
        user_agent = request.headers.get("user-agent", "")
        
        # 获取请求体（如果是POST/PUT/PATCH）
        request_body = None
        if method in ["POST", "PUT", "PATCH"] and request.headers.get("content-type", "").startswith("application/json"):
            try:
                body = await request.body()
                if body:
                    request_body = body.decode("utf-8")[:1000]  # 限制长度
            except Exception:
                request_body = "<无法读取请求体>"
        
        # 记录请求开始
        logger.info(
            f"请求开始: {method} {path}",
            extra={
                "request_id": request_id,
                "method": method,
                "url": url,
                "path": path,
                "client_ip": client_ip,
                "user_agent": user_agent,
                "request_body": request_body,
                "headers": dict(request.headers)
            }
        )
        
        # 处理请求
        try:
            response = await call_next(request)
            
            # 计算处理时间
            process_time = time.time() - start_time
            
            # 记录响应信息
            status_code = response.status_code
            
            # 记录请求完成
            log_level = "error" if status_code >= 400 else "info"
            getattr(logger, log_level)(
                f"请求完成: {method} {path} - {status_code} ({process_time:.3f}s)",
                extra={
                    "request_id": request_id,
                    "method": method,
                    "path": path,
                    "status_code": status_code,
                    "process_time": process_time,
                    "client_ip": client_ip
                }
            )
            
            # 添加响应头
            response.headers["X-Request-ID"] = request_id
            response.headers["X-Process-Time"] = str(process_time)
            
            return response
            
        except Exception as e:
            # 计算处理时间
            process_time = time.time() - start_time
            
            # 记录异常
            logger.error(
                f"请求异常: {method} {path} - {str(e)} ({process_time:.3f}s)",
                extra={
                    "request_id": request_id,
                    "method": method,
                    "path": path,
                    "error": str(e),
                    "process_time": process_time,
                    "client_ip": client_ip
                }
            )
            
            # 重新抛出异常
            raise
    
    def _get_client_ip(self, request: Request) -> str:
        """
        获取客户端IP地址
        
        Args:
            request: HTTP请求
            
        Returns:
            str: 客户端IP地址
        """
        # 检查代理头
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
        
        # 返回直接连接的IP
        return request.client.host if request.client else "unknown"


class GlobalExceptionMiddleware(BaseHTTPMiddleware):
    """
    全局异常处理中间件
    
    捕获并处理所有未处理的异常
    """
    
    async def dispatch(self, request: Request, call_next: RequestResponseEndpoint) -> Response:
        """
        处理全局异常
        
        Args:
            request: HTTP请求
            call_next: 下一个中间件或路由处理器
            
        Returns:
            Response: HTTP响应
        """
        try:
            return await call_next(request)
        
        except HTTPException as e:
            # HTTP异常直接返回
            return JSONResponse(
                status_code=e.status_code,
                content={
                    "error": {
                        "code": e.status_code,
                        "message": e.detail,
                        "type": "http_exception",
                        "request_id": getattr(request.state, "request_id", None)
                    }
                }
            )
        
        except ValueError as e:
            # 值错误
            logger.error(f"值错误: {str(e)}", extra={"request_id": getattr(request.state, "request_id", None)})
            return JSONResponse(
                status_code=status.HTTP_400_BAD_REQUEST,
                content={
                    "error": {
                        "code": 400,
                        "message": "请求参数错误",
                        "detail": str(e),
                        "type": "value_error",
                        "request_id": getattr(request.state, "request_id", None)
                    }
                }
            )
        
        except PermissionError as e:
            # 权限错误
            logger.warning(f"权限错误: {str(e)}", extra={"request_id": getattr(request.state, "request_id", None)})
            return JSONResponse(
                status_code=status.HTTP_403_FORBIDDEN,
                content={
                    "error": {
                        "code": 403,
                        "message": "权限不足",
                        "detail": str(e),
                        "type": "permission_error",
                        "request_id": getattr(request.state, "request_id", None)
                    }
                }
            )
        
        except Exception as e:
            # 其他未知异常
            request_id = getattr(request.state, "request_id", None)
            logger.error(
                f"未处理的异常: {str(e)}",
                extra={
                    "request_id": request_id,
                    "exception_type": type(e).__name__,
                    "traceback": True
                }
            )
            
            # 记录错误到数据库
            await self._log_error_to_database(request, e, request_id)
            
            return JSONResponse(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                content={
                    "error": {
                        "code": 500,
                        "message": "服务器内部错误",
                        "type": "internal_error",
                        "request_id": request_id
                    }
                }
            )
    
    async def _log_error_to_database(self, request: Request, exception: Exception, request_id: Optional[str]) -> None:
        """
        记录错误到数据库
        
        Args:
            request: HTTP请求
            exception: 异常对象
            request_id: 请求ID
        """
        try:
            db_manager = get_database_manager()
            engine = db_manager.get_engine()
            
            error_data = {
                "error_type": type(exception).__name__,
                "error_message": str(exception),
                "request_method": request.method,
                "request_path": request.url.path,
                "request_query": str(request.query_params),
                "request_headers": dict(request.headers),
                "client_ip": request.client.host if request.client else "unknown",
                "user_agent": request.headers.get("user-agent", ""),
                "request_id": request_id
            }
            
            sql = """
                INSERT INTO error_logs (
                    error_type, error_message, stack_trace, request_data,
                    user_id, session_id, request_id, timestamp
                ) VALUES (
                    :error_type, :error_message, :stack_trace, :request_data,
                    :user_id, :session_id, :request_id, :timestamp
                )
            """
            
            async with engine.begin() as conn:
                await conn.execute(text(sql), {
                    "error_type": error_data["error_type"],
                    "error_message": error_data["error_message"][:500],
                    "stack_trace": "",  # 可以添加堆栈跟踪
                    "request_data": json.dumps(error_data, ensure_ascii=False)[:2000],
                    "user_id": getattr(request.state, "user_id", None),
                    "session_id": getattr(request.state, "session_id", None),
                    "request_id": request_id,
                    "timestamp": datetime.now()
                })
        
        except Exception as e:
            logger.error(f"记录错误到数据库失败: {str(e)}")


class AuthenticationMiddleware(BaseHTTPMiddleware):
    """
    用户认证中间件
    
    验证JWT令牌并设置用户上下文
    """
    
    def __init__(self, app, exclude_paths: Optional[list] = None):
        super().__init__(app)
        self.exclude_paths = exclude_paths or [
            "/health", "/metrics", "/docs", "/redoc", "/openapi.json",
            "/auth/login", "/auth/register", "/auth/refresh"
        ]
        self.jwt_handler = JWTHandler()
        self.permission_checker = PermissionChecker()
    
    async def dispatch(self, request: Request, call_next: RequestResponseEndpoint) -> Response:
        """
        处理用户认证
        
        Args:
            request: HTTP请求
            call_next: 下一个中间件或路由处理器
            
        Returns:
            Response: HTTP响应
        """
        path = request.url.path
        
        # 跳过排除的路径
        if path in self.exclude_paths or path.startswith("/static/"):
            return await call_next(request)
        
        # 获取Authorization头
        authorization = request.headers.get("Authorization")
        if not authorization:
            return JSONResponse(
                status_code=status.HTTP_401_UNAUTHORIZED,
                content={
                    "error": {
                        "code": 401,
                        "message": "缺少认证令牌",
                        "type": "authentication_required"
                    }
                }
            )
        
        # 验证Bearer令牌格式
        if not authorization.startswith("Bearer "):
            return JSONResponse(
                status_code=status.HTTP_401_UNAUTHORIZED,
                content={
                    "error": {
                        "code": 401,
                        "message": "无效的认证令牌格式",
                        "type": "invalid_token_format"
                    }
                }
            )
        
        # 提取令牌
        token = authorization[7:]  # 移除"Bearer "前缀
        
        try:
            # 验证JWT令牌
            payload = await self.jwt_handler.verify_token(token)
            user_id = payload.get("user_id")
            session_id = payload.get("session_id")
            
            if not user_id:
                raise ValueError("令牌中缺少用户ID")
            
            # 验证用户状态
            user_info = await self._get_user_info(user_id)
            if not user_info:
                raise ValueError("用户不存在")
            
            if not user_info["is_active"]:
                raise ValueError("用户已被禁用")
            
            # 设置请求上下文
            request.state.user_id = user_id
            request.state.session_id = session_id
            request.state.user_info = user_info
            
            # 检查权限（如果需要）
            if hasattr(request.state, "required_permissions"):
                has_permission = await self.permission_checker.check_permissions(
                    user_id, request.state.required_permissions
                )
                if not has_permission:
                    return JSONResponse(
                        status_code=status.HTTP_403_FORBIDDEN,
                        content={
                            "error": {
                                "code": 403,
                                "message": "权限不足",
                                "type": "insufficient_permissions"
                            }
                        }
                    )
            
            # 记录用户活动
            await self._log_user_activity(user_id, request)
            
            return await call_next(request)
        
        except Exception as e:
            logger.warning(f"认证失败: {str(e)}", extra={"token": token[:20] + "..." if len(token) > 20 else token})
            return JSONResponse(
                status_code=status.HTTP_401_UNAUTHORIZED,
                content={
                    "error": {
                        "code": 401,
                        "message": "认证失败",
                        "detail": str(e),
                        "type": "authentication_failed"
                    }
                }
            )
    
    async def _get_user_info(self, user_id: int) -> Optional[Dict[str, Any]]:
        """
        获取用户信息
        
        Args:
            user_id: 用户ID
            
        Returns:
            Optional[Dict[str, Any]]: 用户信息
        """
        try:
            db_manager = get_database_manager()
            engine = db_manager.get_engine()
            
            sql = """
                SELECT id, username, email, is_active, role, created_at
                FROM users
                WHERE id = :user_id AND deleted_at IS NULL
            """
            
            async with engine.begin() as conn:
                result = await conn.execute(text(sql), {"user_id": user_id})
                row = result.fetchone()
                
                if row:
                    return {
                        "id": row[0],
                        "username": row[1],
                        "email": row[2],
                        "is_active": row[3],
                        "role": row[4],
                        "created_at": row[5]
                    }
                
                return None
        
        except Exception as e:
            logger.error(f"获取用户信息失败: {str(e)}")
            return None
    
    async def _log_user_activity(self, user_id: int, request: Request) -> None:
        """
        记录用户活动
        
        Args:
            user_id: 用户ID
            request: HTTP请求
        """
        try:
            db_manager = get_database_manager()
            engine = db_manager.get_engine()
            
            activity_data = {
                "action": f"{request.method} {request.url.path}",
                "ip_address": request.client.host if request.client else "unknown",
                "user_agent": request.headers.get("user-agent", ""),
                "request_id": getattr(request.state, "request_id", None)
            }
            
            sql = """
                INSERT INTO user_activity_logs (
                    user_id, action, ip_address, user_agent, details, timestamp
                ) VALUES (
                    :user_id, :action, :ip_address, :user_agent, :details, :timestamp
                )
            """
            
            async with engine.begin() as conn:
                await conn.execute(text(sql), {
                    "user_id": user_id,
                    "action": activity_data["action"],
                    "ip_address": activity_data["ip_address"],
                    "user_agent": activity_data["user_agent"][:200],
                    "details": json.dumps(activity_data, ensure_ascii=False),
                    "timestamp": datetime.now()
                })
        
        except Exception as e:
            logger.error(f"记录用户活动失败: {str(e)}")


def setup_cors_middleware(app, settings) -> None:
    """
    设置CORS中间件
    
    Args:
        app: FastAPI应用实例
        settings: 应用配置
    """
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.cors_origins,
        allow_credentials=True,
        allow_methods=["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"],
        allow_headers=[
            "Authorization",
            "Content-Type",
            "X-Requested-With",
            "X-Request-ID",
            "Accept",
            "Origin",
            "User-Agent"
        ],
        expose_headers=[
            "X-Request-ID",
            "X-Process-Time",
            "X-Total-Count"
        ]
    )


def setup_middlewares(app) -> None:
    """
    设置所有中间件
    
    Args:
        app: FastAPI应用实例
    """
    settings = get_settings()
    
    # 设置CORS中间件（最外层）
    setup_cors_middleware(app, settings)
    
    # 添加全局异常处理中间件
    app.add_middleware(GlobalExceptionMiddleware)
    
    # 添加请求日志记录中间件
    app.add_middleware(RequestLoggingMiddleware)
    
    # 添加用户认证中间件
    app.add_middleware(AuthenticationMiddleware)
    
    logger.info("中间件设置完成")