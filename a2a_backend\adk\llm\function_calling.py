#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A2A多智能体系统 函数调用工具

支持所有LLM的工具调用功能
"""

import json
import asyncio
import logging
from typing import Dict, List, Any, Optional, Callable, Union, Type
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime
import inspect
from functools import wraps


class ParameterType(Enum):
    """参数类型枚举"""
    STRING = "string"
    INTEGER = "integer"
    NUMBER = "number"
    BOOLEAN = "boolean"
    ARRAY = "array"
    OBJECT = "object"
    NULL = "null"


@dataclass
class FunctionParameter:
    """函数参数定义"""
    name: str
    type: ParameterType
    description: str
    required: bool = True
    default: Any = None
    enum: Optional[List[Any]] = None
    items: Optional['FunctionParameter'] = None  # 用于数组类型
    properties: Optional[Dict[str, 'FunctionParameter']] = None  # 用于对象类型
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        result = {
            "type": self.type.value,
            "description": self.description
        }
        
        if self.enum:
            result["enum"] = self.enum
        
        if self.items:
            result["items"] = self.items.to_dict()
        
        if self.properties:
            result["properties"] = {
                k: v.to_dict() for k, v in self.properties.items()
            }
            result["required"] = [
                k for k, v in self.properties.items() if v.required
            ]
        
        return result


@dataclass
class FunctionDefinition:
    """函数定义"""
    name: str
    description: str
    parameters: Dict[str, FunctionParameter]
    function: Callable
    category: str = "general"
    tags: List[str] = field(default_factory=list)
    examples: List[Dict[str, Any]] = field(default_factory=list)
    
    def to_openai_format(self) -> Dict[str, Any]:
        """转换为OpenAI函数调用格式"""
        return {
            "name": self.name,
            "description": self.description,
            "parameters": {
                "type": "object",
                "properties": {
                    k: v.to_dict() for k, v in self.parameters.items()
                },
                "required": [
                    k for k, v in self.parameters.items() if v.required
                ]
            }
        }
    
    def to_google_format(self) -> Dict[str, Any]:
        """转换为Google函数调用格式"""
        return {
            "name": self.name,
            "description": self.description,
            "parameters": {
                "type": "OBJECT",
                "properties": {
                    k: {
                        "type": v.type.value.upper(),
                        "description": v.description
                    } for k, v in self.parameters.items()
                },
                "required": [
                    k for k, v in self.parameters.items() if v.required
                ]
            }
        }
    
    def to_anthropic_format(self) -> Dict[str, Any]:
        """转换为Anthropic函数调用格式"""
        return {
            "name": self.name,
            "description": self.description,
            "input_schema": {
                "type": "object",
                "properties": {
                    k: v.to_dict() for k, v in self.parameters.items()
                },
                "required": [
                    k for k, v in self.parameters.items() if v.required
                ]
            }
        }


@dataclass
class FunctionCall:
    """函数调用"""
    name: str
    arguments: Dict[str, Any]
    call_id: Optional[str] = None
    timestamp: datetime = field(default_factory=datetime.now)


@dataclass
class FunctionResult:
    """函数执行结果"""
    call_id: Optional[str]
    name: str
    result: Any
    success: bool
    error: Optional[str] = None
    execution_time: Optional[float] = None
    timestamp: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "call_id": self.call_id,
            "name": self.name,
            "result": self.result,
            "success": self.success,
            "error": self.error,
            "execution_time": self.execution_time,
            "timestamp": self.timestamp.isoformat()
        }


class FunctionCallingTool:
    """函数调用工具"""
    
    def __init__(self):
        """
        初始化函数调用工具
        """
        self.logger = logging.getLogger(__name__)
        
        # 注册的函数
        self._functions: Dict[str, FunctionDefinition] = {}
        
        # 函数分类
        self._categories: Dict[str, List[str]] = {}
        
        # 执行历史
        self._execution_history: List[FunctionResult] = []
        
        # 最大历史记录数
        self._max_history = 1000
        
        self.logger.info("函数调用工具初始化完成")
    
    def register_function(
        self,
        name: str,
        description: str,
        parameters: Dict[str, FunctionParameter],
        category: str = "general",
        tags: Optional[List[str]] = None,
        examples: Optional[List[Dict[str, Any]]] = None
    ) -> Callable:
        """
        注册函数装饰器
        
        Args:
            name: 函数名称
            description: 函数描述
            parameters: 函数参数定义
            category: 函数分类
            tags: 函数标签
            examples: 使用示例
        
        Returns:
            Callable: 装饰器函数
        """
        def decorator(func: Callable) -> Callable:
            # 创建函数定义
            function_def = FunctionDefinition(
                name=name,
                description=description,
                parameters=parameters,
                function=func,
                category=category,
                tags=tags or [],
                examples=examples or []
            )
            
            # 注册函数
            self._functions[name] = function_def
            
            # 更新分类
            if category not in self._categories:
                self._categories[category] = []
            self._categories[category].append(name)
            
            self.logger.info(f"注册函数: {name}, 分类: {category}")
            
            @wraps(func)
            async def wrapper(*args, **kwargs):
                return await func(*args, **kwargs)
            
            return wrapper
        
        return decorator
    
    def register_function_direct(
        self,
        func: Callable,
        name: Optional[str] = None,
        description: Optional[str] = None,
        category: str = "general",
        tags: Optional[List[str]] = None
    ) -> None:
        """
        直接注册函数
        
        Args:
            func: 函数对象
            name: 函数名称（默认使用函数名）
            description: 函数描述（默认使用函数文档字符串）
            category: 函数分类
            tags: 函数标签
        """
        try:
            # 获取函数信息
            func_name = name or func.__name__
            func_description = description or func.__doc__ or "无描述"
            
            # 自动解析参数
            parameters = self._parse_function_parameters(func)
            
            # 创建函数定义
            function_def = FunctionDefinition(
                name=func_name,
                description=func_description,
                parameters=parameters,
                function=func,
                category=category,
                tags=tags or []
            )
            
            # 注册函数
            self._functions[func_name] = function_def
            
            # 更新分类
            if category not in self._categories:
                self._categories[category] = []
            self._categories[category].append(func_name)
            
            self.logger.info(f"直接注册函数: {func_name}, 分类: {category}")
            
        except Exception as e:
            self.logger.error(f"直接注册函数失败: {e}")
            raise
    
    async def execute_function(
        self,
        function_call: FunctionCall
    ) -> FunctionResult:
        """
        执行函数调用
        
        Args:
            function_call: 函数调用信息
        
        Returns:
            FunctionResult: 执行结果
        """
        start_time = datetime.now()
        
        try:
            # 检查函数是否存在
            if function_call.name not in self._functions:
                raise ValueError(f"未找到函数: {function_call.name}")
            
            function_def = self._functions[function_call.name]
            
            # 验证参数
            validated_args = self._validate_arguments(
                function_def, function_call.arguments
            )
            
            # 执行函数
            self.logger.info(f"执行函数: {function_call.name}")
            
            if asyncio.iscoroutinefunction(function_def.function):
                result = await function_def.function(**validated_args)
            else:
                result = function_def.function(**validated_args)
            
            # 计算执行时间
            execution_time = (datetime.now() - start_time).total_seconds()
            
            # 创建结果
            function_result = FunctionResult(
                call_id=function_call.call_id,
                name=function_call.name,
                result=result,
                success=True,
                execution_time=execution_time
            )
            
            # 记录执行历史
            self._add_to_history(function_result)
            
            self.logger.info(
                f"函数执行成功: {function_call.name}, "
                f"耗时: {execution_time:.3f}s"
            )
            
            return function_result
            
        except Exception as e:
            execution_time = (datetime.now() - start_time).total_seconds()
            
            # 创建错误结果
            function_result = FunctionResult(
                call_id=function_call.call_id,
                name=function_call.name,
                result=None,
                success=False,
                error=str(e),
                execution_time=execution_time
            )
            
            # 记录执行历史
            self._add_to_history(function_result)
            
            self.logger.error(
                f"函数执行失败: {function_call.name}, "
                f"错误: {e}, 耗时: {execution_time:.3f}s"
            )
            
            return function_result
    
    async def execute_multiple_functions(
        self,
        function_calls: List[FunctionCall],
        parallel: bool = False
    ) -> List[FunctionResult]:
        """
        执行多个函数调用
        
        Args:
            function_calls: 函数调用列表
            parallel: 是否并行执行
        
        Returns:
            List[FunctionResult]: 执行结果列表
        """
        try:
            if parallel:
                # 并行执行
                tasks = [
                    self.execute_function(call) for call in function_calls
                ]
                results = await asyncio.gather(*tasks, return_exceptions=True)
                
                # 处理异常结果
                processed_results = []
                for i, result in enumerate(results):
                    if isinstance(result, Exception):
                        processed_results.append(
                            FunctionResult(
                                call_id=function_calls[i].call_id,
                                name=function_calls[i].name,
                                result=None,
                                success=False,
                                error=str(result)
                            )
                        )
                    else:
                        processed_results.append(result)
                
                return processed_results
            else:
                # 顺序执行
                results = []
                for call in function_calls:
                    result = await self.execute_function(call)
                    results.append(result)
                
                return results
                
        except Exception as e:
            self.logger.error(f"批量执行函数失败: {e}")
            raise
    
    def get_function_definitions(
        self,
        format_type: str = "openai",
        category: Optional[str] = None,
        tags: Optional[List[str]] = None
    ) -> List[Dict[str, Any]]:
        """
        获取函数定义
        
        Args:
            format_type: 格式类型（openai, google, anthropic）
            category: 函数分类过滤
            tags: 标签过滤
        
        Returns:
            List[Dict[str, Any]]: 函数定义列表
        """
        try:
            # 过滤函数
            filtered_functions = self._filter_functions(category, tags)
            
            # 转换格式
            definitions = []
            for func_def in filtered_functions:
                if format_type == "openai":
                    definitions.append(func_def.to_openai_format())
                elif format_type == "google":
                    definitions.append(func_def.to_google_format())
                elif format_type == "anthropic":
                    definitions.append(func_def.to_anthropic_format())
                else:
                    raise ValueError(f"不支持的格式类型: {format_type}")
            
            return definitions
            
        except Exception as e:
            self.logger.error(f"获取函数定义失败: {e}")
            return []
    
    def get_function_by_name(self, name: str) -> Optional[FunctionDefinition]:
        """
        根据名称获取函数定义
        
        Args:
            name: 函数名称
        
        Returns:
            Optional[FunctionDefinition]: 函数定义
        """
        return self._functions.get(name)
    
    def get_functions_by_category(self, category: str) -> List[FunctionDefinition]:
        """
        根据分类获取函数列表
        
        Args:
            category: 函数分类
        
        Returns:
            List[FunctionDefinition]: 函数定义列表
        """
        if category not in self._categories:
            return []
        
        return [
            self._functions[name] for name in self._categories[category]
            if name in self._functions
        ]
    
    def get_execution_history(
        self,
        limit: Optional[int] = None,
        function_name: Optional[str] = None,
        success_only: bool = False
    ) -> List[FunctionResult]:
        """
        获取执行历史
        
        Args:
            limit: 限制数量
            function_name: 函数名称过滤
            success_only: 只返回成功的执行
        
        Returns:
            List[FunctionResult]: 执行历史列表
        """
        try:
            # 过滤历史记录
            filtered_history = self._execution_history
            
            if function_name:
                filtered_history = [
                    result for result in filtered_history
                    if result.name == function_name
                ]
            
            if success_only:
                filtered_history = [
                    result for result in filtered_history
                    if result.success
                ]
            
            # 按时间倒序排列
            filtered_history.sort(key=lambda x: x.timestamp, reverse=True)
            
            # 限制数量
            if limit:
                filtered_history = filtered_history[:limit]
            
            return filtered_history
            
        except Exception as e:
            self.logger.error(f"获取执行历史失败: {e}")
            return []
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            total_executions = len(self._execution_history)
            successful_executions = sum(
                1 for result in self._execution_history if result.success
            )
            
            # 按函数统计
            function_stats = {}
            for result in self._execution_history:
                if result.name not in function_stats:
                    function_stats[result.name] = {
                        "total": 0,
                        "success": 0,
                        "avg_time": 0.0
                    }
                
                function_stats[result.name]["total"] += 1
                if result.success:
                    function_stats[result.name]["success"] += 1
                
                if result.execution_time:
                    current_avg = function_stats[result.name]["avg_time"]
                    total_count = function_stats[result.name]["total"]
                    function_stats[result.name]["avg_time"] = (
                        (current_avg * (total_count - 1) + result.execution_time) / total_count
                    )
            
            return {
                "total_functions": len(self._functions),
                "total_categories": len(self._categories),
                "total_executions": total_executions,
                "successful_executions": successful_executions,
                "success_rate": successful_executions / total_executions if total_executions > 0 else 0,
                "function_stats": function_stats,
                "categories": {k: len(v) for k, v in self._categories.items()}
            }
            
        except Exception as e:
            self.logger.error(f"获取统计信息失败: {e}")
            return {}
    
    def clear_history(self) -> None:
        """
        清空执行历史
        """
        self._execution_history.clear()
        self.logger.info("执行历史已清空")
    
    def _parse_function_parameters(self, func: Callable) -> Dict[str, FunctionParameter]:
        """
        自动解析函数参数
        
        Args:
            func: 函数对象
        
        Returns:
            Dict[str, FunctionParameter]: 参数定义字典
        """
        try:
            signature = inspect.signature(func)
            parameters = {}
            
            for param_name, param in signature.parameters.items():
                # 跳过self和cls参数
                if param_name in ['self', 'cls']:
                    continue
                
                # 确定参数类型
                param_type = ParameterType.STRING  # 默认类型
                if param.annotation != inspect.Parameter.empty:
                    if param.annotation == int:
                        param_type = ParameterType.INTEGER
                    elif param.annotation == float:
                        param_type = ParameterType.NUMBER
                    elif param.annotation == bool:
                        param_type = ParameterType.BOOLEAN
                    elif param.annotation == list:
                        param_type = ParameterType.ARRAY
                    elif param.annotation == dict:
                        param_type = ParameterType.OBJECT
                
                # 确定是否必需
                required = param.default == inspect.Parameter.empty
                
                # 创建参数定义
                parameters[param_name] = FunctionParameter(
                    name=param_name,
                    type=param_type,
                    description=f"参数 {param_name}",
                    required=required,
                    default=param.default if not required else None
                )
            
            return parameters
            
        except Exception as e:
            self.logger.error(f"解析函数参数失败: {e}")
            return {}
    
    def _validate_arguments(
        self,
        function_def: FunctionDefinition,
        arguments: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        验证函数参数
        
        Args:
            function_def: 函数定义
            arguments: 参数值
        
        Returns:
            Dict[str, Any]: 验证后的参数
        
        Raises:
            ValueError: 参数验证失败
        """
        validated_args = {}
        
        # 检查必需参数
        for param_name, param_def in function_def.parameters.items():
            if param_def.required and param_name not in arguments:
                raise ValueError(f"缺少必需参数: {param_name}")
        
        # 验证参数类型和值
        for param_name, value in arguments.items():
            if param_name not in function_def.parameters:
                # 允许额外参数，但记录警告
                self.logger.warning(f"未定义的参数: {param_name}")
                validated_args[param_name] = value
                continue
            
            param_def = function_def.parameters[param_name]
            
            # 类型验证
            if not self._validate_parameter_type(value, param_def):
                raise ValueError(
                    f"参数 {param_name} 类型错误，期望: {param_def.type.value}, "
                    f"实际: {type(value).__name__}"
                )
            
            validated_args[param_name] = value
        
        # 添加默认值
        for param_name, param_def in function_def.parameters.items():
            if param_name not in validated_args and param_def.default is not None:
                validated_args[param_name] = param_def.default
        
        return validated_args
    
    def _validate_parameter_type(self, value: Any, param_def: FunctionParameter) -> bool:
        """
        验证参数类型
        
        Args:
            value: 参数值
            param_def: 参数定义
        
        Returns:
            bool: 是否有效
        """
        try:
            if param_def.type == ParameterType.STRING:
                return isinstance(value, str)
            elif param_def.type == ParameterType.INTEGER:
                return isinstance(value, int)
            elif param_def.type == ParameterType.NUMBER:
                return isinstance(value, (int, float))
            elif param_def.type == ParameterType.BOOLEAN:
                return isinstance(value, bool)
            elif param_def.type == ParameterType.ARRAY:
                return isinstance(value, list)
            elif param_def.type == ParameterType.OBJECT:
                return isinstance(value, dict)
            elif param_def.type == ParameterType.NULL:
                return value is None
            else:
                return True
                
        except Exception:
            return False
    
    def _filter_functions(
        self,
        category: Optional[str] = None,
        tags: Optional[List[str]] = None
    ) -> List[FunctionDefinition]:
        """
        过滤函数
        
        Args:
            category: 分类过滤
            tags: 标签过滤
        
        Returns:
            List[FunctionDefinition]: 过滤后的函数列表
        """
        functions = list(self._functions.values())
        
        if category:
            functions = [f for f in functions if f.category == category]
        
        if tags:
            functions = [
                f for f in functions
                if any(tag in f.tags for tag in tags)
            ]
        
        return functions
    
    def _add_to_history(self, result: FunctionResult) -> None:
        """
        添加到执行历史
        
        Args:
            result: 执行结果
        """
        self._execution_history.append(result)
        
        # 限制历史记录数量
        if len(self._execution_history) > self._max_history:
            self._execution_history = self._execution_history[-self._max_history:]