#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A2A多智能体系统 服务工厂

创建自定义服务实例，集成用户权限验证
"""

import logging
from typing import Dict, List, Optional, Any, Type, Union
from datetime import datetime
import asyncio

from app.models.user import User
from app.core.logging import get_logger
from app.auth.permissions import check_user_permission
from app.core.database import get_db

from .database_session_service import DatabaseSessionService
from .database_memory_service import DatabaseMemoryService
from .database_artifact_service import DatabaseArtifactService

class ServiceFactory:
    """
    服务工厂，创建自定义服务实例，集成用户权限验证
    
    提供以下功能：
    1. 服务实例创建和管理
    2. 用户权限验证和服务访问控制
    3. 服务配置验证和处理
    4. 服务实例缓存和复用
    5. 服务生命周期管理
    6. 服务使用统计和监控
    7. 批量服务创建支持
    8. 服务依赖注入
    """
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        """
        初始化服务工厂
        
        Args:
            logger: 日志记录器
        """
        self.logger = logger or get_logger("service_factory")
        
        # 服务实例缓存
        self._service_cache: Dict[str, Any] = {}
        
        # 服务类型注册表
        self._service_registry: Dict[str, Type] = {
            "session": DatabaseSessionService,
            "memory": DatabaseMemoryService,
            "artifact": DatabaseArtifactService
        }
        
        # 服务配置模板
        self._service_configs: Dict[str, Dict[str, Any]] = {
            "session": {
                "required_permissions": ["session.create", "session.read"],
                "default_config": {},
                "singleton": True
            },
            "memory": {
                "required_permissions": ["memory.create", "memory.read"],
                "default_config": {},
                "singleton": True
            },
            "artifact": {
                "required_permissions": ["artifact.create", "artifact.read"],
                "default_config": {},
                "singleton": True
            }
        }
        
        # 工厂统计
        self.factory_stats = {
            "total_services_created": 0,
            "active_services": 0,
            "service_types_used": set(),
            "users_served": set(),
            "creation_history": [],
            "last_activity": None
        }
        
        self.logger.info("ServiceFactory已初始化")
    
    async def _check_permission(
        self,
        user_id: int,
        owner_id: int,
        service_type: str,
        action: str = "create"
    ) -> bool:
        """
        检查用户权限
        
        Args:
            user_id: 用户ID
            owner_id: 拥有者ID
            service_type: 服务类型
            action: 操作类型
            
        Returns:
            bool: 是否有权限
        """
        try:
            # 检查用户是否存在
            user = await User.get_by_id(user_id)
            if not user:
                self.logger.error(f"用户不存在: {user_id}")
                return False
            
            # 检查服务类型权限
            service_config = self._service_configs.get(service_type, {})
            required_permissions = service_config.get("required_permissions", [])
            
            for permission in required_permissions:
                has_permission = await check_user_permission(
                    user_id=user_id,
                    owner_id=owner_id,
                    resource_type=service_type,
                    action=action
                )
                
                if not has_permission:
                    self.logger.error(f"用户 {user_id} 没有权限 {permission}")
                    return False
            
            return True
        except Exception as e:
            self.logger.error(f"权限检查错误: {str(e)}")
            return False
    
    def _validate_service_config(
        self,
        service_type: str,
        config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        验证和处理服务配置
        
        Args:
            service_type: 服务类型
            config: 服务配置
            
        Returns:
            Dict[str, Any]: 处理后的配置
        """
        try:
            service_config = self._service_configs.get(service_type, {})
            default_config = service_config.get("default_config", {})
            
            # 合并默认配置
            final_config = default_config.copy()
            final_config.update(config)
            
            # 验证必需的配置项
            required_fields = service_config.get("required_fields", [])
            for field in required_fields:
                if field not in final_config:
                    raise ValueError(f"缺少必需的配置项: {field}")
            
            # 验证配置值类型
            field_types = service_config.get("field_types", {})
            for field, expected_type in field_types.items():
                if field in final_config:
                    if not isinstance(final_config[field], expected_type):
                        raise ValueError(f"配置项 {field} 类型错误，期望 {expected_type.__name__}")
            
            return final_config
        except Exception as e:
            self.logger.error(f"配置验证错误: {str(e)}")
            raise e
    
    def _get_service_cache_key(
        self,
        service_type: str,
        user_id: int,
        owner_id: int,
        config: Dict[str, Any]
    ) -> str:
        """
        生成服务缓存键
        
        Args:
            service_type: 服务类型
            user_id: 用户ID
            owner_id: 拥有者ID
            config: 服务配置
            
        Returns:
            str: 缓存键
        """
        import hashlib
        import json
        
        # 对于单例服务，只使用服务类型作为键
        service_config = self._service_configs.get(service_type, {})
        if service_config.get("singleton", False):
            return f"{service_type}_singleton"
        
        # 对于非单例服务，使用完整信息生成键
        key_data = {
            "service_type": service_type,
            "user_id": user_id,
            "owner_id": owner_id,
            "config": config
        }
        
        key_str = json.dumps(key_data, sort_keys=True)
        return hashlib.md5(key_str.encode()).hexdigest()
    
    async def create_service(
        self,
        service_type: str,
        user_id: int,
        owner_id: int,
        config: Optional[Dict[str, Any]] = None
    ) -> Optional[Any]:
        """
        创建服务实例
        
        Args:
            service_type: 服务类型
            user_id: 用户ID
            owner_id: 拥有者ID
            config: 服务配置
            
        Returns:
            Optional[Any]: 服务实例
        """
        try:
            # 检查服务类型是否支持
            if service_type not in self._service_registry:
                raise ValueError(f"不支持的服务类型: {service_type}")
            
            # 检查用户权限
            has_permission = await self._check_permission(user_id, owner_id, service_type, "create")
            if not has_permission:
                raise PermissionError(f"用户 {user_id} 没有权限创建 {service_type} 服务")
            
            # 验证和处理配置
            config = config or {}
            validated_config = self._validate_service_config(service_type, config)
            
            # 生成缓存键
            cache_key = self._get_service_cache_key(service_type, user_id, owner_id, validated_config)
            
            # 检查缓存
            if cache_key in self._service_cache:
                service_instance = self._service_cache[cache_key]
                self.logger.info(f"从缓存返回服务实例: {service_type}")
                return service_instance
            
            # 创建服务实例
            service_class = self._service_registry[service_type]
            service_instance = service_class(
                logger=self.logger.getChild(service_type)
            )
            
            # 缓存服务实例
            self._service_cache[cache_key] = service_instance
            
            # 更新统计
            self.factory_stats["total_services_created"] += 1
            self.factory_stats["active_services"] += 1
            self.factory_stats["service_types_used"].add(service_type)
            self.factory_stats["users_served"].add(user_id)
            self.factory_stats["last_activity"] = datetime.now().isoformat()
            
            # 记录创建历史
            creation_record = {
                "service_type": service_type,
                "user_id": user_id,
                "owner_id": owner_id,
                "cache_key": cache_key,
                "created_at": datetime.now().isoformat(),
                "config": validated_config
            }
            self.factory_stats["creation_history"].append(creation_record)
            
            # 限制历史记录数量
            if len(self.factory_stats["creation_history"]) > 1000:
                self.factory_stats["creation_history"] = self.factory_stats["creation_history"][-500:]
            
            self.logger.info(f"服务实例已创建: {service_type} (用户: {user_id})")
            return service_instance
        except Exception as e:
            self.logger.error(f"创建服务实例错误: {str(e)}")
            raise e
    
    async def get_service(
        self,
        service_type: str,
        user_id: int,
        owner_id: int,
        config: Optional[Dict[str, Any]] = None
    ) -> Optional[Any]:
        """
        获取服务实例（如果不存在则创建）
        
        Args:
            service_type: 服务类型
            user_id: 用户ID
            owner_id: 拥有者ID
            config: 服务配置
            
        Returns:
            Optional[Any]: 服务实例
        """
        try:
            # 检查权限
            has_permission = await self._check_permission(user_id, owner_id, service_type, "read")
            if not has_permission:
                raise PermissionError(f"用户 {user_id} 没有权限访问 {service_type} 服务")
            
            # 验证配置
            config = config or {}
            validated_config = self._validate_service_config(service_type, config)
            
            # 生成缓存键
            cache_key = self._get_service_cache_key(service_type, user_id, owner_id, validated_config)
            
            # 检查缓存
            if cache_key in self._service_cache:
                return self._service_cache[cache_key]
            
            # 创建新实例
            return await self.create_service(service_type, user_id, owner_id, config)
        except Exception as e:
            self.logger.error(f"获取服务实例错误: {str(e)}")
            return None
    
    async def create_session_service(
        self,
        user_id: int,
        owner_id: int,
        config: Optional[Dict[str, Any]] = None
    ) -> Optional[DatabaseSessionService]:
        """
        创建会话服务实例
        
        Args:
            user_id: 用户ID
            owner_id: 拥有者ID
            config: 服务配置
            
        Returns:
            Optional[DatabaseSessionService]: 会话服务实例
        """
        return await self.create_service("session", user_id, owner_id, config)
    
    async def create_memory_service(
        self,
        user_id: int,
        owner_id: int,
        config: Optional[Dict[str, Any]] = None
    ) -> Optional[DatabaseMemoryService]:
        """
        创建内存服务实例
        
        Args:
            user_id: 用户ID
            owner_id: 拥有者ID
            config: 服务配置
            
        Returns:
            Optional[DatabaseMemoryService]: 内存服务实例
        """
        return await self.create_service("memory", user_id, owner_id, config)
    
    async def create_artifact_service(
        self,
        user_id: int,
        owner_id: int,
        config: Optional[Dict[str, Any]] = None
    ) -> Optional[DatabaseArtifactService]:
        """
        创建工件服务实例
        
        Args:
            user_id: 用户ID
            owner_id: 拥有者ID
            config: 服务配置
            
        Returns:
            Optional[DatabaseArtifactService]: 工件服务实例
        """
        return await self.create_service("artifact", user_id, owner_id, config)
    
    async def batch_create_services(
        self,
        service_requests: List[Dict[str, Any]],
        user_id: int,
        owner_id: int
    ) -> Dict[str, Any]:
        """
        批量创建服务实例
        
        Args:
            service_requests: 服务请求列表，每个请求包含service_type和config
            user_id: 用户ID
            owner_id: 拥有者ID
            
        Returns:
            Dict[str, Any]: 创建结果
        """
        try:
            results = {
                "success": {},
                "failed": {},
                "summary": {
                    "total": len(service_requests),
                    "success_count": 0,
                    "failed_count": 0
                }
            }
            
            for i, request in enumerate(service_requests):
                try:
                    service_type = request.get("service_type")
                    config = request.get("config", {})
                    
                    if not service_type:
                        raise ValueError("缺少service_type")
                    
                    service_instance = await self.create_service(
                        service_type=service_type,
                        user_id=user_id,
                        owner_id=owner_id,
                        config=config
                    )
                    
                    results["success"][f"request_{i}"] = {
                        "service_type": service_type,
                        "service_instance": service_instance,
                        "config": config
                    }
                    results["summary"]["success_count"] += 1
                except Exception as e:
                    results["failed"][f"request_{i}"] = {
                        "service_type": request.get("service_type", "unknown"),
                        "error": str(e),
                        "config": request.get("config", {})
                    }
                    results["summary"]["failed_count"] += 1
                    self.logger.error(f"批量创建服务失败 (请求 {i}): {str(e)}")
            
            return results
        except Exception as e:
            self.logger.error(f"批量创建服务错误: {str(e)}")
            return {
                "success": {},
                "failed": {"all": str(e)},
                "summary": {"total": len(service_requests), "success_count": 0, "failed_count": len(service_requests)}
            }
    
    def register_service_type(
        self,
        service_type: str,
        service_class: Type,
        config: Optional[Dict[str, Any]] = None
    ) -> None:
        """
        注册新的服务类型
        
        Args:
            service_type: 服务类型名称
            service_class: 服务类
            config: 服务配置
        """
        try:
            self._service_registry[service_type] = service_class
            
            if config:
                self._service_configs[service_type] = config
            
            self.logger.info(f"服务类型已注册: {service_type}")
        except Exception as e:
            self.logger.error(f"注册服务类型错误: {str(e)}")
            raise e
    
    def unregister_service_type(self, service_type: str) -> bool:
        """
        注销服务类型
        
        Args:
            service_type: 服务类型名称
            
        Returns:
            bool: 是否注销成功
        """
        try:
            if service_type in self._service_registry:
                del self._service_registry[service_type]
            
            if service_type in self._service_configs:
                del self._service_configs[service_type]
            
            # 清理相关缓存
            cache_keys_to_remove = [
                key for key in self._service_cache.keys()
                if key.startswith(f"{service_type}_")
            ]
            
            for key in cache_keys_to_remove:
                del self._service_cache[key]
            
            self.logger.info(f"服务类型已注销: {service_type}")
            return True
        except Exception as e:
            self.logger.error(f"注销服务类型错误: {str(e)}")
            return False
    
    def list_service_types(self) -> List[str]:
        """
        列出所有支持的服务类型
        
        Returns:
            List[str]: 服务类型列表
        """
        return list(self._service_registry.keys())
    
    def get_service_config(self, service_type: str) -> Optional[Dict[str, Any]]:
        """
        获取服务配置
        
        Args:
            service_type: 服务类型
            
        Returns:
            Optional[Dict[str, Any]]: 服务配置
        """
        return self._service_configs.get(service_type)
    
    async def cleanup_inactive_services(self, max_idle_time: int = 3600) -> int:
        """
        清理不活跃的服务实例
        
        Args:
            max_idle_time: 最大空闲时间（秒）
            
        Returns:
            int: 清理的服务数量
        """
        try:
            current_time = datetime.now()
            cleaned_count = 0
            
            # 获取需要清理的缓存键
            keys_to_remove = []
            
            for cache_key, service_instance in self._service_cache.items():
                try:
                    # 检查服务是否有is_active方法
                    if hasattr(service_instance, 'is_active'):
                        if not service_instance.is_active():
                            keys_to_remove.append(cache_key)
                            continue
                    
                    # 检查服务最后活动时间
                    if hasattr(service_instance, '_last_activity'):
                        last_activity = service_instance._last_activity
                        if isinstance(last_activity, datetime):
                            idle_time = (current_time - last_activity).total_seconds()
                            if idle_time > max_idle_time:
                                keys_to_remove.append(cache_key)
                except Exception as e:
                    self.logger.error(f"检查服务状态错误: {str(e)}")
                    keys_to_remove.append(cache_key)
            
            # 清理服务
            for cache_key in keys_to_remove:
                try:
                    service_instance = self._service_cache[cache_key]
                    
                    # 关闭服务
                    if hasattr(service_instance, 'close'):
                        await service_instance.close()
                    
                    # 从缓存中移除
                    del self._service_cache[cache_key]
                    cleaned_count += 1
                    
                    self.logger.info(f"已清理不活跃服务: {cache_key}")
                except Exception as e:
                    self.logger.error(f"清理服务错误: {str(e)}")
            
            # 更新统计
            self.factory_stats["active_services"] = len(self._service_cache)
            
            if cleaned_count > 0:
                self.logger.info(f"已清理 {cleaned_count} 个不活跃服务")
            
            return cleaned_count
        except Exception as e:
            self.logger.error(f"清理不活跃服务错误: {str(e)}")
            return 0
    
    def get_factory_stats(self) -> Dict[str, Any]:
        """
        获取工厂统计
        
        Returns:
            Dict[str, Any]: 工厂统计信息
        """
        stats = self.factory_stats.copy()
        stats["service_types_used"] = list(stats["service_types_used"])
        stats["users_served"] = len(stats["users_served"])
        stats["cached_services"] = len(self._service_cache)
        stats["registered_service_types"] = list(self._service_registry.keys())
        return stats
    
    async def close(self) -> None:
        """
        关闭工厂和所有服务实例
        """
        try:
            # 关闭所有服务实例
            for service_instance in self._service_cache.values():
                try:
                    if hasattr(service_instance, 'close'):
                        await service_instance.close()
                except Exception as e:
                    self.logger.error(f"关闭服务实例错误: {str(e)}")
            
            # 清空缓存
            self._service_cache.clear()
            
            # 更新统计
            self.factory_stats["active_services"] = 0
            
            self.logger.info("ServiceFactory已关闭")
        except Exception as e:
            self.logger.error(f"关闭工厂错误: {str(e)}")

# 全局服务工厂实例
_service_factory_instance: Optional[ServiceFactory] = None

def get_service_factory() -> ServiceFactory:
    """
    获取全局服务工厂实例（单例模式）
    
    Returns:
        ServiceFactory: 服务工厂实例
    """
    global _service_factory_instance
    
    if _service_factory_instance is None:
        _service_factory_instance = ServiceFactory()
    
    return _service_factory_instance

async def close_service_factory() -> None:
    """
    关闭全局服务工厂实例
    """
    global _service_factory_instance
    
    if _service_factory_instance is not None:
        await _service_factory_instance.close()
        _service_factory_instance = None