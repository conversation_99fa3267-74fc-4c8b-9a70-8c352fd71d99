#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A2A多智能体系统工作流相关Pydantic模式

定义工作流相关的请求和响应模式
"""

from typing import Dict, List, Optional, Any, Union
from datetime import datetime
from pydantic import Field, validator
from enum import Enum

from .base import BaseSchema


class WorkflowType(str, Enum):
    """工作流类型枚举"""
    SEQUENTIAL = "sequential"
    PARALLEL = "parallel"
    CONDITIONAL = "conditional"
    LOOP = "loop"
    DAG = "dag"
    CUSTOM = "custom"


class WorkflowStatus(str, Enum):
    """工作流状态枚举"""
    DRAFT = "draft"
    ACTIVE = "active"
    INACTIVE = "inactive"
    ARCHIVED = "archived"
    DEPRECATED = "deprecated"


class WorkflowExecutionStatus(str, Enum):
    """工作流执行状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    PAUSED = "paused"


class WorkflowNodeType(str, Enum):
    """工作流节点类型枚举"""
    START = "start"
    END = "end"
    TASK = "task"
    DECISION = "decision"
    PARALLEL = "parallel"
    MERGE = "merge"
    LOOP = "loop"
    CONDITION = "condition"
    AGENT = "agent"
    TOOL = "tool"
    CUSTOM = "custom"


class WorkflowNodeStatus(str, Enum):
    """工作流节点状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"
    CANCELLED = "cancelled"


class WorkflowTriggerType(str, Enum):
    """工作流触发类型枚举"""
    MANUAL = "manual"
    SCHEDULED = "scheduled"
    EVENT = "event"
    API = "api"
    WEBHOOK = "webhook"
    MESSAGE = "message"


class WorkflowPriority(str, Enum):
    """工作流优先级枚举"""
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    URGENT = "urgent"
    CRITICAL = "critical"


class WorkflowCreate(BaseSchema):
    """
    工作流创建请求模式
    """
    
    name: str = Field(
        min_length=1,
        max_length=200,
        description="工作流名称"
    )
    
    description: Optional[str] = Field(
        default=None,
        max_length=2000,
        description="工作流描述"
    )
    
    workflow_type: WorkflowType = Field(
        default=WorkflowType.SEQUENTIAL,
        description="工作流类型"
    )
    
    category: Optional[str] = Field(
        default=None,
        max_length=100,
        description="工作流分类"
    )
    
    definition: Dict[str, Any] = Field(
        description="工作流定义（JSON格式）"
    )
    
    config: Optional[Dict[str, Any]] = Field(
        default=None,
        description="工作流配置"
    )
    
    input_schema: Optional[Dict[str, Any]] = Field(
        default=None,
        description="输入数据模式"
    )
    
    output_schema: Optional[Dict[str, Any]] = Field(
        default=None,
        description="输出数据模式"
    )
    
    trigger_config: Optional[Dict[str, Any]] = Field(
        default=None,
        description="触发器配置"
    )
    
    timeout: Optional[int] = Field(
        default=None,
        ge=1,
        description="超时时间（秒）"
    )
    
    max_retries: int = Field(
        default=3,
        ge=0,
        le=10,
        description="最大重试次数"
    )
    
    tags: Optional[List[str]] = Field(
        default=None,
        description="工作流标签"
    )
    
    is_public: bool = Field(
        default=False,
        description="是否公开"
    )


class WorkflowUpdate(BaseSchema):
    """
    工作流更新请求模式
    """
    
    name: Optional[str] = Field(
        default=None,
        min_length=1,
        max_length=200,
        description="工作流名称"
    )
    
    description: Optional[str] = Field(
        default=None,
        max_length=2000,
        description="工作流描述"
    )
    
    status: Optional[WorkflowStatus] = Field(
        default=None,
        description="工作流状态"
    )
    
    category: Optional[str] = Field(
        default=None,
        max_length=100,
        description="工作流分类"
    )
    
    definition: Optional[Dict[str, Any]] = Field(
        default=None,
        description="工作流定义（JSON格式）"
    )
    
    config: Optional[Dict[str, Any]] = Field(
        default=None,
        description="工作流配置"
    )
    
    input_schema: Optional[Dict[str, Any]] = Field(
        default=None,
        description="输入数据模式"
    )
    
    output_schema: Optional[Dict[str, Any]] = Field(
        default=None,
        description="输出数据模式"
    )
    
    trigger_config: Optional[Dict[str, Any]] = Field(
        default=None,
        description="触发器配置"
    )
    
    timeout: Optional[int] = Field(
        default=None,
        ge=1,
        description="超时时间（秒）"
    )
    
    max_retries: Optional[int] = Field(
        default=None,
        ge=0,
        le=10,
        description="最大重试次数"
    )
    
    tags: Optional[List[str]] = Field(
        default=None,
        description="工作流标签"
    )
    
    is_public: Optional[bool] = Field(
        default=None,
        description="是否公开"
    )


class WorkflowResponse(BaseSchema):
    """
    工作流响应模式
    """
    
    id: int = Field(
        description="工作流ID"
    )
    
    workflow_id: str = Field(
        description="工作流唯一标识"
    )
    
    name: str = Field(
        description="工作流名称"
    )
    
    description: Optional[str] = Field(
        description="工作流描述"
    )
    
    workflow_type: WorkflowType = Field(
        description="工作流类型"
    )
    
    status: WorkflowStatus = Field(
        description="工作流状态"
    )
    
    category: Optional[str] = Field(
        description="工作流分类"
    )
    
    version: str = Field(
        description="工作流版本"
    )
    
    user_id: int = Field(
        description="创建者用户ID"
    )
    
    owner_id: int = Field(
        description="拥有者用户ID"
    )
    
    definition: Dict[str, Any] = Field(
        description="工作流定义（JSON格式）"
    )
    
    config: Optional[Dict[str, Any]] = Field(
        description="工作流配置"
    )
    
    input_schema: Optional[Dict[str, Any]] = Field(
        description="输入数据模式"
    )
    
    output_schema: Optional[Dict[str, Any]] = Field(
        description="输出数据模式"
    )
    
    trigger_config: Optional[Dict[str, Any]] = Field(
        description="触发器配置"
    )
    
    timeout: Optional[int] = Field(
        description="超时时间（秒）"
    )
    
    max_retries: int = Field(
        description="最大重试次数"
    )
    
    execution_count: int = Field(
        description="执行次数"
    )
    
    success_count: int = Field(
        description="成功次数"
    )
    
    failure_count: int = Field(
        description="失败次数"
    )
    
    last_execution_time: Optional[datetime] = Field(
        description="最后执行时间"
    )
    
    average_duration: Optional[float] = Field(
        description="平均执行时长（秒）"
    )
    
    tags: Optional[List[str]] = Field(
        description="工作流标签"
    )
    
    is_public: bool = Field(
        description="是否公开"
    )
    
    created_at: datetime = Field(
        description="创建时间"
    )
    
    updated_at: datetime = Field(
        description="更新时间"
    )


class WorkflowExecutionCreate(BaseSchema):
    """
    工作流执行创建请求模式
    """
    
    input_data: Optional[Dict[str, Any]] = Field(
        default=None,
        description="输入数据"
    )
    
    execution_config: Optional[Dict[str, Any]] = Field(
        default=None,
        description="执行配置"
    )
    
    trigger_type: WorkflowTriggerType = Field(
        default=WorkflowTriggerType.MANUAL,
        description="触发类型"
    )
    
    trigger_data: Optional[Dict[str, Any]] = Field(
        default=None,
        description="触发数据"
    )
    
    priority: int = Field(
        default=5,
        ge=1,
        le=10,
        description="执行优先级（1-10）"
    )


class WorkflowExecutionResponse(BaseSchema):
    """
    工作流执行响应模式
    """
    
    id: int = Field(
        description="执行ID"
    )
    
    execution_id: str = Field(
        description="执行唯一标识"
    )
    
    workflow_id: int = Field(
        description="工作流ID"
    )
    
    user_id: int = Field(
        description="创建者用户ID"
    )
    
    owner_id: int = Field(
        description="拥有者用户ID"
    )
    
    status: WorkflowExecutionStatus = Field(
        description="执行状态"
    )
    
    trigger_type: WorkflowTriggerType = Field(
        description="触发类型"
    )
    
    trigger_data: Optional[Dict[str, Any]] = Field(
        description="触发数据"
    )
    
    input_data: Optional[Dict[str, Any]] = Field(
        description="输入数据"
    )
    
    output_data: Optional[Dict[str, Any]] = Field(
        description="输出数据"
    )
    
    execution_config: Optional[Dict[str, Any]] = Field(
        description="执行配置"
    )
    
    current_node: Optional[str] = Field(
        description="当前执行节点"
    )
    
    progress: float = Field(
        description="执行进度（0-100）"
    )
    
    start_time: Optional[datetime] = Field(
        description="开始时间"
    )
    
    end_time: Optional[datetime] = Field(
        description="结束时间"
    )
    
    duration: Optional[float] = Field(
        description="执行时长（秒）"
    )
    
    error_message: Optional[str] = Field(
        description="错误信息"
    )
    
    error_details: Optional[Dict[str, Any]] = Field(
        description="错误详情"
    )
    
    retry_count: int = Field(
        description="重试次数"
    )
    
    priority: int = Field(
        description="执行优先级"
    )
    
    created_at: datetime = Field(
        description="创建时间"
    )
    
    updated_at: datetime = Field(
        description="更新时间"
    )


class WorkflowNodeCreate(BaseSchema):
    """
    工作流节点创建请求模式
    """
    
    node_name: str = Field(
        min_length=1,
        max_length=200,
        description="节点名称"
    )
    
    description: Optional[str] = Field(
        default=None,
        max_length=1000,
        description="节点描述"
    )
    
    node_type: WorkflowNodeType = Field(
        description="节点类型"
    )
    
    position_x: float = Field(
        description="节点X坐标"
    )
    
    position_y: float = Field(
        description="节点Y坐标"
    )
    
    config: Optional[Dict[str, Any]] = Field(
        default=None,
        description="节点配置"
    )
    
    input_ports: Optional[List[str]] = Field(
        default=None,
        description="输入端口"
    )
    
    output_ports: Optional[List[str]] = Field(
        default=None,
        description="输出端口"
    )
    
    conditions: Optional[Dict[str, Any]] = Field(
        default=None,
        description="执行条件"
    )
    
    timeout: Optional[int] = Field(
        default=None,
        ge=1,
        description="超时时间（秒）"
    )
    
    retry_config: Optional[Dict[str, Any]] = Field(
        default=None,
        description="重试配置"
    )


class WorkflowNodeResponse(BaseSchema):
    """
    工作流节点响应模式
    """
    
    id: int = Field(
        description="节点ID"
    )
    
    node_id: str = Field(
        description="节点唯一标识"
    )
    
    workflow_id: int = Field(
        description="工作流ID"
    )
    
    node_name: str = Field(
        description="节点名称"
    )
    
    description: Optional[str] = Field(
        description="节点描述"
    )
    
    node_type: WorkflowNodeType = Field(
        description="节点类型"
    )
    
    status: WorkflowNodeStatus = Field(
        description="节点状态"
    )
    
    position_x: float = Field(
        description="节点X坐标"
    )
    
    position_y: float = Field(
        description="节点Y坐标"
    )
    
    config: Optional[Dict[str, Any]] = Field(
        description="节点配置"
    )
    
    input_ports: Optional[List[str]] = Field(
        description="输入端口"
    )
    
    output_ports: Optional[List[str]] = Field(
        description="输出端口"
    )
    
    conditions: Optional[Dict[str, Any]] = Field(
        description="执行条件"
    )
    
    timeout: Optional[int] = Field(
        description="超时时间（秒）"
    )
    
    retry_config: Optional[Dict[str, Any]] = Field(
        description="重试配置"
    )
    
    execution_count: int = Field(
        description="执行次数"
    )
    
    success_count: int = Field(
        description="成功次数"
    )
    
    failure_count: int = Field(
        description="失败次数"
    )
    
    average_duration: Optional[float] = Field(
        description="平均执行时长（秒）"
    )
    
    created_at: datetime = Field(
        description="创建时间"
    )
    
    updated_at: datetime = Field(
        description="更新时间"
    )


class WorkflowQuery(BaseSchema):
    """
    工作流查询参数模式
    """
    
    workflow_type: Optional[WorkflowType] = Field(
        default=None,
        description="工作流类型过滤"
    )
    
    status: Optional[WorkflowStatus] = Field(
        default=None,
        description="工作流状态过滤"
    )
    
    category: Optional[str] = Field(
        default=None,
        description="工作流分类过滤"
    )
    
    user_id: Optional[int] = Field(
        default=None,
        description="用户ID过滤"
    )
    
    tags: Optional[List[str]] = Field(
        default=None,
        description="标签过滤"
    )
    
    is_public: Optional[bool] = Field(
        default=None,
        description="是否公开过滤"
    )
    
    search: Optional[str] = Field(
        default=None,
        max_length=200,
        description="搜索关键词"
    )


class WorkflowExecutionQuery(BaseSchema):
    """
    工作流执行查询参数模式
    """
    
    workflow_id: Optional[int] = Field(
        default=None,
        description="工作流ID过滤"
    )
    
    status: Optional[WorkflowExecutionStatus] = Field(
        default=None,
        description="执行状态过滤"
    )
    
    trigger_type: Optional[WorkflowTriggerType] = Field(
        default=None,
        description="触发类型过滤"
    )
    
    user_id: Optional[int] = Field(
        default=None,
        description="用户ID过滤"
    )
    
    start_date: Optional[datetime] = Field(
        default=None,
        description="开始日期过滤"
    )
    
    end_date: Optional[datetime] = Field(
        default=None,
        description="结束日期过滤"
    )


class WorkflowStatistics(BaseSchema):
    """
    工作流统计信息模式
    """
    
    total_workflows: int = Field(
        description="总工作流数"
    )
    
    active_workflows: int = Field(
        description="活跃工作流数"
    )
    
    total_executions: int = Field(
        description="总执行次数"
    )
    
    successful_executions: int = Field(
        description="成功执行次数"
    )
    
    failed_executions: int = Field(
        description="失败执行次数"
    )
    
    success_rate: float = Field(
        description="成功率"
    )
    
    average_execution_time: Optional[float] = Field(
        description="平均执行时间（秒）"
    )
    
    most_used_workflows: List[Dict[str, Any]] = Field(
        description="最常用工作流"
    )


class WorkflowTemplate(BaseSchema):
    """
    工作流模板模式
    """
    
    name: str = Field(
        description="模板名称"
    )
    
    description: Optional[str] = Field(
        description="模板描述"
    )
    
    category: str = Field(
        description="模板分类"
    )
    
    workflow_type: WorkflowType = Field(
        description="工作流类型"
    )
    
    definition: Dict[str, Any] = Field(
        description="工作流定义模板"
    )
    
    config_template: Optional[Dict[str, Any]] = Field(
        description="配置模板"
    )
    
    input_schema: Optional[Dict[str, Any]] = Field(
        description="输入数据模式"
    )
    
    output_schema: Optional[Dict[str, Any]] = Field(
        description="输出数据模式"
    )
    
    tags: Optional[List[str]] = Field(
        description="模板标签"
    )
    
    usage_count: int = Field(
        description="使用次数"
    )


class WorkflowValidation(BaseSchema):
    """
    工作流验证结果模式
    """
    
    is_valid: bool = Field(
        description="是否有效"
    )
    
    errors: List[str] = Field(
        description="错误列表"
    )
    
    warnings: List[str] = Field(
        description="警告列表"
    )
    
    suggestions: List[str] = Field(
        description="建议列表"
    )


class WorkflowBatchOperation(BaseSchema):
    """
    工作流批量操作请求模式
    """
    
    workflow_ids: List[int] = Field(
        min_items=1,
        max_items=50,
        description="工作流ID列表"
    )
    
    operation: str = Field(
        description="操作类型（activate/deactivate/archive/delete）"
    )
    
    @validator('operation')
    def validate_operation(cls, v):
        """验证操作类型"""
        allowed_operations = ['activate', 'deactivate', 'archive', 'delete']
        if v not in allowed_operations:
            raise ValueError(f'操作类型必须是以下之一: {", ".join(allowed_operations)}')
        return v