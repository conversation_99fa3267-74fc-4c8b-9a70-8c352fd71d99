#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A2A多智能体系统日志和性能指标模型

定义日志和性能监控相关的数据模型
"""

from datetime import datetime
from typing import Optional, List, Dict, Any
from sqlalchemy import Column, Integer, String, Text, Boolean, DateTime, ForeignKey, Index, Float
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from .base import BaseModel


class SystemLog(BaseModel):
    """
    系统日志模型
    
    存储系统运行日志
    """
    
    __tablename__ = "system_logs"
    
    # 日志基本信息
    log_level = Column(
        String(20),
        nullable=False,
        comment="日志级别"
    )
    
    logger_name = Column(
        String(100),
        nullable=False,
        comment="日志器名称"
    )
    
    message = Column(
        Text,
        nullable=False,
        comment="日志消息"
    )
    
    # 日志分类
    category = Column(
        String(50),
        nullable=False,
        default="general",
        comment="日志分类"
    )
    
    # 关联信息
    user_id = Column(
        Integer,
        ForeignKey("users.id", ondelete="SET NULL"),
        nullable=True,
        comment="用户ID"
    )
    
    session_id = Column(
        Integer,
        ForeignKey("sessions.id", ondelete="SET NULL"),
        nullable=True,
        comment="会话ID"
    )
    
    request_id = Column(
        String(36),
        nullable=True,
        comment="请求ID"
    )
    
    # 上下文信息
    module = Column(
        String(100),
        nullable=True,
        comment="模块名称"
    )
    
    function = Column(
        String(100),
        nullable=True,
        comment="函数名称"
    )
    
    line_number = Column(
        Integer,
        nullable=True,
        comment="行号"
    )
    
    # 异常信息
    exception_type = Column(
        String(100),
        nullable=True,
        comment="异常类型"
    )
    
    exception_message = Column(
        Text,
        nullable=True,
        comment="异常消息"
    )
    
    stack_trace = Column(
        Text,
        nullable=True,
        comment="堆栈跟踪"
    )
    
    # 额外数据
    extra_data = Column(
        Text,
        nullable=True,
        comment="额外数据（JSON格式）"
    )
    
    # 时间信息
    timestamp = Column(
        DateTime,
        nullable=False,
        default=func.now(),
        comment="时间戳"
    )
    
    # 关联关系
    user = relationship("User")
    session = relationship("Session")
    
    # 索引
    __table_args__ = (
        Index("idx_system_logs_log_level", "log_level"),
        Index("idx_system_logs_logger_name", "logger_name"),
        Index("idx_system_logs_category", "category"),
        Index("idx_system_logs_user_id", "user_id"),
        Index("idx_system_logs_session_id", "session_id"),
        Index("idx_system_logs_request_id", "request_id"),
        Index("idx_system_logs_timestamp", "timestamp"),
        Index("idx_system_logs_module", "module"),
        Index("idx_system_logs_exception_type", "exception_type"),
    )
    
    def set_extra_data(self, data: Dict[str, Any]) -> None:
        """
        设置额外数据
        
        Args:
            data: 额外数据字典
        """
        import json
        self.extra_data = json.dumps(data, ensure_ascii=False)
    
    def get_extra_data(self) -> Dict[str, Any]:
        """
        获取额外数据
        
        Returns:
            Dict[str, Any]: 额外数据字典
        """
        if not self.extra_data:
            return {}
        
        try:
            import json
            return json.loads(self.extra_data)
        except (json.JSONDecodeError, TypeError):
            return {}
    
    def __repr__(self) -> str:
        return f"<SystemLog(id={self.id}, level='{self.log_level}', logger='{self.logger_name}')>"


class PerformanceMetric(BaseModel):
    """
    性能指标模型
    
    存储系统性能监控数据
    """
    
    __tablename__ = "performance_metrics"
    
    # 指标基本信息
    metric_name = Column(
        String(100),
        nullable=False,
        comment="指标名称"
    )
    
    metric_type = Column(
        String(50),
        nullable=False,
        comment="指标类型"
    )
    
    metric_value = Column(
        Float,
        nullable=False,
        comment="指标值"
    )
    
    # 指标单位和分类
    unit = Column(
        String(20),
        nullable=True,
        comment="单位"
    )
    
    category = Column(
        String(50),
        nullable=False,
        default="system",
        comment="指标分类"
    )
    
    # 关联信息
    user_id = Column(
        Integer,
        ForeignKey("users.id", ondelete="SET NULL"),
        nullable=True,
        comment="用户ID"
    )
    
    agent_id = Column(
        Integer,
        ForeignKey("agents.id", ondelete="SET NULL"),
        nullable=True,
        comment="智能体ID"
    )
    
    session_id = Column(
        Integer,
        ForeignKey("sessions.id", ondelete="SET NULL"),
        nullable=True,
        comment="会话ID"
    )
    
    request_id = Column(
        String(36),
        nullable=True,
        comment="请求ID"
    )
    
    # 标签和维度
    tags = Column(
        Text,
        nullable=True,
        comment="标签（JSON格式）"
    )
    
    dimensions = Column(
        Text,
        nullable=True,
        comment="维度（JSON格式）"
    )
    
    # 时间信息
    timestamp = Column(
        DateTime,
        nullable=False,
        default=func.now(),
        comment="时间戳"
    )
    
    # 关联关系
    user = relationship("User")
    agent = relationship("Agent")
    session = relationship("Session")
    
    # 索引
    __table_args__ = (
        Index("idx_performance_metrics_metric_name", "metric_name"),
        Index("idx_performance_metrics_metric_type", "metric_type"),
        Index("idx_performance_metrics_category", "category"),
        Index("idx_performance_metrics_user_id", "user_id"),
        Index("idx_performance_metrics_agent_id", "agent_id"),
        Index("idx_performance_metrics_session_id", "session_id"),
        Index("idx_performance_metrics_request_id", "request_id"),
        Index("idx_performance_metrics_timestamp", "timestamp"),
        # 复合索引
        Index("idx_performance_metrics_name_timestamp", "metric_name", "timestamp"),
    )
    
    def set_tags(self, tags: Dict[str, str]) -> None:
        """
        设置标签
        
        Args:
            tags: 标签字典
        """
        import json
        self.tags = json.dumps(tags, ensure_ascii=False)
    
    def get_tags(self) -> Dict[str, str]:
        """
        获取标签
        
        Returns:
            Dict[str, str]: 标签字典
        """
        if not self.tags:
            return {}
        
        try:
            import json
            return json.loads(self.tags)
        except (json.JSONDecodeError, TypeError):
            return {}
    
    def set_dimensions(self, dimensions: Dict[str, Any]) -> None:
        """
        设置维度
        
        Args:
            dimensions: 维度字典
        """
        import json
        self.dimensions = json.dumps(dimensions, ensure_ascii=False)
    
    def get_dimensions(self) -> Dict[str, Any]:
        """
        获取维度
        
        Returns:
            Dict[str, Any]: 维度字典
        """
        if not self.dimensions:
            return {}
        
        try:
            import json
            return json.loads(self.dimensions)
        except (json.JSONDecodeError, TypeError):
            return {}
    
    def __repr__(self) -> str:
        return f"<PerformanceMetric(id={self.id}, name='{self.metric_name}', value={self.metric_value})>"


class ApiLog(BaseModel):
    """
    API日志模型
    
    存储API请求和响应日志
    """
    
    __tablename__ = "api_logs"
    
    # 请求信息
    request_id = Column(
        String(36),
        nullable=False,
        unique=True,
        comment="请求ID"
    )
    
    method = Column(
        String(10),
        nullable=False,
        comment="HTTP方法"
    )
    
    url = Column(
        String(500),
        nullable=False,
        comment="请求URL"
    )
    
    path = Column(
        String(200),
        nullable=False,
        comment="请求路径"
    )
    
    # 用户信息
    user_id = Column(
        Integer,
        ForeignKey("users.id", ondelete="SET NULL"),
        nullable=True,
        comment="用户ID"
    )
    
    user_agent = Column(
        String(500),
        nullable=True,
        comment="用户代理"
    )
    
    ip_address = Column(
        String(45),
        nullable=True,
        comment="IP地址"
    )
    
    # 请求数据
    request_headers = Column(
        Text,
        nullable=True,
        comment="请求头（JSON格式）"
    )
    
    request_body = Column(
        Text,
        nullable=True,
        comment="请求体"
    )
    
    query_params = Column(
        Text,
        nullable=True,
        comment="查询参数（JSON格式）"
    )
    
    # 响应信息
    status_code = Column(
        Integer,
        nullable=False,
        comment="响应状态码"
    )
    
    response_headers = Column(
        Text,
        nullable=True,
        comment="响应头（JSON格式）"
    )
    
    response_body = Column(
        Text,
        nullable=True,
        comment="响应体"
    )
    
    # 性能指标
    response_time = Column(
        Float,
        nullable=False,
        comment="响应时间（毫秒）"
    )
    
    request_size = Column(
        Integer,
        nullable=False,
        default=0,
        comment="请求大小（字节）"
    )
    
    response_size = Column(
        Integer,
        nullable=False,
        default=0,
        comment="响应大小（字节）"
    )
    
    # 错误信息
    error_message = Column(
        Text,
        nullable=True,
        comment="错误信息"
    )
    
    exception_type = Column(
        String(100),
        nullable=True,
        comment="异常类型"
    )
    
    # 时间信息
    started_at = Column(
        DateTime,
        nullable=False,
        default=func.now(),
        comment="开始时间"
    )
    
    completed_at = Column(
        DateTime,
        nullable=False,
        default=func.now(),
        comment="完成时间"
    )
    
    # 关联关系
    user = relationship("User")
    
    # 索引
    __table_args__ = (
        Index("idx_api_logs_request_id", "request_id"),
        Index("idx_api_logs_method", "method"),
        Index("idx_api_logs_path", "path"),
        Index("idx_api_logs_user_id", "user_id"),
        Index("idx_api_logs_status_code", "status_code"),
        Index("idx_api_logs_ip_address", "ip_address"),
        Index("idx_api_logs_started_at", "started_at"),
        Index("idx_api_logs_response_time", "response_time"),
        # 复合索引
        Index("idx_api_logs_method_path", "method", "path"),
        Index("idx_api_logs_user_time", "user_id", "started_at"),
    )
    
    def set_request_headers(self, headers: Dict[str, str]) -> None:
        """
        设置请求头
        
        Args:
            headers: 请求头字典
        """
        import json
        self.request_headers = json.dumps(headers, ensure_ascii=False)
    
    def get_request_headers(self) -> Dict[str, str]:
        """
        获取请求头
        
        Returns:
            Dict[str, str]: 请求头字典
        """
        if not self.request_headers:
            return {}
        
        try:
            import json
            return json.loads(self.request_headers)
        except (json.JSONDecodeError, TypeError):
            return {}
    
    def set_response_headers(self, headers: Dict[str, str]) -> None:
        """
        设置响应头
        
        Args:
            headers: 响应头字典
        """
        import json
        self.response_headers = json.dumps(headers, ensure_ascii=False)
    
    def get_response_headers(self) -> Dict[str, str]:
        """
        获取响应头
        
        Returns:
            Dict[str, str]: 响应头字典
        """
        if not self.response_headers:
            return {}
        
        try:
            import json
            return json.loads(self.response_headers)
        except (json.JSONDecodeError, TypeError):
            return {}
    
    def set_query_params(self, params: Dict[str, Any]) -> None:
        """
        设置查询参数
        
        Args:
            params: 查询参数字典
        """
        import json
        self.query_params = json.dumps(params, ensure_ascii=False)
    
    def get_query_params(self) -> Dict[str, Any]:
        """
        获取查询参数
        
        Returns:
            Dict[str, Any]: 查询参数字典
        """
        if not self.query_params:
            return {}
        
        try:
            import json
            return json.loads(self.query_params)
        except (json.JSONDecodeError, TypeError):
            return {}
    
    def __repr__(self) -> str:
        return f"<ApiLog(id={self.id}, method='{self.method}', path='{self.path}', status={self.status_code})>"


class ErrorLog(BaseModel):
    """
    错误日志模型
    
    存储系统错误和异常信息
    """
    
    __tablename__ = "error_logs"
    
    # 错误基本信息
    error_id = Column(
        String(36),
        nullable=False,
        unique=True,
        comment="错误ID"
    )
    
    error_type = Column(
        String(100),
        nullable=False,
        comment="错误类型"
    )
    
    error_message = Column(
        Text,
        nullable=False,
        comment="错误消息"
    )
    
    # 错误级别和分类
    severity = Column(
        String(20),
        nullable=False,
        default="error",
        comment="严重程度"
    )
    
    category = Column(
        String(50),
        nullable=False,
        default="general",
        comment="错误分类"
    )
    
    # 关联信息
    user_id = Column(
        Integer,
        ForeignKey("users.id", ondelete="SET NULL"),
        nullable=True,
        comment="用户ID"
    )
    
    session_id = Column(
        Integer,
        ForeignKey("sessions.id", ondelete="SET NULL"),
        nullable=True,
        comment="会话ID"
    )
    
    request_id = Column(
        String(36),
        nullable=True,
        comment="请求ID"
    )
    
    # 上下文信息
    module = Column(
        String(100),
        nullable=True,
        comment="模块名称"
    )
    
    function = Column(
        String(100),
        nullable=True,
        comment="函数名称"
    )
    
    line_number = Column(
        Integer,
        nullable=True,
        comment="行号"
    )
    
    # 堆栈跟踪
    stack_trace = Column(
        Text,
        nullable=True,
        comment="堆栈跟踪"
    )
    
    # 环境信息
    environment = Column(
        Text,
        nullable=True,
        comment="环境信息（JSON格式）"
    )
    
    # 处理状态
    is_resolved = Column(
        Boolean,
        nullable=False,
        default=False,
        comment="是否已解决"
    )
    
    resolved_by = Column(
        Integer,
        ForeignKey("users.id", ondelete="SET NULL"),
        nullable=True,
        comment="解决者用户ID"
    )
    
    resolved_at = Column(
        DateTime,
        nullable=True,
        comment="解决时间"
    )
    
    resolution_note = Column(
        Text,
        nullable=True,
        comment="解决说明"
    )
    
    # 时间信息
    occurred_at = Column(
        DateTime,
        nullable=False,
        default=func.now(),
        comment="发生时间"
    )
    
    # 关联关系
    user = relationship("User", foreign_keys=[user_id])
    session = relationship("Session")
    resolved_by_user = relationship("User", foreign_keys=[resolved_by])
    
    # 索引
    __table_args__ = (
        Index("idx_error_logs_error_id", "error_id"),
        Index("idx_error_logs_error_type", "error_type"),
        Index("idx_error_logs_severity", "severity"),
        Index("idx_error_logs_category", "category"),
        Index("idx_error_logs_user_id", "user_id"),
        Index("idx_error_logs_session_id", "session_id"),
        Index("idx_error_logs_request_id", "request_id"),
        Index("idx_error_logs_module", "module"),
        Index("idx_error_logs_is_resolved", "is_resolved"),
        Index("idx_error_logs_occurred_at", "occurred_at"),
    )
    
    def set_environment(self, env: Dict[str, Any]) -> None:
        """
        设置环境信息
        
        Args:
            env: 环境信息字典
        """
        import json
        self.environment = json.dumps(env, ensure_ascii=False)
    
    def get_environment(self) -> Dict[str, Any]:
        """
        获取环境信息
        
        Returns:
            Dict[str, Any]: 环境信息字典
        """
        if not self.environment:
            return {}
        
        try:
            import json
            return json.loads(self.environment)
        except (json.JSONDecodeError, TypeError):
            return {}
    
    def resolve(self, resolved_by: int, note: str = None) -> None:
        """
        标记错误为已解决
        
        Args:
            resolved_by: 解决者用户ID
            note: 解决说明
        """
        self.is_resolved = True
        self.resolved_by = resolved_by
        self.resolved_at = datetime.now()
        self.resolution_note = note
    
    def __repr__(self) -> str:
        return f"<ErrorLog(id={self.id}, error_id='{self.error_id}', type='{self.error_type}')>"