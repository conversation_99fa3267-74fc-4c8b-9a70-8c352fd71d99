-- A2A多智能体系统完整数据库建表脚本
-- 生成时间: 2024年
-- 数据库类型: MySQL 8.0+

-- 设置字符集和存储引擎
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ============================================================================
-- 基础表结构
-- ============================================================================

-- 用户表
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    email VARCHAR(100) NOT NULL UNIQUE COMMENT '邮箱地址',
    password_hash VARCHAR(255) NOT NULL COMMENT '密码哈希',
    full_name VARCHAR(100) COMMENT '全名',
    avatar_url VARCHAR(500) COMMENT '头像URL',
    phone VARCHAR(20) COMMENT '手机号码',
    role VARCHAR(20) NOT NULL DEFAULT 'user' COMMENT '用户角色',
    is_verified BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否已验证邮箱',
    is_locked BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否被锁定',
    last_login_at TIMESTAMP COMMENT '最后登录时间',
    last_login_ip VARCHAR(45) COMMENT '最后登录IP',
    login_count INTEGER NOT NULL DEFAULT 0 COMMENT '登录次数',
    password_changed_at TIMESTAMP COMMENT '密码修改时间',
    failed_login_attempts INTEGER NOT NULL DEFAULT 0 COMMENT '失败登录尝试次数',
    locked_until TIMESTAMP COMMENT '锁定到期时间',
    preferences TEXT COMMENT '用户偏好设置（JSON格式）',
    timezone VARCHAR(50) NOT NULL DEFAULT 'UTC' COMMENT '时区',
    language VARCHAR(10) NOT NULL DEFAULT 'zh-CN' COMMENT '语言',
    is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否激活',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted_at TIMESTAMP COMMENT '删除时间'
);

-- 用户令牌表
CREATE TABLE user_tokens (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL COMMENT '用户ID',
    session_id VARCHAR(36) NOT NULL UNIQUE COMMENT '会话ID',
    access_token TEXT COMMENT '访问令牌',
    refresh_token TEXT NOT NULL COMMENT '刷新令牌',
    access_expires_at TIMESTAMP COMMENT '访问令牌过期时间',
    refresh_expires_at TIMESTAMP NOT NULL COMMENT '刷新令牌过期时间',
    is_revoked BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否已撤销',
    revoked_at TIMESTAMP COMMENT '撤销时间',
    device_info TEXT COMMENT '设备信息（JSON格式）',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent VARCHAR(500) COMMENT '用户代理',
    is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否激活',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted_at TIMESTAMP COMMENT '删除时间',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 用户权限表
CREATE TABLE user_permissions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INTEGER NOT NULL COMMENT '用户ID',
    permission_name VARCHAR(100) NOT NULL COMMENT '权限名称',
    is_granted BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否授予',
    granted_at TIMESTAMP COMMENT '授予时间',
    revoked_at TIMESTAMP COMMENT '撤销时间',
    expires_at TIMESTAMP COMMENT '过期时间',
    granted_by INT COMMENT '授予者ID',
    notes TEXT COMMENT '备注',
    is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否激活',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted_at TIMESTAMP COMMENT '删除时间',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY idx_user_permissions_unique (user_id, permission_name)
);

-- 用户活动日志表
CREATE TABLE user_activity_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INTEGER NOT NULL COMMENT '用户ID',
    action VARCHAR(100) NOT NULL COMMENT '操作动作',
    resource_type VARCHAR(50) COMMENT '资源类型',
    resource_id INT COMMENT '资源ID',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent VARCHAR(500) COMMENT '用户代理',
    details TEXT COMMENT '详细信息（JSON格式）',
    status VARCHAR(20) NOT NULL DEFAULT 'success' COMMENT '操作状态',
    error_message TEXT COMMENT '错误信息',
    timestamp TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '时间戳',
    is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否激活',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted_at TIMESTAMP COMMENT '删除时间',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- ============================================================================
-- 智能体相关表
-- ============================================================================

-- 智能体表
CREATE TABLE agents (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL COMMENT '创建者用户ID',
    owner_id INT NOT NULL COMMENT '拥有者用户ID',
    agent_id VARCHAR(36) NOT NULL UNIQUE COMMENT '智能体唯一标识',
    name VARCHAR(100) NOT NULL COMMENT '智能体名称',
    description TEXT COMMENT '智能体描述',
    agent_type VARCHAR(50) NOT NULL DEFAULT 'general' COMMENT '智能体类型',
    category VARCHAR(50) NOT NULL DEFAULT 'general' COMMENT '智能体分类',
    avatar_url VARCHAR(500) COMMENT '头像URL',
    config TEXT COMMENT '智能体配置（JSON格式）',
    system_prompt TEXT COMMENT '系统提示词',
    model_config TEXT COMMENT '模型配置（JSON格式）',
    capabilities TEXT COMMENT '能力列表（JSON格式）',
    status VARCHAR(20) NOT NULL DEFAULT 'active' COMMENT '智能体状态',
    is_public BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否公开',
    is_template BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否为模板',
    version VARCHAR(20) NOT NULL DEFAULT '1.0.0' COMMENT '版本号',
    usage_count INTEGER NOT NULL DEFAULT 0 COMMENT '使用次数',
    success_rate DECIMAL(5,2) NOT NULL DEFAULT 0.00 COMMENT '成功率',
    avg_response_time DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '平均响应时间（毫秒）',
    last_used_at TIMESTAMP COMMENT '最后使用时间',
    is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否激活',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted_at TIMESTAMP COMMENT '删除时间',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (owner_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 智能体能力表
CREATE TABLE agent_capabilities (
    id SERIAL PRIMARY KEY,
    agent_id INTEGER NOT NULL COMMENT '智能体ID',
    capability_name VARCHAR(100) NOT NULL COMMENT '能力名称',
    capability_type VARCHAR(50) NOT NULL COMMENT '能力类型',
    description TEXT COMMENT '能力描述',
    config TEXT COMMENT '能力配置（JSON格式）',
    is_enabled BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否启用',
    priority INTEGER NOT NULL DEFAULT 0 COMMENT '优先级',
    is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否激活',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted_at TIMESTAMP COMMENT '删除时间',
    FOREIGN KEY (agent_id) REFERENCES agents(id) ON DELETE CASCADE
);

-- 智能体协作表
CREATE TABLE agent_collaborations (
    id SERIAL PRIMARY KEY,
    primary_agent_id INTEGER NOT NULL COMMENT '主智能体ID',
    secondary_agent_id INTEGER NOT NULL COMMENT '协作智能体ID',
    collaboration_type VARCHAR(50) NOT NULL COMMENT '协作类型',
    config TEXT COMMENT '协作配置（JSON格式）',
    is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否激活',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted_at TIMESTAMP COMMENT '删除时间',
    FOREIGN KEY (primary_agent_id) REFERENCES agents(id) ON DELETE CASCADE,
    FOREIGN KEY (secondary_agent_id) REFERENCES agents(id) ON DELETE CASCADE
);

-- 智能体执行记录表
CREATE TABLE agent_executions (
    id SERIAL PRIMARY KEY,
    agent_id INTEGER NOT NULL COMMENT '智能体ID',
    user_id INTEGER NOT NULL COMMENT '用户ID',
    session_id INTEGER COMMENT '会话ID',
    execution_id VARCHAR(36) NOT NULL UNIQUE COMMENT '执行唯一标识',
    task_type VARCHAR(50) NOT NULL COMMENT '任务类型',
    input_data TEXT COMMENT '输入数据（JSON格式）',
    output_data TEXT COMMENT '输出数据（JSON格式）',
    status VARCHAR(20) NOT NULL DEFAULT 'running' COMMENT '执行状态',
    error_message TEXT COMMENT '错误信息',
    start_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '开始时间',
    end_time TIMESTAMP COMMENT '结束时间',
    duration DECIMAL(10,3) COMMENT '执行时长（秒）',
    tokens_used INTEGER NOT NULL DEFAULT 0 COMMENT '使用的token数',
    cost DECIMAL(10,4) NOT NULL DEFAULT 0.0000 COMMENT '执行费用',
    is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否激活',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted_at TIMESTAMP COMMENT '删除时间',
    FOREIGN KEY (agent_id) REFERENCES agents(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (session_id) REFERENCES sessions(id) ON DELETE SET NULL
);

-- ============================================================================
-- 会话和消息相关表
-- ============================================================================

-- 会话表
CREATE TABLE sessions (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL COMMENT '用户ID',
    owner_id INTEGER NOT NULL COMMENT '拥有者用户ID',
    session_id VARCHAR(36) NOT NULL UNIQUE COMMENT '会话唯一标识',
    title VARCHAR(200) COMMENT '会话标题',
    session_type VARCHAR(50) NOT NULL DEFAULT 'chat' COMMENT '会话类型',
    status VARCHAR(20) NOT NULL DEFAULT 'active' COMMENT '会话状态',
    config TEXT COMMENT '会话配置（JSON格式）',
    context TEXT COMMENT '会话上下文（JSON格式）',
    metadata TEXT COMMENT '元数据（JSON格式）',
    message_count INTEGER NOT NULL DEFAULT 0 COMMENT '消息数量',
    last_message_at TIMESTAMP COMMENT '最后消息时间',
    expires_at TIMESTAMP COMMENT '过期时间',
    is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否激活',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted_at TIMESTAMP COMMENT '删除时间',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (owner_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 消息表
CREATE TABLE messages (
    id SERIAL PRIMARY KEY,
    session_id INTEGER NOT NULL COMMENT '会话ID',
    user_id INTEGER NOT NULL COMMENT '用户ID',
    owner_id INTEGER NOT NULL COMMENT '拥有者用户ID',
    message_id VARCHAR(36) NOT NULL UNIQUE COMMENT '消息唯一标识',
    parent_message_id INTEGER COMMENT '父消息ID',
    agent_id INTEGER COMMENT '智能体ID',
    role VARCHAR(20) NOT NULL COMMENT '角色',
    message_type VARCHAR(50) NOT NULL DEFAULT 'text' COMMENT '消息类型',
    content TEXT COMMENT '消息内容',
    content_type VARCHAR(50) NOT NULL DEFAULT 'text' COMMENT '内容类型',
    metadata TEXT COMMENT '元数据（JSON格式）',
    attachments TEXT COMMENT '附件信息（JSON格式）',
    status VARCHAR(20) NOT NULL DEFAULT 'sent' COMMENT '消息状态',
    is_edited BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否已编辑',
    edited_at TIMESTAMP COMMENT '编辑时间',
    tokens_count INTEGER NOT NULL DEFAULT 0 COMMENT 'token数量',
    processing_time DECIMAL(10,3) COMMENT '处理时间（秒）',
    is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否激活',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted_at TIMESTAMP COMMENT '删除时间',
    FOREIGN KEY (session_id) REFERENCES sessions(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (owner_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (parent_message_id) REFERENCES messages(id) ON DELETE SET NULL,
    FOREIGN KEY (agent_id) REFERENCES agents(id) ON DELETE SET NULL
);

-- 消息附件表
CREATE TABLE message_attachments (
    id SERIAL PRIMARY KEY,
    message_id INTEGER NOT NULL COMMENT '消息ID',
    attachment_id VARCHAR(36) NOT NULL UNIQUE COMMENT '附件唯一标识',
    file_name VARCHAR(255) NOT NULL COMMENT '文件名',
    file_type VARCHAR(100) NOT NULL COMMENT '文件类型',
    file_size INTEGER NOT NULL COMMENT '文件大小（字节）',
    file_path VARCHAR(500) COMMENT '文件路径',
    file_url VARCHAR(500) COMMENT '文件URL',
    mime_type VARCHAR(100) COMMENT 'MIME类型',
    metadata TEXT COMMENT '元数据（JSON格式）',
    is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否激活',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted_at TIMESTAMP COMMENT '删除时间',
    FOREIGN KEY (message_id) REFERENCES messages(id) ON DELETE CASCADE
);

-- 消息反应表
CREATE TABLE message_reactions (
    id SERIAL PRIMARY KEY,
    message_id INTEGER NOT NULL COMMENT '消息ID',
    user_id INTEGER NOT NULL COMMENT '用户ID',
    reaction_type VARCHAR(50) NOT NULL COMMENT '反应类型',
    emoji VARCHAR(10) COMMENT '表情符号',
    is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否激活',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted_at TIMESTAMP COMMENT '删除时间',
    FOREIGN KEY (message_id) REFERENCES messages(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY idx_message_reactions_unique (message_id, user_id, reaction_type)
);

-- ============================================================================
-- 任务管理相关表
-- ============================================================================

-- 任务表
CREATE TABLE tasks (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL COMMENT '创建者用户ID',
    owner_id INTEGER NOT NULL COMMENT '拥有者用户ID',
    agent_id INTEGER NOT NULL COMMENT '执行智能体ID',
    task_id VARCHAR(36) NOT NULL UNIQUE COMMENT '任务唯一标识',
    title VARCHAR(200) NOT NULL COMMENT '任务标题',
    description TEXT COMMENT '任务描述',
    task_type VARCHAR(50) NOT NULL DEFAULT 'general' COMMENT '任务类型',
    priority VARCHAR(20) NOT NULL DEFAULT 'medium' COMMENT '任务优先级',
    status VARCHAR(20) NOT NULL DEFAULT 'pending' COMMENT '任务状态',
    progress DECIMAL(5,2) NOT NULL DEFAULT 0.00 COMMENT '任务进度（0-100）',
    config TEXT COMMENT '任务配置（JSON格式）',
    input_data TEXT COMMENT '输入数据（JSON格式）',
    output_data TEXT COMMENT '输出数据（JSON格式）',
    execution_log TEXT COMMENT '执行日志（JSON格式）',
    error_message TEXT COMMENT '错误信息',
    scheduled_at TIMESTAMP COMMENT '计划执行时间',
    started_at TIMESTAMP COMMENT '开始执行时间',
    completed_at TIMESTAMP COMMENT '完成时间',
    execution_time DECIMAL(10,3) COMMENT '执行时间（秒）',
    tokens_used INTEGER NOT NULL DEFAULT 0 COMMENT '使用的token数',
    cost DECIMAL(10,4) NOT NULL DEFAULT 0.0000 COMMENT '执行费用',
    parent_task_id INTEGER COMMENT '父任务ID',
    is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否激活',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted_at TIMESTAMP COMMENT '删除时间',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (owner_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (agent_id) REFERENCES agents(id) ON DELETE CASCADE,
    FOREIGN KEY (parent_task_id) REFERENCES tasks(id) ON DELETE SET NULL
);

-- 任务执行记录表
CREATE TABLE task_executions (
    id SERIAL PRIMARY KEY,
    task_id INTEGER NOT NULL COMMENT '任务ID',
    user_id INTEGER NOT NULL COMMENT '创建者用户ID',
    owner_id INTEGER NOT NULL COMMENT '拥有者用户ID',
    execution_id VARCHAR(36) NOT NULL UNIQUE COMMENT '执行唯一标识',
    status VARCHAR(20) NOT NULL DEFAULT 'running' COMMENT '执行状态',
    result TEXT COMMENT '执行结果（JSON格式）',
    error_details TEXT COMMENT '错误详情（JSON格式）',
    start_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '开始时间',
    end_time TIMESTAMP COMMENT '结束时间',
    duration DECIMAL(10,3) COMMENT '执行时长（秒）',
    memory_usage DECIMAL(10,2) COMMENT '内存使用（MB）',
    cpu_usage DECIMAL(5,2) COMMENT 'CPU使用率（%）',
    tokens_consumed INTEGER NOT NULL DEFAULT 0 COMMENT '消耗的token数',
    api_calls INTEGER NOT NULL DEFAULT 0 COMMENT 'API调用次数',
    environment TEXT COMMENT '执行环境信息（JSON格式）',
    is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否激活',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted_at TIMESTAMP COMMENT '删除时间',
    FOREIGN KEY (task_id) REFERENCES tasks(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (owner_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 任务模板表
CREATE TABLE task_templates (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL COMMENT '模板名称',
    description TEXT COMMENT '模板描述',
    category VARCHAR(50) NOT NULL DEFAULT 'general' COMMENT '模板分类',
    template_config TEXT NOT NULL COMMENT '模板配置（JSON格式）',
    input_schema TEXT COMMENT '输入数据结构定义（JSON Schema）',
    output_schema TEXT COMMENT '输出数据结构定义（JSON Schema）',
    is_public BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否公开',
    is_system BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否为系统模板',
    usage_count INTEGER NOT NULL DEFAULT 0 COMMENT '使用次数',
    success_rate DECIMAL(5,2) NOT NULL DEFAULT 0.00 COMMENT '成功率',
    created_by INTEGER COMMENT '创建者ID',
    is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否激活',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted_at TIMESTAMP COMMENT '删除时间'
);

-- 任务步骤表
CREATE TABLE task_steps (
    id SERIAL PRIMARY KEY,
    task_id INTEGER NOT NULL COMMENT '任务ID',
    user_id INTEGER NOT NULL COMMENT '用户ID',
    owner_id INTEGER NOT NULL COMMENT '拥有者用户ID',
    execution_id INTEGER COMMENT '执行ID',
    step_id VARCHAR(36) NOT NULL UNIQUE COMMENT '步骤唯一标识',
    step_name VARCHAR(200) NOT NULL COMMENT '步骤名称',
    description TEXT COMMENT '步骤描述',
    step_order INTEGER NOT NULL COMMENT '步骤顺序',
    parent_step_id INTEGER COMMENT '父步骤ID',
    level INTEGER NOT NULL DEFAULT 0 COMMENT '步骤层级',
    step_type VARCHAR(50) NOT NULL COMMENT '步骤类型',
    status VARCHAR(20) NOT NULL DEFAULT 'pending' COMMENT '步骤状态',
    agent_id INTEGER COMMENT '执行智能体ID',
    tool_name VARCHAR(100) COMMENT '使用的工具名称',
    input_data TEXT COMMENT '输入数据（JSON格式）',
    output_data TEXT COMMENT '输出数据（JSON格式）',
    config TEXT COMMENT '步骤配置（JSON格式）',
    start_time TIMESTAMP COMMENT '开始时间',
    end_time TIMESTAMP COMMENT '结束时间',
    duration DECIMAL(10,3) COMMENT '执行时长（秒）',
    result TEXT COMMENT '执行结果（JSON格式）',
    error_message TEXT COMMENT '错误信息',
    error_details TEXT COMMENT '错误详情（JSON格式）',
    retry_count INTEGER NOT NULL DEFAULT 0 COMMENT '重试次数',
    max_retries INTEGER NOT NULL DEFAULT 3 COMMENT '最大重试次数',
    dependencies TEXT COMMENT '依赖步骤（JSON格式）',
    metadata TEXT COMMENT '步骤元数据（JSON格式）',
    is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否激活',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted_at TIMESTAMP COMMENT '删除时间',
    FOREIGN KEY (task_id) REFERENCES tasks(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (owner_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (execution_id) REFERENCES task_executions(id) ON DELETE SET NULL,
    FOREIGN KEY (agent_id) REFERENCES agents(id) ON DELETE SET NULL,
    FOREIGN KEY (parent_step_id) REFERENCES task_steps(id) ON DELETE SET NULL
);

-- 任务结果表
CREATE TABLE task_results (
    id SERIAL PRIMARY KEY,
    task_id INTEGER NOT NULL COMMENT '任务ID',
    result_type VARCHAR(50) NOT NULL COMMENT '结果类型',
    result_data TEXT COMMENT '结果数据（JSON格式）',
    status VARCHAR(20) NOT NULL DEFAULT 'success' COMMENT '结果状态',
    execution_time DECIMAL(10,3) COMMENT '执行时间（秒）',
    tokens_used INTEGER NOT NULL DEFAULT 0 COMMENT '使用的token数',
    cost DECIMAL(10,4) NOT NULL DEFAULT 0.0000 COMMENT '执行费用',
    error_message TEXT COMMENT '错误信息',
    error_details TEXT COMMENT '错误详情（JSON格式）',
    is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否激活',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted_at TIMESTAMP COMMENT '删除时间',
    FOREIGN KEY (task_id) REFERENCES tasks(id) ON DELETE CASCADE
);

-- 任务依赖表
CREATE TABLE task_dependencies (
    id SERIAL PRIMARY KEY,
    task_id INTEGER NOT NULL COMMENT '任务ID',
    depends_on_task_id INTEGER NOT NULL COMMENT '依赖的任务ID',
    dependency_type VARCHAR(20) NOT NULL DEFAULT 'sequential' COMMENT '依赖类型',
    condition TEXT COMMENT '依赖条件（JSON格式）',
    is_satisfied BOOLEAN NOT NULL DEFAULT FALSE COMMENT '依赖是否已满足',
    satisfied_at TIMESTAMP COMMENT '依赖满足时间',
    is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否激活',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted_at TIMESTAMP COMMENT '删除时间',
    FOREIGN KEY (task_id) REFERENCES tasks(id) ON DELETE CASCADE,
    FOREIGN KEY (depends_on_task_id) REFERENCES tasks(id) ON DELETE CASCADE,
    UNIQUE KEY idx_task_dependencies_unique (task_id, depends_on_task_id)
);

-- ============================================================================
-- 工作流相关表
-- ============================================================================

-- 工作流表
CREATE TABLE workflows (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL COMMENT '创建者用户ID',
    owner_id INTEGER NOT NULL COMMENT '拥有者用户ID',
    workflow_id VARCHAR(36) NOT NULL UNIQUE COMMENT '工作流唯一标识',
    name VARCHAR(100) NOT NULL COMMENT '工作流名称',
    description TEXT COMMENT '工作流描述',
    category VARCHAR(50) NOT NULL DEFAULT 'general' COMMENT '工作流分类',
    workflow_type VARCHAR(50) NOT NULL DEFAULT 'sequential' COMMENT '工作流类型',
    status VARCHAR(20) NOT NULL DEFAULT 'draft' COMMENT '工作流状态',
    version VARCHAR(20) NOT NULL DEFAULT '1.0.0' COMMENT '版本号',
    config TEXT COMMENT '工作流配置（JSON格式）',
    input_schema TEXT COMMENT '输入数据结构定义（JSON Schema）',
    output_schema TEXT COMMENT '输出数据结构定义（JSON Schema）',
    trigger_config TEXT COMMENT '触发器配置（JSON格式）',
    is_public BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否公开',
    is_template BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否为模板',
    execution_count INTEGER NOT NULL DEFAULT 0 COMMENT '执行次数',
    success_count INTEGER NOT NULL DEFAULT 0 COMMENT '成功次数',
    avg_execution_time DECIMAL(10,3) NOT NULL DEFAULT 0.000 COMMENT '平均执行时长（秒）',
    last_executed_at TIMESTAMP COMMENT '最后执行时间',
    is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否激活',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted_at TIMESTAMP COMMENT '删除时间',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (owner_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 工作流步骤表
CREATE TABLE workflow_steps (
    id SERIAL PRIMARY KEY,
    workflow_id INTEGER NOT NULL COMMENT '工作流ID',
    user_id INTEGER NOT NULL COMMENT '用户ID',
    owner_id INTEGER NOT NULL COMMENT '拥有者用户ID',
    step_id VARCHAR(36) NOT NULL UNIQUE COMMENT '步骤唯一标识',
    name VARCHAR(100) NOT NULL COMMENT '步骤名称',
    description TEXT COMMENT '步骤描述',
    step_type VARCHAR(50) NOT NULL COMMENT '步骤类型',
    step_order INTEGER NOT NULL COMMENT '执行顺序',
    config TEXT COMMENT '步骤配置（JSON格式）',
    input_mapping TEXT COMMENT '输入映射（JSON格式）',
    output_mapping TEXT COMMENT '输出映射（JSON格式）',
    conditions TEXT COMMENT '执行条件（JSON格式）',
    dependencies TEXT COMMENT '依赖关系（JSON格式）',
    error_handling TEXT COMMENT '错误处理（JSON格式）',
    timeout_seconds INTEGER COMMENT '超时时间（秒）',
    retry_count INTEGER NOT NULL DEFAULT 0 COMMENT '重试次数',
    retry_delay INTEGER NOT NULL DEFAULT 0 COMMENT '重试延迟（秒）',
    is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否激活',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted_at TIMESTAMP COMMENT '删除时间',
    FOREIGN KEY (workflow_id) REFERENCES workflows(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (owner_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 工作流执行记录表
CREATE TABLE workflow_executions (
    id SERIAL PRIMARY KEY,
    workflow_id INTEGER NOT NULL COMMENT '工作流ID',
    execution_id VARCHAR(36) NOT NULL UNIQUE COMMENT '执行唯一标识',
    trigger_type VARCHAR(50) NOT NULL COMMENT '触发类型',
    triggered_by INTEGER COMMENT '触发者用户ID',
    status VARCHAR(20) NOT NULL DEFAULT 'running' COMMENT '执行状态',
    current_step VARCHAR(36) COMMENT '当前步骤ID',
    input_data TEXT COMMENT '输入数据（JSON格式）',
    output_data TEXT COMMENT '输出数据（JSON格式）',
    execution_log TEXT COMMENT '执行日志（JSON格式）',
    error_message TEXT COMMENT '错误信息',
    error_step VARCHAR(36) COMMENT '错误步骤ID',
    started_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '开始时间',
    completed_at TIMESTAMP COMMENT '完成时间',
    duration DECIMAL(10,3) COMMENT '执行时长（秒）',
    steps_completed INTEGER NOT NULL DEFAULT 0 COMMENT '已完成步骤数',
    total_steps INTEGER NOT NULL DEFAULT 0 COMMENT '总步骤数',
    tokens_used INTEGER NOT NULL DEFAULT 0 COMMENT '使用的token数',
    cost DECIMAL(10,4) NOT NULL DEFAULT 0.0000 COMMENT '执行费用',
    is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否激活',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted_at TIMESTAMP COMMENT '删除时间',
    FOREIGN KEY (workflow_id) REFERENCES workflows(id) ON DELETE CASCADE,
    FOREIGN KEY (triggered_by) REFERENCES users(id) ON DELETE SET NULL
);

-- 工作流步骤执行记录表
CREATE TABLE workflow_step_executions (
    id SERIAL PRIMARY KEY,
    workflow_execution_id INTEGER NOT NULL COMMENT '工作流执行ID',
    step_id VARCHAR(36) NOT NULL COMMENT '步骤ID',
    step_name VARCHAR(100) NOT NULL COMMENT '步骤名称',
    status VARCHAR(20) NOT NULL DEFAULT 'pending' COMMENT '执行状态',
    input_data TEXT COMMENT '输入数据（JSON格式）',
    output_data TEXT COMMENT '输出数据（JSON格式）',
    error_message TEXT COMMENT '错误信息',
    started_at TIMESTAMP COMMENT '开始时间',
    completed_at TIMESTAMP COMMENT '完成时间',
    duration DECIMAL(10,3) COMMENT '执行时长（秒）',
    retry_count INTEGER NOT NULL DEFAULT 0 COMMENT '重试次数',
    is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否激活',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted_at TIMESTAMP COMMENT '删除时间',
    FOREIGN KEY (workflow_execution_id) REFERENCES workflow_executions(id) ON DELETE CASCADE
);

-- ============================================================================
-- 工具相关表
-- ============================================================================

-- 工具表
CREATE TABLE tools (
    id SERIAL PRIMARY KEY,
    tool_id VARCHAR(36) NOT NULL UNIQUE COMMENT '工具唯一标识',
    name VARCHAR(100) NOT NULL COMMENT '工具名称',
    description TEXT COMMENT '工具描述',
    tool_type VARCHAR(50) NOT NULL COMMENT '工具类型',
    category VARCHAR(50) NOT NULL DEFAULT 'general' COMMENT '工具分类',
    schema_definition TEXT COMMENT '模式定义（JSON Schema）',
    implementation TEXT COMMENT '实现代码',
    config TEXT COMMENT '工具配置（JSON格式）',
    version VARCHAR(20) NOT NULL DEFAULT '1.0.0' COMMENT '版本号',
    status VARCHAR(20) NOT NULL DEFAULT 'active' COMMENT '工具状态',
    is_public BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否公开',
    requires_auth BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否需要认证',
    permission_level VARCHAR(20) NOT NULL DEFAULT 'user' COMMENT '权限级别',
    created_by INTEGER COMMENT '创建者ID',
    usage_count INTEGER NOT NULL DEFAULT 0 COMMENT '使用次数',
    success_count INTEGER NOT NULL DEFAULT 0 COMMENT '成功次数',
    avg_response_time DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '平均响应时间（毫秒）',
    last_used_at TIMESTAMP COMMENT '最后使用时间',
    is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否激活',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted_at TIMESTAMP COMMENT '删除时间',
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
);

-- 智能体工具关联表
CREATE TABLE agent_tools (
    id SERIAL PRIMARY KEY,
    agent_id INTEGER NOT NULL COMMENT '智能体ID',
    tool_id INTEGER NOT NULL COMMENT '工具ID',
    config TEXT COMMENT '配置（JSON格式）',
    is_enabled BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否启用',
    priority INTEGER NOT NULL DEFAULT 0 COMMENT '优先级',
    usage_count INTEGER NOT NULL DEFAULT 0 COMMENT '使用次数',
    is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否激活',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted_at TIMESTAMP COMMENT '删除时间',
    FOREIGN KEY (agent_id) REFERENCES agents(id) ON DELETE CASCADE,
    FOREIGN KEY (tool_id) REFERENCES tools(id) ON DELETE CASCADE,
    UNIQUE KEY idx_agent_tools_unique (agent_id, tool_id)
);

-- 工具执行记录表
CREATE TABLE tool_executions (
    id SERIAL PRIMARY KEY,
    tool_id INTEGER NOT NULL COMMENT '工具ID',
    agent_id INTEGER COMMENT '智能体ID',
    user_id INTEGER COMMENT '用户ID',
    session_id INTEGER COMMENT '会话ID',
    message_id INTEGER COMMENT '消息ID',
    execution_id VARCHAR(36) NOT NULL UNIQUE COMMENT '执行唯一标识',
    input_data TEXT COMMENT '输入数据（JSON格式）',
    output_data TEXT COMMENT '输出数据（JSON格式）',
    status VARCHAR(20) NOT NULL DEFAULT 'running' COMMENT '执行状态',
    error_message TEXT COMMENT '错误信息',
    error_type VARCHAR(50) COMMENT '错误类型',
    started_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '开始时间',
    completed_at TIMESTAMP COMMENT '完成时间',
    duration DECIMAL(10,3) COMMENT '执行时长（秒）',
    is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否激活',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted_at TIMESTAMP COMMENT '删除时间',
    FOREIGN KEY (tool_id) REFERENCES tools(id) ON DELETE CASCADE,
    FOREIGN KEY (agent_id) REFERENCES agents(id) ON DELETE SET NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (session_id) REFERENCES sessions(id) ON DELETE SET NULL,
    FOREIGN KEY (message_id) REFERENCES messages(id) ON DELETE SET NULL
);

-- 工具分类表
CREATE TABLE tool_categories (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE COMMENT '分类名称',
    description TEXT COMMENT '分类描述',
    parent_id INTEGER COMMENT '父分类ID',
    display_order INTEGER NOT NULL DEFAULT 0 COMMENT '显示顺序',
    icon VARCHAR(100) COMMENT '图标',
    is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否激活',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted_at TIMESTAMP COMMENT '删除时间',
    FOREIGN KEY (parent_id) REFERENCES tool_categories(id) ON DELETE SET NULL
);

-- ============================================================================
-- 配置相关表
-- ============================================================================

-- 系统配置表
CREATE TABLE system_configs (
    id SERIAL PRIMARY KEY,
    config_key VARCHAR(100) NOT NULL UNIQUE COMMENT '配置键',
    config_value TEXT COMMENT '配置值',
    value_type VARCHAR(20) NOT NULL DEFAULT 'string' COMMENT '值类型',
    category VARCHAR(50) NOT NULL DEFAULT 'general' COMMENT '配置分类',
    description TEXT COMMENT '配置描述',
    is_required BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否必需',
    is_sensitive BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否敏感',
    is_readonly BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否只读',
    default_value TEXT COMMENT '默认值',
    validation_rule TEXT COMMENT '验证规则（JSON格式）',
    updated_by INTEGER COMMENT '更新者用户ID',
    is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否激活',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted_at TIMESTAMP COMMENT '删除时间',
    FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL
);

-- 用户配置表
CREATE TABLE user_configs (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL COMMENT '用户ID',
    config_key VARCHAR(100) NOT NULL COMMENT '配置键',
    config_value TEXT COMMENT '配置值',
    value_type VARCHAR(20) NOT NULL DEFAULT 'string' COMMENT '值类型',
    is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否激活',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted_at TIMESTAMP COMMENT '删除时间',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY idx_user_configs_unique (user_id, config_key)
);

-- 智能体配置表
CREATE TABLE agent_configs (
    id SERIAL PRIMARY KEY,
    agent_id INTEGER NOT NULL COMMENT '智能体ID',
    user_id INTEGER NOT NULL COMMENT '用户ID',
    config_key VARCHAR(100) NOT NULL COMMENT '配置键',
    config_value TEXT COMMENT '配置值',
    value_type VARCHAR(20) NOT NULL DEFAULT 'string' COMMENT '值类型',
    is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否激活',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted_at TIMESTAMP COMMENT '删除时间',
    FOREIGN KEY (agent_id) REFERENCES agents(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY idx_agent_configs_unique (agent_id, config_key)
);

-- 配置模板表
CREATE TABLE config_templates (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL COMMENT '创建者用户ID',
    name VARCHAR(100) NOT NULL COMMENT '模板名称',
    description TEXT COMMENT '模板描述',
    template_type VARCHAR(50) NOT NULL COMMENT '模板类型',
    template_data TEXT NOT NULL COMMENT '模板数据（JSON格式）',
    is_default BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否为默认模板',
    is_public BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否公开',
    created_by INTEGER COMMENT '创建者用户ID',
    usage_count INTEGER NOT NULL DEFAULT 0 COMMENT '使用次数',
    is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否激活',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted_at TIMESTAMP COMMENT '删除时间',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
);

-- LLM配置表
CREATE TABLE llm_configs (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL COMMENT '用户ID',
    agent_id INTEGER COMMENT '智能体ID',
    config_name VARCHAR(100) NOT NULL COMMENT '配置名称',
    description TEXT COMMENT '配置描述',
    provider VARCHAR(50) NOT NULL COMMENT 'LLM提供商',
    model_name VARCHAR(100) NOT NULL COMMENT '模型名称',
    model_version VARCHAR(50) COMMENT '模型版本',
    api_key VARCHAR(500) COMMENT 'API密钥',
    api_base VARCHAR(200) COMMENT 'API基础URL',
    temperature DECIMAL(3,2) NOT NULL DEFAULT 0.70 COMMENT '温度参数',
    max_tokens INTEGER COMMENT '最大令牌数',
    top_p DECIMAL(3,2) COMMENT 'Top-p参数',
    frequency_penalty DECIMAL(3,2) COMMENT '频率惩罚',
    presence_penalty DECIMAL(3,2) COMMENT '存在惩罚',
    is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否激活',
    is_default BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否为默认配置',
    extra_params TEXT COMMENT '扩展参数（JSON格式）',
    usage_count INTEGER NOT NULL DEFAULT 0 COMMENT '使用次数',
    last_used_at TIMESTAMP COMMENT '最后使用时间',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted_at TIMESTAMP COMMENT '删除时间',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (agent_id) REFERENCES agents(id) ON DELETE SET NULL
);

-- 工具配置表
CREATE TABLE tool_configs (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL COMMENT '用户ID',
    agent_id INTEGER COMMENT '智能体ID',
    config_name VARCHAR(100) NOT NULL COMMENT '配置名称',
    description TEXT COMMENT '配置描述',
    tool_name VARCHAR(100) NOT NULL COMMENT '工具名称',
    tool_type VARCHAR(50) NOT NULL COMMENT '工具类型',
    tool_version VARCHAR(50) COMMENT '工具版本',
    tool_config TEXT COMMENT '工具配置（JSON格式）',
    permissions TEXT COMMENT '权限配置（JSON格式）',
    is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否激活',
    is_default BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否为默认配置',
    usage_count INTEGER NOT NULL DEFAULT 0 COMMENT '使用次数',
    last_used_at TIMESTAMP COMMENT '最后使用时间',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted_at TIMESTAMP COMMENT '删除时间',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (agent_id) REFERENCES agents(id) ON DELETE SET NULL
);

-- ============================================================================
-- 记忆相关表
-- ============================================================================

-- 记忆表
CREATE TABLE memories (
    id SERIAL PRIMARY KEY,
    memory_id VARCHAR(36) NOT NULL UNIQUE COMMENT '记忆唯一标识',
    user_id INTEGER NOT NULL COMMENT '用户ID',
    owner_id INTEGER NOT NULL COMMENT '拥有者用户ID',
    memory_name VARCHAR(100) NOT NULL COMMENT '记忆名称',
    memory_type VARCHAR(50) NOT NULL DEFAULT 'general' COMMENT '记忆类型',
    status VARCHAR(20) NOT NULL DEFAULT 'active' COMMENT '记忆状态',
    entry_count INTEGER NOT NULL DEFAULT 0 COMMENT '条目数量',
    last_accessed_at TIMESTAMP COMMENT '最后访问时间',
    metadata TEXT COMMENT '元数据（JSON格式）',
    is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否激活',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted_at TIMESTAMP COMMENT '删除时间',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (owner_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 记忆条目表
CREATE TABLE memory_entries (
    id SERIAL PRIMARY KEY,
    memory_id INTEGER NOT NULL COMMENT '记忆ID',
    entry_key VARCHAR(200) NOT NULL COMMENT '条目键',
    entry_value TEXT COMMENT '条目值',
    entry_type VARCHAR(50) NOT NULL DEFAULT 'text' COMMENT '条目类型',
    priority INTEGER NOT NULL DEFAULT 0 COMMENT '优先级',
    expires_at TIMESTAMP COMMENT '过期时间',
    last_accessed_at TIMESTAMP COMMENT '最后访问时间',
    metadata TEXT COMMENT '元数据（JSON格式）',
    is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否激活',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted_at TIMESTAMP COMMENT '删除时间',
    FOREIGN KEY (memory_id) REFERENCES memories(id) ON DELETE CASCADE,
    UNIQUE KEY idx_memory_entries_unique (memory_id, entry_key)
);

-- ============================================================================
-- 工件相关表
-- ============================================================================

-- 工件表
CREATE TABLE artifacts (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL COMMENT '创建者用户ID',
    owner_id INTEGER NOT NULL COMMENT '拥有者用户ID',
    artifact_id VARCHAR(36) NOT NULL UNIQUE COMMENT '工件唯一标识',
    name VARCHAR(200) NOT NULL COMMENT '工件名称',
    description TEXT COMMENT '工件描述',
    artifact_type VARCHAR(50) NOT NULL COMMENT '工件类型',
    category VARCHAR(50) NOT NULL DEFAULT 'general' COMMENT '工件分类',
    file_path VARCHAR(500) COMMENT '文件路径',
    file_size INTEGER COMMENT '文件大小（字节）',
    mime_type VARCHAR(100) COMMENT 'MIME类型',
    content TEXT COMMENT '内容',
    metadata TEXT COMMENT '元数据（JSON格式）',
    tags TEXT COMMENT '标签（JSON格式）',
    version VARCHAR(20) NOT NULL DEFAULT '1.0.0' COMMENT '版本号',
    status VARCHAR(20) NOT NULL DEFAULT 'active' COMMENT '工件状态',
    is_public BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否公开',
    download_count INTEGER NOT NULL DEFAULT 0 COMMENT '下载次数',
    view_count INTEGER NOT NULL DEFAULT 0 COMMENT '查看次数',
    last_accessed_at TIMESTAMP COMMENT '最后访问时间',
    is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否激活',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted_at TIMESTAMP COMMENT '删除时间',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (owner_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 工件版本表
CREATE TABLE artifact_versions (
    id SERIAL PRIMARY KEY,
    artifact_id INTEGER NOT NULL COMMENT '工件ID',
    version_number VARCHAR(20) NOT NULL COMMENT '版本号',
    description TEXT COMMENT '版本描述',
    file_path VARCHAR(500) COMMENT '文件路径',
    file_size INTEGER COMMENT '文件大小（字节）',
    content TEXT COMMENT '内容',
    changes TEXT COMMENT '变更说明',
    created_by INTEGER NOT NULL COMMENT '创建者用户ID',
    is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否激活',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted_at TIMESTAMP COMMENT '删除时间',
    FOREIGN KEY (artifact_id) REFERENCES artifacts(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY idx_artifact_versions_unique (artifact_id, version_number)
);

-- 工件共享表
CREATE TABLE artifact_shares (
    id SERIAL PRIMARY KEY,
    artifact_id INTEGER NOT NULL COMMENT '工件ID',
    shared_with_user_id INTEGER NOT NULL COMMENT '共享给用户ID',
    permission_level VARCHAR(20) NOT NULL DEFAULT 'read' COMMENT '权限级别',
    shared_by INTEGER NOT NULL COMMENT '共享者用户ID',
    expires_at TIMESTAMP COMMENT '过期时间',
    is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否激活',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted_at TIMESTAMP COMMENT '删除时间',
    FOREIGN KEY (artifact_id) REFERENCES artifacts(id) ON DELETE CASCADE,
    FOREIGN KEY (shared_with_user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (shared_by) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY idx_artifact_shares_unique (artifact_id, shared_with_user_id)
);

-- ============================================================================
-- 日志和监控相关表
-- ============================================================================

-- 系统日志表
CREATE TABLE system_logs (
    id SERIAL PRIMARY KEY,
    log_level VARCHAR(20) NOT NULL COMMENT '日志级别',
    logger_name VARCHAR(100) NOT NULL COMMENT '日志器名称',
    message TEXT NOT NULL COMMENT '日志消息',
    category VARCHAR(50) NOT NULL DEFAULT 'general' COMMENT '日志分类',
    user_id INTEGER COMMENT '用户ID',
    session_id INTEGER COMMENT '会话ID',
    request_id VARCHAR(36) COMMENT '请求ID',
    module VARCHAR(100) COMMENT '模块名称',
    function VARCHAR(100) COMMENT '函数名称',
    line_number INTEGER COMMENT '行号',
    exception_type VARCHAR(100) COMMENT '异常类型',
    exception_message TEXT COMMENT '异常消息',
    stack_trace TEXT COMMENT '堆栈跟踪',
    extra_data TEXT COMMENT '额外数据（JSON格式）',
    timestamp TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '时间戳',
    is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否激活',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted_at TIMESTAMP COMMENT '删除时间',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (session_id) REFERENCES sessions(id) ON DELETE SET NULL
);

-- 性能指标表
CREATE TABLE performance_metrics (
    id SERIAL PRIMARY KEY,
    metric_name VARCHAR(100) NOT NULL COMMENT '指标名称',
    metric_type VARCHAR(50) NOT NULL COMMENT '指标类型',
    metric_value DECIMAL(15,6) NOT NULL COMMENT '指标值',
    unit VARCHAR(20) COMMENT '单位',
    category VARCHAR(50) NOT NULL DEFAULT 'system' COMMENT '指标分类',
    user_id INTEGER COMMENT '用户ID',
    agent_id INTEGER COMMENT '智能体ID',
    session_id INTEGER COMMENT '会话ID',
    task_id INTEGER COMMENT '任务ID',
    tags TEXT COMMENT '标签（JSON格式）',
    timestamp TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '时间戳',
    is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否激活',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted_at TIMESTAMP COMMENT '删除时间',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (agent_id) REFERENCES agents(id) ON DELETE SET NULL,
    FOREIGN KEY (session_id) REFERENCES sessions(id) ON DELETE SET NULL
);

-- 错误日志表
CREATE TABLE error_logs (
    id SERIAL PRIMARY KEY,
    error_id VARCHAR(36) NOT NULL UNIQUE COMMENT '错误唯一标识',
    error_type VARCHAR(100) NOT NULL COMMENT '错误类型',
    error_code VARCHAR(50) COMMENT '错误代码',
    error_message TEXT NOT NULL COMMENT '错误消息',
    severity VARCHAR(20) NOT NULL DEFAULT 'error' COMMENT '严重程度',
    category VARCHAR(50) NOT NULL DEFAULT 'general' COMMENT '错误分类',
    user_id INTEGER COMMENT '用户ID',
    agent_id INTEGER COMMENT '智能体ID',
    session_id INTEGER COMMENT '会话ID',
    task_id INTEGER COMMENT '任务ID',
    request_id VARCHAR(36) COMMENT '请求ID',
    module VARCHAR(100) COMMENT '模块名称',
    function VARCHAR(100) COMMENT '函数名称',
    line_number INTEGER COMMENT '行号',
    stack_trace TEXT COMMENT '堆栈跟踪',
    context_data TEXT COMMENT '上下文数据（JSON格式）',
    resolution_status VARCHAR(20) NOT NULL DEFAULT 'open' COMMENT '解决状态',
    resolved_at TIMESTAMP COMMENT '解决时间',
    resolved_by INTEGER COMMENT '解决者用户ID',
    resolution_notes TEXT COMMENT '解决说明',
    occurrence_count INTEGER NOT NULL DEFAULT 1 COMMENT '发生次数',
    first_occurred_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '首次发生时间',
    last_occurred_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最后发生时间',
    is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否激活',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted_at TIMESTAMP COMMENT '删除时间',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (agent_id) REFERENCES agents(id) ON DELETE SET NULL,
    FOREIGN KEY (session_id) REFERENCES sessions(id) ON DELETE SET NULL,
    FOREIGN KEY (resolved_by) REFERENCES users(id) ON DELETE SET NULL
);

-- 审计日志表
CREATE TABLE audit_logs (
    id SERIAL PRIMARY KEY,
    audit_id VARCHAR(36) NOT NULL UNIQUE COMMENT '审计唯一标识',
    user_id INTEGER COMMENT '用户ID',
    action VARCHAR(100) NOT NULL COMMENT '操作动作',
    resource_type VARCHAR(50) NOT NULL COMMENT '资源类型',
    resource_id VARCHAR(36) COMMENT '资源ID',
    old_values TEXT COMMENT '旧值（JSON格式）',
    new_values TEXT COMMENT '新值（JSON格式）',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent VARCHAR(500) COMMENT '用户代理',
    request_id VARCHAR(36) COMMENT '请求ID',
    session_id INTEGER COMMENT '会话ID',
    status VARCHAR(20) NOT NULL DEFAULT 'success' COMMENT '操作状态',
    error_message TEXT COMMENT '错误信息',
    timestamp TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '时间戳',
    is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否激活',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted_at TIMESTAMP COMMENT '删除时间',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (session_id) REFERENCES sessions(id) ON DELETE SET NULL
);

-- ============================================================================
-- 索引创建
-- ============================================================================

-- 用户表索引
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_users_is_active ON users(is_active);
CREATE INDEX idx_users_created_at ON users(created_at);
CREATE INDEX idx_users_last_login_at ON users(last_login_at);
CREATE INDEX idx_users_deleted_at ON users(deleted_at);

-- 用户令牌表索引
CREATE INDEX idx_user_tokens_user_id ON user_tokens(user_id);
CREATE INDEX idx_user_tokens_session_id ON user_tokens(session_id);
CREATE INDEX idx_user_tokens_refresh_expires_at ON user_tokens(refresh_expires_at);
CREATE INDEX idx_user_tokens_is_revoked ON user_tokens(is_revoked);
CREATE INDEX idx_user_tokens_created_at ON user_tokens(created_at);
CREATE INDEX idx_user_tokens_deleted_at ON user_tokens(deleted_at);

-- 用户权限表索引
CREATE INDEX idx_user_permissions_user_id ON user_permissions(user_id);
CREATE INDEX idx_user_permissions_permission_name ON user_permissions(permission_name);
CREATE INDEX idx_user_permissions_is_granted ON user_permissions(is_granted);
CREATE INDEX idx_user_permissions_expires_at ON user_permissions(expires_at);
CREATE INDEX idx_user_permissions_deleted_at ON user_permissions(deleted_at);

-- 用户活动日志表索引
CREATE INDEX idx_user_activity_logs_user_id ON user_activity_logs(user_id);
CREATE INDEX idx_user_activity_logs_action ON user_activity_logs(action);
CREATE INDEX idx_user_activity_logs_resource_type ON user_activity_logs(resource_type);
CREATE INDEX idx_user_activity_logs_timestamp ON user_activity_logs(timestamp);
CREATE INDEX idx_user_activity_logs_status ON user_activity_logs(status);
CREATE INDEX idx_user_activity_logs_deleted_at ON user_activity_logs(deleted_at);

-- 智能体表索引
CREATE INDEX idx_agents_user_id ON agents(user_id);
CREATE INDEX idx_agents_owner_id ON agents(owner_id);
CREATE INDEX idx_agents_agent_id ON agents(agent_id);
CREATE INDEX idx_agents_agent_type ON agents(agent_type);
CREATE INDEX idx_agents_category ON agents(category);
CREATE INDEX idx_agents_status ON agents(status);
CREATE INDEX idx_agents_is_public ON agents(is_public);
CREATE INDEX idx_agents_is_template ON agents(is_template);
CREATE INDEX idx_agents_created_at ON agents(created_at);
CREATE INDEX idx_agents_last_used_at ON agents(last_used_at);
CREATE INDEX idx_agents_deleted_at ON agents(deleted_at);

-- 智能体能力表索引
CREATE INDEX idx_agent_capabilities_agent_id ON agent_capabilities(agent_id);
CREATE INDEX idx_agent_capabilities_capability_name ON agent_capabilities(capability_name);
CREATE INDEX idx_agent_capabilities_capability_type ON agent_capabilities(capability_type);
CREATE INDEX idx_agent_capabilities_is_enabled ON agent_capabilities(is_enabled);
CREATE INDEX idx_agent_capabilities_priority ON agent_capabilities(priority);
CREATE INDEX idx_agent_capabilities_deleted_at ON agent_capabilities(deleted_at);

-- 智能体协作表索引
CREATE INDEX idx_agent_collaborations_primary_agent_id ON agent_collaborations(primary_agent_id);
CREATE INDEX idx_agent_collaborations_secondary_agent_id ON agent_collaborations(secondary_agent_id);
CREATE INDEX idx_agent_collaborations_collaboration_type ON agent_collaborations(collaboration_type);
CREATE INDEX idx_agent_collaborations_deleted_at ON agent_collaborations(deleted_at);

-- 智能体执行记录表索引
CREATE INDEX idx_agent_executions_agent_id ON agent_executions(agent_id);
CREATE INDEX idx_agent_executions_user_id ON agent_executions(user_id);
CREATE INDEX idx_agent_executions_session_id ON agent_executions(session_id);
CREATE INDEX idx_agent_executions_execution_id ON agent_executions(execution_id);
CREATE INDEX idx_agent_executions_task_type ON agent_executions(task_type);
CREATE INDEX idx_agent_executions_status ON agent_executions(status);
CREATE INDEX idx_agent_executions_start_time ON agent_executions(start_time);
CREATE INDEX idx_agent_executions_deleted_at ON agent_executions(deleted_at);

-- 会话表索引
CREATE INDEX idx_sessions_user_id ON sessions(user_id);
CREATE INDEX idx_sessions_owner_id ON sessions(owner_id);
CREATE INDEX idx_sessions_session_id ON sessions(session_id);
CREATE INDEX idx_sessions_session_type ON sessions(session_type);
CREATE INDEX idx_sessions_status ON sessions(status);
CREATE INDEX idx_sessions_created_at ON sessions(created_at);
CREATE INDEX idx_sessions_last_message_at ON sessions(last_message_at);
CREATE INDEX idx_sessions_expires_at ON sessions(expires_at);
CREATE INDEX idx_sessions_deleted_at ON sessions(deleted_at);

-- 消息表索引
CREATE INDEX idx_messages_session_id ON messages(session_id);
CREATE INDEX idx_messages_user_id ON messages(user_id);
CREATE INDEX idx_messages_owner_id ON messages(owner_id);
CREATE INDEX idx_messages_message_id ON messages(message_id);
CREATE INDEX idx_messages_parent_message_id ON messages(parent_message_id);
CREATE INDEX idx_messages_agent_id ON messages(agent_id);
CREATE INDEX idx_messages_role ON messages(role);
CREATE INDEX idx_messages_message_type ON messages(message_type);
CREATE INDEX idx_messages_status ON messages(status);
CREATE INDEX idx_messages_created_at ON messages(created_at);
CREATE INDEX idx_messages_deleted_at ON messages(deleted_at);

-- 消息附件表索引
CREATE INDEX idx_message_attachments_message_id ON message_attachments(message_id);
CREATE INDEX idx_message_attachments_attachment_id ON message_attachments(attachment_id);
CREATE INDEX idx_message_attachments_file_type ON message_attachments(file_type);
CREATE INDEX idx_message_attachments_created_at ON message_attachments(created_at);
CREATE INDEX idx_message_attachments_deleted_at ON message_attachments(deleted_at);

-- 消息反应表索引
CREATE INDEX idx_message_reactions_message_id ON message_reactions(message_id);
CREATE INDEX idx_message_reactions_user_id ON message_reactions(user_id);
CREATE INDEX idx_message_reactions_reaction_type ON message_reactions(reaction_type);
CREATE INDEX idx_message_reactions_created_at ON message_reactions(created_at);
CREATE INDEX idx_message_reactions_deleted_at ON message_reactions(deleted_at);

-- 任务表索引
CREATE INDEX idx_tasks_user_id ON tasks(user_id);
CREATE INDEX idx_tasks_owner_id ON tasks(owner_id);
CREATE INDEX idx_tasks_agent_id ON tasks(agent_id);
CREATE INDEX idx_tasks_task_id ON tasks(task_id);
CREATE INDEX idx_tasks_task_type ON tasks(task_type);
CREATE INDEX idx_tasks_priority ON tasks(priority);
CREATE INDEX idx_tasks_status ON tasks(status);
CREATE INDEX idx_tasks_parent_task_id ON tasks(parent_task_id);
CREATE INDEX idx_tasks_scheduled_at ON tasks(scheduled_at);
CREATE INDEX idx_tasks_started_at ON tasks(started_at);
CREATE INDEX idx_tasks_completed_at ON tasks(completed_at);
CREATE INDEX idx_tasks_created_at ON tasks(created_at);
CREATE INDEX idx_tasks_deleted_at ON tasks(deleted_at);

-- 任务执行记录表索引
CREATE INDEX idx_task_executions_task_id ON task_executions(task_id);
CREATE INDEX idx_task_executions_user_id ON task_executions(user_id);
CREATE INDEX idx_task_executions_owner_id ON task_executions(owner_id);
CREATE INDEX idx_task_executions_execution_id ON task_executions(execution_id);
CREATE INDEX idx_task_executions_status ON task_executions(status);
CREATE INDEX idx_task_executions_start_time ON task_executions(start_time);
CREATE INDEX idx_task_executions_end_time ON task_executions(end_time);
CREATE INDEX idx_task_executions_deleted_at ON task_executions(deleted_at);

-- 任务模板表索引
CREATE INDEX idx_task_templates_name ON task_templates(name);
CREATE INDEX idx_task_templates_category ON task_templates(category);
CREATE INDEX idx_task_templates_is_public ON task_templates(is_public);
CREATE INDEX idx_task_templates_is_system ON task_templates(is_system);
CREATE INDEX idx_task_templates_created_by ON task_templates(created_by);
CREATE INDEX idx_task_templates_created_at ON task_templates(created_at);
CREATE INDEX idx_task_templates_deleted_at ON task_templates(deleted_at);

-- 任务步骤表索引
CREATE INDEX idx_task_steps_task_id ON task_steps(task_id);
CREATE INDEX idx_task_steps_user_id ON task_steps(user_id);
CREATE INDEX idx_task_steps_owner_id ON task_steps(owner_id);
CREATE INDEX idx_task_steps_execution_id ON task_steps(execution_id);
CREATE INDEX idx_task_steps_step_id ON task_steps(step_id);
CREATE INDEX idx_task_steps_parent_step_id ON task_steps(parent_step_id);
CREATE INDEX idx_task_steps_step_order ON task_steps(step_order);
CREATE INDEX idx_task_steps_level ON task_steps(level);
CREATE INDEX idx_task_steps_step_type ON task_steps(step_type);
CREATE INDEX idx_task_steps_status ON task_steps(status);
CREATE INDEX idx_task_steps_agent_id ON task_steps(agent_id);
CREATE INDEX idx_task_steps_tool_name ON task_steps(tool_name);
CREATE INDEX idx_task_steps_start_time ON task_steps(start_time);
CREATE INDEX idx_task_steps_deleted_at ON task_steps(deleted_at);

-- 任务结果表索引
CREATE INDEX idx_task_results_task_id ON task_results(task_id);
CREATE INDEX idx_task_results_result_type ON task_results(result_type);
CREATE INDEX idx_task_results_status ON task_results(status);
CREATE INDEX idx_task_results_created_at ON task_results(created_at);
CREATE INDEX idx_task_results_deleted_at ON task_results(deleted_at);

-- 任务依赖表索引
CREATE INDEX idx_task_dependencies_task_id ON task_dependencies(task_id);
CREATE INDEX idx_task_dependencies_depends_on_task_id ON task_dependencies(depends_on_task_id);
CREATE INDEX idx_task_dependencies_dependency_type ON task_dependencies(dependency_type);
CREATE INDEX idx_task_dependencies_is_satisfied ON task_dependencies(is_satisfied);
CREATE INDEX idx_task_dependencies_satisfied_at ON task_dependencies(satisfied_at);
CREATE INDEX idx_task_dependencies_deleted_at ON task_dependencies(deleted_at);

-- 工作流表索引
CREATE INDEX idx_workflows_user_id ON workflows(user_id);
CREATE INDEX idx_workflows_owner_id ON workflows(owner_id);
CREATE INDEX idx_workflows_workflow_id ON workflows(workflow_id);
CREATE INDEX idx_workflows_category ON workflows(category);
CREATE INDEX idx_workflows_workflow_type ON workflows(workflow_type);
CREATE INDEX idx_workflows_status ON workflows(status);
CREATE INDEX idx_workflows_is_public ON workflows(is_public);
CREATE INDEX idx_workflows_is_template ON workflows(is_template);
CREATE INDEX idx_workflows_created_at ON workflows(created_at);
CREATE INDEX idx_workflows_last_executed_at ON workflows(last_executed_at);
CREATE INDEX idx_workflows_deleted_at ON workflows(deleted_at);

-- 工作流步骤表索引
CREATE INDEX idx_workflow_steps_workflow_id ON workflow_steps(workflow_id);
CREATE INDEX idx_workflow_steps_user_id ON workflow_steps(user_id);
CREATE INDEX idx_workflow_steps_owner_id ON workflow_steps(owner_id);
CREATE INDEX idx_workflow_steps_step_id ON workflow_steps(step_id);
CREATE INDEX idx_workflow_steps_step_type ON workflow_steps(step_type);
CREATE INDEX idx_workflow_steps_step_order ON workflow_steps(step_order);
CREATE INDEX idx_workflow_steps_deleted_at ON workflow_steps(deleted_at);

-- 工作流执行记录表索引
CREATE INDEX idx_workflow_executions_workflow_id ON workflow_executions(workflow_id);
CREATE INDEX idx_workflow_executions_execution_id ON workflow_executions(execution_id);
CREATE INDEX idx_workflow_executions_trigger_type ON workflow_executions(trigger_type);
CREATE INDEX idx_workflow_executions_triggered_by ON workflow_executions(triggered_by);
CREATE INDEX idx_workflow_executions_status ON workflow_executions(status);
CREATE INDEX idx_workflow_executions_started_at ON workflow_executions(started_at);
CREATE INDEX idx_workflow_executions_completed_at ON workflow_executions(completed_at);
CREATE INDEX idx_workflow_executions_deleted_at ON workflow_executions(deleted_at);

-- 工作流步骤执行记录表索引
CREATE INDEX idx_workflow_step_executions_workflow_execution_id ON workflow_step_executions(workflow_execution_id);
CREATE INDEX idx_workflow_step_executions_step_id ON workflow_step_executions(step_id);
CREATE INDEX idx_workflow_step_executions_status ON workflow_step_executions(status);
CREATE INDEX idx_workflow_step_executions_started_at ON workflow_step_executions(started_at);
CREATE INDEX idx_workflow_step_executions_deleted_at ON workflow_step_executions(deleted_at);

-- 工具表索引
CREATE INDEX idx_tools_tool_id ON tools(tool_id);
CREATE INDEX idx_tools_name ON tools(name);
CREATE INDEX idx_tools_tool_type ON tools(tool_type);
CREATE INDEX idx_tools_category ON tools(category);
CREATE INDEX idx_tools_status ON tools(status);
CREATE INDEX idx_tools_is_public ON tools(is_public);
CREATE INDEX idx_tools_permission_level ON tools(permission_level);
CREATE INDEX idx_tools_created_by ON tools(created_by);
CREATE INDEX idx_tools_created_at ON tools(created_at);
CREATE INDEX idx_tools_last_used_at ON tools(last_used_at);
CREATE INDEX idx_tools_deleted_at ON tools(deleted_at);

-- 智能体工具关联表索引
CREATE INDEX idx_agent_tools_agent_id ON agent_tools(agent_id);
CREATE INDEX idx_agent_tools_tool_id ON agent_tools(tool_id);
CREATE INDEX idx_agent_tools_is_enabled ON agent_tools(is_enabled);
CREATE INDEX idx_agent_tools_priority ON agent_tools(priority);
CREATE INDEX idx_agent_tools_deleted_at ON agent_tools(deleted_at);

-- 工具执行记录表索引
CREATE INDEX idx_tool_executions_tool_id ON tool_executions(tool_id);
CREATE INDEX idx_tool_executions_agent_id ON tool_executions(agent_id);
CREATE INDEX idx_tool_executions_user_id ON tool_executions(user_id);
CREATE INDEX idx_tool_executions_session_id ON tool_executions(session_id);
CREATE INDEX idx_tool_executions_message_id ON tool_executions(message_id);
CREATE INDEX idx_tool_executions_execution_id ON tool_executions(execution_id);
CREATE INDEX idx_tool_executions_status ON tool_executions(status);
CREATE INDEX idx_tool_executions_started_at ON tool_executions(started_at);
CREATE INDEX idx_tool_executions_deleted_at ON tool_executions(deleted_at);

-- 工具分类表索引
CREATE INDEX idx_tool_categories_name ON tool_categories(name);
CREATE INDEX idx_tool_categories_parent_id ON tool_categories(parent_id);
CREATE INDEX idx_tool_categories_display_order ON tool_categories(display_order);
CREATE INDEX idx_tool_categories_deleted_at ON tool_categories(deleted_at);

-- 系统配置表索引
CREATE INDEX idx_system_configs_config_key ON system_configs(config_key);
CREATE INDEX idx_system_configs_category ON system_configs(category);
CREATE INDEX idx_system_configs_is_required ON system_configs(is_required);
CREATE INDEX idx_system_configs_is_sensitive ON system_configs(is_sensitive);
CREATE INDEX idx_system_configs_updated_by ON system_configs(updated_by);
CREATE INDEX idx_system_configs_deleted_at ON system_configs(deleted_at);

-- 用户配置表索引
CREATE INDEX idx_user_configs_user_id ON user_configs(user_id);
CREATE INDEX idx_user_configs_config_key ON user_configs(config_key);
CREATE INDEX idx_user_configs_deleted_at ON user_configs(deleted_at);

-- 智能体配置表索引
CREATE INDEX idx_agent_configs_agent_id ON agent_configs(agent_id);
CREATE INDEX idx_agent_configs_user_id ON agent_configs(user_id);
CREATE INDEX idx_agent_configs_config_key ON agent_configs(config_key);
CREATE INDEX idx_agent_configs_deleted_at ON agent_configs(deleted_at);

-- 配置模板表索引
CREATE INDEX idx_config_templates_user_id ON config_templates(user_id);
CREATE INDEX idx_config_templates_name ON config_templates(name);
CREATE INDEX idx_config_templates_template_type ON config_templates(template_type);
CREATE INDEX idx_config_templates_is_default ON config_templates(is_default);
CREATE INDEX idx_config_templates_is_public ON config_templates(is_public);
CREATE INDEX idx_config_templates_created_by ON config_templates(created_by);
CREATE INDEX idx_config_templates_deleted_at ON config_templates(deleted_at);

-- LLM配置表索引
CREATE INDEX idx_llm_configs_user_id ON llm_configs(user_id);
CREATE INDEX idx_llm_configs_agent_id ON llm_configs(agent_id);
CREATE INDEX idx_llm_configs_provider ON llm_configs(provider);
CREATE INDEX idx_llm_configs_model_name ON llm_configs(model_name);
CREATE INDEX idx_llm_configs_is_default ON llm_configs(is_default);
CREATE INDEX idx_llm_configs_last_used_at ON llm_configs(last_used_at);
CREATE INDEX idx_llm_configs_deleted_at ON llm_configs(deleted_at);

-- 工具配置表索引
CREATE INDEX idx_tool_configs_user_id ON tool_configs(user_id);
CREATE INDEX idx_tool_configs_agent_id ON tool_configs(agent_id);
CREATE INDEX idx_tool_configs_tool_name ON tool_configs(tool_name);
CREATE INDEX idx_tool_configs_tool_type ON tool_configs(tool_type);
CREATE INDEX idx_tool_configs_is_default ON tool_configs(is_default);
CREATE INDEX idx_tool_configs_last_used_at ON tool_configs(last_used_at);
CREATE INDEX idx_tool_configs_deleted_at ON tool_configs(deleted_at);

-- 记忆表索引
CREATE INDEX idx_memories_memory_id ON memories(memory_id);
CREATE INDEX idx_memories_user_id ON memories(user_id);
CREATE INDEX idx_memories_owner_id ON memories(owner_id);
CREATE INDEX idx_memories_memory_type ON memories(memory_type);
CREATE INDEX idx_memories_status ON memories(status);
CREATE INDEX idx_memories_last_accessed_at ON memories(last_accessed_at);
CREATE INDEX idx_memories_deleted_at ON memories(deleted_at);

-- 记忆条目表索引
CREATE INDEX idx_memory_entries_memory_id ON memory_entries(memory_id);
CREATE INDEX idx_memory_entries_entry_key ON memory_entries(entry_key);
CREATE INDEX idx_memory_entries_entry_type ON memory_entries(entry_type);
CREATE INDEX idx_memory_entries_priority ON memory_entries(priority);
CREATE INDEX idx_memory_entries_expires_at ON memory_entries(expires_at);
CREATE INDEX idx_memory_entries_last_accessed_at ON memory_entries(last_accessed_at);
CREATE INDEX idx_memory_entries_deleted_at ON memory_entries(deleted_at);

-- 工件表索引
CREATE INDEX idx_artifacts_user_id ON artifacts(user_id);
CREATE INDEX idx_artifacts_owner_id ON artifacts(owner_id);
CREATE INDEX idx_artifacts_artifact_id ON artifacts(artifact_id);
CREATE INDEX idx_artifacts_artifact_type ON artifacts(artifact_type);
CREATE INDEX idx_artifacts_category ON artifacts(category);
CREATE INDEX idx_artifacts_status ON artifacts(status);
CREATE INDEX idx_artifacts_is_public ON artifacts(is_public);
CREATE INDEX idx_artifacts_created_at ON artifacts(created_at);
CREATE INDEX idx_artifacts_last_accessed_at ON artifacts(last_accessed_at);
CREATE INDEX idx_artifacts_deleted_at ON artifacts(deleted_at);

-- 工件版本表索引
CREATE INDEX idx_artifact_versions_artifact_id ON artifact_versions(artifact_id);
CREATE INDEX idx_artifact_versions_version_number ON artifact_versions(version_number);
CREATE INDEX idx_artifact_versions_created_by ON artifact_versions(created_by);
CREATE INDEX idx_artifact_versions_created_at ON artifact_versions(created_at);
CREATE INDEX idx_artifact_versions_deleted_at ON artifact_versions(deleted_at);

-- 工件共享表索引
CREATE INDEX idx_artifact_shares_artifact_id ON artifact_shares(artifact_id);
CREATE INDEX idx_artifact_shares_shared_with_user_id ON artifact_shares(shared_with_user_id);
CREATE INDEX idx_artifact_shares_permission_level ON artifact_shares(permission_level);
CREATE INDEX idx_artifact_shares_shared_by ON artifact_shares(shared_by);
CREATE INDEX idx_artifact_shares_expires_at ON artifact_shares(expires_at);
CREATE INDEX idx_artifact_shares_deleted_at ON artifact_shares(deleted_at);

-- 系统日志表索引
CREATE INDEX idx_system_logs_log_level ON system_logs(log_level);
CREATE INDEX idx_system_logs_logger_name ON system_logs(logger_name);
CREATE INDEX idx_system_logs_category ON system_logs(category);
CREATE INDEX idx_system_logs_user_id ON system_logs(user_id);
CREATE INDEX idx_system_logs_session_id ON system_logs(session_id);
CREATE INDEX idx_system_logs_request_id ON system_logs(request_id);
CREATE INDEX idx_system_logs_module ON system_logs(module);
CREATE INDEX idx_system_logs_timestamp ON system_logs(timestamp);
CREATE INDEX idx_system_logs_deleted_at ON system_logs(deleted_at);

-- 性能指标表索引
CREATE INDEX idx_performance_metrics_metric_name ON performance_metrics(metric_name);
CREATE INDEX idx_performance_metrics_metric_type ON performance_metrics(metric_type);
CREATE INDEX idx_performance_metrics_category ON performance_metrics(category);
CREATE INDEX idx_performance_metrics_user_id ON performance_metrics(user_id);
CREATE INDEX idx_performance_metrics_agent_id ON performance_metrics(agent_id);
CREATE INDEX idx_performance_metrics_session_id ON performance_metrics(session_id);
CREATE INDEX idx_performance_metrics_timestamp ON performance_metrics(timestamp);
CREATE INDEX idx_performance_metrics_deleted_at ON performance_metrics(deleted_at);

-- 错误日志表索引
CREATE INDEX idx_error_logs_error_id ON error_logs(error_id);
CREATE INDEX idx_error_logs_error_type ON error_logs(error_type);
CREATE INDEX idx_error_logs_error_code ON error_logs(error_code);
CREATE INDEX idx_error_logs_severity ON error_logs(severity);
CREATE INDEX idx_error_logs_category ON error_logs(category);
CREATE INDEX idx_error_logs_user_id ON error_logs(user_id);
CREATE INDEX idx_error_logs_agent_id ON error_logs(agent_id);
CREATE INDEX idx_error_logs_session_id ON error_logs(session_id);
CREATE INDEX idx_error_logs_task_id ON error_logs(task_id);
CREATE INDEX idx_error_logs_resolution_status ON error_logs(resolution_status);
CREATE INDEX idx_error_logs_first_occurred_at ON error_logs(first_occurred_at);
CREATE INDEX idx_error_logs_last_occurred_at ON error_logs(last_occurred_at);
CREATE INDEX idx_error_logs_deleted_at ON error_logs(deleted_at);

-- 审计日志表索引
CREATE INDEX idx_audit_logs_audit_id ON audit_logs(audit_id);
CREATE INDEX idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX idx_audit_logs_action ON audit_logs(action);
CREATE INDEX idx_audit_logs_resource_type ON audit_logs(resource_type);
CREATE INDEX idx_audit_logs_resource_id ON audit_logs(resource_id);
CREATE INDEX idx_audit_logs_session_id ON audit_logs(session_id);
CREATE INDEX idx_audit_logs_status ON audit_logs(status);
CREATE INDEX idx_audit_logs_timestamp ON audit_logs(timestamp);
CREATE INDEX idx_audit_logs_deleted_at ON audit_logs(deleted_at);

-- ============================================================================
-- 初始数据插入
-- ============================================================================

-- 插入默认系统配置
INSERT INTO system_configs (config_key, config_value, value_type, category, description, is_required, is_sensitive, default_value) VALUES
('system.name', 'A2A多智能体系统', 'string', 'system', '系统名称', true, false, 'A2A多智能体系统'),
('system.version', '1.0.0', 'string', 'system', '系统版本', true, false, '1.0.0'),
('system.timezone', 'Asia/Shanghai', 'string', 'system', '系统时区', true, false, 'Asia/Shanghai'),
('system.language', 'zh-CN', 'string', 'system', '系统默认语言', true, false, 'zh-CN'),
('system.max_session_duration', '86400', 'integer', 'security', '最大会话持续时间（秒）', true, false, '86400'),
('system.max_file_size', '104857600', 'integer', 'upload', '最大文件上传大小（字节）', true, false, '104857600'),
('system.allowed_file_types', '["txt", "pdf", "doc", "docx", "jpg", "png", "gif"]', 'json', 'upload', '允许的文件类型', true, false, '["txt", "pdf", "doc", "docx", "jpg", "png", "gif"]'),
('llm.default_provider', 'openai', 'string', 'llm', '默认LLM提供商', true, false, 'openai'),
('llm.default_model', 'gpt-4', 'string', 'llm', '默认LLM模型', true, false, 'gpt-4'),
('llm.default_temperature', '0.7', 'float', 'llm', '默认温度参数', true, false, '0.7'),
('llm.default_max_tokens', '4096', 'integer', 'llm', '默认最大令牌数', true, false, '4096'),
('security.password_min_length', '8', 'integer', 'security', '密码最小长度', true, false, '8'),
('security.password_require_uppercase', 'true', 'boolean', 'security', '密码是否需要大写字母', true, false, 'true'),
('security.password_require_lowercase', 'true', 'boolean', 'security', '密码是否需要小写字母', true, false, 'true'),
('security.password_require_numbers', 'true', 'boolean', 'security', '密码是否需要数字', true, false, 'true'),
('security.password_require_symbols', 'false', 'boolean', 'security', '密码是否需要特殊字符', true, false, 'false'),
('security.max_login_attempts', '5', 'integer', 'security', '最大登录尝试次数', true, false, '5'),
('security.account_lockout_duration', '1800', 'integer', 'security', '账户锁定持续时间（秒）', true, false, '1800'),
('task.default_timeout', '3600', 'integer', 'task', '任务默认超时时间（秒）', true, false, '3600'),
('task.max_retry_count', '3', 'integer', 'task', '任务最大重试次数', true, false, '3'),
('workflow.max_steps', '100', 'integer', 'workflow', '工作流最大步骤数', true, false, '100'),
('workflow.default_timeout', '7200', 'integer', 'workflow', '工作流默认超时时间（秒）', true, false, '7200'),
('memory.default_retention_days', '30', 'integer', 'memory', '记忆默认保留天数', true, false, '30'),
('memory.max_entries_per_memory', '10000', 'integer', 'memory', '每个记忆的最大条目数', true, false, '10000'),
('artifact.max_file_size', '********', 'integer', 'artifact', '工件最大文件大小（字节）', true, false, '********'),
('artifact.retention_days', '365', 'integer', 'artifact', '工件保留天数', true, false, '365'),
('log.retention_days', '90', 'integer', 'logging', '日志保留天数', true, false, '90'),
('log.max_log_size', '10485760', 'integer', 'logging', '单个日志文件最大大小（字节）', true, false, '10485760'),
('monitoring.metrics_retention_days', '30', 'integer', 'monitoring', '监控指标保留天数', true, false, '30'),
('monitoring.enable_performance_tracking', 'true', 'boolean', 'monitoring', '是否启用性能跟踪', true, false, 'true');

-- 插入默认工具分类
INSERT INTO tool_categories (name, description, display_order, icon) VALUES
('通用工具', '通用功能工具', 1, 'general'),
('文本处理', '文本分析和处理工具', 2, 'text'),
('图像处理', '图像分析和处理工具', 3, 'image'),
('数据分析', '数据分析和可视化工具', 4, 'data'),
('网络工具', '网络请求和API调用工具', 5, 'network'),
('文件操作', '文件读写和管理工具', 6, 'file'),
('代码工具', '代码生成和执行工具', 7, 'code'),
('搜索工具', '搜索和检索工具', 8, 'search'),
('通信工具', '消息发送和通信工具', 9, 'communication'),
('系统工具', '系统管理和监控工具', 10, 'system');

-- ============================================================================
-- 视图创建
-- ============================================================================

-- 用户统计视图
CREATE VIEW user_stats AS
SELECT 
    u.id,
    u.username,
    u.email,
    u.role,
    u.created_at,
    u.last_login_at,
    u.login_count,
    COUNT(DISTINCT s.id) as session_count,
    COUNT(DISTINCT m.id) as message_count,
    COUNT(DISTINCT t.id) as task_count,
    COUNT(DISTINCT a.id) as agent_count
FROM users u
LEFT JOIN sessions s ON u.id = s.user_id AND s.deleted_at IS NULL
LEFT JOIN messages m ON u.id = m.user_id AND m.deleted_at IS NULL
LEFT JOIN tasks t ON u.id = t.user_id AND t.deleted_at IS NULL
LEFT JOIN agents a ON u.id = a.user_id AND a.deleted_at IS NULL
WHERE u.deleted_at IS NULL
GROUP BY u.id, u.username, u.email, u.role, u.created_at, u.last_login_at, u.login_count;

-- 智能体统计视图
CREATE VIEW agent_stats AS
SELECT 
    a.id,
    a.agent_id,
    a.name,
    a.agent_type,
    a.category,
    a.status,
    a.usage_count,
    a.success_rate,
    a.avg_response_time,
    a.last_used_at,
    a.created_at,
    COUNT(DISTINCT t.id) as task_count,
    COUNT(DISTINCT ae.id) as execution_count,
    COUNT(DISTINCT ac.id) as capability_count
FROM agents a
LEFT JOIN tasks t ON a.id = t.agent_id AND t.deleted_at IS NULL
LEFT JOIN agent_executions ae ON a.id = ae.agent_id AND ae.deleted_at IS NULL
LEFT JOIN agent_capabilities ac ON a.id = ac.agent_id AND ac.deleted_at IS NULL
WHERE a.deleted_at IS NULL
GROUP BY a.id, a.agent_id, a.name, a.agent_type, a.category, a.status, a.usage_count, a.success_rate, a.avg_response_time, a.last_used_at, a.created_at;

-- 任务统计视图
CREATE VIEW task_stats AS
SELECT 
    DATE(created_at) as date,
    task_type,
    status,
    priority,
    COUNT(*) as task_count,
    AVG(execution_time) as avg_execution_time,
    SUM(tokens_used) as total_tokens_used,
    SUM(cost) as total_cost
FROM tasks
WHERE deleted_at IS NULL
GROUP BY DATE(created_at), task_type, status, priority;

-- 会话统计视图
CREATE VIEW session_stats AS
SELECT 
    DATE(created_at) as date,
    session_type,
    status,
    COUNT(*) as session_count,
    AVG(message_count) as avg_message_count,
    AVG(TIMESTAMPDIFF(SECOND, created_at, last_message_at)) as avg_duration_seconds
FROM sessions
WHERE deleted_at IS NULL
GROUP BY DATE(created_at), session_type, status;

-- 工具使用统计视图
CREATE VIEW tool_usage_stats AS
SELECT 
    t.id,
    t.tool_id,
    t.name,
    t.tool_type,
    t.category,
    t.usage_count,
    t.success_count,
    t.avg_response_time,
    t.last_used_at,
    COUNT(DISTINCT te.id) as execution_count,
    COUNT(DISTINCT at.agent_id) as agent_count
FROM tools t
LEFT JOIN tool_executions te ON t.id = te.tool_id AND te.deleted_at IS NULL
LEFT JOIN agent_tools at ON t.id = at.tool_id AND at.deleted_at IS NULL
WHERE t.deleted_at IS NULL
GROUP BY t.id, t.tool_id, t.name, t.tool_type, t.category, t.usage_count, t.success_count, t.avg_response_time, t.last_used_at;

-- 系统性能统计视图
CREATE VIEW system_performance_stats AS
SELECT 
    DATE(timestamp) as date,
    metric_name,
    metric_type,
    category,
    AVG(metric_value) as avg_value,
    MIN(metric_value) as min_value,
    MAX(metric_value) as max_value,
    COUNT(*) as sample_count
FROM performance_metrics
WHERE deleted_at IS NULL
GROUP BY DATE(timestamp), metric_name, metric_type, category;

-- ============================================================================
-- 存储过程和函数
-- ============================================================================

-- 清理过期数据的存储过程
DELIMITER //
CREATE PROCEDURE CleanupExpiredData()
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE retention_days INT;
    
    -- 获取日志保留天数配置
    SELECT CAST(config_value AS UNSIGNED) INTO retention_days 
    FROM system_configs 
    WHERE config_key = 'log.retention_days' AND is_active = TRUE;
    
    -- 清理过期的系统日志
    DELETE FROM system_logs 
    WHERE created_at < DATE_SUB(NOW(), INTERVAL retention_days DAY);
    
    -- 清理过期的用户活动日志
    DELETE FROM user_activity_logs 
    WHERE created_at < DATE_SUB(NOW(), INTERVAL retention_days DAY);
    
    -- 清理过期的审计日志
    DELETE FROM audit_logs 
    WHERE created_at < DATE_SUB(NOW(), INTERVAL retention_days DAY);
    
    -- 获取监控指标保留天数配置
    SELECT CAST(config_value AS UNSIGNED) INTO retention_days 
    FROM system_configs 
    WHERE config_key = 'monitoring.metrics_retention_days' AND is_active = TRUE;
    
    -- 清理过期的性能指标
    DELETE FROM performance_metrics 
    WHERE created_at < DATE_SUB(NOW(), INTERVAL retention_days DAY);
    
    -- 清理过期的用户令牌
    UPDATE user_tokens 
    SET is_revoked = TRUE, revoked_at = NOW() 
    WHERE refresh_expires_at < NOW() AND is_revoked = FALSE;
    
    -- 清理过期的记忆条目
    DELETE FROM memory_entries 
    WHERE expires_at IS NOT NULL AND expires_at < NOW();
    
    -- 清理过期的工件共享
    UPDATE artifact_shares 
    SET is_active = FALSE 
    WHERE expires_at IS NOT NULL AND expires_at < NOW() AND is_active = TRUE;
    
END //
DELIMITER ;

-- 更新用户统计信息的存储过程
DELIMITER //
CREATE PROCEDURE UpdateUserStats(IN user_id_param INT)
BEGIN
    -- 更新用户的登录统计
    UPDATE users 
    SET 
        last_login_at = NOW(),
        login_count = login_count + 1,
        failed_login_attempts = 0
    WHERE id = user_id_param;
END //
DELIMITER ;

-- 更新智能体统计信息的存储过程
DELIMITER //
CREATE PROCEDURE UpdateAgentStats(IN agent_id_param INT, IN execution_time_param DECIMAL(10,3), IN success_param BOOLEAN)
BEGIN
    DECLARE current_usage_count INT DEFAULT 0;
    DECLARE current_success_count INT DEFAULT 0;
    DECLARE current_avg_time DECIMAL(10,2) DEFAULT 0.00;
    DECLARE new_success_rate DECIMAL(5,2) DEFAULT 0.00;
    DECLARE new_avg_time DECIMAL(10,2) DEFAULT 0.00;
    
    -- 获取当前统计信息
    SELECT usage_count, success_rate * usage_count / 100, avg_response_time
    INTO current_usage_count, current_success_count, current_avg_time
    FROM agents 
    WHERE id = agent_id_param;
    
    -- 计算新的统计信息
    SET current_usage_count = current_usage_count + 1;
    
    IF success_param THEN
        SET current_success_count = current_success_count + 1;
    END IF;
    
    SET new_success_rate = (current_success_count * 100.0) / current_usage_count;
    SET new_avg_time = ((current_avg_time * (current_usage_count - 1)) + (execution_time_param * 1000)) / current_usage_count;
    
    -- 更新智能体统计信息
    UPDATE agents 
    SET 
        usage_count = current_usage_count,
        success_rate = new_success_rate,
        avg_response_time = new_avg_time,
        last_used_at = NOW()
    WHERE id = agent_id_param;
END //
DELIMITER ;

-- 获取用户权限的函数
DELIMITER //
CREATE FUNCTION HasUserPermission(user_id_param INT, permission_name_param VARCHAR(100)) 
RETURNS BOOLEAN
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE has_permission BOOLEAN DEFAULT FALSE;
    
    SELECT COUNT(*) > 0 INTO has_permission
    FROM user_permissions 
    WHERE user_id = user_id_param 
        AND permission_name = permission_name_param 
        AND is_granted = TRUE 
        AND is_active = TRUE
        AND (expires_at IS NULL OR expires_at > NOW())
        AND deleted_at IS NULL;
    
    RETURN has_permission;
END //
DELIMITER ;

-- ============================================================================
-- 触发器
-- ============================================================================

-- 用户表更新时间触发器
DELIMITER //
CREATE TRIGGER tr_users_updated_at
    BEFORE UPDATE ON users
    FOR EACH ROW
BEGIN
    SET NEW.updated_at = NOW();
END //
DELIMITER ;

-- 智能体表更新时间触发器
DELIMITER //
CREATE TRIGGER tr_agents_updated_at
    BEFORE UPDATE ON agents
    FOR EACH ROW
BEGIN
    SET NEW.updated_at = NOW();
END //
DELIMITER ;

-- 会话表更新时间触发器
DELIMITER //
CREATE TRIGGER tr_sessions_updated_at
    BEFORE UPDATE ON sessions
    FOR EACH ROW
BEGIN
    SET NEW.updated_at = NOW();
END //
DELIMITER ;

-- 消息表插入后更新会话统计触发器
DELIMITER //
CREATE TRIGGER tr_messages_after_insert
    AFTER INSERT ON messages
    FOR EACH ROW
BEGIN
    UPDATE sessions 
    SET 
        message_count = message_count + 1,
        last_message_at = NEW.created_at
    WHERE id = NEW.session_id;
END //
DELIMITER ;

-- 任务表更新时间触发器
DELIMITER //
CREATE TRIGGER tr_tasks_updated_at
    BEFORE UPDATE ON tasks
    FOR EACH ROW
BEGIN
    SET NEW.updated_at = NOW();
END //
DELIMITER ;

-- 工具表更新时间触发器
DELIMITER //
CREATE TRIGGER tr_tools_updated_at
    BEFORE UPDATE ON tools
    FOR EACH ROW
BEGIN
    SET NEW.updated_at = NOW();
END //
DELIMITER ;

-- ============================================================================
-- 数据库架构说明
-- ============================================================================

/*
A2A多智能体系统数据库架构说明：

1. 核心模块：
   - 用户管理：users, user_tokens, user_permissions, user_activity_logs
   - 智能体管理：agents, agent_capabilities, agent_collaborations, agent_executions
   - 会话管理：sessions, messages, message_attachments, message_reactions
   - 任务管理：tasks, task_executions, task_templates, task_steps, task_results, task_dependencies
   - 工作流管理：workflows, workflow_steps, workflow_executions, workflow_step_executions
   - 工具管理：tools, agent_tools, tool_executions, tool_categories
   - 配置管理：system_configs, user_configs, agent_configs, config_templates, llm_configs, tool_configs
   - 记忆管理：memories, memory_entries
   - 工件管理：artifacts, artifact_versions, artifact_shares
   - 日志监控：system_logs, performance_metrics, error_logs, audit_logs

2. 设计特点：
   - 支持软删除（deleted_at字段）
   - 完整的审计追踪
   - 灵活的JSON配置存储
   - 多租户支持（owner_id字段）
   - 完善的索引优化
   - 统计视图支持
   - 存储过程和触发器

3. 扩展性：
   - 模块化设计，易于扩展
   - 支持插件化工具
   - 灵活的配置系统
   - 可扩展的权限模型

4. 性能优化：
   - 合理的索引设计
   - 分区表支持（可选）
   - 定期清理过期数据
   - 统计信息缓存

5. 安全性：
   - 密码哈希存储
   - 令牌管理
   - 权限控制
   - 审计日志
   - 敏感配置保护

使用说明：
1. 根据实际数据库类型调整语法（MySQL/PostgreSQL）
2. 根据业务需求调整字段长度和约束
3. 根据数据量调整索引策略
4. 定期执行清理存储过程
5. 监控性能指标和错误日志
*/

-- 数据库架构创建完成
-- 版本：1.0.0
-- 创建时间：2024年
-- 兼容性：MySQL 8.0+ / PostgreSQL 12+