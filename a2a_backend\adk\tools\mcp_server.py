#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A2A多智能体系统 MCP服务器

实现MCP协议的服务器功能，支持工具、资源和提示词的发布
"""

import asyncio
import logging
import json
import uuid
from typing import Dict, List, Any, Optional, Callable, Union, Set
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime, timedelta
import websockets
from aiohttp import web, WSMsgType
import aiohttp_cors
from urllib.parse import urlparse

# Google ADK imports
from google.ai.generativelanguage import FunctionDeclaration, Schema, Type as SchemaType

from .base_tool import (
    BaseTool, ToolConfig, ToolResult, ToolError, ToolStatus,
    ToolExecutionContext, ToolPermission
)
from .mcp_client import MCPMessage, MCPMessageType, MCPToolInfo, MCPResource, MCPPrompt


class MCPServerState(Enum):
    """MCP服务器状态枚举"""
    STOPPED = "stopped"
    STARTING = "starting"
    RUNNING = "running"
    STOPPING = "stopping"
    ERROR = "error"


class MCPTransportType(Enum):
    """MCP传输类型枚举"""
    WEBSOCKET = "websocket"
    HTTP = "http"
    STDIO = "stdio"


@dataclass
class MCPServerConfig:
    """MCP服务器配置"""
    name: str = "A2A-MCP-Server"
    version: str = "1.0.0"
    description: str = "A2A多智能体系统MCP服务器"
    
    # 网络配置
    host: str = "localhost"
    port: int = 8080
    transport: MCPTransportType = MCPTransportType.WEBSOCKET
    
    # 安全配置
    enable_auth: bool = True
    auth_tokens: Set[str] = field(default_factory=set)
    allowed_origins: List[str] = field(default_factory=lambda: ["*"])
    max_connections: int = 100
    
    # 协议配置
    protocol_version: str = "2024-11-05"
    capabilities: Dict[str, Any] = field(default_factory=lambda: {
        "tools": True,
        "resources": True,
        "prompts": True,
        "notifications": True,
        "experimental": {}
    })
    
    # 性能配置
    max_message_size: int = 1024 * 1024  # 1MB
    heartbeat_interval: int = 30
    request_timeout: int = 30
    enable_compression: bool = True
    
    # 日志配置
    log_level: str = "INFO"
    log_requests: bool = True
    log_responses: bool = False


@dataclass
class MCPClientConnection:
    """MCP客户端连接信息"""
    id: str
    transport: MCPTransportType
    websocket: Optional[websockets.WebSocketServerProtocol] = None
    request: Optional[web.Request] = None
    authenticated: bool = False
    client_info: Optional[Dict[str, Any]] = None
    capabilities: Optional[Dict[str, Any]] = None
    connected_at: datetime = field(default_factory=datetime.now)
    last_activity: datetime = field(default_factory=datetime.now)
    message_count: int = 0


class MCPServerError(Exception):
    """MCP服务器异常"""
    pass


class MCPServer:
    """MCP协议服务器"""
    
    def __init__(self, config: MCPServerConfig):
        """
        初始化MCP服务器
        
        Args:
            config: 服务器配置
        """
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
        self.logger.setLevel(getattr(logging, config.log_level.upper()))
        
        # 服务器状态
        self.state = MCPServerState.STOPPED
        self.server = None
        self.app = None
        
        # 连接管理
        self.connections: Dict[str, MCPClientConnection] = {}
        self.connection_lock = asyncio.Lock()
        
        # 工具、资源和提示词注册表
        self.tools: Dict[str, MCPToolInfo] = {}
        self.resources: Dict[str, MCPResource] = {}
        self.prompts: Dict[str, MCPPrompt] = {}
        
        # 处理器注册表
        self.tool_handlers: Dict[str, Callable] = {}
        self.resource_handlers: Dict[str, Callable] = {}
        self.prompt_handlers: Dict[str, Callable] = {}
        self.notification_handlers: List[Callable] = []
        
        # 统计信息
        self.stats = {
            'connections_total': 0,
            'connections_active': 0,
            'messages_received': 0,
            'messages_sent': 0,
            'tools_called': 0,
            'resources_read': 0,
            'prompts_used': 0,
            'errors': 0,
            'start_time': None
        }
        
        # 心跳任务
        self._heartbeat_task: Optional[asyncio.Task] = None
    
    async def start(self) -> None:
        """
        启动MCP服务器
        """
        if self.state != MCPServerState.STOPPED:
            raise MCPServerError(f"服务器状态错误: {self.state}")
        
        try:
            self.state = MCPServerState.STARTING
            
            if self.config.transport == MCPTransportType.WEBSOCKET:
                await self._start_websocket_server()
            elif self.config.transport == MCPTransportType.HTTP:
                await self._start_http_server()
            elif self.config.transport == MCPTransportType.STDIO:
                await self._start_stdio_server()
            else:
                raise MCPServerError(f"不支持的传输类型: {self.config.transport}")
            
            # 启动心跳任务
            self._heartbeat_task = asyncio.create_task(self._heartbeat_loop())
            
            self.state = MCPServerState.RUNNING
            self.stats['start_time'] = datetime.now()
            
            self.logger.info(
                f"MCP服务器已启动 - {self.config.transport.value}://{self.config.host}:{self.config.port}"
            )
            
        except Exception as e:
            self.state = MCPServerState.ERROR
            self.logger.error(f"启动MCP服务器失败: {e}")
            raise
    
    async def stop(self) -> None:
        """
        停止MCP服务器
        """
        if self.state == MCPServerState.STOPPED:
            return
        
        try:
            self.state = MCPServerState.STOPPING
            
            # 停止心跳任务
            if self._heartbeat_task:
                self._heartbeat_task.cancel()
                try:
                    await self._heartbeat_task
                except asyncio.CancelledError:
                    pass
                self._heartbeat_task = None
            
            # 关闭所有连接
            async with self.connection_lock:
                for connection in list(self.connections.values()):
                    await self._close_connection(connection)
                self.connections.clear()
            
            # 停止服务器
            if self.server:
                if hasattr(self.server, 'close'):
                    self.server.close()
                    if hasattr(self.server, 'wait_closed'):
                        await self.server.wait_closed()
                elif hasattr(self.server, 'cleanup'):
                    await self.server.cleanup()
                self.server = None
            
            self.state = MCPServerState.STOPPED
            self.logger.info("MCP服务器已停止")
            
        except Exception as e:
            self.state = MCPServerState.ERROR
            self.logger.error(f"停止MCP服务器失败: {e}")
            raise
    
    def register_tool(
        self,
        name: str,
        description: str,
        input_schema: Dict[str, Any],
        handler: Callable,
        output_schema: Optional[Dict[str, Any]] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> None:
        """
        注册工具
        
        Args:
            name: 工具名称
            description: 工具描述
            input_schema: 输入模式
            handler: 处理函数
            output_schema: 输出模式
            metadata: 元数据
        """
        tool_info = MCPToolInfo(
            name=name,
            description=description,
            input_schema=input_schema,
            output_schema=output_schema,
            metadata=metadata or {}
        )
        
        self.tools[name] = tool_info
        self.tool_handlers[name] = handler
        
        self.logger.info(f"工具已注册: {name}")
    
    def unregister_tool(self, name: str) -> None:
        """
        注销工具
        
        Args:
            name: 工具名称
        """
        if name in self.tools:
            del self.tools[name]
        if name in self.tool_handlers:
            del self.tool_handlers[name]
        
        self.logger.info(f"工具已注销: {name}")
    
    def register_resource(
        self,
        uri: str,
        name: str,
        description: str,
        handler: Callable,
        mime_type: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> None:
        """
        注册资源
        
        Args:
            uri: 资源URI
            name: 资源名称
            description: 资源描述
            handler: 处理函数
            mime_type: MIME类型
            metadata: 元数据
        """
        resource_info = MCPResource(
            uri=uri,
            name=name,
            description=description,
            mime_type=mime_type,
            metadata=metadata or {}
        )
        
        self.resources[uri] = resource_info
        self.resource_handlers[uri] = handler
        
        self.logger.info(f"资源已注册: {uri}")
    
    def unregister_resource(self, uri: str) -> None:
        """
        注销资源
        
        Args:
            uri: 资源URI
        """
        if uri in self.resources:
            del self.resources[uri]
        if uri in self.resource_handlers:
            del self.resource_handlers[uri]
        
        self.logger.info(f"资源已注销: {uri}")
    
    def register_prompt(
        self,
        name: str,
        description: str,
        handler: Callable,
        arguments: Optional[List[Dict[str, Any]]] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> None:
        """
        注册提示词
        
        Args:
            name: 提示词名称
            description: 提示词描述
            handler: 处理函数
            arguments: 参数定义
            metadata: 元数据
        """
        prompt_info = MCPPrompt(
            name=name,
            description=description,
            arguments=arguments or [],
            metadata=metadata or {}
        )
        
        self.prompts[name] = prompt_info
        self.prompt_handlers[name] = handler
        
        self.logger.info(f"提示词已注册: {name}")
    
    def unregister_prompt(self, name: str) -> None:
        """
        注销提示词
        
        Args:
            name: 提示词名称
        """
        if name in self.prompts:
            del self.prompts[name]
        if name in self.prompt_handlers:
            del self.prompt_handlers[name]
        
        self.logger.info(f"提示词已注销: {name}")
    
    def add_notification_handler(self, handler: Callable) -> None:
        """
        添加通知处理器
        
        Args:
            handler: 通知处理函数
        """
        self.notification_handlers.append(handler)
    
    def remove_notification_handler(self, handler: Callable) -> None:
        """
        移除通知处理器
        
        Args:
            handler: 通知处理函数
        """
        if handler in self.notification_handlers:
            self.notification_handlers.remove(handler)
    
    async def send_notification(
        self,
        method: str,
        params: Dict[str, Any],
        connection_id: Optional[str] = None
    ) -> None:
        """
        发送通知
        
        Args:
            method: 方法名
            params: 参数
            connection_id: 连接ID，如果为None则广播给所有连接
        """
        message = MCPMessage(
            method=method,
            params=params
        )
        
        async with self.connection_lock:
            if connection_id:
                # 发送给指定连接
                if connection_id in self.connections:
                    await self._send_message(self.connections[connection_id], message)
            else:
                # 广播给所有连接
                for connection in self.connections.values():
                    try:
                        await self._send_message(connection, message)
                    except Exception as e:
                        self.logger.error(f"发送通知失败 {connection.id}: {e}")
    
    async def _start_websocket_server(self) -> None:
        """
        启动WebSocket服务器
        """
        async def handle_websocket(websocket, path):
            connection_id = str(uuid.uuid4())
            connection = MCPClientConnection(
                id=connection_id,
                transport=MCPTransportType.WEBSOCKET,
                websocket=websocket
            )
            
            async with self.connection_lock:
                self.connections[connection_id] = connection
                self.stats['connections_total'] += 1
                self.stats['connections_active'] += 1
            
            self.logger.info(f"WebSocket连接建立: {connection_id}")
            
            try:
                async for message in websocket:
                    await self._handle_websocket_message(connection, message)
            except websockets.exceptions.ConnectionClosed:
                self.logger.info(f"WebSocket连接关闭: {connection_id}")
            except Exception as e:
                self.logger.error(f"WebSocket连接异常 {connection_id}: {e}")
                self.stats['errors'] += 1
            finally:
                async with self.connection_lock:
                    if connection_id in self.connections:
                        del self.connections[connection_id]
                        self.stats['connections_active'] -= 1
        
        self.server = await websockets.serve(
            handle_websocket,
            self.config.host,
            self.config.port,
            max_size=self.config.max_message_size,
            compression="deflate" if self.config.enable_compression else None,
            ping_interval=self.config.heartbeat_interval
        )
    
    async def _start_http_server(self) -> None:
        """
        启动HTTP服务器
        """
        self.app = web.Application()
        
        # 配置CORS
        cors = aiohttp_cors.setup(self.app, defaults={
            origin: aiohttp_cors.ResourceOptions(
                allow_credentials=True,
                expose_headers="*",
                allow_headers="*",
                allow_methods="*"
            ) for origin in self.config.allowed_origins
        })
        
        # 添加路由
        self.app.router.add_post('/', self._handle_http_request)
        self.app.router.add_get('/health', self._handle_health_check)
        self.app.router.add_get('/stats', self._handle_stats_request)
        
        # 应用CORS
        for route in list(self.app.router.routes()):
            cors.add(route)
        
        # 启动服务器
        runner = web.AppRunner(self.app)
        await runner.setup()
        
        site = web.TCPSite(runner, self.config.host, self.config.port)
        await site.start()
        
        self.server = runner
    
    async def _start_stdio_server(self) -> None:
        """
        启动STDIO服务器
        """
        # TODO: 实现STDIO传输
        raise NotImplementedError("STDIO传输尚未实现")
    
    async def _handle_websocket_message(
        self,
        connection: MCPClientConnection,
        message: str
    ) -> None:
        """
        处理WebSocket消息
        
        Args:
            connection: 客户端连接
            message: 消息内容
        """
        try:
            data = json.loads(message)
            mcp_message = MCPMessage.from_dict(data)
            
            connection.last_activity = datetime.now()
            connection.message_count += 1
            self.stats['messages_received'] += 1
            
            if self.config.log_requests:
                self.logger.debug(f"收到消息 {connection.id}: {mcp_message.method}")
            
            response = await self._process_message(connection, mcp_message)
            
            if response:
                await self._send_message(connection, response)
                
        except json.JSONDecodeError as e:
            self.logger.error(f"JSON解析错误 {connection.id}: {e}")
            error_response = MCPMessage(
                id=None,
                error={
                    "code": -32700,
                    "message": "Parse error",
                    "data": str(e)
                }
            )
            await self._send_message(connection, error_response)
            self.stats['errors'] += 1
        except Exception as e:
            self.logger.error(f"处理WebSocket消息异常 {connection.id}: {e}")
            self.stats['errors'] += 1
    
    async def _handle_http_request(self, request: web.Request) -> web.Response:
        """
        处理HTTP请求
        
        Args:
            request: HTTP请求
        
        Returns:
            web.Response: HTTP响应
        """
        connection_id = str(uuid.uuid4())
        connection = MCPClientConnection(
            id=connection_id,
            transport=MCPTransportType.HTTP,
            request=request
        )
        
        try:
            # 解析请求
            data = await request.json()
            mcp_message = MCPMessage.from_dict(data)
            
            self.stats['messages_received'] += 1
            
            if self.config.log_requests:
                self.logger.debug(f"收到HTTP请求 {connection_id}: {mcp_message.method}")
            
            # 处理消息
            response = await self._process_message(connection, mcp_message)
            
            if response:
                response_data = response.to_dict()
                if self.config.log_responses:
                    self.logger.debug(f"HTTP响应 {connection_id}: {response_data}")
                return web.json_response(response_data)
            else:
                return web.Response(status=204)  # No Content
                
        except json.JSONDecodeError as e:
            self.logger.error(f"HTTP JSON解析错误 {connection_id}: {e}")
            error_response = {
                "jsonrpc": "2.0",
                "error": {
                    "code": -32700,
                    "message": "Parse error",
                    "data": str(e)
                }
            }
            self.stats['errors'] += 1
            return web.json_response(error_response, status=400)
        except Exception as e:
            self.logger.error(f"处理HTTP请求异常 {connection_id}: {e}")
            error_response = {
                "jsonrpc": "2.0",
                "error": {
                    "code": -32603,
                    "message": "Internal error",
                    "data": str(e)
                }
            }
            self.stats['errors'] += 1
            return web.json_response(error_response, status=500)
    
    async def _handle_health_check(self, request: web.Request) -> web.Response:
        """
        处理健康检查请求
        
        Args:
            request: HTTP请求
        
        Returns:
            web.Response: 健康状态响应
        """
        health_data = {
            "status": "healthy" if self.state == MCPServerState.RUNNING else "unhealthy",
            "server_state": self.state.value,
            "connections": len(self.connections),
            "uptime": (
                (datetime.now() - self.stats['start_time']).total_seconds()
                if self.stats['start_time'] else 0
            )
        }
        
        return web.json_response(health_data)
    
    async def _handle_stats_request(self, request: web.Request) -> web.Response:
        """
        处理统计信息请求
        
        Args:
            request: HTTP请求
        
        Returns:
            web.Response: 统计信息响应
        """
        stats_data = {
            **self.stats,
            'server_state': self.state.value,
            'tools_count': len(self.tools),
            'resources_count': len(self.resources),
            'prompts_count': len(self.prompts),
            'uptime': (
                (datetime.now() - self.stats['start_time']).total_seconds()
                if self.stats['start_time'] else 0
            )
        }
        
        return web.json_response(stats_data)
    
    async def _process_message(
        self,
        connection: MCPClientConnection,
        message: MCPMessage
    ) -> Optional[MCPMessage]:
        """
        处理MCP消息
        
        Args:
            connection: 客户端连接
            message: MCP消息
        
        Returns:
            Optional[MCPMessage]: 响应消息
        """
        try:
            # 认证检查
            if (self.config.enable_auth and 
                not connection.authenticated and 
                message.method != MCPMessageType.INITIALIZE.value):
                return MCPMessage(
                    id=message.id,
                    error={
                        "code": -32001,
                        "message": "Unauthorized",
                        "data": "Authentication required"
                    }
                )
            
            # 处理不同类型的消息
            if message.method == MCPMessageType.INITIALIZE.value:
                return await self._handle_initialize(connection, message)
            elif message.method == MCPMessageType.LIST_TOOLS.value:
                return await self._handle_list_tools(connection, message)
            elif message.method == MCPMessageType.CALL_TOOL.value:
                return await self._handle_call_tool(connection, message)
            elif message.method == MCPMessageType.LIST_RESOURCES.value:
                return await self._handle_list_resources(connection, message)
            elif message.method == MCPMessageType.READ_RESOURCE.value:
                return await self._handle_read_resource(connection, message)
            elif message.method == MCPMessageType.LIST_PROMPTS.value:
                return await self._handle_list_prompts(connection, message)
            elif message.method == MCPMessageType.GET_PROMPT.value:
                return await self._handle_get_prompt(connection, message)
            elif message.method == MCPMessageType.PING.value:
                return await self._handle_ping(connection, message)
            elif message.method == MCPMessageType.NOTIFICATION.value:
                await self._handle_notification(connection, message)
                return None
            else:
                return MCPMessage(
                    id=message.id,
                    error={
                        "code": -32601,
                        "message": "Method not found",
                        "data": f"Unknown method: {message.method}"
                    }
                )
                
        except Exception as e:
            self.logger.error(f"处理消息异常: {e}")
            return MCPMessage(
                id=message.id,
                error={
                    "code": -32603,
                    "message": "Internal error",
                    "data": str(e)
                }
            )
    
    async def _handle_initialize(
        self,
        connection: MCPClientConnection,
        message: MCPMessage
    ) -> MCPMessage:
        """
        处理初始化请求
        
        Args:
            connection: 客户端连接
            message: 初始化消息
        
        Returns:
            MCPMessage: 初始化响应
        """
        params = message.params or {}
        
        # 验证协议版本
        protocol_version = params.get('protocolVersion')
        if protocol_version != self.config.protocol_version:
            return MCPMessage(
                id=message.id,
                error={
                    "code": -32002,
                    "message": "Protocol version mismatch",
                    "data": f"Expected {self.config.protocol_version}, got {protocol_version}"
                }
            )
        
        # 保存客户端信息
        connection.client_info = params.get('clientInfo', {})
        connection.capabilities = params.get('capabilities', {})
        connection.authenticated = True
        
        # 返回服务器信息
        return MCPMessage(
            id=message.id,
            result={
                "protocolVersion": self.config.protocol_version,
                "capabilities": self.config.capabilities,
                "serverInfo": {
                    "name": self.config.name,
                    "version": self.config.version,
                    "description": self.config.description
                }
            }
        )
    
    async def _handle_list_tools(
        self,
        connection: MCPClientConnection,
        message: MCPMessage
    ) -> MCPMessage:
        """
        处理工具列表请求
        
        Args:
            connection: 客户端连接
            message: 请求消息
        
        Returns:
            MCPMessage: 工具列表响应
        """
        tools_list = []
        for tool in self.tools.values():
            tool_data = {
                "name": tool.name,
                "description": tool.description,
                "inputSchema": tool.input_schema
            }
            if tool.output_schema:
                tool_data["outputSchema"] = tool.output_schema
            if tool.metadata:
                tool_data["metadata"] = tool.metadata
            tools_list.append(tool_data)
        
        return MCPMessage(
            id=message.id,
            result={"tools": tools_list}
        )
    
    async def _handle_call_tool(
        self,
        connection: MCPClientConnection,
        message: MCPMessage
    ) -> MCPMessage:
        """
        处理工具调用请求
        
        Args:
            connection: 客户端连接
            message: 请求消息
        
        Returns:
            MCPMessage: 工具调用响应
        """
        params = message.params or {}
        tool_name = params.get('name')
        arguments = params.get('arguments', {})
        
        if not tool_name:
            return MCPMessage(
                id=message.id,
                error={
                    "code": -32602,
                    "message": "Invalid params",
                    "data": "Missing tool name"
                }
            )
        
        if tool_name not in self.tool_handlers:
            return MCPMessage(
                id=message.id,
                error={
                    "code": -32601,
                    "message": "Tool not found",
                    "data": f"Tool '{tool_name}' is not registered"
                }
            )
        
        try:
            handler = self.tool_handlers[tool_name]
            result = await handler(arguments)
            
            self.stats['tools_called'] += 1
            
            return MCPMessage(
                id=message.id,
                result=result
            )
            
        except Exception as e:
            self.logger.error(f"工具调用异常 {tool_name}: {e}")
            return MCPMessage(
                id=message.id,
                error={
                    "code": -32603,
                    "message": "Tool execution error",
                    "data": str(e)
                }
            )
    
    async def _handle_list_resources(
        self,
        connection: MCPClientConnection,
        message: MCPMessage
    ) -> MCPMessage:
        """
        处理资源列表请求
        
        Args:
            connection: 客户端连接
            message: 请求消息
        
        Returns:
            MCPMessage: 资源列表响应
        """
        resources_list = []
        for resource in self.resources.values():
            resource_data = {
                "uri": resource.uri,
                "name": resource.name,
                "description": resource.description
            }
            if resource.mime_type:
                resource_data["mimeType"] = resource.mime_type
            if resource.metadata:
                resource_data["metadata"] = resource.metadata
            resources_list.append(resource_data)
        
        return MCPMessage(
            id=message.id,
            result={"resources": resources_list}
        )
    
    async def _handle_read_resource(
        self,
        connection: MCPClientConnection,
        message: MCPMessage
    ) -> MCPMessage:
        """
        处理资源读取请求
        
        Args:
            connection: 客户端连接
            message: 请求消息
        
        Returns:
            MCPMessage: 资源读取响应
        """
        params = message.params or {}
        uri = params.get('uri')
        
        if not uri:
            return MCPMessage(
                id=message.id,
                error={
                    "code": -32602,
                    "message": "Invalid params",
                    "data": "Missing resource URI"
                }
            )
        
        if uri not in self.resource_handlers:
            return MCPMessage(
                id=message.id,
                error={
                    "code": -32601,
                    "message": "Resource not found",
                    "data": f"Resource '{uri}' is not registered"
                }
            )
        
        try:
            handler = self.resource_handlers[uri]
            result = await handler()
            
            self.stats['resources_read'] += 1
            
            return MCPMessage(
                id=message.id,
                result=result
            )
            
        except Exception as e:
            self.logger.error(f"资源读取异常 {uri}: {e}")
            return MCPMessage(
                id=message.id,
                error={
                    "code": -32603,
                    "message": "Resource read error",
                    "data": str(e)
                }
            )
    
    async def _handle_list_prompts(
        self,
        connection: MCPClientConnection,
        message: MCPMessage
    ) -> MCPMessage:
        """
        处理提示词列表请求
        
        Args:
            connection: 客户端连接
            message: 请求消息
        
        Returns:
            MCPMessage: 提示词列表响应
        """
        prompts_list = []
        for prompt in self.prompts.values():
            prompt_data = {
                "name": prompt.name,
                "description": prompt.description,
                "arguments": prompt.arguments
            }
            if prompt.metadata:
                prompt_data["metadata"] = prompt.metadata
            prompts_list.append(prompt_data)
        
        return MCPMessage(
            id=message.id,
            result={"prompts": prompts_list}
        )
    
    async def _handle_get_prompt(
        self,
        connection: MCPClientConnection,
        message: MCPMessage
    ) -> MCPMessage:
        """
        处理提示词获取请求
        
        Args:
            connection: 客户端连接
            message: 请求消息
        
        Returns:
            MCPMessage: 提示词获取响应
        """
        params = message.params or {}
        prompt_name = params.get('name')
        arguments = params.get('arguments', {})
        
        if not prompt_name:
            return MCPMessage(
                id=message.id,
                error={
                    "code": -32602,
                    "message": "Invalid params",
                    "data": "Missing prompt name"
                }
            )
        
        if prompt_name not in self.prompt_handlers:
            return MCPMessage(
                id=message.id,
                error={
                    "code": -32601,
                    "message": "Prompt not found",
                    "data": f"Prompt '{prompt_name}' is not registered"
                }
            )
        
        try:
            handler = self.prompt_handlers[prompt_name]
            result = await handler(arguments)
            
            self.stats['prompts_used'] += 1
            
            return MCPMessage(
                id=message.id,
                result=result
            )
            
        except Exception as e:
            self.logger.error(f"提示词获取异常 {prompt_name}: {e}")
            return MCPMessage(
                id=message.id,
                error={
                    "code": -32603,
                    "message": "Prompt execution error",
                    "data": str(e)
                }
            )
    
    async def _handle_ping(
        self,
        connection: MCPClientConnection,
        message: MCPMessage
    ) -> MCPMessage:
        """
        处理ping请求
        
        Args:
            connection: 客户端连接
            message: ping消息
        
        Returns:
            MCPMessage: pong响应
        """
        return MCPMessage(
            id=message.id,
            result={}
        )
    
    async def _handle_notification(
        self,
        connection: MCPClientConnection,
        message: MCPMessage
    ) -> None:
        """
        处理通知消息
        
        Args:
            connection: 客户端连接
            message: 通知消息
        """
        for handler in self.notification_handlers:
            try:
                await handler(connection, message.params or {})
            except Exception as e:
                self.logger.error(f"通知处理器异常: {e}")
    
    async def _send_message(
        self,
        connection: MCPClientConnection,
        message: MCPMessage
    ) -> None:
        """
        发送消息给客户端
        
        Args:
            connection: 客户端连接
            message: 消息对象
        """
        try:
            message_data = json.dumps(message.to_dict())
            
            if connection.transport == MCPTransportType.WEBSOCKET:
                if connection.websocket and not connection.websocket.closed:
                    await connection.websocket.send(message_data)
                    self.stats['messages_sent'] += 1
            
            if self.config.log_responses:
                self.logger.debug(f"发送消息 {connection.id}: {message.method or 'response'}")
                
        except Exception as e:
            self.logger.error(f"发送消息失败 {connection.id}: {e}")
            self.stats['errors'] += 1
            raise
    
    async def _close_connection(self, connection: MCPClientConnection) -> None:
        """
        关闭客户端连接
        
        Args:
            connection: 客户端连接
        """
        try:
            if connection.transport == MCPTransportType.WEBSOCKET:
                if connection.websocket and not connection.websocket.closed:
                    await connection.websocket.close()
            
            self.logger.info(f"连接已关闭: {connection.id}")
            
        except Exception as e:
            self.logger.error(f"关闭连接异常 {connection.id}: {e}")
    
    async def _heartbeat_loop(self) -> None:
        """
        心跳循环
        """
        try:
            while self.state == MCPServerState.RUNNING:
                await asyncio.sleep(self.config.heartbeat_interval)
                
                # 检查连接状态
                current_time = datetime.now()
                timeout_threshold = timedelta(seconds=self.config.request_timeout * 2)
                
                async with self.connection_lock:
                    expired_connections = []
                    for connection_id, connection in self.connections.items():
                        if current_time - connection.last_activity > timeout_threshold:
                            expired_connections.append(connection_id)
                    
                    # 清理过期连接
                    for connection_id in expired_connections:
                        connection = self.connections[connection_id]
                        await self._close_connection(connection)
                        del self.connections[connection_id]
                        self.stats['connections_active'] -= 1
                        self.logger.info(f"清理过期连接: {connection_id}")
                        
        except asyncio.CancelledError:
            pass
        except Exception as e:
            self.logger.error(f"心跳循环异常: {e}")
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取服务器统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        return {
            **self.stats,
            'server_state': self.state.value,
            'connections_active': len(self.connections),
            'tools_count': len(self.tools),
            'resources_count': len(self.resources),
            'prompts_count': len(self.prompts),
            'uptime': (
                (datetime.now() - self.stats['start_time']).total_seconds()
                if self.stats['start_time'] else 0
            )
        }
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.start()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.stop()