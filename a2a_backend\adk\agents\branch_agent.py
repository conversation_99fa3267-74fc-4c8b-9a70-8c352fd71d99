# -*- coding: utf-8 -*-
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""Branch agent implementation."""

from __future__ import annotations

from typing import AsyncGenerator, Dict, Any, List

from typing_extensions import override

from adk.agents.invocation_context import InvocationContext
from adk.events.event import Event
from .base_agent import BaseAgent


class BranchAgent(BaseAgent):
    """A shell agent that executes different branches based on conditions.
    
    This agent evaluates conditions and executes the appropriate branch
    of sub-agents based on the evaluation results.
    """
    
    def __init__(self, agent_id: str, name: str, description: str = "", 
                 config: Dict[str, Any] = None, user_id: str = None, **kwargs):
        """初始化分支智能体
        
        Args:
            agent_id: 智能体ID
            name: 智能体名称
            description: 智能体描述
            config: 配置信息
            user_id: 用户ID
        """
        super().__init__(**kwargs)
        self.agent_id = agent_id
        self.name = name
        self.description = description
        self.config = config or {}
        self.user_id = user_id
        self.branches: Dict[str, List[BaseAgent]] = {}
        self.conditions: Dict[str, callable] = {}
    
    def add_branch(self, condition_name: str, condition_func: callable, agents: List[BaseAgent]):
        """添加分支
        
        Args:
            condition_name: 条件名称
            condition_func: 条件评估函数
            agents: 该分支下的智能体列表
        """
        self.conditions[condition_name] = condition_func
        self.branches[condition_name] = agents
    
    def evaluate_conditions(self, context: Dict[str, Any]) -> str:
        """评估条件并返回匹配的分支名称
        
        Args:
            context: 上下文数据
            
        Returns:
            匹配的分支名称，如果没有匹配则返回'default'
        """
        for condition_name, condition_func in self.conditions.items():
            try:
                if condition_func(context):
                    return condition_name
            except Exception:
                continue
        return 'default'
    
    @override
    async def _run_async_impl(
        self, ctx: InvocationContext
    ) -> AsyncGenerator[Event, None]:
        """异步执行分支逻辑"""
        # 获取上下文数据
        context_data = getattr(ctx, 'data', {}) if hasattr(ctx, 'data') else {}
        
        # 评估条件选择分支
        selected_branch = self.evaluate_conditions(context_data)
        
        # 执行选中分支的智能体
        if selected_branch in self.branches:
            for agent in self.branches[selected_branch]:
                async for event in agent.run_async(ctx):
                    yield event
        elif 'default' in self.branches:
            for agent in self.branches['default']:
                async for event in agent.run_async(ctx):
                    yield event
    
    @override
    async def _run_live_impl(
        self, ctx: InvocationContext
    ) -> AsyncGenerator[Event, None]:
        """实时执行分支逻辑"""
        raise NotImplementedError('This is not supported yet for BranchAgent.')
        yield  # AsyncGenerator requires having at least one yield statement