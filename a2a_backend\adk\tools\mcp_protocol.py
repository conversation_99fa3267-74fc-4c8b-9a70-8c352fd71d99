#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A2A多智能体系统 MCP协议处理

定义MCP协议的核心数据结构和消息处理逻辑
"""

import json
import uuid
from typing import Dict, List, Any, Optional, Union, Literal
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime
import logging


class MCPVersion(Enum):
    """MCP协议版本枚举"""
    V2024_11_05 = "2024-11-05"
    V2024_10_07 = "2024-10-07"


class MCPErrorCode(Enum):
    """MCP错误代码枚举"""
    # JSON-RPC 2.0 标准错误代码
    PARSE_ERROR = -32700
    INVALID_REQUEST = -32600
    METHOD_NOT_FOUND = -32601
    INVALID_PARAMS = -32602
    INTERNAL_ERROR = -32603
    
    # MCP特定错误代码
    UNAUTHORIZED = -32001
    PROTOCOL_VERSION_MISMATCH = -32002
    TOOL_NOT_FOUND = -32004
    RESOURCE_NOT_FOUND = -32005
    PROMPT_NOT_FOUND = -32006
    TOOL_EXECUTION_ERROR = -32007
    RESOURCE_READ_ERROR = -32008
    PROMPT_EXECUTION_ERROR = -32009
    RATE_LIMIT_EXCEEDED = -32010
    PERMISSION_DENIED = -32011
    VALIDATION_ERROR = -32012
    TIMEOUT_ERROR = -32013
    CONNECTION_ERROR = -32014
    CAPABILITY_NOT_SUPPORTED = -32015


@dataclass
class MCPError:
    """MCP错误信息"""
    code: int
    message: str
    data: Optional[Any] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        result = {
            "code": self.code,
            "message": self.message
        }
        if self.data is not None:
            result["data"] = self.data
        return result
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'MCPError':
        """从字典创建错误对象"""
        return cls(
            code=data["code"],
            message=data["message"],
            data=data.get("data")
        )
    
    @classmethod
    def parse_error(cls, details: Optional[str] = None) -> 'MCPError':
        """创建解析错误"""
        return cls(
            code=MCPErrorCode.PARSE_ERROR.value,
            message="Parse error",
            data=details
        )
    
    @classmethod
    def invalid_request(cls, details: Optional[str] = None) -> 'MCPError':
        """创建无效请求错误"""
        return cls(
            code=MCPErrorCode.INVALID_REQUEST.value,
            message="Invalid Request",
            data=details
        )
    
    @classmethod
    def method_not_found(cls, method: Optional[str] = None) -> 'MCPError':
        """创建方法未找到错误"""
        return cls(
            code=MCPErrorCode.METHOD_NOT_FOUND.value,
            message="Method not found",
            data=f"Method '{method}' not found" if method else None
        )
    
    @classmethod
    def invalid_params(cls, details: Optional[str] = None) -> 'MCPError':
        """创建无效参数错误"""
        return cls(
            code=MCPErrorCode.INVALID_PARAMS.value,
            message="Invalid params",
            data=details
        )
    
    @classmethod
    def internal_error(cls, details: Optional[str] = None) -> 'MCPError':
        """创建内部错误"""
        return cls(
            code=MCPErrorCode.INTERNAL_ERROR.value,
            message="Internal error",
            data=details
        )
    
    @classmethod
    def unauthorized(cls, details: Optional[str] = None) -> 'MCPError':
        """创建未授权错误"""
        return cls(
            code=MCPErrorCode.UNAUTHORIZED.value,
            message="Unauthorized",
            data=details
        )
    
    @classmethod
    def tool_not_found(cls, tool_name: Optional[str] = None) -> 'MCPError':
        """创建工具未找到错误"""
        return cls(
            code=MCPErrorCode.TOOL_NOT_FOUND.value,
            message="Tool not found",
            data=f"Tool '{tool_name}' not found" if tool_name else None
        )
    
    @classmethod
    def resource_not_found(cls, uri: Optional[str] = None) -> 'MCPError':
        """创建资源未找到错误"""
        return cls(
            code=MCPErrorCode.RESOURCE_NOT_FOUND.value,
            message="Resource not found",
            data=f"Resource '{uri}' not found" if uri else None
        )
    
    @classmethod
    def prompt_not_found(cls, prompt_name: Optional[str] = None) -> 'MCPError':
        """创建提示词未找到错误"""
        return cls(
            code=MCPErrorCode.PROMPT_NOT_FOUND.value,
            message="Prompt not found",
            data=f"Prompt '{prompt_name}' not found" if prompt_name else None
        )


@dataclass
class MCPCapabilities:
    """MCP能力声明"""
    # 工具能力
    tools: bool = False
    list_changed: bool = False
    
    # 资源能力
    resources: bool = False
    subscribe: bool = False
    
    # 提示词能力
    prompts: bool = False
    
    # 日志能力
    logging: bool = False
    
    # 采样能力
    sampling: bool = False
    
    # 实验性能力
    experimental: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        result = {}
        
        if self.tools or self.list_changed:
            result["tools"] = {}
            if self.list_changed:
                result["tools"]["listChanged"] = True
        
        if self.resources or self.subscribe:
            result["resources"] = {}
            if self.subscribe:
                result["resources"]["subscribe"] = True
        
        if self.prompts:
            result["prompts"] = {}
        
        if self.logging:
            result["logging"] = {}
        
        if self.sampling:
            result["sampling"] = {}
        
        if self.experimental:
            result["experimental"] = self.experimental
        
        return result
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'MCPCapabilities':
        """从字典创建能力对象"""
        return cls(
            tools="tools" in data,
            list_changed=data.get("tools", {}).get("listChanged", False),
            resources="resources" in data,
            subscribe=data.get("resources", {}).get("subscribe", False),
            prompts="prompts" in data,
            logging="logging" in data,
            sampling="sampling" in data,
            experimental=data.get("experimental", {})
        )


@dataclass
class MCPClientInfo:
    """MCP客户端信息"""
    name: str
    version: str
    description: Optional[str] = None
    homepage: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        result = {
            "name": self.name,
            "version": self.version
        }
        if self.description:
            result["description"] = self.description
        if self.homepage:
            result["homepage"] = self.homepage
        return result
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'MCPClientInfo':
        """从字典创建客户端信息"""
        return cls(
            name=data["name"],
            version=data["version"],
            description=data.get("description"),
            homepage=data.get("homepage")
        )


@dataclass
class MCPServerInfo:
    """MCP服务器信息"""
    name: str
    version: str
    description: Optional[str] = None
    homepage: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        result = {
            "name": self.name,
            "version": self.version
        }
        if self.description:
            result["description"] = self.description
        if self.homepage:
            result["homepage"] = self.homepage
        return result
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'MCPServerInfo':
        """从字典创建服务器信息"""
        return cls(
            name=data["name"],
            version=data["version"],
            description=data.get("description"),
            homepage=data.get("homepage")
        )


@dataclass
class MCPToolSchema:
    """MCP工具模式定义"""
    type: str = "object"
    properties: Dict[str, Any] = field(default_factory=dict)
    required: List[str] = field(default_factory=list)
    additionalProperties: bool = False
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        result = {
            "type": self.type,
            "properties": self.properties
        }
        if self.required:
            result["required"] = self.required
        if not self.additionalProperties:
            result["additionalProperties"] = self.additionalProperties
        return result
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'MCPToolSchema':
        """从字典创建工具模式"""
        return cls(
            type=data.get("type", "object"),
            properties=data.get("properties", {}),
            required=data.get("required", []),
            additionalProperties=data.get("additionalProperties", False)
        )


@dataclass
class MCPTool:
    """MCP工具定义"""
    name: str
    description: str
    inputSchema: MCPToolSchema
    outputSchema: Optional[MCPToolSchema] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        result = {
            "name": self.name,
            "description": self.description,
            "inputSchema": self.inputSchema.to_dict()
        }
        if self.outputSchema:
            result["outputSchema"] = self.outputSchema.to_dict()
        if self.metadata:
            result["metadata"] = self.metadata
        return result
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'MCPTool':
        """从字典创建工具定义"""
        return cls(
            name=data["name"],
            description=data["description"],
            inputSchema=MCPToolSchema.from_dict(data["inputSchema"]),
            outputSchema=(
                MCPToolSchema.from_dict(data["outputSchema"])
                if "outputSchema" in data else None
            ),
            metadata=data.get("metadata", {})
        )


@dataclass
class MCPResourceTemplate:
    """MCP资源模板"""
    uriTemplate: str
    name: str
    description: str
    mimeType: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        result = {
            "uriTemplate": self.uriTemplate,
            "name": self.name,
            "description": self.description
        }
        if self.mimeType:
            result["mimeType"] = self.mimeType
        if self.metadata:
            result["metadata"] = self.metadata
        return result
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'MCPResourceTemplate':
        """从字典创建资源模板"""
        return cls(
            uriTemplate=data["uriTemplate"],
            name=data["name"],
            description=data["description"],
            mimeType=data.get("mimeType"),
            metadata=data.get("metadata", {})
        )


@dataclass
class MCPResourceContent:
    """MCP资源内容"""
    uri: str
    mimeType: Optional[str] = None
    text: Optional[str] = None
    blob: Optional[str] = None  # Base64编码的二进制数据
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        result = {"uri": self.uri}
        
        if self.mimeType:
            result["mimeType"] = self.mimeType
        if self.text is not None:
            result["text"] = self.text
        if self.blob is not None:
            result["blob"] = self.blob
        if self.metadata:
            result["metadata"] = self.metadata
        
        return result
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'MCPResourceContent':
        """从字典创建资源内容"""
        return cls(
            uri=data["uri"],
            mimeType=data.get("mimeType"),
            text=data.get("text"),
            blob=data.get("blob"),
            metadata=data.get("metadata", {})
        )


@dataclass
class MCPPromptArgument:
    """MCP提示词参数"""
    name: str
    description: str
    required: bool = False
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        result = {
            "name": self.name,
            "description": self.description
        }
        if self.required:
            result["required"] = self.required
        return result
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'MCPPromptArgument':
        """从字典创建提示词参数"""
        return cls(
            name=data["name"],
            description=data["description"],
            required=data.get("required", False)
        )


@dataclass
class MCPPromptMessage:
    """MCP提示词消息"""
    role: Literal["user", "assistant", "system"]
    content: Union[str, Dict[str, Any]]
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "role": self.role,
            "content": self.content
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'MCPPromptMessage':
        """从字典创建提示词消息"""
        return cls(
            role=data["role"],
            content=data["content"]
        )


@dataclass
class MCPPrompt:
    """MCP提示词定义"""
    name: str
    description: str
    arguments: List[MCPPromptArgument] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        result = {
            "name": self.name,
            "description": self.description
        }
        if self.arguments:
            result["arguments"] = [arg.to_dict() for arg in self.arguments]
        if self.metadata:
            result["metadata"] = self.metadata
        return result
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'MCPPrompt':
        """从字典创建提示词定义"""
        return cls(
            name=data["name"],
            description=data["description"],
            arguments=[
                MCPPromptArgument.from_dict(arg)
                for arg in data.get("arguments", [])
            ],
            metadata=data.get("metadata", {})
        )


@dataclass
class MCPLogLevel(Enum):
    """MCP日志级别"""
    DEBUG = "debug"
    INFO = "info"
    NOTICE = "notice"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"
    ALERT = "alert"
    EMERGENCY = "emergency"


@dataclass
class MCPLogEntry:
    """MCP日志条目"""
    level: MCPLogLevel
    data: Any
    logger: Optional[str] = None
    timestamp: Optional[datetime] = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        result = {
            "level": self.level.value,
            "data": self.data
        }
        if self.logger:
            result["logger"] = self.logger
        if self.timestamp:
            result["timestamp"] = self.timestamp.isoformat()
        return result
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'MCPLogEntry':
        """从字典创建日志条目"""
        timestamp = None
        if "timestamp" in data:
            timestamp = datetime.fromisoformat(data["timestamp"])
        
        return cls(
            level=MCPLogLevel(data["level"]),
            data=data["data"],
            logger=data.get("logger"),
            timestamp=timestamp
        )


class MCPMessageValidator:
    """MCP消息验证器"""
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def validate_message(self, data: Dict[str, Any]) -> Optional[MCPError]:
        """
        验证MCP消息格式
        
        Args:
            data: 消息数据
        
        Returns:
            Optional[MCPError]: 验证错误，如果验证通过则返回None
        """
        # 检查JSON-RPC版本
        if "jsonrpc" not in data or data["jsonrpc"] != "2.0":
            return MCPError.invalid_request("Missing or invalid jsonrpc version")
        
        # 检查消息类型
        has_method = "method" in data
        has_id = "id" in data
        has_result = "result" in data
        has_error = "error" in data
        
        # 请求消息
        if has_method and has_id:
            return self._validate_request(data)
        
        # 通知消息
        elif has_method and not has_id:
            return self._validate_notification(data)
        
        # 响应消息
        elif has_id and (has_result or has_error):
            return self._validate_response(data)
        
        else:
            return MCPError.invalid_request("Invalid message structure")
    
    def _validate_request(self, data: Dict[str, Any]) -> Optional[MCPError]:
        """
        验证请求消息
        
        Args:
            data: 消息数据
        
        Returns:
            Optional[MCPError]: 验证错误
        """
        method = data.get("method")
        if not isinstance(method, str):
            return MCPError.invalid_request("Method must be a string")
        
        # 验证参数
        if "params" in data:
            params = data["params"]
            if not isinstance(params, (dict, list)):
                return MCPError.invalid_params("Params must be an object or array")
        
        return None
    
    def _validate_notification(self, data: Dict[str, Any]) -> Optional[MCPError]:
        """
        验证通知消息
        
        Args:
            data: 消息数据
        
        Returns:
            Optional[MCPError]: 验证错误
        """
        return self._validate_request(data)
    
    def _validate_response(self, data: Dict[str, Any]) -> Optional[MCPError]:
        """
        验证响应消息
        
        Args:
            data: 消息数据
        
        Returns:
            Optional[MCPError]: 验证错误
        """
        has_result = "result" in data
        has_error = "error" in data
        
        # 响应必须有result或error，但不能同时有
        if has_result and has_error:
            return MCPError.invalid_request("Response cannot have both result and error")
        
        if not has_result and not has_error:
            return MCPError.invalid_request("Response must have either result or error")
        
        # 验证错误格式
        if has_error:
            error = data["error"]
            if not isinstance(error, dict):
                return MCPError.invalid_request("Error must be an object")
            
            if "code" not in error or "message" not in error:
                return MCPError.invalid_request("Error must have code and message")
            
            if not isinstance(error["code"], int):
                return MCPError.invalid_request("Error code must be an integer")
            
            if not isinstance(error["message"], str):
                return MCPError.invalid_request("Error message must be a string")
        
        return None
    
    def validate_initialize_params(self, params: Dict[str, Any]) -> Optional[MCPError]:
        """
        验证初始化参数
        
        Args:
            params: 初始化参数
        
        Returns:
            Optional[MCPError]: 验证错误
        """
        # 检查协议版本
        if "protocolVersion" not in params:
            return MCPError.invalid_params("Missing protocolVersion")
        
        protocol_version = params["protocolVersion"]
        if not isinstance(protocol_version, str):
            return MCPError.invalid_params("protocolVersion must be a string")
        
        # 检查能力声明
        if "capabilities" in params:
            capabilities = params["capabilities"]
            if not isinstance(capabilities, dict):
                return MCPError.invalid_params("capabilities must be an object")
        
        # 检查客户端信息
        if "clientInfo" in params:
            client_info = params["clientInfo"]
            if not isinstance(client_info, dict):
                return MCPError.invalid_params("clientInfo must be an object")
            
            if "name" not in client_info or "version" not in client_info:
                return MCPError.invalid_params("clientInfo must have name and version")
        
        return None
    
    def validate_tool_call_params(self, params: Dict[str, Any]) -> Optional[MCPError]:
        """
        验证工具调用参数
        
        Args:
            params: 工具调用参数
        
        Returns:
            Optional[MCPError]: 验证错误
        """
        if "name" not in params:
            return MCPError.invalid_params("Missing tool name")
        
        name = params["name"]
        if not isinstance(name, str):
            return MCPError.invalid_params("Tool name must be a string")
        
        if "arguments" in params:
            arguments = params["arguments"]
            if not isinstance(arguments, dict):
                return MCPError.invalid_params("Tool arguments must be an object")
        
        return None
    
    def validate_resource_read_params(self, params: Dict[str, Any]) -> Optional[MCPError]:
        """
        验证资源读取参数
        
        Args:
            params: 资源读取参数
        
        Returns:
            Optional[MCPError]: 验证错误
        """
        if "uri" not in params:
            return MCPError.invalid_params("Missing resource URI")
        
        uri = params["uri"]
        if not isinstance(uri, str):
            return MCPError.invalid_params("Resource URI must be a string")
        
        return None
    
    def validate_prompt_get_params(self, params: Dict[str, Any]) -> Optional[MCPError]:
        """
        验证提示词获取参数
        
        Args:
            params: 提示词获取参数
        
        Returns:
            Optional[MCPError]: 验证错误
        """
        if "name" not in params:
            return MCPError.invalid_params("Missing prompt name")
        
        name = params["name"]
        if not isinstance(name, str):
            return MCPError.invalid_params("Prompt name must be a string")
        
        if "arguments" in params:
            arguments = params["arguments"]
            if not isinstance(arguments, dict):
                return MCPError.invalid_params("Prompt arguments must be an object")
        
        return None


class MCPMessageBuilder:
    """MCP消息构建器"""
    
    @staticmethod
    def create_request(
        method: str,
        params: Optional[Dict[str, Any]] = None,
        request_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        创建请求消息
        
        Args:
            method: 方法名
            params: 参数
            request_id: 请求ID
        
        Returns:
            Dict[str, Any]: 请求消息
        """
        message = {
            "jsonrpc": "2.0",
            "method": method,
            "id": request_id or str(uuid.uuid4())
        }
        
        if params is not None:
            message["params"] = params
        
        return message
    
    @staticmethod
    def create_notification(
        method: str,
        params: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        创建通知消息
        
        Args:
            method: 方法名
            params: 参数
        
        Returns:
            Dict[str, Any]: 通知消息
        """
        message = {
            "jsonrpc": "2.0",
            "method": method
        }
        
        if params is not None:
            message["params"] = params
        
        return message
    
    @staticmethod
    def create_success_response(
        request_id: str,
        result: Any
    ) -> Dict[str, Any]:
        """
        创建成功响应消息
        
        Args:
            request_id: 请求ID
            result: 结果
        
        Returns:
            Dict[str, Any]: 成功响应消息
        """
        return {
            "jsonrpc": "2.0",
            "id": request_id,
            "result": result
        }
    
    @staticmethod
    def create_error_response(
        request_id: Optional[str],
        error: MCPError
    ) -> Dict[str, Any]:
        """
        创建错误响应消息
        
        Args:
            request_id: 请求ID
            error: 错误信息
        
        Returns:
            Dict[str, Any]: 错误响应消息
        """
        return {
            "jsonrpc": "2.0",
            "id": request_id,
            "error": error.to_dict()
        }
    
    @staticmethod
    def create_initialize_request(
        protocol_version: str,
        capabilities: MCPCapabilities,
        client_info: MCPClientInfo
    ) -> Dict[str, Any]:
        """
        创建初始化请求
        
        Args:
            protocol_version: 协议版本
            capabilities: 能力声明
            client_info: 客户端信息
        
        Returns:
            Dict[str, Any]: 初始化请求消息
        """
        return MCPMessageBuilder.create_request(
            "initialize",
            {
                "protocolVersion": protocol_version,
                "capabilities": capabilities.to_dict(),
                "clientInfo": client_info.to_dict()
            }
        )
    
    @staticmethod
    def create_initialize_response(
        request_id: str,
        protocol_version: str,
        capabilities: MCPCapabilities,
        server_info: MCPServerInfo
    ) -> Dict[str, Any]:
        """
        创建初始化响应
        
        Args:
            request_id: 请求ID
            protocol_version: 协议版本
            capabilities: 能力声明
            server_info: 服务器信息
        
        Returns:
            Dict[str, Any]: 初始化响应消息
        """
        return MCPMessageBuilder.create_success_response(
            request_id,
            {
                "protocolVersion": protocol_version,
                "capabilities": capabilities.to_dict(),
                "serverInfo": server_info.to_dict()
            }
        )
    
    @staticmethod
    def create_tools_list_response(
        request_id: str,
        tools: List[MCPTool]
    ) -> Dict[str, Any]:
        """
        创建工具列表响应
        
        Args:
            request_id: 请求ID
            tools: 工具列表
        
        Returns:
            Dict[str, Any]: 工具列表响应消息
        """
        return MCPMessageBuilder.create_success_response(
            request_id,
            {
                "tools": [tool.to_dict() for tool in tools]
            }
        )
    
    @staticmethod
    def create_resources_list_response(
        request_id: str,
        resources: List[MCPResourceTemplate]
    ) -> Dict[str, Any]:
        """
        创建资源列表响应
        
        Args:
            request_id: 请求ID
            resources: 资源列表
        
        Returns:
            Dict[str, Any]: 资源列表响应消息
        """
        return MCPMessageBuilder.create_success_response(
            request_id,
            {
                "resources": [resource.to_dict() for resource in resources]
            }
        )
    
    @staticmethod
    def create_prompts_list_response(
        request_id: str,
        prompts: List[MCPPrompt]
    ) -> Dict[str, Any]:
        """
        创建提示词列表响应
        
        Args:
            request_id: 请求ID
            prompts: 提示词列表
        
        Returns:
            Dict[str, Any]: 提示词列表响应消息
        """
        return MCPMessageBuilder.create_success_response(
            request_id,
            {
                "prompts": [prompt.to_dict() for prompt in prompts]
            }
        )