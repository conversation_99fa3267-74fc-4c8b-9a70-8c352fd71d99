#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A2A多智能体系统 ADK Runner工厂

根据配置和用户权限创建不同类型的Runner
"""

import logging
from typing import Dict, List, Optional, Any, Union, Type, TypeVar
from enum import Enum

from google.adk.agents.llm_agent import Agent

from app.models.user import User
from app.models.agent import Agent as AgentModel
from app.models.workflow import Workflow
from app.models.config import AgentConfig, ConfigTemplate
from app.core.logging import get_logger
from app.auth.permissions import check_user_permission

from .base_runner import BaseRunner
from .agent_runner import AgentRunner
from .workflow_runner import WorkflowRunner
from ..services.database_session_service import DatabaseSessionService
from ..services.database_memory_service import DatabaseMemoryService
from ..services.database_artifact_service import DatabaseArtifactService
from ..agents.agent_factory import AgentFactory

# 创建类型变量用于子类继承
T = TypeVar('T', bound=BaseRunner)

class RunnerType(Enum):
    """
    Runner类型枚举
    """
    AGENT = "agent"
    WORKFLOW = "workflow"
    CUSTOM = "custom"

class RunnerFactory:
    """
    Runner工厂，根据配置和用户权限创建不同类型的Runner
    
    提供以下功能：
    1. 根据类型创建不同的Runner
    2. 配置验证和处理
    3. 用户权限验证
    4. 依赖注入
    5. 错误处理和日志记录
    """
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        """
        初始化Runner工厂
        
        Args:
            logger: 日志记录器
        """
        self.logger = logger or setup_logger("runner_factory")
        
        # 注册Runner类型映射
        self.runner_classes: Dict[RunnerType, Type[BaseRunner]] = {
            RunnerType.AGENT: AgentRunner,
            RunnerType.WORKFLOW: WorkflowRunner
        }
        
        # 智能体工厂
        self.agent_factory = AgentFactory()
        
        self.logger.info("Runner工厂已初始化")
    
    def register_runner_class(self, runner_type: RunnerType, runner_class: Type[BaseRunner]) -> None:
        """
        注册自定义Runner类
        
        Args:
            runner_type: Runner类型
            runner_class: Runner类
        """
        self.runner_classes[runner_type] = runner_class
        self.logger.info(f"已注册Runner类: {runner_type.value} -> {runner_class.__name__}")
    
    async def _check_user_permission(
        self, 
        user_id: int, 
        owner_id: int, 
        resource_type: str, 
        resource_id: Optional[Union[int, str]] = None
    ) -> bool:
        """
        检查用户权限
        
        Args:
            user_id: 用户ID
            owner_id: 拥有者ID
            resource_type: 资源类型
            resource_id: 资源ID
            
        Returns:
            bool: 是否有权限
        """
        try:
            # 检查用户是否存在
            user = await User.get_by_id(user_id)
            if not user:
                self.logger.error(f"用户不存在: {user_id}")
                return False
            
            # 检查用户是否有权限访问此资源
            has_permission = await check_user_permission(
                user_id=user_id,
                owner_id=owner_id,
                resource_type=resource_type,
                action="create",
                resource_id=str(resource_id) if resource_id else None
            )
            
            if not has_permission:
                self.logger.error(f"用户 {user_id} 没有权限创建 {resource_type} Runner")
            
            return has_permission
        except Exception as e:
            self.logger.error(f"权限检查错误: {str(e)}")
            return False
    
    async def _validate_config(self, config: Dict[str, Any], runner_type: RunnerType) -> Dict[str, Any]:
        """
        验证和处理配置
        
        Args:
            config: 原始配置
            runner_type: Runner类型
            
        Returns:
            Dict[str, Any]: 验证后的配置
        """
        validated_config = config.copy()
        
        # 根据Runner类型进行特定验证
        if runner_type == RunnerType.AGENT:
            # 验证智能体配置
            if "agent_id" not in validated_config:
                raise ValueError("智能体Runner需要agent_id配置")
        elif runner_type == RunnerType.WORKFLOW:
            # 验证工作流配置
            if "workflow_id" not in validated_config:
                raise ValueError("工作流Runner需要workflow_id配置")
        
        # 设置默认值
        validated_config.setdefault("timeout", 3600)  # 默认1小时超时
        validated_config.setdefault("max_retries", 3)  # 默认最大重试3次
        validated_config.setdefault("stream", False)  # 默认不使用流式输出
        
        return validated_config
    
    async def _create_services(
        self, 
        user_id: int, 
        owner_id: int, 
        config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        创建服务实例
        
        Args:
            user_id: 用户ID
            owner_id: 拥有者ID
            config: 配置
            
        Returns:
            Dict[str, Any]: 服务实例字典
        """
        services = {}
        
        # 创建会话服务
        if config.get("use_database_session", True):
            services["session"] = DatabaseSessionService(user_id=user_id, owner_id=owner_id)
        
        # 创建内存服务
        if config.get("use_database_memory", True):
            services["memory_service"] = DatabaseMemoryService(user_id=user_id, owner_id=owner_id)
        
        # 创建工件服务
        if config.get("use_database_artifact", True):
            services["artifact_service"] = DatabaseArtifactService(user_id=user_id, owner_id=owner_id)
        
        return services
    
    async def _create_agent_instance(self, agent_id: int, user_id: int, owner_id: int) -> Optional[Agent]:
        """
        创建ADK Agent实例
        
        Args:
            agent_id: 智能体ID
            user_id: 用户ID
            owner_id: 拥有者ID
            
        Returns:
            Optional[Agent]: ADK Agent实例
        """
        try:
            # 加载智能体模型
            agent_model = await AgentModel.get_by_id(agent_id)
            if not agent_model:
                self.logger.error(f"智能体不存在: {agent_id}")
                return None
            
            # 检查用户是否有权限访问此智能体
            if agent_model.user_id != user_id and agent_model.owner_id != owner_id and not agent_model.is_public:
                self.logger.error(f"用户 {user_id} 没有权限访问智能体 {agent_id}")
                return None
            
            # 使用智能体工厂创建ADK Agent实例
            agent_instance = await self.agent_factory.create_adk_agent(
                agent_model=agent_model,
                user_id=user_id,
                owner_id=owner_id
            )
            
            return agent_instance
        except Exception as e:
            self.logger.error(f"创建智能体实例错误: {str(e)}")
            return None
    
    async def _create_workflow_instance(self, workflow_id: int, user_id: int, owner_id: int) -> Optional[Agent]:
        """
        创建ADK工作流实例
        
        Args:
            workflow_id: 工作流ID
            user_id: 用户ID
            owner_id: 拥有者ID
            
        Returns:
            Optional[Agent]: ADK工作流实例
        """
        try:
            # 加载工作流模型
            workflow_model = await Workflow.get_by_id(workflow_id)
            if not workflow_model:
                self.logger.error(f"工作流不存在: {workflow_id}")
                return None
            
            # 检查用户是否有权限访问此工作流
            if workflow_model.user_id != user_id and workflow_model.owner_id != owner_id and not workflow_model.is_public:
                self.logger.error(f"用户 {user_id} 没有权限访问工作流 {workflow_id}")
                return None
            
            # 这里需要根据工作流配置创建ADK Agent实例
            # 暂时返回None，在实际使用时需要实现
            # workflow_instance = await self._build_workflow_agent(workflow_model)
            workflow_instance = None
            
            return workflow_instance
        except Exception as e:
            self.logger.error(f"创建工作流实例错误: {str(e)}")
            return None
    
    async def create_runner(
        self, 
        runner_type: Union[RunnerType, str], 
        user_id: int, 
        owner_id: int, 
        config: Dict[str, Any]
    ) -> Optional[BaseRunner]:
        """
        创建Runner实例
        
        Args:
            runner_type: Runner类型
            user_id: 用户ID
            owner_id: 拥有者ID
            config: 配置
            
        Returns:
            Optional[BaseRunner]: Runner实例
        """
        try:
            # 转换Runner类型
            if isinstance(runner_type, str):
                runner_type = RunnerType(runner_type)
            
            # 验证配置
            validated_config = await self._validate_config(config, runner_type)
            
            # 检查用户权限
            resource_id = None
            if runner_type == RunnerType.AGENT:
                resource_id = validated_config.get("agent_id")
            elif runner_type == RunnerType.WORKFLOW:
                resource_id = validated_config.get("workflow_id")
            
            has_permission = await self._check_user_permission(
                user_id=user_id,
                owner_id=owner_id,
                resource_type=runner_type.value,
                resource_id=resource_id
            )
            if not has_permission:
                raise PermissionError(f"用户 {user_id} 没有权限创建 {runner_type.value} Runner")
            
            # 创建服务实例
            services = await self._create_services(user_id, owner_id, validated_config)
            
            # 获取Runner类
            runner_class = self.runner_classes.get(runner_type)
            if not runner_class:
                raise ValueError(f"不支持的Runner类型: {runner_type.value}")
            
            # 根据Runner类型创建特定实例
            if runner_type == RunnerType.AGENT:
                return await self._create_agent_runner(
                    runner_class=runner_class,
                    user_id=user_id,
                    owner_id=owner_id,
                    config=validated_config,
                    services=services
                )
            elif runner_type == RunnerType.WORKFLOW:
                return await self._create_workflow_runner(
                    runner_class=runner_class,
                    user_id=user_id,
                    owner_id=owner_id,
                    config=validated_config,
                    services=services
                )
            else:
                # 自定义Runner类型
                return await self._create_custom_runner(
                    runner_class=runner_class,
                    user_id=user_id,
                    owner_id=owner_id,
                    config=validated_config,
                    services=services
                )
        except Exception as e:
            self.logger.error(f"创建Runner错误: {str(e)}")
            raise e
    
    async def _create_agent_runner(
        self, 
        runner_class: Type[BaseRunner], 
        user_id: int, 
        owner_id: int, 
        config: Dict[str, Any], 
        services: Dict[str, Any]
    ) -> Optional[AgentRunner]:
        """
        创建智能体Runner
        
        Args:
            runner_class: Runner类
            user_id: 用户ID
            owner_id: 拥有者ID
            config: 配置
            services: 服务实例
            
        Returns:
            Optional[AgentRunner]: 智能体Runner实例
        """
        agent_id = config["agent_id"]
        
        # 创建ADK Agent实例
        agent_instance = await self._create_agent_instance(agent_id, user_id, owner_id)
        if not agent_instance:
            raise ValueError(f"无法创建智能体实例: {agent_id}")
        
        # 创建智能体Runner
        runner = await runner_class.create(
            user_id=user_id,
            owner_id=owner_id,
            agent_instance=agent_instance,
            agent_id=agent_id,
            agent_config=config.get("agent_config", {}),
            task_id=config.get("task_id"),
            execution_id=config.get("execution_id"),
            config=config,
            **services
        )
        
        self.logger.info(f"已创建智能体Runner: {runner.execution_id}, 智能体ID: {agent_id}")
        return runner
    
    async def _create_workflow_runner(
        self, 
        runner_class: Type[BaseRunner], 
        user_id: int, 
        owner_id: int, 
        config: Dict[str, Any], 
        services: Dict[str, Any]
    ) -> Optional[WorkflowRunner]:
        """
        创建工作流Runner
        
        Args:
            runner_class: Runner类
            user_id: 用户ID
            owner_id: 拥有者ID
            config: 配置
            services: 服务实例
            
        Returns:
            Optional[WorkflowRunner]: 工作流Runner实例
        """
        workflow_id = config["workflow_id"]
        
        # 创建ADK工作流实例
        workflow_instance = await self._create_workflow_instance(workflow_id, user_id, owner_id)
        # 注意：workflow_instance可能为None，WorkflowRunner会处理这种情况
        
        # 创建工作流Runner
        runner = await runner_class.create(
            user_id=user_id,
            owner_id=owner_id,
            workflow_id=workflow_id,
            workflow_instance=workflow_instance,
            workflow_config=config.get("workflow_config", {}),
            task_id=config.get("task_id"),
            execution_id=config.get("execution_id"),
            config=config,
            **services
        )
        
        self.logger.info(f"已创建工作流Runner: {runner.execution_id}, 工作流ID: {workflow_id}")
        return runner
    
    async def _create_custom_runner(
        self, 
        runner_class: Type[BaseRunner], 
        user_id: int, 
        owner_id: int, 
        config: Dict[str, Any], 
        services: Dict[str, Any]
    ) -> Optional[BaseRunner]:
        """
        创建自定义Runner
        
        Args:
            runner_class: Runner类
            user_id: 用户ID
            owner_id: 拥有者ID
            config: 配置
            services: 服务实例
            
        Returns:
            Optional[BaseRunner]: 自定义Runner实例
        """
        # 自定义Runner需要提供agent_instance
        agent_instance = config.get("agent_instance")
        if not agent_instance:
            raise ValueError("自定义Runner需要提供agent_instance")
        
        # 创建自定义Runner
        runner = await runner_class.create(
            user_id=user_id,
            owner_id=owner_id,
            agent_instance=agent_instance,
            task_id=config.get("task_id"),
            execution_id=config.get("execution_id"),
            config=config,
            **services
        )
        
        self.logger.info(f"已创建自定义Runner: {runner.execution_id}")
        return runner
    
    async def create_agent_runner(
        self, 
        user_id: int, 
        owner_id: int, 
        agent_id: int, 
        **kwargs
    ) -> Optional[AgentRunner]:
        """
        创建智能体Runner的便捷方法
        
        Args:
            user_id: 用户ID
            owner_id: 拥有者ID
            agent_id: 智能体ID
            **kwargs: 其他配置
            
        Returns:
            Optional[AgentRunner]: 智能体Runner实例
        """
        config = {"agent_id": agent_id, **kwargs}
        return await self.create_runner(RunnerType.AGENT, user_id, owner_id, config)
    
    async def create_workflow_runner(
        self, 
        user_id: int, 
        owner_id: int, 
        workflow_id: int, 
        **kwargs
    ) -> Optional[WorkflowRunner]:
        """
        创建工作流Runner的便捷方法
        
        Args:
            user_id: 用户ID
            owner_id: 拥有者ID
            workflow_id: 工作流ID
            **kwargs: 其他配置
            
        Returns:
            Optional[WorkflowRunner]: 工作流Runner实例
        """
        config = {"workflow_id": workflow_id, **kwargs}
        return await self.create_runner(RunnerType.WORKFLOW, user_id, owner_id, config)