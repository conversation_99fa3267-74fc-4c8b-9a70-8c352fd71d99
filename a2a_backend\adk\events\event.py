# -*- coding: utf-8 -*-
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""Simplified event implementation."""

from __future__ import annotations

from datetime import datetime
import random
import string
from typing import Optional, Dict, Any, List

from pydantic import BaseModel, Field
from pydantic import ConfigDict


class EventActions(BaseModel):
    """Actions that can be taken by an agent in an event."""
    
    escalate: bool = False
    """Whether to escalate to another agent."""
    
    skip_summarization: bool = False
    """Whether to skip summarization."""
    
    transfer_to: Optional[str] = None
    """Agent to transfer to."""
    
    end_conversation: bool = False
    """Whether to end the conversation."""


class Event(BaseModel):
    """Represents an event in a conversation between agents and users.
    
    This is a simplified version that contains only the essential
    functionality needed for the A2A backend system.
    
    Attributes:
        invocation_id: The invocation ID of the event.
        author: "user" or the name of the agent.
        content: The content of the event.
        actions: The actions taken by the agent.
        branch: The branch of the event.
        id: The unique identifier of the event.
        timestamp: The timestamp of the event.
        data: Additional data associated with the event.
    """
    
    model_config = ConfigDict(
        extra='allow',
        arbitrary_types_allowed=True,
    )
    
    invocation_id: str = ''
    """The invocation ID of the event."""
    
    author: str
    """'user' or the name of the agent."""
    
    content: Optional[str] = None
    """The text content of the event."""
    
    actions: EventActions = Field(default_factory=EventActions)
    """The actions taken by the agent."""
    
    branch: Optional[str] = None
    """The branch of the event."""
    
    id: str = ''
    """The unique identifier of the event."""
    
    timestamp: float = Field(default_factory=lambda: datetime.now().timestamp())
    """The timestamp of the event."""
    
    data: Dict[str, Any] = Field(default_factory=dict)
    """Additional data associated with the event."""
    
    function_calls: List[Dict[str, Any]] = Field(default_factory=list)
    """Function calls made in this event."""
    
    function_responses: List[Dict[str, Any]] = Field(default_factory=list)
    """Function responses received in this event."""
    
    partial: bool = False
    """Whether this is a partial event."""
    
    def model_post_init(self, __context):
        """Post initialization logic for the event."""
        if not self.id:
            self.id = Event.new_id()
    
    def is_final_response(self) -> bool:
        """Returns whether the event is the final response of the agent."""
        if self.actions.skip_summarization:
            return True
        return (
            not self.function_calls
            and not self.function_responses
            and not self.partial
        )
    
    def get_function_calls(self) -> List[Dict[str, Any]]:
        """Returns the function calls in the event."""
        return self.function_calls
    
    def get_function_responses(self) -> List[Dict[str, Any]]:
        """Returns the function responses in the event."""
        return self.function_responses
    
    def add_function_call(self, function_call: Dict[str, Any]) -> None:
        """Add a function call to the event.
        
        Args:
            function_call: The function call to add
        """
        self.function_calls.append(function_call)
    
    def add_function_response(self, function_response: Dict[str, Any]) -> None:
        """Add a function response to the event.
        
        Args:
            function_response: The function response to add
        """
        self.function_responses.append(function_response)
    
    @staticmethod
    def new_id() -> str:
        """Generate a new event ID.
        
        Returns:
            A new unique event ID
        """
        characters = string.ascii_letters + string.digits
        return ''.join(random.choice(characters) for _ in range(8))