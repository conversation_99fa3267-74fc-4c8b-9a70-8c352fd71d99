#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A2A多智能体系统 MCP客户端

实现MCP协议的客户端功能，支持与MCP服务器通信
"""

import asyncio
import logging
import json
import uuid
from typing import Dict, List, Any, Optional, Callable, Union
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime, timedelta
import websockets
import aiohttp
from urllib.parse import urlparse

# Google ADK imports
from google.ai.generativelanguage import FunctionDeclaration, Schema, Type as SchemaType

from .base_tool import (
    BaseTool, ToolConfig, ToolResult, ToolError, ToolStatus,
    ToolExecutionContext, ToolPermission
)


class MCPClientState(Enum):
    """MCP客户端状态枚举"""
    DISCONNECTED = "disconnected"
    CONNECTING = "connecting"
    CONNECTED = "connected"
    AUTHENTICATING = "authenticating"
    AUTHENTICATED = "authenticated"
    ERROR = "error"


class MCPMessageType(Enum):
    """MCP消息类型枚举"""
    # 连接管理
    INITIALIZE = "initialize"
    INITIALIZED = "initialized"
    PING = "ping"
    PONG = "pong"
    
    # 工具管理
    LIST_TOOLS = "tools/list"
    CALL_TOOL = "tools/call"
    
    # 资源管理
    LIST_RESOURCES = "resources/list"
    READ_RESOURCE = "resources/read"
    
    # 提示词管理
    LIST_PROMPTS = "prompts/list"
    GET_PROMPT = "prompts/get"
    
    # 通知
    NOTIFICATION = "notification"
    
    # 错误
    ERROR = "error"


@dataclass
class MCPMessage:
    """MCP消息结构"""
    jsonrpc: str = "2.0"
    id: Optional[str] = None
    method: Optional[str] = None
    params: Optional[Dict[str, Any]] = None
    result: Optional[Any] = None
    error: Optional[Dict[str, Any]] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        data = {"jsonrpc": self.jsonrpc}
        
        if self.id is not None:
            data["id"] = self.id
        if self.method is not None:
            data["method"] = self.method
        if self.params is not None:
            data["params"] = self.params
        if self.result is not None:
            data["result"] = self.result
        if self.error is not None:
            data["error"] = self.error
            
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'MCPMessage':
        """从字典创建消息"""
        return cls(
            jsonrpc=data.get("jsonrpc", "2.0"),
            id=data.get("id"),
            method=data.get("method"),
            params=data.get("params"),
            result=data.get("result"),
            error=data.get("error")
        )


@dataclass
class MCPToolInfo:
    """MCP工具信息"""
    name: str
    description: str
    input_schema: Dict[str, Any]
    output_schema: Optional[Dict[str, Any]] = None
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class MCPResource:
    """MCP资源信息"""
    uri: str
    name: str
    description: str
    mime_type: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class MCPPrompt:
    """MCP提示词信息"""
    name: str
    description: str
    arguments: List[Dict[str, Any]] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class MCPClientConfig(ToolConfig):
    """MCP客户端配置"""
    server_url: str
    transport: str = "websocket"  # websocket 或 http
    auth_token: Optional[str] = None
    auth_method: str = "bearer"  # bearer, basic, custom
    timeout: int = 30
    retry_attempts: int = 3
    retry_delay: float = 1.0
    heartbeat_interval: int = 30
    max_message_size: int = 1024 * 1024  # 1MB
    enable_compression: bool = True
    custom_headers: Dict[str, str] = field(default_factory=dict)
    client_info: Dict[str, Any] = field(default_factory=lambda: {
        "name": "A2A-MCP-Client",
        "version": "1.0.0"
    })
    capabilities: Dict[str, Any] = field(default_factory=lambda: {
        "tools": True,
        "resources": True,
        "prompts": True,
        "notifications": True
    })


class MCPClientError(Exception):
    """MCP客户端异常"""
    pass


class MCPClient:
    """MCP协议客户端"""
    
    def __init__(self, config: MCPClientConfig):
        """
        初始化MCP客户端
        
        Args:
            config: 客户端配置
        """
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 连接状态
        self.state = MCPClientState.DISCONNECTED
        self.connection = None
        self.session = None
        
        # 消息处理
        self._pending_requests: Dict[str, asyncio.Future] = {}
        self._message_handlers: Dict[str, Callable] = {}
        self._notification_handlers: List[Callable] = []
        
        # 服务器信息
        self.server_info: Optional[Dict[str, Any]] = None
        self.server_capabilities: Optional[Dict[str, Any]] = None
        
        # 缓存
        self._tools_cache: Optional[List[MCPToolInfo]] = None
        self._resources_cache: Optional[List[MCPResource]] = None
        self._prompts_cache: Optional[List[MCPPrompt]] = None
        self._cache_timestamp: Optional[datetime] = None
        self._cache_ttl = timedelta(minutes=5)
        
        # 心跳
        self._heartbeat_task: Optional[asyncio.Task] = None
        self._last_pong: Optional[datetime] = None
        
        # 统计信息
        self.stats = {
            'messages_sent': 0,
            'messages_received': 0,
            'errors': 0,
            'reconnections': 0,
            'tools_called': 0,
            'resources_read': 0,
            'prompts_used': 0
        }
    
    async def connect(self) -> bool:
        """
        连接到MCP服务器
        
        Returns:
            bool: 连接是否成功
        """
        if self.state in [MCPClientState.CONNECTED, MCPClientState.AUTHENTICATED]:
            return True
        
        try:
            self.state = MCPClientState.CONNECTING
            
            if self.config.transport == "websocket":
                await self._connect_websocket()
            elif self.config.transport == "http":
                await self._connect_http()
            else:
                raise MCPClientError(f"不支持的传输协议: {self.config.transport}")
            
            # 初始化连接
            await self._initialize_connection()
            
            # 启动心跳
            if self.config.transport == "websocket":
                self._heartbeat_task = asyncio.create_task(self._heartbeat_loop())
            
            self.state = MCPClientState.AUTHENTICATED
            self.logger.info(f"成功连接到MCP服务器: {self.config.server_url}")
            
            return True
            
        except Exception as e:
            self.state = MCPClientState.ERROR
            self.logger.error(f"连接MCP服务器失败: {e}")
            await self.disconnect()
            return False
    
    async def disconnect(self) -> None:
        """
        断开与MCP服务器的连接
        """
        try:
            # 停止心跳
            if self._heartbeat_task:
                self._heartbeat_task.cancel()
                try:
                    await self._heartbeat_task
                except asyncio.CancelledError:
                    pass
                self._heartbeat_task = None
            
            # 关闭连接
            if self.connection:
                if self.config.transport == "websocket":
                    await self.connection.close()
                self.connection = None
            
            # 关闭HTTP会话
            if self.session:
                await self.session.close()
                self.session = None
            
            # 清理待处理请求
            for future in self._pending_requests.values():
                if not future.done():
                    future.cancel()
            self._pending_requests.clear()
            
            # 清理缓存
            self._clear_cache()
            
            self.state = MCPClientState.DISCONNECTED
            self.logger.info("已断开MCP服务器连接")
            
        except Exception as e:
            self.logger.error(f"断开连接时发生错误: {e}")
    
    async def list_tools(self, force_refresh: bool = False) -> List[MCPToolInfo]:
        """
        获取可用工具列表
        
        Args:
            force_refresh: 强制刷新缓存
        
        Returns:
            List[MCPToolInfo]: 工具列表
        """
        # 检查缓存
        if (not force_refresh and self._tools_cache and 
            self._cache_timestamp and 
            datetime.now() - self._cache_timestamp < self._cache_ttl):
            return self._tools_cache
        
        try:
            response = await self._send_request(
                MCPMessageType.LIST_TOOLS.value,
                {}
            )
            
            tools = []
            for tool_data in response.get('tools', []):
                tool = MCPToolInfo(
                    name=tool_data['name'],
                    description=tool_data['description'],
                    input_schema=tool_data['inputSchema'],
                    output_schema=tool_data.get('outputSchema'),
                    metadata=tool_data.get('metadata', {})
                )
                tools.append(tool)
            
            # 更新缓存
            self._tools_cache = tools
            self._cache_timestamp = datetime.now()
            
            self.logger.info(f"获取到 {len(tools)} 个工具")
            return tools
            
        except Exception as e:
            self.logger.error(f"获取工具列表失败: {e}")
            self.stats['errors'] += 1
            return []
    
    async def call_tool(
        self,
        name: str,
        arguments: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        调用工具
        
        Args:
            name: 工具名称
            arguments: 工具参数
        
        Returns:
            Dict[str, Any]: 工具执行结果
        """
        try:
            response = await self._send_request(
                MCPMessageType.CALL_TOOL.value,
                {
                    "name": name,
                    "arguments": arguments
                }
            )
            
            self.stats['tools_called'] += 1
            self.logger.info(f"工具 {name} 调用成功")
            
            return response
            
        except Exception as e:
            self.logger.error(f"工具 {name} 调用失败: {e}")
            self.stats['errors'] += 1
            raise MCPClientError(f"工具调用失败: {str(e)}")
    
    async def list_resources(self, force_refresh: bool = False) -> List[MCPResource]:
        """
        获取可用资源列表
        
        Args:
            force_refresh: 强制刷新缓存
        
        Returns:
            List[MCPResource]: 资源列表
        """
        # 检查缓存
        if (not force_refresh and self._resources_cache and 
            self._cache_timestamp and 
            datetime.now() - self._cache_timestamp < self._cache_ttl):
            return self._resources_cache
        
        try:
            response = await self._send_request(
                MCPMessageType.LIST_RESOURCES.value,
                {}
            )
            
            resources = []
            for resource_data in response.get('resources', []):
                resource = MCPResource(
                    uri=resource_data['uri'],
                    name=resource_data['name'],
                    description=resource_data['description'],
                    mime_type=resource_data.get('mimeType'),
                    metadata=resource_data.get('metadata', {})
                )
                resources.append(resource)
            
            # 更新缓存
            self._resources_cache = resources
            self._cache_timestamp = datetime.now()
            
            self.logger.info(f"获取到 {len(resources)} 个资源")
            return resources
            
        except Exception as e:
            self.logger.error(f"获取资源列表失败: {e}")
            self.stats['errors'] += 1
            return []
    
    async def read_resource(self, uri: str) -> Dict[str, Any]:
        """
        读取资源内容
        
        Args:
            uri: 资源URI
        
        Returns:
            Dict[str, Any]: 资源内容
        """
        try:
            response = await self._send_request(
                MCPMessageType.READ_RESOURCE.value,
                {"uri": uri}
            )
            
            self.stats['resources_read'] += 1
            self.logger.info(f"资源 {uri} 读取成功")
            
            return response
            
        except Exception as e:
            self.logger.error(f"资源 {uri} 读取失败: {e}")
            self.stats['errors'] += 1
            raise MCPClientError(f"资源读取失败: {str(e)}")
    
    async def list_prompts(self, force_refresh: bool = False) -> List[MCPPrompt]:
        """
        获取可用提示词列表
        
        Args:
            force_refresh: 强制刷新缓存
        
        Returns:
            List[MCPPrompt]: 提示词列表
        """
        # 检查缓存
        if (not force_refresh and self._prompts_cache and 
            self._cache_timestamp and 
            datetime.now() - self._cache_timestamp < self._cache_ttl):
            return self._prompts_cache
        
        try:
            response = await self._send_request(
                MCPMessageType.LIST_PROMPTS.value,
                {}
            )
            
            prompts = []
            for prompt_data in response.get('prompts', []):
                prompt = MCPPrompt(
                    name=prompt_data['name'],
                    description=prompt_data['description'],
                    arguments=prompt_data.get('arguments', []),
                    metadata=prompt_data.get('metadata', {})
                )
                prompts.append(prompt)
            
            # 更新缓存
            self._prompts_cache = prompts
            self._cache_timestamp = datetime.now()
            
            self.logger.info(f"获取到 {len(prompts)} 个提示词")
            return prompts
            
        except Exception as e:
            self.logger.error(f"获取提示词列表失败: {e}")
            self.stats['errors'] += 1
            return []
    
    async def get_prompt(
        self,
        name: str,
        arguments: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        获取提示词内容
        
        Args:
            name: 提示词名称
            arguments: 提示词参数
        
        Returns:
            Dict[str, Any]: 提示词内容
        """
        try:
            params = {"name": name}
            if arguments:
                params["arguments"] = arguments
            
            response = await self._send_request(
                MCPMessageType.GET_PROMPT.value,
                params
            )
            
            self.stats['prompts_used'] += 1
            self.logger.info(f"提示词 {name} 获取成功")
            
            return response
            
        except Exception as e:
            self.logger.error(f"提示词 {name} 获取失败: {e}")
            self.stats['errors'] += 1
            raise MCPClientError(f"提示词获取失败: {str(e)}")
    
    def add_notification_handler(self, handler: Callable[[Dict[str, Any]], None]) -> None:
        """
        添加通知处理器
        
        Args:
            handler: 通知处理函数
        """
        self._notification_handlers.append(handler)
    
    def remove_notification_handler(self, handler: Callable[[Dict[str, Any]], None]) -> None:
        """
        移除通知处理器
        
        Args:
            handler: 通知处理函数
        """
        if handler in self._notification_handlers:
            self._notification_handlers.remove(handler)
    
    async def _connect_websocket(self) -> None:
        """
        建立WebSocket连接
        """
        headers = self._get_auth_headers()
        headers.update(self.config.custom_headers)
        
        try:
            self.connection = await websockets.connect(
                self.config.server_url,
                extra_headers=headers,
                max_size=self.config.max_message_size,
                compression="deflate" if self.config.enable_compression else None,
                ping_interval=self.config.heartbeat_interval,
                ping_timeout=self.config.timeout
            )
            
            # 启动消息接收循环
            asyncio.create_task(self._websocket_message_loop())
            
        except Exception as e:
            raise MCPClientError(f"WebSocket连接失败: {str(e)}")
    
    async def _connect_http(self) -> None:
        """
        建立HTTP连接
        """
        headers = self._get_auth_headers()
        headers.update(self.config.custom_headers)
        headers['Content-Type'] = 'application/json'
        
        timeout = aiohttp.ClientTimeout(total=self.config.timeout)
        
        self.session = aiohttp.ClientSession(
            headers=headers,
            timeout=timeout,
            connector=aiohttp.TCPConnector(
                limit=100,
                limit_per_host=10
            )
        )
    
    async def _initialize_connection(self) -> None:
        """
        初始化连接
        """
        try:
            response = await self._send_request(
                MCPMessageType.INITIALIZE.value,
                {
                    "protocolVersion": "2024-11-05",
                    "capabilities": self.config.capabilities,
                    "clientInfo": self.config.client_info
                }
            )
            
            self.server_info = response.get('serverInfo', {})
            self.server_capabilities = response.get('capabilities', {})
            
            # 发送初始化完成通知
            await self._send_notification(
                MCPMessageType.INITIALIZED.value,
                {}
            )
            
            self.logger.info(f"MCP连接初始化完成，服务器: {self.server_info.get('name', 'Unknown')}")
            
        except Exception as e:
            raise MCPClientError(f"连接初始化失败: {str(e)}")
    
    async def _send_request(
        self,
        method: str,
        params: Dict[str, Any],
        timeout: Optional[float] = None
    ) -> Dict[str, Any]:
        """
        发送请求并等待响应
        
        Args:
            method: 方法名
            params: 参数
            timeout: 超时时间
        
        Returns:
            Dict[str, Any]: 响应结果
        """
        if self.state not in [MCPClientState.CONNECTED, MCPClientState.AUTHENTICATED]:
            raise MCPClientError("客户端未连接")
        
        request_id = str(uuid.uuid4())
        message = MCPMessage(
            id=request_id,
            method=method,
            params=params
        )
        
        # 创建Future等待响应
        future = asyncio.Future()
        self._pending_requests[request_id] = future
        
        try:
            # 发送消息
            await self._send_message(message)
            
            # 等待响应
            timeout = timeout or self.config.timeout
            response = await asyncio.wait_for(future, timeout=timeout)
            
            if 'error' in response:
                error = response['error']
                raise MCPClientError(f"服务器错误: {error.get('message', 'Unknown error')}")
            
            return response.get('result', {})
            
        except asyncio.TimeoutError:
            raise MCPClientError(f"请求超时: {method}")
        finally:
            # 清理待处理请求
            if request_id in self._pending_requests:
                del self._pending_requests[request_id]
    
    async def _send_notification(
        self,
        method: str,
        params: Dict[str, Any]
    ) -> None:
        """
        发送通知（不等待响应）
        
        Args:
            method: 方法名
            params: 参数
        """
        message = MCPMessage(
            method=method,
            params=params
        )
        
        await self._send_message(message)
    
    async def _send_message(self, message: MCPMessage) -> None:
        """
        发送消息
        
        Args:
            message: 消息对象
        """
        try:
            message_data = json.dumps(message.to_dict())
            
            if self.config.transport == "websocket":
                await self.connection.send(message_data)
            elif self.config.transport == "http":
                async with self.session.post(
                    self.config.server_url,
                    data=message_data
                ) as response:
                    if response.status != 200:
                        raise MCPClientError(f"HTTP请求失败: {response.status}")
                    
                    # 处理HTTP响应
                    response_data = await response.json()
                    await self._handle_message(response_data)
            
            self.stats['messages_sent'] += 1
            
        except Exception as e:
            self.logger.error(f"发送消息失败: {e}")
            self.stats['errors'] += 1
            raise
    
    async def _websocket_message_loop(self) -> None:
        """
        WebSocket消息接收循环
        """
        try:
            async for message in self.connection:
                try:
                    data = json.loads(message)
                    await self._handle_message(data)
                except Exception as e:
                    self.logger.error(f"处理消息失败: {e}")
                    self.stats['errors'] += 1
                    
        except websockets.exceptions.ConnectionClosed:
            self.logger.warning("WebSocket连接已关闭")
            self.state = MCPClientState.DISCONNECTED
        except Exception as e:
            self.logger.error(f"WebSocket消息循环异常: {e}")
            self.state = MCPClientState.ERROR
    
    async def _handle_message(self, data: Dict[str, Any]) -> None:
        """
        处理接收到的消息
        
        Args:
            data: 消息数据
        """
        try:
            message = MCPMessage.from_dict(data)
            self.stats['messages_received'] += 1
            
            # 处理响应
            if message.id and message.id in self._pending_requests:
                future = self._pending_requests[message.id]
                if not future.done():
                    future.set_result(data)
                return
            
            # 处理通知
            if message.method:
                if message.method == MCPMessageType.PING.value:
                    # 响应ping
                    pong_message = MCPMessage(
                        id=message.id,
                        result={}
                    )
                    await self._send_message(pong_message)
                elif message.method == MCPMessageType.PONG.value:
                    # 记录pong时间
                    self._last_pong = datetime.now()
                elif message.method == MCPMessageType.NOTIFICATION.value:
                    # 处理通知
                    for handler in self._notification_handlers:
                        try:
                            handler(message.params or {})
                        except Exception as e:
                            self.logger.error(f"通知处理器异常: {e}")
                
                # 调用自定义处理器
                if message.method in self._message_handlers:
                    try:
                        await self._message_handlers[message.method](message)
                    except Exception as e:
                        self.logger.error(f"消息处理器异常: {e}")
            
        except Exception as e:
            self.logger.error(f"消息处理异常: {e}")
            self.stats['errors'] += 1
    
    async def _heartbeat_loop(self) -> None:
        """
        心跳循环
        """
        try:
            while self.state in [MCPClientState.CONNECTED, MCPClientState.AUTHENTICATED]:
                await asyncio.sleep(self.config.heartbeat_interval)
                
                try:
                    # 发送ping
                    ping_message = MCPMessage(
                        id=str(uuid.uuid4()),
                        method=MCPMessageType.PING.value,
                        params={}
                    )
                    await self._send_message(ping_message)
                    
                    # 检查pong响应
                    await asyncio.sleep(5)  # 等待5秒
                    if (self._last_pong is None or 
                        datetime.now() - self._last_pong > timedelta(seconds=self.config.timeout)):
                        self.logger.warning("心跳检测失败，连接可能已断开")
                        break
                        
                except Exception as e:
                    self.logger.error(f"心跳检测异常: {e}")
                    break
                    
        except asyncio.CancelledError:
            pass
        except Exception as e:
            self.logger.error(f"心跳循环异常: {e}")
    
    def _get_auth_headers(self) -> Dict[str, str]:
        """
        获取认证头
        
        Returns:
            Dict[str, str]: 认证头
        """
        headers = {}
        
        if self.config.auth_token:
            if self.config.auth_method == "bearer":
                headers['Authorization'] = f"Bearer {self.config.auth_token}"
            elif self.config.auth_method == "basic":
                headers['Authorization'] = f"Basic {self.config.auth_token}"
            elif self.config.auth_method == "custom":
                headers['X-Auth-Token'] = self.config.auth_token
        
        return headers
    
    def _clear_cache(self) -> None:
        """
        清理缓存
        """
        self._tools_cache = None
        self._resources_cache = None
        self._prompts_cache = None
        self._cache_timestamp = None
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        return {
            **self.stats,
            'state': self.state.value,
            'server_info': self.server_info,
            'server_capabilities': self.server_capabilities,
            'cache_status': {
                'tools_cached': self._tools_cache is not None,
                'resources_cached': self._resources_cache is not None,
                'prompts_cached': self._prompts_cache is not None,
                'cache_timestamp': self._cache_timestamp.isoformat() if self._cache_timestamp else None
            }
        }
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.connect()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.disconnect()