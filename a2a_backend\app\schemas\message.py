#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A2A多智能体系统消息相关Pydantic模式

定义消息相关的请求和响应模式
"""

from typing import Dict, List, Optional, Any, Union
from datetime import datetime
from pydantic import Field, validator
from enum import Enum

from .base import BaseSchema


class MessageType(str, Enum):
    """消息类型枚举"""
    TEXT = "text"
    IMAGE = "image"
    FILE = "file"
    AUDIO = "audio"
    VIDEO = "video"
    CODE = "code"
    SYSTEM = "system"
    ERROR = "error"
    TOOL_CALL = "tool_call"
    TOOL_RESULT = "tool_result"


class MessageRole(str, Enum):
    """消息角色枚举"""
    USER = "user"
    ASSISTANT = "assistant"
    SYSTEM = "system"
    TOOL = "tool"


class MessageStatus(str, Enum):
    """消息状态枚举"""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class ReactionType(str, Enum):
    """反应类型枚举"""
    LIKE = "like"
    DISLIKE = "dislike"
    LOVE = "love"
    LAUGH = "laugh"
    SURPRISE = "surprise"
    ANGRY = "angry"
    SAD = "sad"


class MessageCreate(BaseSchema):
    """
    消息创建请求模式
    """
    
    content: str = Field(
        min_length=1,
        max_length=50000,
        description="消息内容"
    )
    
    type: MessageType = Field(
        default=MessageType.TEXT,
        description="消息类型"
    )
    
    role: MessageRole = Field(
        default=MessageRole.USER,
        description="消息角色"
    )
    
    reply_to_id: Optional[str] = Field(
        default=None,
        description="回复的消息ID"
    )
    
    metadata: Optional[Dict[str, Any]] = Field(
        default_factory=dict,
        description="消息元数据"
    )
    
    attachments: List[Dict[str, Any]] = Field(
        default_factory=list,
        description="附件列表"
    )
    
    tool_calls: Optional[List[Dict[str, Any]]] = Field(
        default=None,
        description="工具调用列表"
    )
    
    context: Optional[Dict[str, Any]] = Field(
        default_factory=dict,
        description="消息上下文"
    )


class MessageUpdate(BaseSchema):
    """
    消息更新请求模式
    """
    
    content: Optional[str] = Field(
        default=None,
        min_length=1,
        max_length=50000,
        description="消息内容"
    )
    
    metadata: Optional[Dict[str, Any]] = Field(
        default=None,
        description="消息元数据"
    )
    
    status: Optional[MessageStatus] = Field(
        default=None,
        description="消息状态"
    )


class MessageResponse(BaseSchema):
    """
    消息响应模式
    """
    
    id: str = Field(
        description="消息ID"
    )
    
    session_id: str = Field(
        description="会话ID"
    )
    
    sender_id: str = Field(
        description="发送者ID"
    )
    
    sender_type: str = Field(
        description="发送者类型（user/agent）"
    )
    
    content: str = Field(
        description="消息内容"
    )
    
    type: MessageType = Field(
        description="消息类型"
    )
    
    role: MessageRole = Field(
        description="消息角色"
    )
    
    status: MessageStatus = Field(
        description="消息状态"
    )
    
    reply_to_id: Optional[str] = Field(
        description="回复的消息ID"
    )
    
    metadata: Dict[str, Any] = Field(
        description="消息元数据"
    )
    
    attachments: List[Dict[str, Any]] = Field(
        description="附件列表"
    )
    
    tool_calls: Optional[List[Dict[str, Any]]] = Field(
        description="工具调用列表"
    )
    
    model_name: Optional[str] = Field(
        description="使用的模型名称"
    )
    
    prompt_tokens: Optional[int] = Field(
        description="提示令牌数"
    )
    
    completion_tokens: Optional[int] = Field(
        description="完成令牌数"
    )
    
    total_tokens: Optional[int] = Field(
        description="总令牌数"
    )
    
    cost: Optional[float] = Field(
        description="成本"
    )
    
    response_time: Optional[float] = Field(
        description="响应时间（秒）"
    )
    
    context: Dict[str, Any] = Field(
        description="消息上下文"
    )
    
    rating: Optional[float] = Field(
        description="评分"
    )
    
    feedback: Optional[str] = Field(
        description="反馈"
    )
    
    created_at: datetime = Field(
        description="创建时间"
    )
    
    updated_at: datetime = Field(
        description="更新时间"
    )


class MessageReactionCreate(BaseSchema):
    """
    消息反应创建请求模式
    """
    
    type: ReactionType = Field(
        description="反应类型"
    )
    
    comment: Optional[str] = Field(
        default=None,
        max_length=500,
        description="反应评论"
    )


class MessageReactionResponse(BaseSchema):
    """
    消息反应响应模式
    """
    
    id: str = Field(
        description="反应ID"
    )
    
    message_id: str = Field(
        description="消息ID"
    )
    
    user_id: str = Field(
        description="用户ID"
    )
    
    type: ReactionType = Field(
        description="反应类型"
    )
    
    comment: Optional[str] = Field(
        description="反应评论"
    )
    
    created_at: datetime = Field(
        description="创建时间"
    )


class MessageEditResponse(BaseSchema):
    """
    消息编辑历史响应模式
    """
    
    id: str = Field(
        description="编辑ID"
    )
    
    message_id: str = Field(
        description="消息ID"
    )
    
    editor_id: str = Field(
        description="编辑者ID"
    )
    
    old_content: str = Field(
        description="原内容"
    )
    
    new_content: str = Field(
        description="新内容"
    )
    
    edit_reason: Optional[str] = Field(
        description="编辑原因"
    )
    
    created_at: datetime = Field(
        description="编辑时间"
    )


class MessageTemplateResponse(BaseSchema):
    """
    消息模板响应模式
    """
    
    id: str = Field(
        description="模板ID"
    )
    
    name: str = Field(
        description="模板名称"
    )
    
    description: Optional[str] = Field(
        description="模板描述"
    )
    
    category: str = Field(
        description="模板分类"
    )
    
    content: str = Field(
        description="模板内容"
    )
    
    variables: List[str] = Field(
        description="模板变量列表"
    )
    
    tags: List[str] = Field(
        description="标签列表"
    )
    
    is_public: bool = Field(
        description="是否公开"
    )
    
    usage_count: int = Field(
        description="使用次数"
    )
    
    creator_id: str = Field(
        description="创建者ID"
    )
    
    created_at: datetime = Field(
        description="创建时间"
    )
    
    updated_at: datetime = Field(
        description="更新时间"
    )


class MessageSearchRequest(BaseSchema):
    """
    消息搜索请求模式
    """
    
    query: str = Field(
        min_length=1,
        max_length=200,
        description="搜索查询"
    )
    
    session_id: Optional[str] = Field(
        default=None,
        description="会话ID过滤"
    )
    
    message_type: Optional[MessageType] = Field(
        default=None,
        description="消息类型过滤"
    )
    
    sender_type: Optional[str] = Field(
        default=None,
        description="发送者类型过滤"
    )
    
    date_range: Optional[Dict[str, datetime]] = Field(
        default=None,
        description="日期范围过滤"
    )
    
    include_content: bool = Field(
        default=True,
        description="是否搜索内容"
    )
    
    include_metadata: bool = Field(
        default=False,
        description="是否搜索元数据"
    )


class MessageRatingRequest(BaseSchema):
    """
    消息评分请求模式
    """
    
    rating: float = Field(
        ge=1.0,
        le=5.0,
        description="评分（1-5）"
    )
    
    feedback: Optional[str] = Field(
        default=None,
        max_length=1000,
        description="反馈内容"
    )
    
    categories: List[str] = Field(
        default_factory=list,
        description="评分类别"
    )


class MessageStreamResponse(BaseSchema):
    """
    消息流响应模式
    """
    
    id: str = Field(
        description="消息ID"
    )
    
    type: str = Field(
        description="流事件类型"
    )
    
    content: Optional[str] = Field(
        description="内容片段"
    )
    
    delta: Optional[str] = Field(
        description="增量内容"
    )
    
    metadata: Optional[Dict[str, Any]] = Field(
        description="元数据"
    )
    
    finished: bool = Field(
        default=False,
        description="是否完成"
    )
    
    timestamp: datetime = Field(
        default_factory=datetime.now,
        description="时间戳"
    )


class MessageBatchRequest(BaseSchema):
    """
    批量消息请求模式
    """
    
    messages: List[MessageCreate] = Field(
        min_items=1,
        max_items=100,
        description="消息列表"
    )
    
    session_id: str = Field(
        description="会话ID"
    )
    
    process_sequentially: bool = Field(
        default=True,
        description="是否顺序处理"
    )