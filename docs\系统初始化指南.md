# A2A多智能体系统初始化指南

## 概述

本指南将帮助您完成A2A多智能体系统的初始化配置和启动。系统基于Python 3.12、FastAPI、Google ADK等技术构建，支持多智能体协作、工作流管理和任务执行。

## 系统要求

### 硬件要求
- **CPU**: 4核心以上
- **内存**: 8GB以上（推荐16GB）
- **存储**: 20GB以上可用空间
- **网络**: 稳定的互联网连接

### 软件要求
- **操作系统**: Windows 11, macOS 10.15+, Ubuntu 20.04+
- **Python**: 3.12+
- **数据库**: MySQL 8.0+ 或 PostgreSQL 13+
- **Redis**: 6.0+（用于缓存和会话存储）

## 环境准备

### 1. 安装Python环境

#### Windows 11 (使用Conda)
```bash
# 创建虚拟环境
conda create -n a2a_backend python=3.12
conda activate a2a_backend

# 验证Python版本
python --version
```

#### macOS/Linux
```bash
# 使用pyenv安装Python 3.12
pyenv install 3.12.0
pyenv local 3.12.0

# 创建虚拟环境
python -m venv a2a_backend
source a2a_backend/bin/activate  # Linux/macOS
```

### 2. 安装数据库

#### MySQL 8.0
```bash
# Windows (使用Chocolatey)
choco install mysql

# macOS (使用Homebrew)
brew install mysql

# Ubuntu
sudo apt update
sudo apt install mysql-server
```

#### PostgreSQL (可选)
```bash
# Windows
choco install postgresql

# macOS
brew install postgresql

# Ubuntu
sudo apt install postgresql postgresql-contrib
```

### 3. 安装Redis
```bash
# Windows
choco install redis-64

# macOS
brew install redis

# Ubuntu
sudo apt install redis-server
```

## 项目配置

### 1. 克隆项目
```bash
git clone <repository-url>
cd A2A
```

### 2. 安装依赖
```bash
# 激活虚拟环境
conda activate a2a_backend  # Windows
# 或 source a2a_backend/bin/activate  # Linux/macOS

# 安装Python依赖
cd a2a_backend
pip install -r requirements.txt
```

### 3. 配置环境变量

创建 `.env` 文件：

```bash
# 复制环境变量模板
cp .env.example .env
```

编辑 `.env` 文件：

```env
# 应用配置
APP_NAME=A2A多智能体系统
DEBUG=true
HOST=0.0.0.0
PORT=8000

# 数据库配置
DATABASE_URL=mysql+aiomysql://root:password@localhost:3306/a2a_system
DATABASE_POOL_SIZE=20
DATABASE_MAX_OVERFLOW=30
DATABASE_POOL_TIMEOUT=30
DATABASE_POOL_RECYCLE=3600

# JWT配置
JWT_SECRET_KEY=your-super-secret-jwt-key-change-in-production
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30
JWT_REFRESH_TOKEN_EXPIRE_DAYS=7

# 密码加密配置
PASSWORD_BCRYPT_ROUNDS=12

# CORS配置
ALLOWED_ORIGINS=["http://localhost:3000","http://127.0.0.1:3000"]

# Google ADK配置
GOOGLE_ADK_PROJECT_ID=your-google-project-id
GOOGLE_ADK_CREDENTIALS_PATH=./credentials/google-credentials.json

# LLM配置
DEFAULT_LLM_PROVIDER=google
DEFAULT_LLM_MODEL=gemini-pro

# 工具配置
TOOLS_ENABLED=true
MCP_ENABLED=true

# 日志配置
LOG_LEVEL=INFO
LOG_TO_DATABASE=true
LOG_FILE_PATH=./logs/app.log

# 监控配置
METRICS_ENABLED=true
PROMETHEUS_ENABLED=false

# 文件存储配置
FILE_STORAGE_TYPE=database
FILE_STORAGE_PATH=./storage
MAX_FILE_SIZE=104857600

# 会话配置
SESSION_TIMEOUT_MINUTES=60
MAX_SESSIONS_PER_USER=10

# 工作流配置
WORKFLOW_MAX_STEPS=100
WORKFLOW_TIMEOUT_MINUTES=30

# 任务配置
MAX_CONCURRENT_TASKS_PER_USER=5
TASK_TIMEOUT_MINUTES=60
MAX_TASK_DURATION_MINUTES=120
MAX_TASK_RETRIES=3
MAX_MEMORY_USAGE_MB=1024

# Redis配置
REDIS_URL=redis://localhost:6379/0
REDIS_PASSWORD=
REDIS_DB=0
REDIS_MAX_CONNECTIONS=20

# 邮件配置（可选）
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
SMTP_USE_TLS=true
FROM_EMAIL=<EMAIL>
```

### 4. 配置Google ADK

#### 获取Google Cloud凭证
1. 访问 [Google Cloud Console](https://console.cloud.google.com/)
2. 创建新项目或选择现有项目
3. 启用必要的API：
   - Generative AI API
   - Cloud Storage API（如果使用）
4. 创建服务账号密钥
5. 下载JSON凭证文件

#### 配置凭证文件
```bash
# 创建凭证目录
mkdir credentials

# 将下载的凭证文件复制到项目中
cp /path/to/your/google-credentials.json ./credentials/

# 设置环境变量
export GOOGLE_APPLICATION_CREDENTIALS=./credentials/google-credentials.json
```

### 5. 数据库初始化

#### 创建数据库
```sql
-- MySQL
CREATE DATABASE a2a_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'a2a_user'@'localhost' IDENTIFIED BY 'secure_password';
GRANT ALL PRIVILEGES ON a2a_system.* TO 'a2a_user'@'localhost';
FLUSH PRIVILEGES;

-- PostgreSQL
CREATE DATABASE a2a_system;
CREATE USER a2a_user WITH PASSWORD 'secure_password';
GRANT ALL PRIVILEGES ON DATABASE a2a_system TO a2a_user;
```

#### 运行数据库迁移
```bash
# 生成数据库表
python scripts/init_database.py

# 或者使用Alembic（如果配置了）
alembic upgrade head
```

### 6. 创建管理员用户

运行初始化脚本：

```bash
python scripts/create_admin.py
```

或者手动创建：

```python
# scripts/create_admin.py
import asyncio
from app.services.user_service import UserService
from app.core.database import init_database

async def create_admin():
    await init_database()
    
    user_service = UserService()
    
    admin_data = {
        "username": "admin",
        "email": "<EMAIL>",
        "password": "Admin123!@#",
        "full_name": "系统管理员",
        "role": "super_admin",
        "is_verified": True
    }
    
    result = await user_service.create_user(admin_data)
    print(f"管理员用户创建结果: {result}")

if __name__ == "__main__":
    asyncio.run(create_admin())
```

## 启动系统

### 1. 启动依赖服务

```bash
# 启动MySQL
sudo systemctl start mysql  # Linux
brew services start mysql   # macOS
net start mysql            # Windows

# 启动Redis
sudo systemctl start redis  # Linux
brew services start redis   # macOS
redis-server               # Windows
```

### 2. 启动应用

#### 开发模式
```bash
# 激活虚拟环境
conda activate a2a_backend

# 启动应用
python main.py

# 或使用uvicorn
uvicorn main:app --host 0.0.0.0 --port 8000 --reload
```

#### 生产模式
```bash
# 使用Gunicorn（推荐）
pip install gunicorn
gunicorn main:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8000

# 或使用uvicorn
uvicorn main:app --host 0.0.0.0 --port 8000 --workers 4
```

### 3. 验证启动

访问以下URL验证系统是否正常启动：

- **健康检查**: http://localhost:8000/health
- **API文档**: http://localhost:8000/docs
- **ReDoc文档**: http://localhost:8000/redoc

## 配置文件详解

### 主配置文件 (app/core/config.py)

系统使用Pydantic Settings进行配置管理，支持环境变量和配置文件。

#### 数据库配置
```python
# MySQL示例
DATABASE_URL=mysql+aiomysql://username:password@host:port/database

# PostgreSQL示例
DATABASE_URL=postgresql+asyncpg://username:password@host:port/database

# SQLite示例（仅用于开发）
DATABASE_URL=sqlite+aiosqlite:///./a2a_system.db
```

#### JWT配置
```python
# 生成安全的密钥
import secrets
JWT_SECRET_KEY = secrets.token_urlsafe(32)
```

#### Google ADK配置
```python
# 项目ID
GOOGLE_ADK_PROJECT_ID=your-project-id

# 凭证文件路径
GOOGLE_ADK_CREDENTIALS_PATH=./credentials/google-credentials.json

# 或使用环境变量
export GOOGLE_APPLICATION_CREDENTIALS=./credentials/google-credentials.json
```

### 日志配置

系统使用loguru进行日志管理：

```python
# 日志级别
LOG_LEVEL=INFO  # DEBUG, INFO, WARNING, ERROR, CRITICAL

# 日志输出
LOG_TO_DATABASE=true    # 是否存储到数据库
LOG_FILE_PATH=./logs/app.log  # 日志文件路径
```

### 性能配置

```python
# 数据库连接池
DATABASE_POOL_SIZE=20
DATABASE_MAX_OVERFLOW=30

# 任务配置
MAX_CONCURRENT_TASKS_PER_USER=5
TASK_TIMEOUT_MINUTES=60

# 会话配置
SESSION_TIMEOUT_MINUTES=60
MAX_SESSIONS_PER_USER=10
```

## 常见问题

### 1. 数据库连接失败
```bash
# 检查数据库服务状态
sudo systemctl status mysql

# 检查连接字符串
python -c "from app.core.database import get_database_manager; print('数据库连接测试')"
```

### 2. Google ADK认证失败
```bash
# 检查凭证文件
ls -la credentials/google-credentials.json

# 验证环境变量
echo $GOOGLE_APPLICATION_CREDENTIALS

# 测试API访问
python -c "import google.generativeai as genai; print('Google ADK连接测试')"
```

### 3. 端口占用
```bash
# 查看端口占用
netstat -tulpn | grep :8000

# 杀死占用进程
sudo kill -9 <PID>

# 或更换端口
uvicorn main:app --port 8001
```

### 4. 依赖安装失败
```bash
# 更新pip
pip install --upgrade pip

# 清理缓存
pip cache purge

# 使用国内镜像
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
```

## 部署建议

### 1. 生产环境配置
- 使用强密码和安全的JWT密钥
- 启用HTTPS
- 配置防火墙
- 设置日志轮转
- 配置监控和告警

### 2. 性能优化
- 使用连接池
- 启用Redis缓存
- 配置负载均衡
- 优化数据库索引

### 3. 安全配置
- 定期更新依赖
- 配置CORS策略
- 启用速率限制
- 设置访问日志

## 下一步

系统初始化完成后，您可以：

1. 访问API文档了解接口使用方法
2. 创建第一个智能体
3. 配置工作流
4. 集成前端应用
5. 配置监控和日志

如有问题，请参考API文档或联系技术支持。
