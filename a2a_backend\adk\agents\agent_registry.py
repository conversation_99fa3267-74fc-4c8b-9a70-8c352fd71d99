#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A2A多智能体系统 智能体注册器

管理智能体类型，支持用户权限过滤
"""

import logging
from typing import Dict, List, Optional, Any, Type, Union
from datetime import datetime
import json

from app.models.user import User
from app.models.agent import Agent as AgentModel
from app.core.logging import get_logger
from app.auth.permissions import check_user_permission

from .custom_llm_agent import CustomLLMAgent
from .workflow_agent import WorkflowAgent
from .tool_agent import ToolAgent

class AgentRegistry:
    """
    智能体注册器，管理智能体类型，支持用户权限过滤
    
    提供以下功能：
    1. 智能体类型注册
    2. 智能体类型查询
    3. 用户权限过滤
    4. 智能体实例管理
    5. 智能体配置验证
    6. 智能体使用统计
    """
    
    _instance = None
    _initialized = False
    
    def __new__(cls):
        """单例模式"""
        if cls._instance is None:
            cls._instance = super(AgentRegistry, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        """初始化智能体注册器"""
        if not self._initialized:
            self.logger = get_logger("agent_registry")
            
            # 注册的智能体类型
            self.registered_types: Dict[str, Dict[str, Any]] = {}
            
            # 智能体实例缓存
            self.agent_instances: Dict[str, Any] = {}
            
            # 使用统计
            self.usage_stats = {
                "total_registrations": 0,
                "total_instances_created": 0,
                "type_usage": {},
                "user_usage": {},
                "last_activity": None
            }
            
            # 注册默认智能体类型
            self._register_default_types()
            
            self._initialized = True
            self.logger.info("AgentRegistry已初始化")
    
    def _register_default_types(self) -> None:
        """
        注册默认智能体类型
        """
        # 注册CustomLLMAgent
        self.register_agent_type(
            type_name="custom_llm",
            agent_class=CustomLLMAgent,
            description="自定义LLM智能体，支持多种LLM提供商",
            required_config=[
                "llm_provider",
                "model_name"
            ],
            optional_config=[
                "api_key",
                "base_url",
                "temperature",
                "max_tokens",
                "top_p",
                "frequency_penalty",
                "presence_penalty"
            ],
            capabilities=[
                "text_generation",
                "conversation",
                "streaming",
                "function_calling"
            ]
        )
        
        # 注册WorkflowAgent
        self.register_agent_type(
            type_name="workflow",
            agent_class=WorkflowAgent,
            description="工作流智能体，支持复杂工作流编排",
            required_config=[
                "workflow_id"
            ],
            optional_config=[
                "max_parallel_nodes",
                "timeout",
                "retry_count"
            ],
            capabilities=[
                "workflow_execution",
                "multi_agent_coordination",
                "conditional_branching",
                "parallel_execution"
            ]
        )
        
        # 注册ToolAgent
        self.register_agent_type(
            type_name="tool",
            agent_class=ToolAgent,
            description="工具智能体，专门处理工具调用",
            required_config=[],
            optional_config=[
                "max_tool_calls",
                "tool_timeout",
                "allowed_tools"
            ],
            capabilities=[
                "tool_calling",
                "function_execution",
                "batch_operations",
                "parameter_validation"
            ]
        )
    
    def register_agent_type(
        self,
        type_name: str,
        agent_class: Type,
        description: str = "",
        required_config: Optional[List[str]] = None,
        optional_config: Optional[List[str]] = None,
        capabilities: Optional[List[str]] = None,
        required_permissions: Optional[List[str]] = None
    ) -> None:
        """
        注册智能体类型
        
        Args:
            type_name: 智能体类型名称
            agent_class: 智能体类
            description: 描述
            required_config: 必需配置项
            optional_config: 可选配置项
            capabilities: 能力列表
            required_permissions: 所需权限列表
        """
        try:
            self.registered_types[type_name] = {
                "class": agent_class,
                "description": description,
                "required_config": required_config or [],
                "optional_config": optional_config or [],
                "capabilities": capabilities or [],
                "required_permissions": required_permissions or [],
                "registered_at": datetime.now().isoformat(),
                "usage_count": 0
            }
            
            self.usage_stats["total_registrations"] += 1
            self.usage_stats["type_usage"][type_name] = 0
            
            self.logger.info(f"智能体类型已注册: {type_name}")
        except Exception as e:
            self.logger.error(f"注册智能体类型错误: {str(e)}")
            raise e
    
    def unregister_agent_type(self, type_name: str) -> None:
        """
        注销智能体类型
        
        Args:
            type_name: 智能体类型名称
        """
        if type_name in self.registered_types:
            del self.registered_types[type_name]
            if type_name in self.usage_stats["type_usage"]:
                del self.usage_stats["type_usage"][type_name]
            self.logger.info(f"智能体类型已注销: {type_name}")
        else:
            self.logger.warning(f"智能体类型不存在: {type_name}")
    
    def get_registered_types(self) -> Dict[str, Dict[str, Any]]:
        """
        获取已注册的智能体类型
        
        Returns:
            Dict[str, Dict[str, Any]]: 智能体类型信息
        """
        types_info = {}
        for type_name, type_info in self.registered_types.items():
            types_info[type_name] = {
                "description": type_info["description"],
                "required_config": type_info["required_config"],
                "optional_config": type_info["optional_config"],
                "capabilities": type_info["capabilities"],
                "required_permissions": type_info["required_permissions"],
                "registered_at": type_info["registered_at"],
                "usage_count": type_info["usage_count"]
            }
        return types_info
    
    async def get_available_types_for_user(
        self,
        user_id: int,
        owner_id: int
    ) -> Dict[str, Dict[str, Any]]:
        """
        获取用户可用的智能体类型
        
        Args:
            user_id: 用户ID
            owner_id: 拥有者ID
            
        Returns:
            Dict[str, Dict[str, Any]]: 用户可用的智能体类型
        """
        try:
            # 检查用户是否存在
            user = await User.get_by_id(user_id)
            if not user:
                self.logger.error(f"用户不存在: {user_id}")
                return {}
            
            available_types = {}
            
            for type_name, type_info in self.registered_types.items():
                # 检查用户是否有权限使用此类型的智能体
                has_permission = await check_user_permission(
                    user_id=user_id,
                    owner_id=owner_id,
                    resource_type="agent_type",
                    action="use",
                    resource_id=type_name
                )
                
                if has_permission:
                    available_types[type_name] = {
                        "description": type_info["description"],
                        "required_config": type_info["required_config"],
                        "optional_config": type_info["optional_config"],
                        "capabilities": type_info["capabilities"],
                        "usage_count": type_info["usage_count"]
                    }
            
            return available_types
        except Exception as e:
            self.logger.error(f"获取用户可用智能体类型错误: {str(e)}")
            return {}
    
    def validate_config(
        self,
        type_name: str,
        config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        验证智能体配置
        
        Args:
            type_name: 智能体类型名称
            config: 配置信息
            
        Returns:
            Dict[str, Any]: 验证结果
        """
        if type_name not in self.registered_types:
            return {
                "valid": False,
                "errors": [f"智能体类型不存在: {type_name}"]
            }
        
        type_info = self.registered_types[type_name]
        errors = []
        
        # 检查必需配置项
        for required_key in type_info["required_config"]:
            if required_key not in config:
                errors.append(f"缺少必需配置项: {required_key}")
        
        # 检查配置项是否有效
        all_config_keys = set(type_info["required_config"] + type_info["optional_config"])
        for config_key in config.keys():
            if config_key not in all_config_keys:
                errors.append(f"无效的配置项: {config_key}")
        
        return {
            "valid": len(errors) == 0,
            "errors": errors
        }
    
    def get_agent_class(self, type_name: str) -> Optional[Type]:
        """
        获取智能体类
        
        Args:
            type_name: 智能体类型名称
            
        Returns:
            Optional[Type]: 智能体类
        """
        if type_name in self.registered_types:
            return self.registered_types[type_name]["class"]
        return None
    
    async def create_agent_instance(
        self,
        type_name: str,
        user_id: int,
        owner_id: int,
        agent_model: AgentModel,
        config: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> Optional[Any]:
        """
        创建智能体实例
        
        Args:
            type_name: 智能体类型名称
            user_id: 用户ID
            owner_id: 拥有者ID
            agent_model: 智能体模型
            config: 配置信息
            **kwargs: 其他参数
            
        Returns:
            Optional[Any]: 智能体实例
        """
        try:
            # 检查智能体类型是否存在
            if type_name not in self.registered_types:
                self.logger.error(f"智能体类型不存在: {type_name}")
                return None
            
            # 检查用户权限
            user = await User.get_by_id(user_id)
            if not user:
                self.logger.error(f"用户不存在: {user_id}")
                return None
            
            has_permission = await check_user_permission(
                user_id=user_id,
                owner_id=owner_id,
                resource_type="agent_type",
                action="create",
                resource_id=type_name
            )
            if not has_permission:
                self.logger.error(f"用户 {user_id} 没有权限创建 {type_name} 类型的智能体")
                return None
            
            # 验证配置
            config = config or {}
            validation_result = self.validate_config(type_name, config)
            if not validation_result["valid"]:
                self.logger.error(f"配置验证失败: {validation_result['errors']}")
                return None
            
            # 获取智能体类
            agent_class = self.registered_types[type_name]["class"]
            
            # 创建实例
            if type_name == "custom_llm":
                # CustomLLMAgent需要特殊处理
                instance = await agent_class.create(
                    user_id=user_id,
                    owner_id=owner_id,
                    agent_model=agent_model,
                    **kwargs
                )
            elif type_name == "workflow":
                # WorkflowAgent需要工作流模型
                workflow_id = config.get("workflow_id")
                if not workflow_id:
                    self.logger.error("WorkflowAgent需要workflow_id配置")
                    return None
                
                # 这里需要加载工作流模型
                # workflow_model = await Workflow.get_by_id(workflow_id)
                # instance = await agent_class.create(
                #     user_id=user_id,
                #     owner_id=owner_id,
                #     workflow_model=workflow_model,
                #     **kwargs
                # )
                instance = None  # 暂时返回None
            elif type_name == "tool":
                # ToolAgent
                instance = await agent_class.create(
                    user_id=user_id,
                    owner_id=owner_id,
                    agent_model=agent_model,
                    **kwargs
                )
            else:
                # 通用创建方法
                instance = agent_class(
                    user_id=user_id,
                    owner_id=owner_id,
                    agent_model=agent_model,
                    **kwargs
                )
            
            if instance:
                # 更新统计
                self.usage_stats["total_instances_created"] += 1
                self.usage_stats["last_activity"] = datetime.now().isoformat()
                self.registered_types[type_name]["usage_count"] += 1
                self.usage_stats["type_usage"][type_name] += 1
                
                # 更新用户使用统计
                if str(user_id) not in self.usage_stats["user_usage"]:
                    self.usage_stats["user_usage"][str(user_id)] = 0
                self.usage_stats["user_usage"][str(user_id)] += 1
                
                # 缓存实例
                instance_key = f"{type_name}_{agent_model.id}_{user_id}"
                self.agent_instances[instance_key] = instance
                
                self.logger.info(f"智能体实例已创建: {type_name} (用户: {user_id})")
            
            return instance
        except Exception as e:
            self.logger.error(f"创建智能体实例错误: {str(e)}")
            return None
    
    def get_cached_instance(
        self,
        type_name: str,
        agent_id: int,
        user_id: int
    ) -> Optional[Any]:
        """
        获取缓存的智能体实例
        
        Args:
            type_name: 智能体类型名称
            agent_id: 智能体ID
            user_id: 用户ID
            
        Returns:
            Optional[Any]: 缓存的智能体实例
        """
        instance_key = f"{type_name}_{agent_id}_{user_id}"
        return self.agent_instances.get(instance_key)
    
    def remove_cached_instance(
        self,
        type_name: str,
        agent_id: int,
        user_id: int
    ) -> None:
        """
        移除缓存的智能体实例
        
        Args:
            type_name: 智能体类型名称
            agent_id: 智能体ID
            user_id: 用户ID
        """
        instance_key = f"{type_name}_{agent_id}_{user_id}"
        if instance_key in self.agent_instances:
            del self.agent_instances[instance_key]
            self.logger.info(f"智能体实例已从缓存中移除: {instance_key}")
    
    def clear_user_cache(self, user_id: int) -> None:
        """
        清除用户的所有缓存实例
        
        Args:
            user_id: 用户ID
        """
        keys_to_remove = []
        for key in self.agent_instances.keys():
            if key.endswith(f"_{user_id}"):
                keys_to_remove.append(key)
        
        for key in keys_to_remove:
            del self.agent_instances[key]
        
        self.logger.info(f"用户 {user_id} 的所有缓存实例已清除")
    
    def get_usage_stats(self) -> Dict[str, Any]:
        """
        获取使用统计
        
        Returns:
            Dict[str, Any]: 使用统计信息
        """
        return self.usage_stats.copy()
    
    def get_type_capabilities(self, type_name: str) -> List[str]:
        """
        获取智能体类型的能力列表
        
        Args:
            type_name: 智能体类型名称
            
        Returns:
            List[str]: 能力列表
        """
        if type_name in self.registered_types:
            return self.registered_types[type_name]["capabilities"].copy()
        return []
    
    def search_types_by_capability(
        self,
        capability: str
    ) -> List[str]:
        """
        根据能力搜索智能体类型
        
        Args:
            capability: 能力名称
            
        Returns:
            List[str]: 支持该能力的智能体类型列表
        """
        matching_types = []
        for type_name, type_info in self.registered_types.items():
            if capability in type_info["capabilities"]:
                matching_types.append(type_name)
        return matching_types

# 全局注册器实例
agent_registry = AgentRegistry()