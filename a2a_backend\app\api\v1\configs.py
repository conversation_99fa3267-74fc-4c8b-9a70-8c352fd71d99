# -*- coding: utf-8 -*-
"""
A2A多智能体系统配置管理API

提供系统配置、用户配置、LLM配置和工具配置的管理接口
"""

import json
from datetime import datetime
from typing import Optional, List, Dict, Any, Union
from fastapi import APIRouter, Depends, HTTPException, status, Query, Body, UploadFile, File
from fastapi.responses import JSONResponse, StreamingResponse
from sqlalchemy.orm import Session
from pydantic import BaseModel, Field
from io import BytesIO

from app.core.database import get_db
from app.core.logging import get_logger
from app.auth.dependencies import get_current_user
from app.models.user import User
from app.models.config import SystemConfig, UserConfig, LLMConfig, ToolConfig
from app.services.config_service import get_config_service, ConfigService
from app.schemas.config import (
    SystemConfigCreate, SystemConfigUpdate, SystemConfigResponse,
    UserConfigCreate, UserConfigUpdate, UserConfigResponse,
    LLMConfigCreate, LLMConfigUpdate, LLMConfigResponse,
    ToolConfigCreate, ToolConfigUpdate, ToolConfigResponse,
    ConfigBackupResponse, ConfigImportRequest
)

logger = get_logger(__name__)
router = APIRouter(prefix="/configs", tags=["配置管理"])


# ==================== 请求/响应模型 ====================

class ConfigValueRequest(BaseModel):
    """配置值请求模型"""
    config_key: str = Field(..., description="配置键")
    config_value: Any = Field(..., description="配置值")
    value_type: str = Field(default="string", description="值类型")
    description: Optional[str] = Field(None, description="配置描述")
    category: Optional[str] = Field("general", description="配置分类")
    is_sensitive: bool = Field(default=False, description="是否敏感信息")
    is_readonly: bool = Field(default=False, description="是否只读")


class ConfigListResponse(BaseModel):
    """配置列表响应模型"""
    total: int = Field(..., description="总数量")
    items: List[Dict[str, Any]] = Field(..., description="配置项列表")
    skip: int = Field(..., description="跳过数量")
    limit: int = Field(..., description="限制数量")


class ConfigStatsResponse(BaseModel):
    """配置统计响应模型"""
    total_configs: int = Field(..., description="总配置数")
    system_configs: int = Field(..., description="系统配置数")
    user_configs: int = Field(..., description="用户配置数")
    llm_configs: int = Field(..., description="LLM配置数")
    tool_configs: int = Field(..., description="工具配置数")
    categories: Dict[str, int] = Field(..., description="分类统计")


# ==================== 系统配置管理接口 ====================

@router.get("/system", response_model=ConfigListResponse, summary="获取系统配置列表")
async def list_system_configs(
    category: Optional[str] = Query(None, description="配置分类过滤"),
    include_sensitive: bool = Query(False, description="是否包含敏感配置"),
    skip: int = Query(0, ge=0, description="跳过数量"),
    limit: int = Query(100, ge=1, le=1000, description="限制数量"),
    current_user: User = Depends(get_current_user),
    config_service: ConfigService = Depends(get_config_service)
):
    """
    获取系统配置列表
    
    需要管理员权限才能查看敏感配置
    """
    try:
        configs = await config_service.list_system_configs(
            user_id=current_user.id,
            category=category,
            include_sensitive=include_sensitive,
            skip=skip,
            limit=limit
        )
        
        # 转换为响应格式
        items = []
        for config in configs:
            item = {
                "config_key": config.config_key,
                "config_value": config_service._parse_config_value(
                    config.config_value, config.value_type
                ) if not config.is_sensitive or current_user.role == "admin" else "***",
                "value_type": config.value_type,
                "category": config.category,
                "description": config.description,
                "is_required": config.is_required,
                "is_sensitive": config.is_sensitive,
                "is_readonly": config.is_readonly,
                "default_value": config.default_value,
                "updated_at": config.updated_at.isoformat() if config.updated_at else None
            }
            items.append(item)
        
        return ConfigListResponse(
            total=len(items),
            items=items,
            skip=skip,
            limit=limit
        )
        
    except Exception as e:
        logger.error(f"获取系统配置列表失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取系统配置列表失败: {str(e)}"
        )


@router.get("/system/{config_key}", summary="获取系统配置")
async def get_system_config(
    config_key: str,
    current_user: User = Depends(get_current_user),
    config_service: ConfigService = Depends(get_config_service)
):
    """
    获取指定的系统配置
    """
    try:
        value = await config_service.get_system_config(
            config_key=config_key,
            user_id=current_user.id
        )
        
        return {
            "config_key": config_key,
            "config_value": value,
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取系统配置失败: {config_key}, 错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取系统配置失败: {str(e)}"
        )


@router.post("/system", response_model=SystemConfigResponse, summary="设置系统配置")
async def set_system_config(
    config_request: ConfigValueRequest,
    current_user: User = Depends(get_current_user),
    config_service: ConfigService = Depends(get_config_service)
):
    """
    设置系统配置
    
    需要管理员权限
    """
    try:
        config = await config_service.set_system_config(
            config_key=config_request.config_key,
            config_value=config_request.config_value,
            user_id=current_user.id,
            description=config_request.description,
            category=config_request.category,
            value_type=config_request.value_type,
            is_sensitive=config_request.is_sensitive,
            is_readonly=config_request.is_readonly
        )
        
        return SystemConfigResponse(
            id=config.id,
            config_key=config.config_key,
            config_value=config_service._parse_config_value(
                config.config_value, config.value_type
            ),
            value_type=config.value_type,
            category=config.category,
            description=config.description,
            is_required=config.is_required,
            is_sensitive=config.is_sensitive,
            is_readonly=config.is_readonly,
            default_value=config.default_value,
            validation_rule=config.validation_rule,
            updated_by=config.updated_by,
            updated_at=config.updated_at
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"设置系统配置失败: {config_request.config_key}, 错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"设置系统配置失败: {str(e)}"
        )


@router.delete("/system/{config_key}", summary="删除系统配置")
async def delete_system_config(
    config_key: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    删除系统配置
    
    需要管理员权限
    """
    try:
        # 权限检查
        if current_user.role != "admin":
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="删除系统配置需要管理员权限"
            )
        
        # 查找配置
        config = db.query(SystemConfig).filter(
            SystemConfig.config_key == config_key
        ).first()
        
        if not config:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="系统配置不存在"
            )
        
        # 检查是否为只读配置
        if config.is_readonly:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="只读配置不能删除"
            )
        
        # 删除配置
        db.delete(config)
        db.commit()
        
        # 清除缓存
        config_service = get_config_service(db)
        config_service.clear_cache(f"system_config:{config_key}")
        
        return {"message": f"系统配置已删除: {config_key}"}
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"删除系统配置失败: {config_key}, 错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除系统配置失败: {str(e)}"
        )


# ==================== 用户配置管理接口 ====================

@router.get("/user", response_model=ConfigListResponse, summary="获取用户配置列表")
async def list_user_configs(
    skip: int = Query(0, ge=0, description="跳过数量"),
    limit: int = Query(100, ge=1, le=1000, description="限制数量"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    获取当前用户的配置列表
    """
    try:
        configs = db.query(UserConfig).filter(
            UserConfig.user_id == current_user.id
        ).offset(skip).limit(limit).all()
        
        config_service = get_config_service(db)
        
        # 转换为响应格式
        items = []
        for config in configs:
            item = {
                "config_key": config.config_key,
                "config_value": config_service._parse_config_value(
                    config.config_value, config.value_type
                ),
                "value_type": config.value_type,
                "description": config.description,
                "created_at": config.created_at.isoformat(),
                "updated_at": config.updated_at.isoformat() if config.updated_at else None
            }
            items.append(item)
        
        return ConfigListResponse(
            total=len(items),
            items=items,
            skip=skip,
            limit=limit
        )
        
    except Exception as e:
        logger.error(f"获取用户配置列表失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取用户配置列表失败: {str(e)}"
        )


@router.get("/user/{config_key}", summary="获取用户配置")
async def get_user_config(
    config_key: str,
    current_user: User = Depends(get_current_user),
    config_service: ConfigService = Depends(get_config_service)
):
    """
    获取指定的用户配置
    """
    try:
        value = await config_service.get_user_config(
            user_id=current_user.id,
            config_key=config_key
        )
        
        return {
            "config_key": config_key,
            "config_value": value,
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取用户配置失败: {config_key}, 错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取用户配置失败: {str(e)}"
        )


@router.post("/user", response_model=UserConfigResponse, summary="设置用户配置")
async def set_user_config(
    config_key: str = Body(..., description="配置键"),
    config_value: Any = Body(..., description="配置值"),
    value_type: str = Body(default="string", description="值类型"),
    description: Optional[str] = Body(None, description="配置描述"),
    current_user: User = Depends(get_current_user),
    config_service: ConfigService = Depends(get_config_service)
):
    """
    设置用户配置
    """
    try:
        config = await config_service.set_user_config(
            user_id=current_user.id,
            config_key=config_key,
            config_value=config_value,
            value_type=value_type,
            description=description
        )
        
        return UserConfigResponse(
            id=config.id,
            user_id=config.user_id,
            config_key=config.config_key,
            config_value=config_service._parse_config_value(
                config.config_value, config.value_type
            ),
            value_type=config.value_type,
            description=config.description,
            created_at=config.created_at,
            updated_at=config.updated_at
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"设置用户配置失败: {config_key}, 错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"设置用户配置失败: {str(e)}"
        )


@router.delete("/user/{config_key}", summary="删除用户配置")
async def delete_user_config(
    config_key: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    删除用户配置
    """
    try:
        # 查找配置
        config = db.query(UserConfig).filter(
            UserConfig.user_id == current_user.id,
            UserConfig.config_key == config_key
        ).first()
        
        if not config:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户配置不存在"
            )
        
        # 删除配置
        db.delete(config)
        db.commit()
        
        # 清除缓存
        config_service = get_config_service(db)
        config_service.clear_cache(f"user_config:{current_user.id}:{config_key}")
        
        return {"message": f"用户配置已删除: {config_key}"}
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"删除用户配置失败: {config_key}, 错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除用户配置失败: {str(e)}"
        )


# ==================== LLM配置管理接口 ====================

@router.get("/llm", response_model=List[LLMConfigResponse], summary="获取LLM配置列表")
async def list_llm_configs(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    获取当前用户的LLM配置列表
    """
    try:
        configs = db.query(LLMConfig).filter(
            LLMConfig.user_id == current_user.id
        ).all()
        
        return [
            LLMConfigResponse(
                id=config.id,
                user_id=config.user_id,
                config_name=config.config_name,
                model_name=config.model_name,
                model_params=config.model_params,
                api_key="***" if config.api_key else None,  # 隐藏API密钥
                api_endpoint=config.api_endpoint,
                description=config.description,
                is_active=config.is_active,
                created_at=config.created_at,
                updated_at=config.updated_at
            )
            for config in configs
        ]
        
    except Exception as e:
        logger.error(f"获取LLM配置列表失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取LLM配置列表失败: {str(e)}"
        )


@router.get("/llm/{config_name}", response_model=LLMConfigResponse, summary="获取LLM配置")
async def get_llm_config(
    config_name: str,
    current_user: User = Depends(get_current_user),
    config_service: ConfigService = Depends(get_config_service)
):
    """
    获取指定的LLM配置
    """
    try:
        config = await config_service.get_llm_config(
            user_id=current_user.id,
            config_name=config_name
        )
        
        if not config:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="LLM配置不存在"
            )
        
        return LLMConfigResponse(
            id=config.id,
            user_id=config.user_id,
            config_name=config.config_name,
            model_name=config.model_name,
            model_params=config.model_params,
            api_key="***" if config.api_key else None,  # 隐藏API密钥
            api_endpoint=config.api_endpoint,
            description=config.description,
            is_active=config.is_active,
            created_at=config.created_at,
            updated_at=config.updated_at
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取LLM配置失败: {config_name}, 错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取LLM配置失败: {str(e)}"
        )


@router.post("/llm", response_model=LLMConfigResponse, summary="创建LLM配置")
async def create_llm_config(
    config_data: LLMConfigCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    创建LLM配置
    """
    try:
        # 检查配置名称是否已存在
        existing_config = db.query(LLMConfig).filter(
            LLMConfig.user_id == current_user.id,
            LLMConfig.config_name == config_data.config_name
        ).first()
        
        if existing_config:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="LLM配置名称已存在"
            )
        
        # 创建配置
        config = LLMConfig(
            user_id=current_user.id,
            config_name=config_data.config_name,
            model_name=config_data.model_name,
            model_params=config_data.model_params,
            api_key=config_data.api_key,
            api_endpoint=config_data.api_endpoint,
            description=config_data.description,
            is_active=config_data.is_active
        )
        
        db.add(config)
        db.commit()
        db.refresh(config)
        
        return LLMConfigResponse(
            id=config.id,
            user_id=config.user_id,
            config_name=config.config_name,
            model_name=config.model_name,
            model_params=config.model_params,
            api_key="***" if config.api_key else None,
            api_endpoint=config.api_endpoint,
            description=config.description,
            is_active=config.is_active,
            created_at=config.created_at,
            updated_at=config.updated_at
        )
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"创建LLM配置失败: {config_data.config_name}, 错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建LLM配置失败: {str(e)}"
        )


# ==================== 工具配置管理接口 ====================

@router.get("/tool", response_model=List[ToolConfigResponse], summary="获取工具配置列表")
async def list_tool_configs(
    tool_name: Optional[str] = Query(None, description="工具名称过滤"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    获取当前用户的工具配置列表
    """
    try:
        query = db.query(ToolConfig).filter(
            ToolConfig.user_id == current_user.id
        )
        
        if tool_name:
            query = query.filter(ToolConfig.tool_name == tool_name)
        
        configs = query.all()
        
        return [
            ToolConfigResponse(
                id=config.id,
                user_id=config.user_id,
                tool_name=config.tool_name,
                version=config.version,
                config_data=config.config_data,
                description=config.description,
                is_active=config.is_active,
                created_at=config.created_at,
                updated_at=config.updated_at
            )
            for config in configs
        ]
        
    except Exception as e:
        logger.error(f"获取工具配置列表失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取工具配置列表失败: {str(e)}"
        )


@router.get("/tool/{tool_name}", response_model=ToolConfigResponse, summary="获取工具配置")
async def get_tool_config(
    tool_name: str,
    version: Optional[str] = Query(None, description="版本号"),
    current_user: User = Depends(get_current_user),
    config_service: ConfigService = Depends(get_config_service)
):
    """
    获取指定的工具配置
    """
    try:
        config = await config_service.get_tool_config(
            user_id=current_user.id,
            tool_name=tool_name,
            version=version
        )
        
        if not config:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="工具配置不存在"
            )
        
        return ToolConfigResponse(
            id=config.id,
            user_id=config.user_id,
            tool_name=config.tool_name,
            version=config.version,
            config_data=config.config_data,
            description=config.description,
            is_active=config.is_active,
            created_at=config.created_at,
            updated_at=config.updated_at
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取工具配置失败: {tool_name}, 错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取工具配置失败: {str(e)}"
        )


# ==================== 配置导入导出接口 ====================

@router.post("/export", summary="导出配置")
async def export_configs(
    include_system: bool = Body(default=False, description="是否包含系统配置"),
    include_user: bool = Body(default=True, description="是否包含用户配置"),
    include_llm: bool = Body(default=True, description="是否包含LLM配置"),
    include_tool: bool = Body(default=True, description="是否包含工具配置"),
    current_user: User = Depends(get_current_user),
    config_service: ConfigService = Depends(get_config_service),
    db: Session = Depends(get_db)
):
    """
    导出配置数据
    """
    try:
        export_data = {
            "export_info": {
                "user_id": current_user.id,
                "username": current_user.username,
                "export_time": datetime.utcnow().isoformat(),
                "version": "1.0"
            },
            "configs": {}
        }
        
        # 导出系统配置（需要管理员权限）
        if include_system and current_user.role == "admin":
            system_configs = await config_service.list_system_configs(
                user_id=current_user.id,
                include_sensitive=False
            )
            export_data["configs"]["system"] = [
                {
                    "config_key": config.config_key,
                    "config_value": config.config_value,
                    "value_type": config.value_type,
                    "category": config.category,
                    "description": config.description
                }
                for config in system_configs
            ]
        
        # 导出用户配置
        if include_user:
            backup_data = await config_service.backup_user_configs(current_user.id)
            export_data["configs"]["user"] = backup_data["user_configs"]
            
            if include_llm:
                export_data["configs"]["llm"] = backup_data["llm_configs"]
            
            if include_tool:
                export_data["configs"]["tool"] = backup_data["tool_configs"]
        
        # 生成文件响应
        json_data = json.dumps(export_data, ensure_ascii=False, indent=2)
        
        return StreamingResponse(
            BytesIO(json_data.encode('utf-8')),
            media_type="application/json",
            headers={
                "Content-Disposition": f"attachment; filename=configs_export_{current_user.id}_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}.json"
            }
        )
        
    except Exception as e:
        logger.error(f"导出配置失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"导出配置失败: {str(e)}"
        )


@router.post("/import", summary="导入配置")
async def import_configs(
    file: UploadFile = File(..., description="配置文件"),
    overwrite: bool = Body(default=False, description="是否覆盖现有配置"),
    current_user: User = Depends(get_current_user),
    config_service: ConfigService = Depends(get_config_service),
    db: Session = Depends(get_db)
):
    """
    导入配置数据
    """
    try:
        # 读取文件内容
        content = await file.read()
        
        try:
            import_data = json.loads(content.decode('utf-8'))
        except json.JSONDecodeError as e:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"配置文件格式错误: {str(e)}"
            )
        
        # 验证文件格式
        if "configs" not in import_data:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="配置文件格式错误：缺少configs字段"
            )
        
        import_results = {
            "imported": 0,
            "skipped": 0,
            "errors": []
        }
        
        configs = import_data["configs"]
        
        # 导入用户配置
        if "user" in configs:
            for config_data in configs["user"]:
                try:
                    existing_config = db.query(UserConfig).filter(
                        UserConfig.user_id == current_user.id,
                        UserConfig.config_key == config_data["config_key"]
                    ).first()
                    
                    if existing_config and not overwrite:
                        import_results["skipped"] += 1
                        continue
                    
                    await config_service.set_user_config(
                        user_id=current_user.id,
                        config_key=config_data["config_key"],
                        config_value=config_data["config_value"],
                        value_type=config_data.get("value_type", "string"),
                        description=config_data.get("description")
                    )
                    
                    import_results["imported"] += 1
                    
                except Exception as e:
                    import_results["errors"].append({
                        "config_key": config_data.get("config_key", "unknown"),
                        "error": str(e)
                    })
        
        # 导入LLM配置
        if "llm" in configs:
            for config_data in configs["llm"]:
                try:
                    existing_config = db.query(LLMConfig).filter(
                        LLMConfig.user_id == current_user.id,
                        LLMConfig.config_name == config_data["config_name"]
                    ).first()
                    
                    if existing_config and not overwrite:
                        import_results["skipped"] += 1
                        continue
                    
                    if existing_config:
                        # 更新现有配置
                        for key, value in config_data.items():
                            if hasattr(existing_config, key):
                                setattr(existing_config, key, value)
                        existing_config.updated_at = datetime.utcnow()
                    else:
                        # 创建新配置
                        new_config = LLMConfig(
                            user_id=current_user.id,
                            **config_data
                        )
                        db.add(new_config)
                    
                    import_results["imported"] += 1
                    
                except Exception as e:
                    import_results["errors"].append({
                        "config_name": config_data.get("config_name", "unknown"),
                        "error": str(e)
                    })
        
        # 导入工具配置
        if "tool" in configs:
            for config_data in configs["tool"]:
                try:
                    existing_config = db.query(ToolConfig).filter(
                        ToolConfig.user_id == current_user.id,
                        ToolConfig.tool_name == config_data["tool_name"],
                        ToolConfig.version == config_data["version"]
                    ).first()
                    
                    if existing_config and not overwrite:
                        import_results["skipped"] += 1
                        continue
                    
                    if existing_config:
                        # 更新现有配置
                        for key, value in config_data.items():
                            if hasattr(existing_config, key):
                                setattr(existing_config, key, value)
                        existing_config.updated_at = datetime.utcnow()
                    else:
                        # 创建新配置
                        new_config = ToolConfig(
                            user_id=current_user.id,
                            **config_data
                        )
                        db.add(new_config)
                    
                    import_results["imported"] += 1
                    
                except Exception as e:
                    import_results["errors"].append({
                        "tool_name": config_data.get("tool_name", "unknown"),
                        "version": config_data.get("version", "unknown"),
                        "error": str(e)
                    })
        
        db.commit()
        
        # 清除缓存
        config_service.clear_cache()
        
        return {
            "message": "配置导入完成",
            "results": import_results
        }
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"导入配置失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"导入配置失败: {str(e)}"
        )


# ==================== 配置统计接口 ====================

@router.get("/stats", response_model=ConfigStatsResponse, summary="获取配置统计")
async def get_config_stats(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    获取配置统计信息
    """
    try:
        # 用户配置统计
        user_configs_count = db.query(UserConfig).filter(
            UserConfig.user_id == current_user.id
        ).count()
        
        # LLM配置统计
        llm_configs_count = db.query(LLMConfig).filter(
            LLMConfig.user_id == current_user.id
        ).count()
        
        # 工具配置统计
        tool_configs_count = db.query(ToolConfig).filter(
            ToolConfig.user_id == current_user.id
        ).count()
        
        # 系统配置统计（管理员可见）
        system_configs_count = 0
        categories = {}
        
        if current_user.role == "admin":
            system_configs_count = db.query(SystemConfig).count()
            
            # 系统配置分类统计
            category_stats = db.query(
                SystemConfig.category,
                db.func.count(SystemConfig.id)
            ).group_by(SystemConfig.category).all()
            
            categories = {stat[0]: stat[1] for stat in category_stats}
        
        total_configs = user_configs_count + llm_configs_count + tool_configs_count + system_configs_count
        
        return ConfigStatsResponse(
            total_configs=total_configs,
            system_configs=system_configs_count,
            user_configs=user_configs_count,
            llm_configs=llm_configs_count,
            tool_configs=tool_configs_count,
            categories=categories
        )
        
    except Exception as e:
        logger.error(f"获取配置统计失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取配置统计失败: {str(e)}"
        )


# ==================== 缓存管理接口 ====================

@router.post("/cache/clear", summary="清除配置缓存")
async def clear_config_cache(
    pattern: Optional[str] = Body(None, description="缓存键模式"),
    current_user: User = Depends(get_current_user),
    config_service: ConfigService = Depends(get_config_service)
):
    """
    清除配置缓存
    
    管理员可以清除所有缓存，普通用户只能清除自己的缓存
    """
    try:
        if current_user.role == "admin":
            # 管理员可以清除所有缓存
            config_service.clear_cache(pattern)
            message = "配置缓存已清除"
        else:
            # 普通用户只能清除自己的缓存
            user_pattern = f"user_config:{current_user.id}"
            if pattern:
                user_pattern = f"{user_pattern}:{pattern}"
            config_service.clear_cache(user_pattern)
            message = "用户配置缓存已清除"
        
        return {"message": message}
        
    except Exception as e:
        logger.error(f"清除配置缓存失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"清除配置缓存失败: {str(e)}"
        )