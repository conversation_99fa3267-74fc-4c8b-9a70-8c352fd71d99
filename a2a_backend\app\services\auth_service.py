# -*- coding: utf-8 -*-
"""
A2A多智能体系统认证服务

提供JWT令牌管理、权限检查和访问控制功能
"""

import json
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Set
from sqlalchemy import and_, or_, desc
from sqlalchemy.orm import Session
from loguru import logger

from app.core.database import get_database_manager
from app.models.user import User, UserToken, UserPermission
from app.auth.jwt_handler import J<PERSON>THandler
from app.auth.password import PasswordHandler
from app.core.config import get_settings


class AuthService:
    """
    认证服务
    
    提供用户认证、授权和会话管理功能
    """
    
    def __init__(self):
        self.db_manager = get_database_manager()
        self.jwt_handler = JWTHandler()
        self.password_handler = PasswordHandler()
        self.settings = get_settings()
    
    async def refresh_token(self, refresh_token: str) -> Dict[str, Any]:
        """
        刷新访问令牌
        
        Args:
            refresh_token: 刷新令牌
            
        Returns:
            Dict[str, Any]: 新的访问令牌信息
            
        Raises:
            ValueError: 令牌无效或已过期
            Exception: 刷新失败
        """
        async with self.db_manager.get_session() as session:
            try:
                # 验证刷新令牌
                payload = await self.jwt_handler.verify_token(refresh_token)
                
                if payload.get("token_type") != "refresh":
                    raise ValueError("无效的刷新令牌")
                
                user_id = payload.get("user_id")
                session_id = payload.get("session_id")
                
                # 检查用户是否存在且活跃
                result = await session.execute(
                    session.query(User).filter(
                        and_(
                            User.id == user_id,
                            User.is_active == True,
                            User.deleted_at.is_(None)
                        )
                    )
                )
                user = result.scalar_one_or_none()
                
                if not user:
                    raise ValueError("用户不存在或已被禁用")
                
                # 检查令牌是否在数据库中存在且有效
                token_result = await session.execute(
                    session.query(UserToken).filter(
                        and_(
                            UserToken.user_id == user_id,
                            UserToken.token_type == "refresh",
                            UserToken.expires_at > datetime.utcnow(),
                            UserToken.revoked_at.is_(None)
                        )
                    )
                )
                db_token = token_result.scalar_one_or_none()
                
                if not db_token:
                    raise ValueError("刷新令牌已失效")
                
                # 生成新的访问令牌
                new_tokens = await self.jwt_handler.generate_tokens(user_id, session_id)
                
                logger.info(f"令牌刷新成功", extra={
                    "user_id": user_id,
                    "session_id": session_id
                })
                
                return {
                    "success": True,
                    "message": "令牌刷新成功",
                    "data": {
                        "access_token": new_tokens["access_token"],
                        "token_type": new_tokens["token_type"],
                        "expires_in": new_tokens["expires_in"]
                    }
                }
                
            except ValueError as e:
                logger.warning(f"令牌刷新失败: {str(e)}")
                raise
            except Exception as e:
                logger.error(f"令牌刷新异常: {str(e)}")
                raise Exception(f"令牌刷新失败: {str(e)}")
    
    async def logout(self, access_token: str) -> Dict[str, Any]:
        """
        用户登出
        
        Args:
            access_token: 访问令牌
            
        Returns:
            Dict[str, Any]: 登出结果
        """
        async with self.db_manager.get_session() as session:
            try:
                # 验证访问令牌
                payload = await self.jwt_handler.verify_token(access_token)
                
                user_id = payload.get("user_id")
                session_id = payload.get("session_id")
                
                # 撤销所有相关令牌
                await session.execute(
                    session.query(UserToken)
                    .filter(
                        and_(
                            UserToken.user_id == user_id,
                            UserToken.session_id == session_id,
                            UserToken.revoked_at.is_(None)
                        )
                    )
                    .update({"revoked_at": datetime.utcnow()})
                )
                
                await session.commit()
                
                logger.info(f"用户登出成功", extra={
                    "user_id": user_id,
                    "session_id": session_id
                })
                
                return {
                    "success": True,
                    "message": "登出成功"
                }
                
            except Exception as e:
                await session.rollback()
                logger.error(f"用户登出异常: {str(e)}")
                raise Exception(f"登出失败: {str(e)}")
    
    async def verify_token(self, token: str) -> Dict[str, Any]:
        """
        验证访问令牌
        
        Args:
            token: 访问令牌
            
        Returns:
            Dict[str, Any]: 令牌验证结果和用户信息
            
        Raises:
            ValueError: 令牌无效
        """
        try:
            # 验证JWT令牌
            payload = await self.jwt_handler.verify_token(token)
            
            if payload.get("token_type") != "access":
                raise ValueError("无效的访问令牌")
            
            user_id = payload.get("user_id")
            session_id = payload.get("session_id")
            
            # 检查用户状态
            async with self.db_manager.get_session() as session:
                result = await session.execute(
                    session.query(User).filter(
                        and_(
                            User.id == user_id,
                            User.is_active == True,
                            User.deleted_at.is_(None)
                        )
                    )
                )
                user = result.scalar_one_or_none()
                
                if not user:
                    raise ValueError("用户不存在或已被禁用")
                
                # 检查令牌是否被撤销
                token_result = await session.execute(
                    session.query(UserToken).filter(
                        and_(
                            UserToken.user_id == user_id,
                            UserToken.session_id == session_id,
                            UserToken.token_type == "access",
                            UserToken.revoked_at.is_(None)
                        )
                    )
                )
                db_token = token_result.scalar_one_or_none()
                
                if not db_token:
                    raise ValueError("令牌已被撤销")
                
                return {
                    "valid": True,
                    "user_id": user_id,
                    "session_id": session_id,
                    "username": user.username,
                    "email": user.email,
                    "role": user.role,
                    "expires_at": payload.get("exp")
                }
                
        except ValueError as e:
            logger.warning(f"令牌验证失败: {str(e)}")
            raise
        except Exception as e:
            logger.error(f"令牌验证异常: {str(e)}")
            raise ValueError(f"令牌验证失败: {str(e)}")
    
    async def check_permission(self, user_id: int, resource_type: str, 
                              resource_id: Optional[str], permission: str) -> bool:
        """
        检查用户权限
        
        Args:
            user_id: 用户ID
            resource_type: 资源类型
            resource_id: 资源ID
            permission: 权限名称
            
        Returns:
            bool: 是否有权限
        """
        async with self.db_manager.get_session() as session:
            try:
                # 检查用户角色权限
                user_result = await session.execute(
                    session.query(User).filter(
                        and_(
                            User.id == user_id,
                            User.is_active == True,
                            User.deleted_at.is_(None)
                        )
                    )
                )
                user = user_result.scalar_one_or_none()
                
                if not user:
                    return False
                
                # 超级管理员拥有所有权限
                if user.role == "super_admin":
                    return True
                
                # 管理员拥有大部分权限
                if user.role == "admin" and permission in ["read", "write", "update", "delete"]:
                    return True
                
                # 检查具体权限
                permission_result = await session.execute(
                    session.query(UserPermission).filter(
                        and_(
                            UserPermission.user_id == user_id,
                            UserPermission.resource_type == resource_type,
                            or_(
                                UserPermission.resource_id == resource_id,
                                UserPermission.resource_id.is_(None)  # 通用权限
                            ),
                            UserPermission.permission == permission,
                            or_(
                                UserPermission.expires_at.is_(None),
                                UserPermission.expires_at > datetime.utcnow()
                            )
                        )
                    )
                )
                
                return permission_result.scalar_one_or_none() is not None
                
            except Exception as e:
                logger.error(f"权限检查异常: {str(e)}")
                return False
    
    async def check_resource_ownership(self, user_id: int, resource_type: str, resource_id: str) -> bool:
        """
        检查用户是否拥有资源
        
        Args:
            user_id: 用户ID
            resource_type: 资源类型
            resource_id: 资源ID
            
        Returns:
            bool: 是否拥有资源
        """
        async with self.db_manager.get_session() as session:
            try:
                # 根据资源类型查询所有权
                if resource_type == "agent":
                    from app.models.agent import Agent
                    result = await session.execute(
                        session.query(Agent).filter(
                            and_(
                                Agent.id == resource_id,
                                Agent.user_id == user_id,
                                Agent.is_active == True,
                                Agent.deleted_at.is_(None)
                            )
                        )
                    )
                elif resource_type == "session":
                    from app.models.session import Session as SessionModel
                    result = await session.execute(
                        session.query(SessionModel).filter(
                            and_(
                                SessionModel.id == resource_id,
                                SessionModel.user_id == user_id,
                                SessionModel.is_active == True,
                                SessionModel.deleted_at.is_(None)
                            )
                        )
                    )
                elif resource_type == "task":
                    from app.models.task import Task
                    result = await session.execute(
                        session.query(Task).filter(
                            and_(
                                Task.id == resource_id,
                                Task.user_id == user_id,
                                Task.is_active == True,
                                Task.deleted_at.is_(None)
                            )
                        )
                    )
                elif resource_type == "workflow":
                    from app.models.workflow import Workflow
                    result = await session.execute(
                        session.query(Workflow).filter(
                            and_(
                                Workflow.id == resource_id,
                                Workflow.user_id == user_id,
                                Workflow.is_active == True,
                                Workflow.deleted_at.is_(None)
                            )
                        )
                    )
                else:
                    return False
                
                return result.scalar_one_or_none() is not None
                
            except Exception as e:
                logger.error(f"资源所有权检查异常: {str(e)}")
                return False
    
    async def get_user_roles_and_permissions(self, user_id: int) -> Dict[str, Any]:
        """
        获取用户角色和权限信息
        
        Args:
            user_id: 用户ID
            
        Returns:
            Dict[str, Any]: 角色和权限信息
        """
        async with self.db_manager.get_session() as session:
            try:
                # 获取用户信息
                user_result = await session.execute(
                    session.query(User).filter(
                        and_(
                            User.id == user_id,
                            User.is_active == True,
                            User.deleted_at.is_(None)
                        )
                    )
                )
                user = user_result.scalar_one_or_none()
                
                if not user:
                    raise ValueError("用户不存在")
                
                # 获取用户权限
                permissions_result = await session.execute(
                    session.query(UserPermission).filter(
                        and_(
                            UserPermission.user_id == user_id,
                            or_(
                                UserPermission.expires_at.is_(None),
                                UserPermission.expires_at > datetime.utcnow()
                            )
                        )
                    ).order_by(UserPermission.resource_type, UserPermission.permission)
                )
                permissions = permissions_result.scalars().all()
                
                # 组织权限数据
                permission_groups = {}
                for perm in permissions:
                    resource_key = f"{perm.resource_type}:{perm.resource_id or '*'}"
                    if resource_key not in permission_groups:
                        permission_groups[resource_key] = []
                    permission_groups[resource_key].append(perm.permission)
                
                return {
                    "user_id": user_id,
                    "username": user.username,
                    "role": user.role,
                    "permissions": [
                        {
                            "resource_type": perm.resource_type,
                            "resource_id": perm.resource_id,
                            "permission": perm.permission,
                            "granted_at": perm.granted_at.isoformat(),
                            "expires_at": perm.expires_at.isoformat() if perm.expires_at else None
                        }
                        for perm in permissions
                    ],
                    "permission_groups": permission_groups
                }
                
            except Exception as e:
                logger.error(f"获取用户角色权限异常: {str(e)}")
                raise Exception(f"获取用户角色权限失败: {str(e)}")
    
    async def reset_password(self, email: str, reset_token: str, new_password: str) -> Dict[str, Any]:
        """
        重置用户密码
        
        Args:
            email: 用户邮箱
            reset_token: 重置令牌
            new_password: 新密码
            
        Returns:
            Dict[str, Any]: 重置结果
            
        Raises:
            ValueError: 令牌无效或用户不存在
            Exception: 重置失败
        """
        async with self.db_manager.get_session() as session:
            try:
                # 验证重置令牌
                payload = await self.jwt_handler.verify_token(reset_token)
                
                if payload.get("token_type") != "reset":
                    raise ValueError("无效的重置令牌")
                
                token_email = payload.get("email")
                if token_email != email:
                    raise ValueError("邮箱不匹配")
                
                # 查找用户
                result = await session.execute(
                    session.query(User).filter(
                        and_(
                            User.email == email,
                            User.is_active == True,
                            User.deleted_at.is_(None)
                        )
                    )
                )
                user = result.scalar_one_or_none()
                
                if not user:
                    raise ValueError("用户不存在")
                
                # 检查令牌是否已使用
                token_result = await session.execute(
                    session.query(UserToken).filter(
                        and_(
                            UserToken.user_id == user.id,
                            UserToken.token_type == "reset",
                            UserToken.used_at.is_(None),
                            UserToken.expires_at > datetime.utcnow()
                        )
                    )
                )
                db_token = token_result.scalar_one_or_none()
                
                if not db_token:
                    raise ValueError("重置令牌已失效")
                
                # 更新密码
                new_password_hash = self.password_handler.hash_password(new_password)
                user.password_hash = new_password_hash
                user.password_changed_at = datetime.utcnow()
                user.updated_at = datetime.utcnow()
                
                # 标记令牌为已使用
                db_token.used_at = datetime.utcnow()
                
                # 撤销所有现有会话
                await session.execute(
                    session.query(UserToken)
                    .filter(
                        and_(
                            UserToken.user_id == user.id,
                            UserToken.token_type.in_(["access", "refresh"]),
                            UserToken.revoked_at.is_(None)
                        )
                    )
                    .update({"revoked_at": datetime.utcnow()})
                )
                
                await session.commit()
                
                logger.info(f"密码重置成功: {user.username}", extra={"user_id": user.id})
                
                return {
                    "success": True,
                    "message": "密码重置成功"
                }
                
            except ValueError as e:
                await session.rollback()
                logger.warning(f"密码重置失败: {str(e)}")
                raise
            except Exception as e:
                await session.rollback()
                logger.error(f"密码重置异常: {str(e)}")
                raise Exception(f"密码重置失败: {str(e)}")
    
    async def generate_reset_token(self, email: str) -> Dict[str, Any]:
        """
        生成密码重置令牌
        
        Args:
            email: 用户邮箱
            
        Returns:
            Dict[str, Any]: 重置令牌信息
            
        Raises:
            ValueError: 用户不存在
            Exception: 生成失败
        """
        async with self.db_manager.get_session() as session:
            try:
                # 查找用户
                result = await session.execute(
                    session.query(User).filter(
                        and_(
                            User.email == email,
                            User.is_active == True,
                            User.deleted_at.is_(None)
                        )
                    )
                )
                user = result.scalar_one_or_none()
                
                if not user:
                    raise ValueError("用户不存在")
                
                # 撤销现有的重置令牌
                await session.execute(
                    session.query(UserToken)
                    .filter(
                        and_(
                            UserToken.user_id == user.id,
                            UserToken.token_type == "reset",
                            UserToken.used_at.is_(None)
                        )
                    )
                    .update({"revoked_at": datetime.utcnow()})
                )
                
                # 生成新的重置令牌
                reset_token = await self.jwt_handler.generate_reset_token(user.id, email)
                
                await session.commit()
                
                logger.info(f"密码重置令牌生成成功: {user.username}", extra={"user_id": user.id})
                
                return {
                    "success": True,
                    "message": "重置令牌生成成功",
                    "data": {
                        "reset_token": reset_token["reset_token"],
                        "expires_in": reset_token["expires_in"]
                    }
                }
                
            except ValueError as e:
                await session.rollback()
                logger.warning(f"重置令牌生成失败: {str(e)}")
                raise
            except Exception as e:
                await session.rollback()
                logger.error(f"重置令牌生成异常: {str(e)}")
                raise Exception(f"重置令牌生成失败: {str(e)}")
    
    async def revoke_all_user_tokens(self, user_id: int) -> Dict[str, Any]:
        """
        撤销用户所有令牌
        
        Args:
            user_id: 用户ID
            
        Returns:
            Dict[str, Any]: 撤销结果
        """
        async with self.db_manager.get_session() as session:
            try:
                # 撤销所有令牌
                result = await session.execute(
                    session.query(UserToken)
                    .filter(
                        and_(
                            UserToken.user_id == user_id,
                            UserToken.revoked_at.is_(None)
                        )
                    )
                    .update({"revoked_at": datetime.utcnow()})
                )
                
                revoked_count = result.rowcount
                await session.commit()
                
                logger.info(f"用户所有令牌撤销成功", extra={
                    "user_id": user_id,
                    "revoked_count": revoked_count
                })
                
                return {
                    "success": True,
                    "message": f"成功撤销 {revoked_count} 个令牌",
                    "data": {
                        "revoked_count": revoked_count
                    }
                }
                
            except Exception as e:
                await session.rollback()
                logger.error(f"撤销用户令牌异常: {str(e)}")
                raise Exception(f"撤销用户令牌失败: {str(e)}")
    
    async def get_active_sessions(self, user_id: int) -> List[Dict[str, Any]]:
        """
        获取用户活跃会话
        
        Args:
            user_id: 用户ID
            
        Returns:
            List[Dict[str, Any]]: 活跃会话列表
        """
        async with self.db_manager.get_session() as session:
            try:
                result = await session.execute(
                    session.query(UserToken).filter(
                        and_(
                            UserToken.user_id == user_id,
                            UserToken.token_type == "access",
                            UserToken.expires_at > datetime.utcnow(),
                            UserToken.revoked_at.is_(None)
                        )
                    ).order_by(desc(UserToken.created_at))
                )
                tokens = result.scalars().all()
                
                sessions = []
                for token in tokens:
                    try:
                        # 解析令牌获取会话信息
                        payload = await self.jwt_handler.verify_token(token.token_hash)
                        sessions.append({
                            "session_id": payload.get("session_id"),
                            "created_at": token.created_at.isoformat(),
                            "expires_at": token.expires_at.isoformat(),
                            "last_used": token.updated_at.isoformat() if token.updated_at else token.created_at.isoformat()
                        })
                    except Exception:
                        # 忽略无效令牌
                        continue
                
                return sessions
                
            except Exception as e:
                logger.error(f"获取活跃会话异常: {str(e)}")
                raise Exception(f"获取活跃会话失败: {str(e)}")
    
    async def validate_api_access(self, user_id: int, endpoint: str, method: str) -> bool:
        """
        验证API访问权限
        
        Args:
            user_id: 用户ID
            endpoint: API端点
            method: HTTP方法
            
        Returns:
            bool: 是否有访问权限
        """
        try:
            # 获取用户信息
            async with self.db_manager.get_session() as session:
                result = await session.execute(
                    session.query(User).filter(
                        and_(
                            User.id == user_id,
                            User.is_active == True,
                            User.deleted_at.is_(None)
                        )
                    )
                )
                user = result.scalar_one_or_none()
                
                if not user:
                    return False
                
                # 超级管理员拥有所有API访问权限
                if user.role == "super_admin":
                    return True
                
                # 管理员拥有大部分API访问权限
                if user.role == "admin":
                    # 排除一些敏感操作
                    sensitive_endpoints = [
                        "/api/v1/admin/users/delete",
                        "/api/v1/admin/system/reset"
                    ]
                    return endpoint not in sensitive_endpoints
                
                # 普通用户的API访问控制
                if user.role == "user":
                    # 允许访问的端点模式
                    allowed_patterns = [
                        "/api/v1/auth/",
                        "/api/v1/users/me",
                        "/api/v1/agents/",
                        "/api/v1/sessions/",
                        "/api/v1/messages/",
                        "/api/v1/tasks/",
                        "/api/v1/workflows/"
                    ]
                    
                    return any(endpoint.startswith(pattern) for pattern in allowed_patterns)
                
                return False
                
        except Exception as e:
            logger.error(f"API访问验证异常: {str(e)}")
            return False