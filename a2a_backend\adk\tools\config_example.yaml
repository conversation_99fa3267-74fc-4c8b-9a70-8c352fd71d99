# A2A工具包配置示例
# 这是一个完整的配置文件示例，展示了所有可配置的选项

# 全局配置
global:
  # 环境设置
  environment: "development"  # development, testing, production
  debug: true
  log_level: "INFO"  # DEBUG, INFO, WARNING, ERROR, CRITICAL
  
  # 数据目录
  data_dir: "./data"
  log_dir: "./logs"
  cache_dir: "./cache"
  plugin_dir: "./plugins"

# 安全配置
security:
  # 默认安全级别
  default_level: "MEDIUM"  # LOW, MEDIUM, HIGH, CRITICAL
  
  # 认证设置
  require_authentication: true
  authentication_methods: ["api_key", "jwt", "oauth2"]
  session_timeout: 3600  # 秒
  
  # 速率限制
  enable_rate_limiting: true
  rate_limit_requests: 100  # 每个时间窗口的请求数
  rate_limit_window: 60     # 时间窗口（秒）
  rate_limit_burst: 10      # 突发请求数
  
  # IP访问控制
  allowed_ips: []  # 允许的IP地址列表，空表示允许所有
  blocked_ips: []  # 禁止的IP地址列表
  
  # 输入验证
  enable_input_validation: true
  max_input_size: 1048576  # 1MB
  allowed_file_types: [".txt", ".json", ".yaml", ".csv"]
  
  # 输出过滤
  enable_output_filtering: true
  filter_sensitive_data: true
  
  # 审计日志
  enable_audit_logging: true
  audit_log_file: "./logs/audit.log"
  audit_log_rotation: "daily"

# 缓存配置
cache:
  # 缓存策略
  strategy: "LRU"  # LRU, LFU, FIFO, TTL, ADAPTIVE
  
  # 缓存大小
  max_size: 1000
  max_memory: 104857600  # 100MB
  
  # TTL设置
  default_ttl: 3600  # 默认过期时间（秒）
  max_ttl: 86400     # 最大过期时间（秒）
  
  # 存储后端
  storage_backend: "MEMORY"  # MEMORY, REDIS, FILE, DATABASE
  
  # Redis配置（当使用Redis后端时）
  redis:
    host: "localhost"
    port: 6379
    db: 0
    password: null
    ssl: false
    connection_pool_size: 10
  
  # 文件缓存配置（当使用文件后端时）
  file:
    cache_dir: "./cache"
    max_file_size: 10485760  # 10MB
    compression: true
  
  # 序列化格式
  serialization_format: "JSON"  # JSON, PICKLE, MSGPACK
  enable_compression: true
  compression_algorithm: "gzip"  # gzip, lz4, zstd
  
  # 缓存预热
  enable_preloading: false
  preload_patterns: []
  
  # 后台清理
  enable_background_cleanup: true
  cleanup_interval: 300  # 5分钟

# 监控配置
monitor:
  # 监控级别
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR
  
  # 指标收集
  enable_metrics: true
  metrics_interval: 30  # 收集间隔（秒）
  metrics_retention: 86400  # 保留时间（秒）
  
  # 健康检查
  enable_health_checks: true
  health_check_interval: 30  # 检查间隔（秒）
  health_check_timeout: 10   # 检查超时（秒）
  
  # 性能监控
  enable_performance_monitoring: true
  performance_threshold: 1000  # 性能阈值（毫秒）
  
  # 告警规则
  alert_rules:
    - name: "high_error_rate"
      description: "错误率过高告警"
      condition: "error_rate > 0.1"
      level: "WARNING"
      actions: ["log", "email", "webhook"]
      cooldown: 300  # 冷却时间（秒）
    
    - name: "high_latency"
      description: "延迟过高告警"
      condition: "avg_latency > 5000"
      level: "WARNING"
      actions: ["log", "slack"]
      cooldown: 600
    
    - name: "memory_usage"
      description: "内存使用率过高"
      condition: "memory_usage > 0.8"
      level: "CRITICAL"
      actions: ["log", "email", "sms"]
      cooldown: 180
  
  # 通知配置
  notifications:
    email:
      enabled: false
      smtp_host: "smtp.example.com"
      smtp_port: 587
      username: "<EMAIL>"
      password: "password"
      recipients: ["<EMAIL>"]
    
    slack:
      enabled: false
      webhook_url: "https://hooks.slack.com/services/..."
      channel: "#alerts"
    
    webhook:
      enabled: false
      url: "https://api.example.com/alerts"
      headers:
        Authorization: "Bearer token"

# 插件配置
plugin:
  # 插件目录
  plugin_dirs: ["./plugins", "./custom_plugins"]
  
  # 自动发现
  auto_discovery: true
  discovery_interval: 60  # 发现间隔（秒）
  
  # 热重载
  enable_hot_reload: true
  reload_on_change: true
  
  # 沙箱设置
  sandbox_enabled: true
  sandbox_timeout: 30  # 沙箱超时（秒）
  
  # 插件限制
  max_plugins: 50
  max_plugin_size: 10485760  # 10MB
  
  # 依赖管理
  auto_install_dependencies: false
  dependency_timeout: 300  # 依赖安装超时（秒）
  
  # 插件优先级
  default_priority: 100
  
  # 插件配置
  plugin_configs:
    example_plugin:
      enabled: true
      config:
        setting1: "value1"
        setting2: 42

# API配置
api:
  # 服务器设置
  host: "0.0.0.0"
  port: 8000
  workers: 4
  
  # CORS设置
  enable_cors: true
  cors_origins: ["*"]
  cors_methods: ["GET", "POST", "PUT", "DELETE"]
  cors_headers: ["*"]
  
  # 请求限制
  max_request_size: 10485760  # 10MB
  request_timeout: 30  # 请求超时（秒）
  
  # 文档设置
  enable_docs: true
  docs_url: "/docs"
  redoc_url: "/redoc"
  openapi_url: "/openapi.json"
  
  # WebSocket设置
  enable_websocket: true
  websocket_path: "/ws"
  max_connections: 100
  ping_interval: 30
  ping_timeout: 10
  
  # 中间件
  middlewares:
    - name: "cors"
      enabled: true
    - name: "gzip"
      enabled: true
      minimum_size: 1000
    - name: "rate_limit"
      enabled: true

# MCP配置
mcp:
  # 服务器配置
  servers:
    - name: "tool-server"
      description: "主要工具服务器"
      transport: "websocket"  # websocket, http, stdio
      host: "localhost"
      port: 8001
      path: "/mcp"
      auto_start: true
      
      # 服务器能力
      capabilities:
        tools: true
        resources: true
        prompts: true
        logging: true
      
      # 工具配置
      tools:
        - name: "web_tool"
          enabled: true
        - name: "calculation_tool"
          enabled: true
    
    - name: "resource-server"
      description: "资源服务器"
      transport: "http"
      host: "localhost"
      port: 8002
      auto_start: false
      
      capabilities:
        resources: true
        logging: false
  
  # 客户端配置
  clients:
    - name: "external-client"
      description: "外部MCP客户端"
      server_url: "ws://external-server:8001/mcp"
      transport: "websocket"
      auto_connect: true
      
      # 连接设置
      retry_attempts: 3
      retry_delay: 5  # 重试延迟（秒）
      connection_timeout: 10
      
      # 认证
      authentication:
        type: "api_key"
        api_key: "your-api-key"
      
      # 心跳设置
      heartbeat_interval: 30
      heartbeat_timeout: 10
    
    - name: "backup-client"
      description: "备份客户端"
      server_url: "http://backup-server:8002"
      transport: "http"
      auto_connect: false
  
  # 协议设置
  protocol:
    version: "2024-11-05"
    timeout: 30
    max_message_size: 1048576  # 1MB
    
  # 日志设置
  logging:
    enable_protocol_logging: true
    log_level: "INFO"
    log_file: "./logs/mcp.log"

# 工具执行配置
execution:
  # 默认执行模式
  default_mode: "ASYNC"  # SYNC, ASYNC, BACKGROUND, BATCH
  
  # 超时设置
  default_timeout: 30  # 默认超时（秒）
  max_timeout: 300     # 最大超时（秒）
  
  # 并发控制
  max_concurrent_executions: 10
  max_queue_size: 100
  
  # 重试设置
  enable_retry: true
  max_retry_attempts: 3
  retry_delay: 1  # 重试延迟（秒）
  retry_backoff: 2  # 退避倍数
  
  # 批量执行
  batch_size: 10
  batch_timeout: 60
  
  # 结果保留
  result_retention: 3600  # 结果保留时间（秒）
  max_results: 1000       # 最大结果数量

# 数据库配置（如果使用数据库存储）
database:
  # 数据库类型
  type: "sqlite"  # sqlite, postgresql, mysql
  
  # SQLite配置
  sqlite:
    database: "./data/tools.db"
    timeout: 30
  
  # PostgreSQL配置
  postgresql:
    host: "localhost"
    port: 5432
    database: "a2a_tools"
    username: "postgres"
    password: "password"
    pool_size: 10
    max_overflow: 20
  
  # MySQL配置
  mysql:
    host: "localhost"
    port: 3306
    database: "a2a_tools"
    username: "root"
    password: "password"
    charset: "utf8mb4"
    pool_size: 10
    max_overflow: 20
  
  # 连接池设置
  pool_pre_ping: true
  pool_recycle: 3600
  echo: false  # 是否打印SQL语句

# 工具特定配置
tools:
  # Web工具配置
  web_tool:
    # 默认设置
    default_timeout: 30
    max_redirects: 5
    verify_ssl: true
    
    # 用户代理
    user_agent: "A2A-Tools/1.0.0"
    
    # 代理设置
    proxy:
      enabled: false
      http: "http://proxy:8080"
      https: "https://proxy:8080"
    
    # 请求头
    default_headers:
      Accept: "application/json"
      Content-Type: "application/json"
    
    # 安全设置
    allowed_domains: []  # 允许的域名，空表示允许所有
    blocked_domains: []  # 禁止的域名
    max_response_size: 10485760  # 10MB
  
  # 计算工具配置
  calculation_tool:
    # 安全设置
    enable_safe_mode: true
    max_execution_time: 5  # 最大执行时间（秒）
    max_memory_usage: 10485760  # 10MB
    
    # 允许的函数
    allowed_functions: ["sin", "cos", "tan", "log", "exp", "sqrt"]
    
    # 禁止的操作
    forbidden_operations: ["import", "exec", "eval", "open", "file"]
    
    # 精度设置
    decimal_precision: 10
    
    # 输出格式
    default_format: "JSON"

# 开发和调试配置
development:
  # 调试模式
  debug: true
  
  # 热重载
  auto_reload: true
  
  # 性能分析
  enable_profiling: false
  profiling_output: "./logs/profile.log"
  
  # 测试设置
  test_mode: false
  mock_external_services: false
  
  # 开发工具
  enable_dev_tools: true
  dev_tools_port: 8080

# 生产环境配置
production:
  # 性能优化
  enable_optimization: true
  
  # 日志设置
  log_level: "WARNING"
  log_rotation: "daily"
  log_retention: 30  # 日志保留天数
  
  # 安全加固
  strict_security: true
  disable_debug_endpoints: true
  
  # 监控
  enable_detailed_monitoring: true
  
  # 备份
  enable_backup: true
  backup_interval: 86400  # 24小时
  backup_retention: 7     # 保留7天