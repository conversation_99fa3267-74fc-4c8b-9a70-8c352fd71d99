# A2A多智能体系统 API接口文档

## 概述

A2A多智能体系统提供完整的RESTful API接口，支持用户认证、智能体管理、会话处理、任务执行、工作流管理等功能。

**基础URL**: `http://localhost:8000/api/v1`

**认证方式**: Bearer <PERSON> (JWT)

**内容类型**: `application/json`

## 目录

1. [认证接口](#认证接口)
2. [用户管理接口](#用户管理接口)
3. [智能体管理接口](#智能体管理接口)
4. [会话管理接口](#会话管理接口)
5. [消息管理接口](#消息管理接口)
6. [任务管理接口](#任务管理接口)
7. [工作流管理接口](#工作流管理接口)
8. [流式输出接口](#流式输出接口)
9. [WebSocket接口](#websocket接口)
10. [配置管理接口](#配置管理接口)
11. [工件管理接口](#工件管理接口)
12. [监控管理接口](#监控管理接口)

---

## 认证接口

### 1. 用户注册

**接口地址**: `POST /auth/register`

**功能描述**: 注册新用户账户

**请求参数**:
```json
{
  "username": "string",        // 用户名（3-50字符，只能包含字母、数字、下划线和连字符）
  "email": "string",          // 邮箱地址
  "password": "string",       // 密码（8-128字符，必须包含大小写字母、数字和特殊字符）
  "full_name": "string",      // 全名（可选）
  "phone": "string",          // 手机号（可选）
  "invite_code": "string"     // 邀请码（可选）
}
```

**响应格式**:
```json
{
  "success": true,
  "message": "注册成功，请查收邮箱验证邮件",
  "data": {
    "user_id": 123,
    "username": "testuser",
    "email": "<EMAIL>",
    "verification_required": true
  }
}
```

**错误响应**:
- `400 Bad Request`: 参数验证失败或用户名/邮箱已存在
- `429 Too Many Requests`: 注册频率限制（每小时最多5次）

### 2. 用户登录

**接口地址**: `POST /auth/login`

**功能描述**: 用户登录获取访问令牌

**请求参数**:
```json
{
  "username": "string",       // 用户名或邮箱
  "password": "string",       // 密码
  "remember_me": false,       // 是否记住登录状态
  "device_info": {}          // 设备信息（可选）
}
```

**响应格式**:
```json
{
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "token_type": "bearer",
  "expires_in": 1800,
  "user_info": {
    "user_id": 123,
    "username": "testuser",
    "email": "<EMAIL>",
    "full_name": "Test User",
    "role": "user",
    "avatar_url": null,
    "is_verified": true,
    "last_login_at": "2024-01-01T12:00:00Z"
  }
}
```

### 3. 刷新令牌

**接口地址**: `POST /auth/refresh`

**请求参数**:
```json
{
  "refresh_token": "string"   // 刷新令牌
}
```

### 4. 用户登出

**接口地址**: `POST /auth/logout`

**请求头**: `Authorization: Bearer <access_token>`

### 5. 密码重置请求

**接口地址**: `POST /auth/password/reset`

**请求参数**:
```json
{
  "email": "string"           // 邮箱地址
}
```

### 6. 密码重置确认

**接口地址**: `POST /auth/password/reset/confirm`

**请求参数**:
```json
{
  "token": "string",          // 重置令牌
  "new_password": "string"    // 新密码
}
```

### 7. 修改密码

**接口地址**: `POST /auth/password/change`

**请求头**: `Authorization: Bearer <access_token>`

**请求参数**:
```json
{
  "current_password": "string",  // 当前密码
  "new_password": "string"       // 新密码
}
```

### 8. 验证令牌

**接口地址**: `GET /auth/verify`

**请求头**: `Authorization: Bearer <access_token>`

### 9. 获取活跃会话

**接口地址**: `GET /auth/sessions`

**请求头**: `Authorization: Bearer <access_token>`

### 10. 撤销会话

**接口地址**: `DELETE /auth/sessions/{session_id}`

**请求头**: `Authorization: Bearer <access_token>`

---

## 用户管理接口

### 1. 获取当前用户信息

**接口地址**: `GET /users/me`

**请求头**: `Authorization: Bearer <access_token>`

**响应格式**:
```json
{
  "user_id": 123,
  "username": "testuser",
  "email": "<EMAIL>",
  "full_name": "Test User",
  "phone": "+1234567890",
  "avatar_url": "https://example.com/avatar.jpg",
  "role": "user",
  "is_verified": true,
  "is_locked": false,
  "timezone": "UTC",
  "language": "zh-CN",
  "preferences": {
    "theme": "light",
    "notifications": true
  },
  "created_at": "2024-01-01T12:00:00Z",
  "updated_at": "2024-01-01T12:00:00Z",
  "last_login_at": "2024-01-01T12:00:00Z"
}
```

### 2. 更新用户信息

**接口地址**: `PUT /users/me`

**请求头**: `Authorization: Bearer <access_token>`

**请求参数**:
```json
{
  "full_name": "string",      // 全名（可选）
  "phone": "string",          // 手机号（可选）
  "avatar_url": "string",     // 头像URL（可选）
  "timezone": "string",       // 时区（可选）
  "language": "string",       // 语言（可选）
  "preferences": {}           // 用户偏好设置（可选）
}
```

### 3. 获取用户列表（管理员）

**接口地址**: `GET /users`

**请求头**: `Authorization: Bearer <access_token>`

**权限要求**: 管理员权限

**查询参数**:
- `page`: 页码（默认1）
- `size`: 每页数量（默认20）
- `role`: 角色过滤
- `status`: 状态过滤
- `search`: 搜索关键词

### 4. 获取用户详情（管理员）

**接口地址**: `GET /users/{user_id}`

**请求头**: `Authorization: Bearer <access_token>`

**权限要求**: 管理员权限

### 5. 更新用户角色（管理员）

**接口地址**: `PUT /users/{user_id}/role`

**请求头**: `Authorization: Bearer <access_token>`

**权限要求**: 超级管理员权限

**请求参数**:
```json
{
  "role": "string"            // 用户角色：user, premium, admin, super_admin
}
```

### 6. 更新用户状态（管理员）

**接口地址**: `PUT /users/{user_id}/status`

**请求头**: `Authorization: Bearer <access_token>`

**权限要求**: 管理员权限

**请求参数**:
```json
{
  "is_verified": true,        // 是否已验证（可选）
  "is_locked": false,         // 是否已锁定（可选）
  "locked_until": "2024-01-01T12:00:00Z",  // 锁定到期时间（可选）
  "reason": "string"          // 操作原因（可选）
}
```

### 7. 删除用户（管理员）

**接口地址**: `DELETE /users/{user_id}`

**请求头**: `Authorization: Bearer <access_token>`

**权限要求**: 超级管理员权限

### 8. 获取用户权限

**接口地址**: `GET /users/me/permissions`

**请求头**: `Authorization: Bearer <access_token>`

### 9. 检查用户权限

**接口地址**: `POST /users/me/permissions/check`

**请求头**: `Authorization: Bearer <access_token>`

**请求参数**:
```json
{
  "resource_type": "string",  // 资源类型：user, agent, session, task, message, system
  "resource_id": "string",    // 资源ID（可选）
  "permission": "string"      // 权限名称：read, write, delete, manage, execute, admin
}
```

### 10. 获取用户活动日志

**接口地址**: `GET /users/me/activity`

**请求头**: `Authorization: Bearer <access_token>`

**查询参数**:
- `page`: 页码（默认1）
- `size`: 每页数量（默认20）
- `action_type`: 操作类型过滤
- `start_date`: 开始日期
- `end_date`: 结束日期

---

## 智能体管理接口

### 1. 创建智能体

**接口地址**: `POST /agents`

**请求头**: `Authorization: Bearer <access_token>`

**请求参数**:
```json
{
  "name": "string",           // 智能体名称（1-100字符）
  "description": "string",    // 智能体描述（可选，最多500字符）
  "agent_type": "string",     // 智能体类型：chat, task, workflow, tool, composite
  "config": {                 // 智能体配置
    "model": "gemini-pro",
    "temperature": 0.7,
    "max_tokens": 1000
  },
  "parent_id": 123,          // 父智能体ID（可选）
  "is_public": false,        // 是否公开
  "tags": ["tag1", "tag2"]   // 标签列表（可选）
}
```

**响应格式**:
```json
{
  "success": true,
  "message": "智能体创建成功",
  "data": {
    "agent_id": 456,
    "name": "My Agent",
    "agent_type": "chat",
    "status": "active",
    "created_at": "2024-01-01T12:00:00Z"
  }
}
```

### 2. 获取智能体列表

**接口地址**: `GET /agents`

**请求头**: `Authorization: Bearer <access_token>`

**查询参数**:
- `page`: 页码（默认1）
- `size`: 每页数量（默认20）
- `agent_type`: 智能体类型过滤
- `status`: 状态过滤
- `search`: 搜索关键词
- `tags`: 标签过滤

### 3. 获取智能体详情

**接口地址**: `GET /agents/{agent_id}`

**请求头**: `Authorization: Bearer <access_token>`

### 4. 更新智能体

**接口地址**: `PUT /agents/{agent_id}`

**请求头**: `Authorization: Bearer <access_token>`

### 5. 删除智能体

**接口地址**: `DELETE /agents/{agent_id}`

**请求头**: `Authorization: Bearer <access_token>`

### 6. 执行智能体

**接口地址**: `POST /agents/{agent_id}/execute`

**请求头**: `Authorization: Bearer <access_token>`

**请求参数**:
```json
{
  "input_data": {},          // 输入数据
  "context": {},             // 执行上下文（可选）
  "stream": false,           // 是否流式输出（可选）
  "timeout": 60              // 超时时间（秒，可选）
}
```

---

## 会话管理接口

### 1. 创建会话

**接口地址**: `POST /sessions`

**请求头**: `Authorization: Bearer <access_token>`

**请求参数**:
```json
{
  "title": "string",          // 会话标题（1-200字符）
  "description": "string",    // 会话描述（可选，最多1000字符）
  "session_type": "string",   // 会话类型：chat, task, workflow, collaboration, debug
  "agent_ids": [456],        // 参与的智能体ID列表（可选）
  "participant_ids": [789],  // 参与的用户ID列表（可选）
  "config": {},              // 会话配置（可选）
  "is_private": true,        // 是否私有会话
  "tags": ["tag1"]           // 标签列表（可选）
}
```

### 2. 获取会话列表

**接口地址**: `GET /sessions`

**请求头**: `Authorization: Bearer <access_token>`

**查询参数**:
- `page`: 页码（默认1）
- `size`: 每页数量（默认20）
- `session_type`: 会话类型过滤
- `status`: 状态过滤
- `search`: 搜索关键词

### 3. 获取会话详情

**接口地址**: `GET /sessions/{session_id}`

**请求头**: `Authorization: Bearer <access_token>`

### 4. 更新会话

**接口地址**: `PUT /sessions/{session_id}`

**请求头**: `Authorization: Bearer <access_token>`

### 5. 删除会话

**接口地址**: `DELETE /sessions/{session_id}`

**请求头**: `Authorization: Bearer <access_token>`

### 6. 发送消息

**接口地址**: `POST /sessions/{session_id}/messages`

**请求头**: `Authorization: Bearer <access_token>`

**请求参数**:
```json
{
  "content": "string",        // 消息内容
  "message_type": "text",     // 消息类型：text, image, file, code, system, error
  "metadata": {},            // 消息元数据（可选）
  "reply_to_id": 123,        // 回复的消息ID（可选）
  "agent_id": 456            // 发送消息的智能体ID（可选）
}
```

### 7. 获取会话消息

**接口地址**: `GET /sessions/{session_id}/messages`

**请求头**: `Authorization: Bearer <access_token>`

**查询参数**:
- `page`: 页码（默认1）
- `size`: 每页数量（默认50）
- `message_type`: 消息类型过滤
- `sender_type`: 发送者类型过滤
- `start_date`: 开始日期
- `end_date`: 结束日期

### 8. 添加参与者

**接口地址**: `POST /sessions/{session_id}/participants`

**请求头**: `Authorization: Bearer <access_token>`

**请求参数**:
```json
{
  "user_id": 123,            // 用户ID（可选）
  "agent_id": 456,           // 智能体ID（可选）
  "role": "participant"      // 角色：participant, moderator, observer
}
```

### 9. 移除参与者

**接口地址**: `DELETE /sessions/{session_id}/participants/{participant_id}`

**请求头**: `Authorization: Bearer <access_token>`

### 10. 获取会话参与者

**接口地址**: `GET /sessions/{session_id}/participants`

**请求头**: `Authorization: Bearer <access_token>`

---

## 消息管理接口

### 1. 获取消息详情

**接口地址**: `GET /messages/{message_id}`

**请求头**: `Authorization: Bearer <access_token>`

### 2. 更新消息

**接口地址**: `PUT /messages/{message_id}`

**请求头**: `Authorization: Bearer <access_token>`

**请求参数**:
```json
{
  "content": "string",       // 消息内容（可选）
  "metadata": {}            // 消息元数据（可选）
}
```

### 3. 删除消息

**接口地址**: `DELETE /messages/{message_id}`

**请求头**: `Authorization: Bearer <access_token>`

### 4. 搜索消息

**接口地址**: `GET /messages/search`

**请求头**: `Authorization: Bearer <access_token>`

**查询参数**:
- `q`: 搜索关键词
- `session_id`: 会话ID过滤
- `message_type`: 消息类型过滤
- `sender_type`: 发送者类型过滤
- `start_date`: 开始日期
- `end_date`: 结束日期
- `page`: 页码（默认1）
- `size`: 每页数量（默认20）

### 5. 获取消息统计

**接口地址**: `GET /messages/stats`

**请求头**: `Authorization: Bearer <access_token>`

**查询参数**:
- `session_id`: 会话ID过滤
- `start_date`: 开始日期
- `end_date`: 结束日期

---

## 任务管理接口

### 1. 创建任务

**接口地址**: `POST /tasks`

**请求头**: `Authorization: Bearer <access_token>`

**请求参数**:
```json
{
  "title": "string",         // 任务标题（1-200字符）
  "description": "string",   // 任务描述（可选，最多1000字符）
  "task_type": "string",     // 任务类型：agent_execution, workflow, data_processing, custom
  "config": {},              // 任务配置
  "agent_id": 456,           // 执行任务的智能体ID（可选）
  "workflow_id": 789,        // 关联的工作流ID（可选）
  "priority": "medium",      // 优先级：low, medium, high, urgent
  "scheduled_at": "2024-01-01T12:00:00Z",  // 计划执行时间（可选）
  "timeout": 3600,           // 超时时间（秒，可选）
  "tags": ["tag1"]           // 标签列表（可选）
}
```

### 2. 获取任务列表

**接口地址**: `GET /tasks`

**请求头**: `Authorization: Bearer <access_token>`

**查询参数**:
- `page`: 页码（默认1）
- `size`: 每页数量（默认20）
- `task_type`: 任务类型过滤
- `status`: 状态过滤
- `priority`: 优先级过滤
- `agent_id`: 智能体ID过滤
- `search`: 搜索关键词

### 3. 获取任务详情

**接口地址**: `GET /tasks/{task_id}`

**请求头**: `Authorization: Bearer <access_token>`

### 4. 更新任务

**接口地址**: `PUT /tasks/{task_id}`

**请求头**: `Authorization: Bearer <access_token>`

### 5. 删除任务

**接口地址**: `DELETE /tasks/{task_id}`

**请求头**: `Authorization: Bearer <access_token>`

### 6. 执行任务

**接口地址**: `POST /tasks/{task_id}/execute`

**请求头**: `Authorization: Bearer <access_token>`

### 7. 暂停任务

**接口地址**: `POST /tasks/{task_id}/pause`

**请求头**: `Authorization: Bearer <access_token>`

### 8. 恢复任务

**接口地址**: `POST /tasks/{task_id}/resume`

**请求头**: `Authorization: Bearer <access_token>`

### 9. 取消任务

**接口地址**: `POST /tasks/{task_id}/cancel`

**请求头**: `Authorization: Bearer <access_token>`

### 10. 获取任务执行日志

**接口地址**: `GET /tasks/{task_id}/logs`

**请求头**: `Authorization: Bearer <access_token>`

---

## 工作流管理接口

### 1. 创建工作流

**接口地址**: `POST /workflows`

**请求头**: `Authorization: Bearer <access_token>`

**请求参数**:
```json
{
  "name": "string",          // 工作流名称（1-100字符）
  "description": "string",   // 工作流描述（可选，最多1000字符）
  "definition": {},          // 工作流定义
  "version": "1.0.0",        // 版本号
  "is_public": false,        // 是否公开
  "tags": ["tag1"]           // 标签列表（可选）
}
```

### 2. 获取工作流列表

**接口地址**: `GET /workflows`

**请求头**: `Authorization: Bearer <access_token>`

### 3. 获取工作流详情

**接口地址**: `GET /workflows/{workflow_id}`

**请求头**: `Authorization: Bearer <access_token>`

### 4. 更新工作流

**接口地址**: `PUT /workflows/{workflow_id}`

**请求头**: `Authorization: Bearer <access_token>`

### 5. 删除工作流

**接口地址**: `DELETE /workflows/{workflow_id}`

**请求头**: `Authorization: Bearer <access_token>`

### 6. 执行工作流

**接口地址**: `POST /workflows/{workflow_id}/execute`

**请求头**: `Authorization: Bearer <access_token>`

**请求参数**:
```json
{
  "input_data": {},          // 输入数据
  "context": {},             // 执行上下文（可选）
  "config": {}               // 执行配置（可选）
}
```

### 7. 获取工作流执行历史

**接口地址**: `GET /workflows/{workflow_id}/executions`

**请求头**: `Authorization: Bearer <access_token>`

### 8. 获取工作流执行详情

**接口地址**: `GET /workflows/{workflow_id}/executions/{execution_id}`

**请求头**: `Authorization: Bearer <access_token>`

---

## 流式输出接口

### 1. 创建流式会话

**接口地址**: `POST /stream/sessions`

**请求头**: `Authorization: Bearer <access_token>`

**请求参数**:
```json
{
  "agent_id": 456,           // 智能体ID
  "config": {},              // 流式配置（可选）
  "timeout": 300             // 超时时间（秒，可选）
}
```

### 2. 发送流式消息

**接口地址**: `POST /stream/sessions/{stream_session_id}/send`

**请求头**: `Authorization: Bearer <access_token>`

**请求参数**:
```json
{
  "content": "string",       // 消息内容
  "message_type": "text"     // 消息类型
}
```

### 3. 获取流式响应

**接口地址**: `GET /stream/sessions/{stream_session_id}/events`

**请求头**: `Authorization: Bearer <access_token>`

**响应格式**: Server-Sent Events (SSE)

### 4. 关闭流式会话

**接口地址**: `DELETE /stream/sessions/{stream_session_id}`

**请求头**: `Authorization: Bearer <access_token>`

---

## WebSocket接口

### 1. WebSocket连接

**接口地址**: `WS /websocket`

**查询参数**:
- `token`: 访问令牌

**连接示例**:
```javascript
const ws = new WebSocket('ws://localhost:8000/api/v1/websocket?token=your_access_token');
```

### 2. 消息格式

**发送消息格式**:
```json
{
  "type": "string",          // 消息类型：chat, command, heartbeat
  "session_id": 123,         // 会话ID（可选）
  "agent_id": 456,           // 智能体ID（可选）
  "data": {}                 // 消息数据
}
```

**接收消息格式**:
```json
{
  "type": "string",          // 消息类型：response, notification, error, heartbeat
  "session_id": 123,         // 会话ID（可选）
  "timestamp": "2024-01-01T12:00:00Z",
  "data": {}                 // 消息数据
}
```

### 3. 支持的消息类型

- `chat`: 聊天消息
- `command`: 命令消息
- `heartbeat`: 心跳消息
- `response`: 响应消息
- `notification`: 通知消息
- `error`: 错误消息

---

## 配置管理接口

### 1. 获取系统配置

**接口地址**: `GET /configs/system`

**请求头**: `Authorization: Bearer <access_token>`

**权限要求**: 管理员权限

### 2. 更新系统配置

**接口地址**: `PUT /configs/system`

**请求头**: `Authorization: Bearer <access_token>`

**权限要求**: 超级管理员权限

**请求参数**:
```json
{
  "config_key": "string",    // 配置键
  "config_value": "string",  // 配置值
  "description": "string"    // 配置描述（可选）
}
```

### 3. 获取用户配置

**接口地址**: `GET /configs/user`

**请求头**: `Authorization: Bearer <access_token>`

### 4. 更新用户配置

**接口地址**: `PUT /configs/user`

**请求头**: `Authorization: Bearer <access_token>`

**请求参数**:
```json
{
  "config_key": "string",    // 配置键
  "config_value": "string"   // 配置值
}
```

### 5. 获取智能体配置模板

**接口地址**: `GET /configs/agent-templates`

**请求头**: `Authorization: Bearer <access_token>`

### 6. 创建智能体配置模板

**接口地址**: `POST /configs/agent-templates`

**请求头**: `Authorization: Bearer <access_token>`

**请求参数**:
```json
{
  "name": "string",          // 模板名称
  "description": "string",   // 模板描述（可选）
  "agent_type": "string",    // 智能体类型
  "config": {},              // 配置模板
  "is_public": false         // 是否公开
}
```

---

## 工件管理接口

### 1. 上传工件

**接口地址**: `POST /artifacts`

**请求头**: `Authorization: Bearer <access_token>`

**请求格式**: `multipart/form-data`

**请求参数**:
- `file`: 文件（必需）
- `name`: 工件名称（可选）
- `description`: 工件描述（可选）
- `artifact_type`: 工件类型（可选）
- `tags`: 标签列表（可选）

### 2. 获取工件列表

**接口地址**: `GET /artifacts`

**请求头**: `Authorization: Bearer <access_token>`

**查询参数**:
- `page`: 页码（默认1）
- `size`: 每页数量（默认20）
- `artifact_type`: 工件类型过滤
- `search`: 搜索关键词
- `tags`: 标签过滤

### 3. 获取工件详情

**接口地址**: `GET /artifacts/{artifact_id}`

**请求头**: `Authorization: Bearer <access_token>`

### 4. 下载工件

**接口地址**: `GET /artifacts/{artifact_id}/download`

**请求头**: `Authorization: Bearer <access_token>`

### 5. 更新工件

**接口地址**: `PUT /artifacts/{artifact_id}`

**请求头**: `Authorization: Bearer <access_token>`

**请求参数**:
```json
{
  "name": "string",          // 工件名称（可选）
  "description": "string",   // 工件描述（可选）
  "tags": ["tag1"]           // 标签列表（可选）
}
```

### 6. 删除工件

**接口地址**: `DELETE /artifacts/{artifact_id}`

**请求头**: `Authorization: Bearer <access_token>`

---

## 监控管理接口

### 1. 获取系统状态

**接口地址**: `GET /monitoring/status`

**请求头**: `Authorization: Bearer <access_token>`

**权限要求**: 管理员权限

**响应格式**:
```json
{
  "system": {
    "status": "healthy",
    "uptime": 86400,
    "version": "1.0.0"
  },
  "database": {
    "status": "connected",
    "connections": 15,
    "max_connections": 100
  },
  "cache": {
    "status": "connected",
    "memory_usage": "50MB",
    "hit_rate": 0.95
  },
  "services": {
    "agent_service": "running",
    "workflow_service": "running",
    "task_service": "running"
  }
}
```

### 2. 获取系统指标

**接口地址**: `GET /monitoring/metrics`

**请求头**: `Authorization: Bearer <access_token>`

**权限要求**: 管理员权限

**查询参数**:
- `start_time`: 开始时间
- `end_time`: 结束时间
- `metric_type`: 指标类型

### 3. 获取性能统计

**接口地址**: `GET /monitoring/performance`

**请求头**: `Authorization: Bearer <access_token>`

**权限要求**: 管理员权限

### 4. 获取错误日志

**接口地址**: `GET /monitoring/errors`

**请求头**: `Authorization: Bearer <access_token>`

**权限要求**: 管理员权限

**查询参数**:
- `page`: 页码（默认1）
- `size`: 每页数量（默认20）
- `level`: 日志级别过滤
- `start_date`: 开始日期
- `end_date`: 结束日期

### 5. 获取用户活动统计

**接口地址**: `GET /monitoring/user-activity`

**请求头**: `Authorization: Bearer <access_token>`

**权限要求**: 管理员权限

### 6. 获取智能体使用统计

**接口地址**: `GET /monitoring/agent-usage`

**请求头**: `Authorization: Bearer <access_token>`

**权限要求**: 管理员权限

---

## 通用响应格式

### 成功响应

```json
{
  "success": true,
  "message": "操作成功",
  "data": {},
  "timestamp": "2024-01-01T12:00:00Z"
}
```

### 错误响应

```json
{
  "error": true,
  "error_code": "VALIDATION_ERROR",
  "message": "参数验证失败",
  "details": {
    "field": "username",
    "reason": "用户名已存在"
  },
  "timestamp": "2024-01-01T12:00:00Z"
}
```

### 分页响应

```json
{
  "success": true,
  "message": "获取数据成功",
  "data": {
    "items": [],
    "pagination": {
      "page": 1,
      "size": 20,
      "total": 100,
      "pages": 5,
      "has_next": true,
      "has_prev": false
    }
  }
}
```

---

## 错误代码说明

| 错误代码 | HTTP状态码 | 说明 |
|---------|-----------|------|
| VALIDATION_ERROR | 400 | 参数验证失败 |
| AUTHENTICATION_REQUIRED | 401 | 需要身份认证 |
| PERMISSION_DENIED | 403 | 权限不足 |
| RESOURCE_NOT_FOUND | 404 | 资源不存在 |
| RATE_LIMIT_EXCEEDED | 429 | 请求频率超限 |
| INTERNAL_SERVER_ERROR | 500 | 服务器内部错误 |
| SERVICE_UNAVAILABLE | 503 | 服务不可用 |

---

## 使用示例

### Python示例

```python
import requests

# 基础配置
BASE_URL = "http://localhost:8000/api/v1"
headers = {
    "Content-Type": "application/json",
    "Authorization": "Bearer your_access_token"
}

# 登录获取令牌
login_data = {
    "username": "testuser",
    "password": "password123"
}
response = requests.post(f"{BASE_URL}/auth/login", json=login_data)
token_data = response.json()
access_token = token_data["access_token"]

# 更新请求头
headers["Authorization"] = f"Bearer {access_token}"

# 创建智能体
agent_data = {
    "name": "My Assistant",
    "description": "A helpful AI assistant",
    "agent_type": "chat",
    "config": {
        "model": "gemini-pro",
        "temperature": 0.7,
        "max_tokens": 1000
    }
}
response = requests.post(f"{BASE_URL}/agents", json=agent_data, headers=headers)
agent = response.json()

# 执行智能体
execute_data = {
    "input_data": {
        "message": "Hello, how can you help me?"
    }
}
response = requests.post(
    f"{BASE_URL}/agents/{agent['data']['agent_id']}/execute",
    json=execute_data,
    headers=headers
)
result = response.json()
```

### JavaScript示例

```javascript
const BASE_URL = "http://localhost:8000/api/v1";

// 登录获取令牌
async function login(username, password) {
    const response = await fetch(`${BASE_URL}/auth/login`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            username: username,
            password: password
        })
    });

    const data = await response.json();
    return data.access_token;
}

// 创建智能体
async function createAgent(token, agentData) {
    const response = await fetch(`${BASE_URL}/agents`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(agentData)
    });

    return await response.json();
}

// 使用示例
(async () => {
    const token = await login('testuser', 'password123');

    const agent = await createAgent(token, {
        name: 'My Assistant',
        description: 'A helpful AI assistant',
        agent_type: 'chat',
        config: {
            model: 'gemini-pro',
            temperature: 0.7,
            max_tokens: 1000
        }
    });

    console.log('Agent created:', agent);
})();
```

---

## 注意事项

1. **认证**: 除了公开接口外，所有接口都需要提供有效的JWT令牌
2. **频率限制**: 部分接口有频率限制，请注意控制请求频率
3. **权限控制**: 某些接口需要特定权限，请确保用户具有相应权限
4. **数据格式**: 请求和响应数据均为JSON格式
5. **错误处理**: 请根据HTTP状态码和错误代码进行适当的错误处理
6. **版本控制**: API版本通过URL路径控制，当前版本为v1

---

*本文档最后更新时间: 2024-01-01*
