# -*- coding: utf-8 -*-
"""
A2A多智能体系统缓存管理模块

提供配置和工件的内存缓存、本地缓存、缓存失效更新和性能监控功能
"""

import os
import json
import pickle
import hashlib
import asyncio
import aiofiles
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, Union, List, Tuple, Callable
from pathlib import Path
from dataclasses import dataclass, asdict
from collections import OrderedDict
from contextlib import asynccontextmanager
import threading
import time
import weakref
from enum import Enum

from app.core.config import get_settings
from app.core.logging import get_logger

logger = get_logger(__name__)


# ==================== 缓存策略枚举 ====================

class CacheStrategy(Enum):
    """缓存策略枚举"""
    LRU = "lru"  # 最近最少使用
    LFU = "lfu"  # 最少使用频率
    FIFO = "fifo"  # 先进先出
    TTL = "ttl"  # 基于时间过期
    ADAPTIVE = "adaptive"  # 自适应策略


class CacheLevel(Enum):
    """缓存级别枚举"""
    MEMORY = "memory"  # 内存缓存
    DISK = "disk"  # 磁盘缓存
    DISTRIBUTED = "distributed"  # 分布式缓存


# ==================== 缓存项数据结构 ====================

@dataclass
class CacheItem:
    """缓存项数据结构"""
    key: str
    value: Any
    created_at: datetime
    accessed_at: datetime
    access_count: int
    ttl: Optional[int]  # 生存时间（秒）
    size: int  # 数据大小（字节）
    tags: List[str]  # 标签
    metadata: Dict[str, Any]  # 元数据
    
    def is_expired(self) -> bool:
        """检查是否过期"""
        if self.ttl is None:
            return False
        return datetime.now() > self.created_at + timedelta(seconds=self.ttl)
    
    def update_access(self):
        """更新访问信息"""
        self.accessed_at = datetime.now()
        self.access_count += 1
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "key": self.key,
            "created_at": self.created_at.isoformat(),
            "accessed_at": self.accessed_at.isoformat(),
            "access_count": self.access_count,
            "ttl": self.ttl,
            "size": self.size,
            "tags": self.tags,
            "metadata": self.metadata
        }


@dataclass
class CacheStats:
    """缓存统计信息"""
    total_items: int = 0
    total_size: int = 0
    hit_count: int = 0
    miss_count: int = 0
    eviction_count: int = 0
    error_count: int = 0
    
    @property
    def hit_rate(self) -> float:
        """命中率"""
        total = self.hit_count + self.miss_count
        return self.hit_count / total if total > 0 else 0.0
    
    @property
    def miss_rate(self) -> float:
        """未命中率"""
        return 1.0 - self.hit_rate
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "total_items": self.total_items,
            "total_size": self.total_size,
            "hit_count": self.hit_count,
            "miss_count": self.miss_count,
            "eviction_count": self.eviction_count,
            "error_count": self.error_count,
            "hit_rate": self.hit_rate,
            "miss_rate": self.miss_rate
        }


# ==================== 内存缓存管理器 ====================

class MemoryCacheManager:
    """
    内存缓存管理器
    
    提供高性能的内存缓存功能
    """
    
    def __init__(
        self,
        max_size: int = 1000,
        max_memory: int = 100 * 1024 * 1024,  # 100MB
        strategy: CacheStrategy = CacheStrategy.LRU,
        default_ttl: Optional[int] = None
    ):
        """
        初始化内存缓存管理器
        
        Args:
            max_size: 最大缓存项数量
            max_memory: 最大内存使用量（字节）
            strategy: 缓存策略
            default_ttl: 默认TTL（秒）
        """
        self.max_size = max_size
        self.max_memory = max_memory
        self.strategy = strategy
        self.default_ttl = default_ttl
        
        self._cache: Dict[str, CacheItem] = {}
        self._access_order = OrderedDict()  # 用于LRU
        self._frequency = {}  # 用于LFU
        self._stats = CacheStats()
        self._lock = threading.RLock()
        
        # 启动清理任务
        self._cleanup_task = None
        self._start_cleanup_task()
    
    def _start_cleanup_task(self):
        """启动清理任务"""
        async def cleanup_loop():
            while True:
                try:
                    await asyncio.sleep(60)  # 每分钟清理一次
                    await self._cleanup_expired()
                except Exception as e:
                    logger.error(f"缓存清理任务异常: {str(e)}")
        
        try:
            loop = asyncio.get_event_loop()
            self._cleanup_task = loop.create_task(cleanup_loop())
        except RuntimeError:
            # 没有运行的事件循环，稍后启动
            pass
    
    def _calculate_size(self, value: Any) -> int:
        """计算值的大小"""
        try:
            if isinstance(value, (str, bytes)):
                return len(value)
            elif isinstance(value, (int, float, bool)):
                return 8
            elif isinstance(value, (list, tuple, dict)):
                return len(pickle.dumps(value))
            else:
                return len(pickle.dumps(value))
        except Exception:
            return 1024  # 默认1KB
    
    def _should_evict(self) -> bool:
        """检查是否需要驱逐"""
        return (
            len(self._cache) >= self.max_size or
            self._stats.total_size >= self.max_memory
        )
    
    def _evict_items(self):
        """驱逐缓存项"""
        if not self._cache:
            return
        
        evict_count = max(1, len(self._cache) // 10)  # 驱逐10%的项
        
        if self.strategy == CacheStrategy.LRU:
            # 驱逐最近最少使用的项
            keys_to_evict = list(self._access_order.keys())[:evict_count]
        elif self.strategy == CacheStrategy.LFU:
            # 驱逐使用频率最低的项
            sorted_items = sorted(
                self._cache.items(),
                key=lambda x: x[1].access_count
            )
            keys_to_evict = [item[0] for item in sorted_items[:evict_count]]
        elif self.strategy == CacheStrategy.FIFO:
            # 驱逐最早创建的项
            sorted_items = sorted(
                self._cache.items(),
                key=lambda x: x[1].created_at
            )
            keys_to_evict = [item[0] for item in sorted_items[:evict_count]]
        else:
            # 默认使用LRU
            keys_to_evict = list(self._access_order.keys())[:evict_count]
        
        for key in keys_to_evict:
            self._remove_item(key)
            self._stats.eviction_count += 1
    
    def _remove_item(self, key: str):
        """移除缓存项"""
        if key in self._cache:
            item = self._cache[key]
            self._stats.total_size -= item.size
            self._stats.total_items -= 1
            
            del self._cache[key]
            self._access_order.pop(key, None)
            self._frequency.pop(key, None)
    
    def _update_access_order(self, key: str):
        """更新访问顺序"""
        if self.strategy == CacheStrategy.LRU:
            # 移动到末尾
            self._access_order.pop(key, None)
            self._access_order[key] = True
        elif self.strategy == CacheStrategy.LFU:
            # 更新频率
            self._frequency[key] = self._frequency.get(key, 0) + 1
    
    async def get(self, key: str) -> Optional[Any]:
        """
        获取缓存值
        
        Args:
            key: 缓存键
            
        Returns:
            缓存值，如果不存在返回None
        """
        with self._lock:
            if key not in self._cache:
                self._stats.miss_count += 1
                return None
            
            item = self._cache[key]
            
            # 检查是否过期
            if item.is_expired():
                self._remove_item(key)
                self._stats.miss_count += 1
                return None
            
            # 更新访问信息
            item.update_access()
            self._update_access_order(key)
            
            self._stats.hit_count += 1
            return item.value
    
    async def set(
        self,
        key: str,
        value: Any,
        ttl: Optional[int] = None,
        tags: Optional[List[str]] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        设置缓存值
        
        Args:
            key: 缓存键
            value: 缓存值
            ttl: 生存时间（秒）
            tags: 标签
            metadata: 元数据
            
        Returns:
            是否设置成功
        """
        try:
            with self._lock:
                # 计算大小
                size = self._calculate_size(value)
                
                # 检查是否需要驱逐
                while self._should_evict():
                    self._evict_items()
                
                # 创建缓存项
                now = datetime.now()
                item = CacheItem(
                    key=key,
                    value=value,
                    created_at=now,
                    accessed_at=now,
                    access_count=1,
                    ttl=ttl or self.default_ttl,
                    size=size,
                    tags=tags or [],
                    metadata=metadata or {}
                )
                
                # 如果键已存在，先移除旧项
                if key in self._cache:
                    self._remove_item(key)
                
                # 添加新项
                self._cache[key] = item
                self._update_access_order(key)
                
                # 更新统计
                self._stats.total_items += 1
                self._stats.total_size += size
                
                return True
                
        except Exception as e:
            logger.error(f"设置缓存失败: {key}, 错误: {str(e)}")
            self._stats.error_count += 1
            return False
    
    async def delete(self, key: str) -> bool:
        """
        删除缓存项
        
        Args:
            key: 缓存键
            
        Returns:
            是否删除成功
        """
        with self._lock:
            if key in self._cache:
                self._remove_item(key)
                return True
            return False
    
    async def clear(self, tags: Optional[List[str]] = None) -> int:
        """
        清空缓存
        
        Args:
            tags: 如果指定，只清除包含这些标签的项
            
        Returns:
            清除的项数量
        """
        with self._lock:
            if tags is None:
                # 清空所有
                count = len(self._cache)
                self._cache.clear()
                self._access_order.clear()
                self._frequency.clear()
                self._stats.total_items = 0
                self._stats.total_size = 0
                return count
            else:
                # 清除指定标签的项
                keys_to_remove = []
                for key, item in self._cache.items():
                    if any(tag in item.tags for tag in tags):
                        keys_to_remove.append(key)
                
                for key in keys_to_remove:
                    self._remove_item(key)
                
                return len(keys_to_remove)
    
    async def _cleanup_expired(self):
        """清理过期项"""
        with self._lock:
            expired_keys = []
            for key, item in self._cache.items():
                if item.is_expired():
                    expired_keys.append(key)
            
            for key in expired_keys:
                self._remove_item(key)
            
            if expired_keys:
                logger.debug(f"清理过期缓存项: {len(expired_keys)}")
    
    def get_stats(self) -> CacheStats:
        """获取缓存统计信息"""
        with self._lock:
            return CacheStats(
                total_items=self._stats.total_items,
                total_size=self._stats.total_size,
                hit_count=self._stats.hit_count,
                miss_count=self._stats.miss_count,
                eviction_count=self._stats.eviction_count,
                error_count=self._stats.error_count
            )
    
    def get_items_info(self) -> List[Dict[str, Any]]:
        """获取缓存项信息"""
        with self._lock:
            return [item.to_dict() for item in self._cache.values()]


# ==================== 磁盘缓存管理器 ====================

class DiskCacheManager:
    """
    磁盘缓存管理器
    
    提供持久化的磁盘缓存功能
    """
    
    def __init__(
        self,
        cache_dir: str = None,
        max_size: int = 10000,
        max_disk_usage: int = 1024 * 1024 * 1024,  # 1GB
        default_ttl: Optional[int] = None
    ):
        """
        初始化磁盘缓存管理器
        
        Args:
            cache_dir: 缓存目录
            max_size: 最大缓存项数量
            max_disk_usage: 最大磁盘使用量（字节）
            default_ttl: 默认TTL（秒）
        """
        settings = get_settings()
        self.cache_dir = Path(cache_dir or getattr(settings, 'CACHE_DIR', 'cache'))
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        self.max_size = max_size
        self.max_disk_usage = max_disk_usage
        self.default_ttl = default_ttl
        
        # 元数据文件
        self.metadata_file = self.cache_dir / "metadata.json"
        self._metadata: Dict[str, Dict[str, Any]] = {}
        self._stats = CacheStats()
        self._lock = threading.RLock()
        
        # 加载元数据
        self._load_metadata()
        
        # 启动清理任务
        self._cleanup_task = None
        self._start_cleanup_task()
    
    def _start_cleanup_task(self):
        """启动清理任务"""
        async def cleanup_loop():
            while True:
                try:
                    await asyncio.sleep(300)  # 每5分钟清理一次
                    await self._cleanup_expired()
                    await self._cleanup_oversized()
                except Exception as e:
                    logger.error(f"磁盘缓存清理任务异常: {str(e)}")
        
        try:
            loop = asyncio.get_event_loop()
            self._cleanup_task = loop.create_task(cleanup_loop())
        except RuntimeError:
            pass
    
    def _load_metadata(self):
        """加载元数据"""
        try:
            if self.metadata_file.exists():
                with open(self.metadata_file, 'r', encoding='utf-8') as f:
                    self._metadata = json.load(f)
                
                # 更新统计信息
                self._stats.total_items = len(self._metadata)
                self._stats.total_size = sum(
                    item.get('size', 0) for item in self._metadata.values()
                )
                
                logger.debug(f"加载磁盘缓存元数据: {len(self._metadata)} 项")
        except Exception as e:
            logger.error(f"加载磁盘缓存元数据失败: {str(e)}")
            self._metadata = {}
    
    def _save_metadata(self):
        """保存元数据"""
        try:
            with open(self.metadata_file, 'w', encoding='utf-8') as f:
                json.dump(self._metadata, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"保存磁盘缓存元数据失败: {str(e)}")
    
    def _get_cache_file_path(self, key: str) -> Path:
        """获取缓存文件路径"""
        # 使用哈希避免文件名冲突
        key_hash = hashlib.md5(key.encode()).hexdigest()
        subdir = key_hash[:2]
        cache_subdir = self.cache_dir / subdir
        cache_subdir.mkdir(exist_ok=True)
        return cache_subdir / f"{key_hash}.cache"
    
    def _is_expired(self, metadata: Dict[str, Any]) -> bool:
        """检查是否过期"""
        if 'ttl' not in metadata or metadata['ttl'] is None:
            return False
        
        created_at = datetime.fromisoformat(metadata['created_at'])
        return datetime.now() > created_at + timedelta(seconds=metadata['ttl'])
    
    async def get(self, key: str) -> Optional[Any]:
        """
        获取缓存值
        
        Args:
            key: 缓存键
            
        Returns:
            缓存值，如果不存在返回None
        """
        with self._lock:
            if key not in self._metadata:
                self._stats.miss_count += 1
                return None
            
            metadata = self._metadata[key]
            
            # 检查是否过期
            if self._is_expired(metadata):
                await self._remove_item(key)
                self._stats.miss_count += 1
                return None
            
            # 读取缓存文件
            cache_file = self._get_cache_file_path(key)
            if not cache_file.exists():
                # 文件不存在，清理元数据
                del self._metadata[key]
                self._save_metadata()
                self._stats.miss_count += 1
                return None
            
            try:
                async with aiofiles.open(cache_file, 'rb') as f:
                    data = await f.read()
                
                # 反序列化
                value = pickle.loads(data)
                
                # 更新访问信息
                metadata['accessed_at'] = datetime.now().isoformat()
                metadata['access_count'] = metadata.get('access_count', 0) + 1
                self._save_metadata()
                
                self._stats.hit_count += 1
                return value
                
            except Exception as e:
                logger.error(f"读取磁盘缓存失败: {key}, 错误: {str(e)}")
                await self._remove_item(key)
                self._stats.miss_count += 1
                self._stats.error_count += 1
                return None
    
    async def set(
        self,
        key: str,
        value: Any,
        ttl: Optional[int] = None,
        tags: Optional[List[str]] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        设置缓存值
        
        Args:
            key: 缓存键
            value: 缓存值
            ttl: 生存时间（秒）
            tags: 标签
            metadata: 元数据
            
        Returns:
            是否设置成功
        """
        try:
            # 序列化数据
            data = pickle.dumps(value)
            size = len(data)
            
            # 检查磁盘空间
            if self._stats.total_size + size > self.max_disk_usage:
                await self._cleanup_oversized()
            
            # 写入缓存文件
            cache_file = self._get_cache_file_path(key)
            async with aiofiles.open(cache_file, 'wb') as f:
                await f.write(data)
            
            with self._lock:
                # 如果键已存在，先移除旧项
                if key in self._metadata:
                    old_size = self._metadata[key].get('size', 0)
                    self._stats.total_size -= old_size
                else:
                    self._stats.total_items += 1
                
                # 创建元数据
                now = datetime.now()
                self._metadata[key] = {
                    'created_at': now.isoformat(),
                    'accessed_at': now.isoformat(),
                    'access_count': 1,
                    'ttl': ttl or self.default_ttl,
                    'size': size,
                    'tags': tags or [],
                    'metadata': metadata or {},
                    'file_path': str(cache_file)
                }
                
                # 更新统计
                self._stats.total_size += size
                
                # 保存元数据
                self._save_metadata()
                
                return True
                
        except Exception as e:
            logger.error(f"设置磁盘缓存失败: {key}, 错误: {str(e)}")
            self._stats.error_count += 1
            return False
    
    async def delete(self, key: str) -> bool:
        """
        删除缓存项
        
        Args:
            key: 缓存键
            
        Returns:
            是否删除成功
        """
        return await self._remove_item(key)
    
    async def _remove_item(self, key: str) -> bool:
        """移除缓存项"""
        with self._lock:
            if key not in self._metadata:
                return False
            
            metadata = self._metadata[key]
            
            # 删除缓存文件
            cache_file = self._get_cache_file_path(key)
            try:
                if cache_file.exists():
                    cache_file.unlink()
            except Exception as e:
                logger.warning(f"删除缓存文件失败: {cache_file}, 错误: {str(e)}")
            
            # 更新统计
            self._stats.total_size -= metadata.get('size', 0)
            self._stats.total_items -= 1
            
            # 删除元数据
            del self._metadata[key]
            self._save_metadata()
            
            return True
    
    async def clear(self, tags: Optional[List[str]] = None) -> int:
        """
        清空缓存
        
        Args:
            tags: 如果指定，只清除包含这些标签的项
            
        Returns:
            清除的项数量
        """
        with self._lock:
            if tags is None:
                # 清空所有
                count = len(self._metadata)
                
                # 删除所有缓存文件
                for subdir in self.cache_dir.iterdir():
                    if subdir.is_dir() and subdir.name != '__pycache__':
                        for cache_file in subdir.glob('*.cache'):
                            try:
                                cache_file.unlink()
                            except Exception:
                                pass
                
                # 清空元数据
                self._metadata.clear()
                self._stats.total_items = 0
                self._stats.total_size = 0
                self._save_metadata()
                
                return count
            else:
                # 清除指定标签的项
                keys_to_remove = []
                for key, metadata in self._metadata.items():
                    item_tags = metadata.get('tags', [])
                    if any(tag in item_tags for tag in tags):
                        keys_to_remove.append(key)
                
                for key in keys_to_remove:
                    await self._remove_item(key)
                
                return len(keys_to_remove)
    
    async def _cleanup_expired(self):
        """清理过期项"""
        with self._lock:
            expired_keys = []
            for key, metadata in self._metadata.items():
                if self._is_expired(metadata):
                    expired_keys.append(key)
            
            for key in expired_keys:
                await self._remove_item(key)
            
            if expired_keys:
                logger.debug(f"清理过期磁盘缓存项: {len(expired_keys)}")
    
    async def _cleanup_oversized(self):
        """清理超大缓存"""
        if self._stats.total_size <= self.max_disk_usage:
            return
        
        with self._lock:
            # 按访问时间排序，删除最旧的项
            sorted_items = sorted(
                self._metadata.items(),
                key=lambda x: x[1].get('accessed_at', '1970-01-01')
            )
            
            target_size = self.max_disk_usage * 0.8  # 清理到80%
            
            for key, metadata in sorted_items:
                if self._stats.total_size <= target_size:
                    break
                
                await self._remove_item(key)
                self._stats.eviction_count += 1
    
    def get_stats(self) -> CacheStats:
        """获取缓存统计信息"""
        with self._lock:
            return CacheStats(
                total_items=self._stats.total_items,
                total_size=self._stats.total_size,
                hit_count=self._stats.hit_count,
                miss_count=self._stats.miss_count,
                eviction_count=self._stats.eviction_count,
                error_count=self._stats.error_count
            )


# ==================== 多级缓存管理器 ====================

class MultiLevelCacheManager:
    """
    多级缓存管理器
    
    结合内存缓存和磁盘缓存，提供多级缓存功能
    """
    
    def __init__(
        self,
        memory_config: Optional[Dict[str, Any]] = None,
        disk_config: Optional[Dict[str, Any]] = None
    ):
        """
        初始化多级缓存管理器
        
        Args:
            memory_config: 内存缓存配置
            disk_config: 磁盘缓存配置
        """
        # 初始化内存缓存
        memory_config = memory_config or {}
        self.memory_cache = MemoryCacheManager(**memory_config)
        
        # 初始化磁盘缓存
        disk_config = disk_config or {}
        self.disk_cache = DiskCacheManager(**disk_config)
        
        self._stats = CacheStats()
    
    async def get(self, key: str) -> Optional[Any]:
        """
        获取缓存值（先查内存，再查磁盘）
        
        Args:
            key: 缓存键
            
        Returns:
            缓存值，如果不存在返回None
        """
        # 先查内存缓存
        value = await self.memory_cache.get(key)
        if value is not None:
            self._stats.hit_count += 1
            return value
        
        # 再查磁盘缓存
        value = await self.disk_cache.get(key)
        if value is not None:
            # 将磁盘缓存的值提升到内存缓存
            await self.memory_cache.set(key, value)
            self._stats.hit_count += 1
            return value
        
        self._stats.miss_count += 1
        return None
    
    async def set(
        self,
        key: str,
        value: Any,
        ttl: Optional[int] = None,
        tags: Optional[List[str]] = None,
        metadata: Optional[Dict[str, Any]] = None,
        levels: Optional[List[CacheLevel]] = None
    ) -> bool:
        """
        设置缓存值
        
        Args:
            key: 缓存键
            value: 缓存值
            ttl: 生存时间（秒）
            tags: 标签
            metadata: 元数据
            levels: 缓存级别列表
            
        Returns:
            是否设置成功
        """
        if levels is None:
            levels = [CacheLevel.MEMORY, CacheLevel.DISK]
        
        success = True
        
        # 设置内存缓存
        if CacheLevel.MEMORY in levels:
            memory_success = await self.memory_cache.set(key, value, ttl, tags, metadata)
            success = success and memory_success
        
        # 设置磁盘缓存
        if CacheLevel.DISK in levels:
            disk_success = await self.disk_cache.set(key, value, ttl, tags, metadata)
            success = success and disk_success
        
        return success
    
    async def delete(self, key: str) -> bool:
        """
        删除缓存项（从所有级别删除）
        
        Args:
            key: 缓存键
            
        Returns:
            是否删除成功
        """
        memory_success = await self.memory_cache.delete(key)
        disk_success = await self.disk_cache.delete(key)
        
        return memory_success or disk_success
    
    async def clear(self, tags: Optional[List[str]] = None) -> Dict[str, int]:
        """
        清空缓存
        
        Args:
            tags: 如果指定，只清除包含这些标签的项
            
        Returns:
            各级别清除的项数量
        """
        memory_count = await self.memory_cache.clear(tags)
        disk_count = await self.disk_cache.clear(tags)
        
        return {
            "memory": memory_count,
            "disk": disk_count,
            "total": memory_count + disk_count
        }
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取缓存统计信息
        
        Returns:
            缓存统计信息
        """
        memory_stats = self.memory_cache.get_stats()
        disk_stats = self.disk_cache.get_stats()
        
        total_stats = CacheStats(
            total_items=memory_stats.total_items + disk_stats.total_items,
            total_size=memory_stats.total_size + disk_stats.total_size,
            hit_count=self._stats.hit_count,
            miss_count=self._stats.miss_count,
            eviction_count=memory_stats.eviction_count + disk_stats.eviction_count,
            error_count=memory_stats.error_count + disk_stats.error_count
        )
        
        return {
            "memory": memory_stats.to_dict(),
            "disk": disk_stats.to_dict(),
            "total": total_stats.to_dict()
        }


# ==================== 缓存装饰器 ====================

def cache(
    key_func: Optional[Callable] = None,
    ttl: Optional[int] = None,
    tags: Optional[List[str]] = None,
    levels: Optional[List[CacheLevel]] = None
):
    """
    缓存装饰器
    
    Args:
        key_func: 键生成函数
        ttl: 生存时间（秒）
        tags: 标签
        levels: 缓存级别
    """
    def decorator(func):
        async def wrapper(*args, **kwargs):
            # 生成缓存键
            if key_func:
                cache_key = key_func(*args, **kwargs)
            else:
                cache_key = f"{func.__module__}.{func.__name__}:{hash((args, tuple(kwargs.items())))}"
            
            # 尝试从缓存获取
            cached_value = await cache_manager.get(cache_key)
            if cached_value is not None:
                return cached_value
            
            # 执行函数
            result = await func(*args, **kwargs)
            
            # 存入缓存
            await cache_manager.set(cache_key, result, ttl, tags, levels=levels)
            
            return result
        
        return wrapper
    return decorator


# ==================== 全局缓存管理器实例 ====================

# 全局缓存管理器实例
_cache_manager = None


async def init_cache_manager():
    """初始化缓存管理器"""
    global _cache_manager
    _cache_manager = MultiLevelCacheManager()


async def cleanup_cache_manager():
    """清理缓存管理器"""
    global _cache_manager
    if _cache_manager:
        await _cache_manager.clear()
    _cache_manager = None


def get_cache_manager() -> MultiLevelCacheManager:
    """获取缓存管理器实例"""
    global _cache_manager
    if _cache_manager is None:
        _cache_manager = MultiLevelCacheManager()
    return _cache_manager


# ==================== 便捷函数 ====================


async def get_cached(key: str) -> Optional[Any]:
    """获取缓存值"""
    return await get_cache_manager().get(key)


async def set_cached(
    key: str,
    value: Any,
    ttl: Optional[int] = None,
    tags: Optional[List[str]] = None,
    levels: Optional[List[CacheLevel]] = None
) -> bool:
    """设置缓存值"""
    return await get_cache_manager().set(key, value, ttl, tags, levels=levels)


async def delete_cached(key: str) -> bool:
    """删除缓存项"""
    return await get_cache_manager().delete(key)


async def clear_cache(tags: Optional[List[str]] = None) -> Dict[str, int]:
    """清空缓存"""
    return await get_cache_manager().clear(tags)


def get_cache_stats() -> Dict[str, Any]:
    """获取缓存统计信息"""
    return get_cache_manager().get_stats()