#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A2A多智能体系统工件模型

定义工件相关的数据模型
"""

from datetime import datetime
from typing import Optional, List, Dict, Any
from sqlalchemy import Column, Integer, String, Text, Boolean, DateTime, ForeignKey, Index, Float, LargeBinary
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from .base import BaseModel


class Artifact(BaseModel):
    """
    工件模型
    
    存储智能体生成的工件信息
    """
    
    __tablename__ = "artifacts"
    
    # 关联关系
    user_id = Column(
        Integer,
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        comment="用户ID"
    )
    
    owner_id = Column(
        Integer,
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        comment="拥有者用户ID"
    )
    
    agent_id = Column(
        Integer,
        ForeignKey("agents.id", ondelete="SET NULL"),
        nullable=True,
        comment="生成智能体ID"
    )
    
    session_id = Column(
        Integer,
        ForeignKey("sessions.id", ondelete="SET NULL"),
        nullable=True,
        comment="会话ID"
    )
    
    message_id = Column(
        Integer,
        ForeignKey("messages.id", ondelete="SET NULL"),
        nullable=True,
        comment="消息ID"
    )
    
    # 工件基本信息
    artifact_id = Column(
        String(36),
        nullable=False,
        unique=True,
        comment="工件唯一标识"
    )
    
    name = Column(
        String(200),
        nullable=False,
        comment="工件名称"
    )
    
    description = Column(
        Text,
        nullable=True,
        comment="工件描述"
    )
    
    # 工件类型和格式
    artifact_type = Column(
        String(50),
        nullable=False,
        comment="工件类型"
    )
    
    content_type = Column(
        String(100),
        nullable=False,
        comment="内容类型（MIME类型）"
    )
    
    file_extension = Column(
        String(20),
        nullable=True,
        comment="文件扩展名"
    )
    
    # 工件内容
    content = Column(
        Text,
        nullable=True,
        comment="文本内容"
    )
    
    binary_content = Column(
        LargeBinary,
        nullable=True,
        comment="二进制内容"
    )
    
    # 文件信息
    file_size = Column(
        Integer,
        nullable=False,
        default=0,
        comment="文件大小（字节）"
    )
    
    file_hash = Column(
        String(64),
        nullable=True,
        comment="文件哈希值（SHA256）"
    )
    
    # 存储信息
    storage_type = Column(
        String(20),
        nullable=False,
        default="database",
        comment="存储类型"
    )
    
    storage_path = Column(
        String(500),
        nullable=True,
        comment="存储路径"
    )
    
    # 版本信息
    version = Column(
        String(20),
        nullable=False,
        default="1.0.0",
        comment="版本号"
    )
    
    parent_id = Column(
        Integer,
        ForeignKey("artifacts.id", ondelete="SET NULL"),
        nullable=True,
        comment="父工件ID"
    )
    
    # 状态信息
    status = Column(
        String(20),
        nullable=False,
        default="active",
        comment="工件状态"
    )
    
    is_public = Column(
        Boolean,
        nullable=False,
        default=False,
        comment="是否公开"
    )
    
    # 元数据
    meta_data = Column(
        "metadata",
        Text,
        nullable=True,
        comment="元数据（JSON格式）"
    )
    
    # 标签
    tags = Column(
        Text,
        nullable=True,
        comment="标签（JSON格式）"
    )
    
    # 统计信息
    download_count = Column(
        Integer,
        nullable=False,
        default=0,
        comment="下载次数"
    )
    
    view_count = Column(
        Integer,
        nullable=False,
        default=0,
        comment="查看次数"
    )
    
    # 时间信息
    last_accessed_at = Column(
        DateTime,
        nullable=True,
        comment="最后访问时间"
    )
    
    expires_at = Column(
        DateTime,
        nullable=True,
        comment="过期时间"
    )
    
    # 关联关系
    user = relationship("User", back_populates="artifacts")
    agent = relationship("Agent", back_populates="artifacts")
    session = relationship("Session", back_populates="artifacts")
    message = relationship("Message", back_populates="artifacts")
    parent = relationship("Artifact", back_populates="children", remote_side=[id])
    children = relationship("Artifact", back_populates="parent")
    shares = relationship("ArtifactShare", back_populates="artifact", cascade="all, delete-orphan")
    
    # 索引
    __table_args__ = (
        Index("idx_artifacts_user_id", "user_id"),
        Index("idx_artifacts_agent_id", "agent_id"),
        Index("idx_artifacts_session_id", "session_id"),
        Index("idx_artifacts_message_id", "message_id"),
        Index("idx_artifacts_artifact_id", "artifact_id"),
        Index("idx_artifacts_name", "name"),
        Index("idx_artifacts_type", "artifact_type"),
        Index("idx_artifacts_content_type", "content_type"),
        Index("idx_artifacts_status", "status"),
        Index("idx_artifacts_is_public", "is_public"),
        Index("idx_artifacts_parent_id", "parent_id"),
        Index("idx_artifacts_file_hash", "file_hash"),
        Index("idx_artifacts_is_active", "is_active"),
    )
    
    def set_metadata(self, metadata: Dict[str, Any]) -> None:
        """
        设置元数据
        
        Args:
            metadata: 元数据字典
        """
        import json
        self.meta_data = json.dumps(metadata, ensure_ascii=False)
    
    def get_metadata(self) -> Dict[str, Any]:
        """
        获取元数据
        
        Returns:
            Dict[str, Any]: 元数据字典
        """
        if not self.meta_data:
            return {}
        
        try:
            import json
            return json.loads(self.meta_data)
        except (json.JSONDecodeError, TypeError):
            return {}
    
    def set_tags(self, tags: List[str]) -> None:
        """
        设置标签
        
        Args:
            tags: 标签列表
        """
        import json
        self.tags = json.dumps(tags, ensure_ascii=False)
    
    def get_tags(self) -> List[str]:
        """
        获取标签
        
        Returns:
            List[str]: 标签列表
        """
        if not self.tags:
            return []
        
        try:
            import json
            return json.loads(self.tags)
        except (json.JSONDecodeError, TypeError):
            return []
    
    def calculate_file_hash(self) -> str:
        """
        计算文件哈希值
        
        Returns:
            str: SHA256哈希值
        """
        import hashlib
        
        if self.binary_content:
            content = self.binary_content
        elif self.content:
            content = self.content.encode('utf-8')
        else:
            return ""
        
        return hashlib.sha256(content).hexdigest()
    
    def update_file_hash(self) -> None:
        """
        更新文件哈希值
        """
        self.file_hash = self.calculate_file_hash()
    
    def increment_view_count(self) -> None:
        """
        增加查看次数
        """
        self.view_count += 1
        self.last_accessed_at = datetime.now()
    
    def increment_download_count(self) -> None:
        """
        增加下载次数
        """
        self.download_count += 1
        self.last_accessed_at = datetime.now()
    
    def is_expired(self) -> bool:
        """
        检查是否过期
        
        Returns:
            bool: 是否过期
        """
        if not self.expires_at:
            return False
        return datetime.now() > self.expires_at
    
    def get_file_size_human(self) -> str:
        """
        获取人类可读的文件大小
        
        Returns:
            str: 文件大小字符串
        """
        size = self.file_size
        for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
            if size < 1024.0:
                return f"{size:.1f} {unit}"
            size /= 1024.0
        return f"{size:.1f} PB"
    
    def __repr__(self) -> str:
        return f"<Artifact(id={self.id}, artifact_id='{self.artifact_id}', name='{self.name}')>"


class ArtifactShare(BaseModel):
    """
    工件分享模型
    
    存储工件分享信息
    """
    
    __tablename__ = "artifact_shares"
    
    # 工件关联
    artifact_id = Column(
        Integer,
        ForeignKey("artifacts.id", ondelete="CASCADE"),
        nullable=False,
        comment="工件ID"
    )
    
    # 分享信息
    share_token = Column(
        String(64),
        nullable=False,
        unique=True,
        comment="分享令牌"
    )
    
    share_url = Column(
        String(500),
        nullable=True,
        comment="分享URL"
    )
    
    # 权限设置
    permission = Column(
        String(20),
        nullable=False,
        default="view",
        comment="分享权限"
    )
    
    # 访问控制
    password = Column(
        String(255),
        nullable=True,
        comment="访问密码（加密）"
    )
    
    allowed_ips = Column(
        Text,
        nullable=True,
        comment="允许的IP地址（JSON格式）"
    )
    
    # 时间控制
    expires_at = Column(
        DateTime,
        nullable=True,
        comment="过期时间"
    )
    
    # 访问限制
    max_views = Column(
        Integer,
        nullable=True,
        comment="最大查看次数"
    )
    
    max_downloads = Column(
        Integer,
        nullable=True,
        comment="最大下载次数"
    )
    
    # 统计信息
    view_count = Column(
        Integer,
        nullable=False,
        default=0,
        comment="查看次数"
    )
    
    download_count = Column(
        Integer,
        nullable=False,
        default=0,
        comment="下载次数"
    )
    
    # 时间信息
    last_accessed_at = Column(
        DateTime,
        nullable=True,
        comment="最后访问时间"
    )
    
    # 关联关系
    artifact = relationship("Artifact", back_populates="shares")
    
    # 索引
    __table_args__ = (
        Index("idx_artifact_shares_artifact_id", "artifact_id"),
        Index("idx_artifact_shares_share_token", "share_token"),
        Index("idx_artifact_shares_expires_at", "expires_at"),
        Index("idx_artifact_shares_is_active", "is_active"),
    )
    
    def set_allowed_ips(self, ips: List[str]) -> None:
        """
        设置允许的IP地址
        
        Args:
            ips: IP地址列表
        """
        import json
        self.allowed_ips = json.dumps(ips, ensure_ascii=False)
    
    def get_allowed_ips(self) -> List[str]:
        """
        获取允许的IP地址
        
        Returns:
            List[str]: IP地址列表
        """
        if not self.allowed_ips:
            return []
        
        try:
            import json
            return json.loads(self.allowed_ips)
        except (json.JSONDecodeError, TypeError):
            return []
    
    def is_expired(self) -> bool:
        """
        检查是否过期
        
        Returns:
            bool: 是否过期
        """
        if not self.expires_at:
            return False
        return datetime.now() > self.expires_at
    
    def is_view_limit_reached(self) -> bool:
        """
        检查查看次数是否达到限制
        
        Returns:
            bool: 是否达到限制
        """
        if not self.max_views:
            return False
        return self.view_count >= self.max_views
    
    def is_download_limit_reached(self) -> bool:
        """
        检查下载次数是否达到限制
        
        Returns:
            bool: 是否达到限制
        """
        if not self.max_downloads:
            return False
        return self.download_count >= self.max_downloads
    
    def can_access(self, ip_address: str = None) -> bool:
        """
        检查是否可以访问
        
        Args:
            ip_address: 访问者IP地址
        
        Returns:
            bool: 是否可以访问
        """
        # 检查是否激活
        if not self.is_active:
            return False
        
        # 检查是否过期
        if self.is_expired():
            return False
        
        # 检查IP限制
        if ip_address and self.allowed_ips:
            allowed_ips = self.get_allowed_ips()
            if allowed_ips and ip_address not in allowed_ips:
                return False
        
        return True
    
    def increment_view_count(self) -> None:
        """
        增加查看次数
        """
        self.view_count += 1
        self.last_accessed_at = datetime.now()
    
    def increment_download_count(self) -> None:
        """
        增加下载次数
        """
        self.download_count += 1
        self.last_accessed_at = datetime.now()
    
    def __repr__(self) -> str:
        return f"<ArtifactShare(id={self.id}, share_token='{self.share_token}', permission='{self.permission}')>"


class ArtifactVersion(BaseModel):
    """
    工件版本模型
    
    存储工件的版本历史
    """
    
    __tablename__ = "artifact_versions"
    
    # 工件关联
    artifact_id = Column(
        Integer,
        ForeignKey("artifacts.id", ondelete="CASCADE"),
        nullable=False,
        comment="工件ID"
    )
    
    # 版本信息
    version = Column(
        String(20),
        nullable=False,
        comment="版本号"
    )
    
    # 变更信息
    change_log = Column(
        Text,
        nullable=True,
        comment="变更日志"
    )
    
    changed_by = Column(
        Integer,
        ForeignKey("users.id", ondelete="SET NULL"),
        nullable=True,
        comment="变更者用户ID"
    )
    
    # 内容快照
    content_snapshot = Column(
        Text,
        nullable=True,
        comment="内容快照"
    )
    
    file_hash_snapshot = Column(
        String(64),
        nullable=True,
        comment="文件哈希快照"
    )
    
    # 关联关系
    artifact = relationship("Artifact")
    changed_by_user = relationship("User")
    
    # 索引
    __table_args__ = (
        Index("idx_artifact_versions_artifact_id", "artifact_id"),
        Index("idx_artifact_versions_version", "version"),
        Index("idx_artifact_versions_changed_by", "changed_by"),
        # 复合唯一索引
        Index("idx_artifact_versions_unique", "artifact_id", "version", unique=True),
    )
    
    def __repr__(self) -> str:
        return f"<ArtifactVersion(id={self.id}, artifact_id={self.artifact_id}, version='{self.version}')>"