#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A2A多智能体系统 Google Gemini LLM集成

基于Google ADK GoogleLLM的扩展实现
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any, AsyncIterator, Union
from datetime import datetime

from google.adk.models.google_llm import Gemini as ADKGoogleLLM
from google.genai.types import Content, Part, GenerateContentResponse
from google.generativeai.types import GenerationConfig, SafetySettingDict
import google.generativeai as genai

from app.services.llm_service import LLMConfig, LLMProvider
from .base_llm import BaseLLM, StreamingResponse


class GoogleLLM(BaseLLM):
    """
    Google Gemini LLM集成
    
    基于Google ADK GoogleLLM的扩展实现，支持流式输出和自定义配置
    """
    
    def __init__(self, config: LLMConfig, user_id: int):
        """
        初始化Google LLM
        
        Args:
            config: LLM配置
            user_id: 用户ID
        """
        super().__init__(config, user_id)
        
        self.logger = logging.getLogger(__name__)
        self._adk_llm: Optional[ADKGoogleLLM] = None
        self._client: Optional[genai.GenerativeModel] = None
        
        # 初始化Google AI客户端
        self._initialize_client()
        
        self.logger.info(f"Google LLM初始化完成，用户: {user_id}, 模型: {config.model_name}")
    
    def _initialize_client(self) -> None:
        """
        初始化Google AI客户端
        """
        try:
            # 配置API密钥
            genai.configure(api_key=self.config.api_key)
            
            # 创建生成配置
            generation_config = GenerationConfig(
                temperature=self.config.temperature,
                max_output_tokens=self.config.max_tokens,
                top_p=self.config.extra_params.get("top_p", 0.95),
                top_k=self.config.extra_params.get("top_k", 40),
            )
            
            # 创建安全设置
            safety_settings = self._create_safety_settings()
            
            # 创建生成模型
            self._client = genai.GenerativeModel(
                model_name=self.config.model_name,
                generation_config=generation_config,
                safety_settings=safety_settings
            )
            
            # 创建ADK LLM实例
            self._adk_llm = ADKGoogleLLM(
                model_name=self.config.model_name,
                api_key=self.config.api_key
            )
            
        except Exception as e:
            self.logger.error(f"初始化Google AI客户端失败: {e}")
            raise
    
    def _create_safety_settings(self) -> List[SafetySettingDict]:
        """
        创建安全设置
        
        Returns:
            List[SafetySettingDict]: 安全设置列表
        """
        try:
            safety_settings = []
            
            # 从配置中获取安全设置
            safety_config = self.config.extra_params.get("safety_settings", {})
            
            # 默认安全设置
            default_settings = {
                "HARM_CATEGORY_HARASSMENT": "BLOCK_MEDIUM_AND_ABOVE",
                "HARM_CATEGORY_HATE_SPEECH": "BLOCK_MEDIUM_AND_ABOVE",
                "HARM_CATEGORY_SEXUALLY_EXPLICIT": "BLOCK_MEDIUM_AND_ABOVE",
                "HARM_CATEGORY_DANGEROUS_CONTENT": "BLOCK_MEDIUM_AND_ABOVE",
            }
            
            # 合并配置
            settings = {**default_settings, **safety_config}
            
            for category, threshold in settings.items():
                safety_settings.append(
                    SafetySettingDict(
                        category=getattr(genai.types.HarmCategory, category),
                        threshold=getattr(genai.types.HarmBlockThreshold, threshold)
                    )
                )
            
            return safety_settings
            
        except Exception as e:
            self.logger.error(f"创建安全设置失败: {e}")
            return []
    
    async def generate_content(
        self,
        messages: List[Content],
        stream: bool = False,
        **kwargs
    ) -> Union[GenerateContentResponse, AsyncIterator[GenerateContentResponse]]:
        """
        生成内容
        
        Args:
            messages: 消息列表
            stream: 是否流式输出
            **kwargs: 其他参数
        
        Returns:
            Union[GenerateContentResponse, AsyncIterator[GenerateContentResponse]]: 生成的内容
        """
        try:
            # 记录请求开始
            start_time = datetime.now()
            self.logger.info(f"开始生成内容，用户: {self.user_id}, 流式: {stream}")
            
            # 检查速率限制
            if not await self._check_rate_limit():
                raise Exception("超过速率限制")
            
            # 转换消息格式
            prompt = self._convert_messages_to_prompt(messages)
            
            if stream:
                return self._generate_content_stream(prompt, **kwargs)
            else:
                return await self._generate_content_sync(prompt, **kwargs)
                
        except Exception as e:
            self.logger.error(f"生成内容失败: {e}")
            # 记录失败统计
            await self._record_usage(0, 0.0, False)
            raise
    
    async def _generate_content_sync(
        self,
        prompt: str,
        **kwargs
    ) -> GenerateContentResponse:
        """
        同步生成内容
        
        Args:
            prompt: 提示词
            **kwargs: 其他参数
        
        Returns:
            GenerateContentResponse: 生成的内容
        """
        try:
            # 使用ADK LLM生成内容
            if self._adk_llm:
                response = await self._adk_llm.generate_content(
                    [Content(parts=[Part(text=prompt)])]
                )
            else:
                # 直接使用Google AI客户端
                response = await asyncio.to_thread(
                    self._client.generate_content,
                    prompt
                )
                
                # 转换为ADK格式
                response = self._convert_to_adk_response(response)
            
            # 记录使用统计
            tokens_used = self._count_tokens(response)
            cost = self._calculate_cost(tokens_used)
            await self._record_usage(tokens_used, cost, True)
            
            self.logger.info(f"内容生成完成，用户: {self.user_id}, tokens: {tokens_used}")
            return response
            
        except Exception as e:
            self.logger.error(f"同步生成内容失败: {e}")
            raise
    
    async def _generate_content_stream(
        self,
        prompt: str,
        **kwargs
    ) -> AsyncIterator[GenerateContentResponse]:
        """
        流式生成内容
        
        Args:
            prompt: 提示词
            **kwargs: 其他参数
        
        Yields:
            GenerateContentResponse: 生成的内容片段
        """
        try:
            total_tokens = 0
            
            # 使用Google AI客户端进行流式生成
            async for chunk in self._stream_generate_content(prompt):
                # 转换为ADK格式
                adk_chunk = self._convert_to_adk_response(chunk)
                
                # 统计tokens
                chunk_tokens = self._count_tokens(adk_chunk)
                total_tokens += chunk_tokens
                
                yield adk_chunk
            
            # 记录使用统计
            cost = self._calculate_cost(total_tokens)
            await self._record_usage(total_tokens, cost, True)
            
            self.logger.info(f"流式内容生成完成，用户: {self.user_id}, tokens: {total_tokens}")
            
        except Exception as e:
            self.logger.error(f"流式生成内容失败: {e}")
            raise
    
    async def _stream_generate_content(self, prompt: str) -> AsyncIterator[Any]:
        """
        底层流式生成内容
        
        Args:
            prompt: 提示词
        
        Yields:
            Any: 生成的内容片段
        """
        try:
            # 在线程池中执行流式生成
            def _sync_stream():
                return self._client.generate_content(
                    prompt,
                    stream=True
                )
            
            stream = await asyncio.to_thread(_sync_stream)
            
            for chunk in stream:
                yield chunk
                
        except Exception as e:
            self.logger.error(f"底层流式生成失败: {e}")
            raise
    
    def _convert_messages_to_prompt(self, messages: List[Content]) -> str:
        """
        转换消息为提示词
        
        Args:
            messages: 消息列表
        
        Returns:
            str: 提示词
        """
        try:
            prompt_parts = []
            
            for message in messages:
                for part in message.parts:
                    if hasattr(part, 'text') and part.text:
                        prompt_parts.append(part.text)
                    elif hasattr(part, 'inline_data') and part.inline_data:
                        # 处理内联数据（图片等）
                        prompt_parts.append(f"[{part.inline_data.mime_type} data]")
            
            return "\n".join(prompt_parts)
            
        except Exception as e:
            self.logger.error(f"转换消息为提示词失败: {e}")
            return ""
    
    def _convert_to_adk_response(self, response: Any) -> GenerateContentResponse:
        """
        转换为ADK响应格式
        
        Args:
            response: 原始响应
        
        Returns:
            GenerateContentResponse: ADK格式响应
        """
        try:
            # 如果已经是ADK格式，直接返回
            if isinstance(response, GenerateContentResponse):
                return response
            
            # 转换Google AI响应为ADK格式
            text_content = ""
            if hasattr(response, 'text'):
                text_content = response.text
            elif hasattr(response, 'candidates') and response.candidates:
                candidate = response.candidates[0]
                if hasattr(candidate, 'content') and candidate.content:
                    for part in candidate.content.parts:
                        if hasattr(part, 'text'):
                            text_content += part.text
            
            # 创建ADK响应
            content = Content(parts=[Part(text=text_content)])
            
            # 这里需要根据实际的ADK GenerateContentResponse结构来构建
            # 由于无法直接访问ADK的内部结构，这里提供一个简化的实现
            adk_response = type('GenerateContentResponse', (), {
                'candidates': [type('Candidate', (), {
                    'content': content,
                    'finish_reason': getattr(response, 'finish_reason', None),
                    'safety_ratings': getattr(response, 'safety_ratings', [])
                })()],
                'usage_metadata': getattr(response, 'usage_metadata', None)
            })()
            
            return adk_response
            
        except Exception as e:
            self.logger.error(f"转换ADK响应格式失败: {e}")
            # 返回一个空的响应
            return type('GenerateContentResponse', (), {
                'candidates': [],
                'usage_metadata': None
            })()
    
    def _count_tokens(self, response: GenerateContentResponse) -> int:
        """
        统计响应中的token数量
        
        Args:
            response: 响应
        
        Returns:
            int: token数量
        """
        try:
            # 尝试从usage_metadata获取token数量
            if hasattr(response, 'usage_metadata') and response.usage_metadata:
                if hasattr(response.usage_metadata, 'total_token_count'):
                    return response.usage_metadata.total_token_count
                elif hasattr(response.usage_metadata, 'output_token_count'):
                    return response.usage_metadata.output_token_count
            
            # 如果没有usage_metadata，估算token数量
            text_content = ""
            if hasattr(response, 'candidates') and response.candidates:
                for candidate in response.candidates:
                    if hasattr(candidate, 'content') and candidate.content:
                        for part in candidate.content.parts:
                            if hasattr(part, 'text'):
                                text_content += part.text
            
            # 简单估算：1个token约等于4个字符
            return len(text_content) // 4
            
        except Exception as e:
            self.logger.error(f"统计token数量失败: {e}")
            return 0
    
    def _calculate_cost(self, tokens: int) -> float:
        """
        计算成本
        
        Args:
            tokens: token数量
        
        Returns:
            float: 成本
        """
        try:
            # Google Gemini的定价（示例，实际价格可能不同）
            # 这里需要根据实际的定价来计算
            cost_per_1k_tokens = self.config.extra_params.get("cost_per_1k_tokens", 0.001)
            return (tokens / 1000) * cost_per_1k_tokens
            
        except Exception as e:
            self.logger.error(f"计算成本失败: {e}")
            return 0.0
    
    async def get_model_info(self) -> Dict[str, Any]:
        """
        获取模型信息
        
        Returns:
            Dict[str, Any]: 模型信息
        """
        try:
            return {
                "provider": LLMProvider.GOOGLE,
                "model_name": self.config.model_name,
                "max_tokens": self.config.max_tokens,
                "temperature": self.config.temperature,
                "supports_streaming": True,
                "supports_function_calling": True,
                "supports_vision": "vision" in self.config.model_name.lower(),
                "context_window": self.config.extra_params.get("context_window", 32768)
            }
            
        except Exception as e:
            self.logger.error(f"获取模型信息失败: {e}")
            return {}
    
    async def validate_config(self) -> bool:
        """
        验证配置
        
        Returns:
            bool: 配置是否有效
        """
        try:
            # 检查必需的配置
            if not self.config.api_key:
                self.logger.error("缺少API密钥")
                return False
            
            if not self.config.model_name:
                self.logger.error("缺少模型名称")
                return False
            
            # 尝试进行简单的API调用来验证配置
            test_response = await self._generate_content_sync("Hello")
            
            if test_response and hasattr(test_response, 'candidates'):
                self.logger.info("配置验证成功")
                return True
            else:
                self.logger.error("配置验证失败：无效响应")
                return False
                
        except Exception as e:
            self.logger.error(f"配置验证失败: {e}")
            return False
    
    async def cleanup(self) -> None:
        """
        清理资源
        """
        try:
            self._client = None
            self._adk_llm = None
            self.logger.info(f"Google LLM资源清理完成，用户: {self.user_id}")
            
        except Exception as e:
            self.logger.error(f"清理资源失败: {e}")
    
    @classmethod
    async def create(
        cls,
        config: LLMConfig,
        user_id: int,
        **kwargs
    ) -> "GoogleLLM":
        """
        创建Google LLM实例
        
        Args:
            config: LLM配置
            user_id: 用户ID
            **kwargs: 其他参数
        
        Returns:
            GoogleLLM: Google LLM实例
        """
        try:
            instance = cls(config, user_id)
            
            # 验证配置
            if not await instance.validate_config():
                raise Exception("Google LLM配置验证失败")
            
            return instance
            
        except Exception as e:
            logging.getLogger(__name__).error(f"创建Google LLM实例失败: {e}")
            raise