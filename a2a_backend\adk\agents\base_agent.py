# -*- coding: utf-8 -*-
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""Simplified base agent implementation."""

from __future__ import annotations

from abc import ABC, abstractmethod
from typing import AsyncGenerator, List, Optional, Dict, Any

from pydantic import BaseModel, ConfigDict, Field
from typing_extensions import override

from ..events.event import Event
from .invocation_context import InvocationContext


class BaseAgent(BaseModel, ABC):
    """Base class for all agents in the ADK system.
    
    This is a simplified version that contains only the essential
    functionality needed for the A2A backend system.
    """
    
    model_config = ConfigDict(
        extra='allow',
        arbitrary_types_allowed=True,
    )
    
    name: str = Field(default="")
    """The name of the agent."""
    
    description: str = Field(default="")
    """A description of what the agent does."""
    
    sub_agents: List['BaseAgent'] = Field(default_factory=list)
    """List of sub-agents that this agent can delegate to."""
    
    config: Dict[str, Any] = Field(default_factory=dict)
    """Configuration parameters for the agent."""
    
    enabled: bool = Field(default=True)
    """Whether the agent is enabled."""
    
    def __init__(self, **data):
        """Initialize the base agent."""
        super().__init__(**data)
        if not self.name:
            self.name = self.__class__.__name__
    
    @abstractmethod
    async def _run_async_impl(
        self, ctx: InvocationContext
    ) -> AsyncGenerator[Event, None]:
        """Implementation of the agent's async execution logic.
        
        Args:
            ctx: The invocation context
            
        Yields:
            Events generated during execution
        """
        yield  # This is required for AsyncGenerator
    
    @abstractmethod
    async def _run_live_impl(
        self, ctx: InvocationContext
    ) -> AsyncGenerator[Event, None]:
        """Implementation of the agent's live execution logic.
        
        Args:
            ctx: The invocation context
            
        Yields:
            Events generated during execution
        """
        yield  # This is required for AsyncGenerator
    
    async def run_async(
        self, ctx: InvocationContext
    ) -> AsyncGenerator[Event, None]:
        """Run the agent asynchronously.
        
        Args:
            ctx: The invocation context
            
        Yields:
            Events generated during execution
        """
        if not self.enabled:
            return
        
        async for event in self._run_async_impl(ctx):
            yield event
    
    async def run_live(
        self, ctx: InvocationContext
    ) -> AsyncGenerator[Event, None]:
        """Run the agent in live mode.
        
        Args:
            ctx: The invocation context
            
        Yields:
            Events generated during execution
        """
        if not self.enabled:
            return
        
        async for event in self._run_live_impl(ctx):
            yield event
    
    def add_sub_agent(self, agent: 'BaseAgent') -> None:
        """Add a sub-agent to this agent.
        
        Args:
            agent: The sub-agent to add
        """
        self.sub_agents.append(agent)
    
    def remove_sub_agent(self, agent: 'BaseAgent') -> bool:
        """Remove a sub-agent from this agent.
        
        Args:
            agent: The sub-agent to remove
            
        Returns:
            True if the agent was removed, False if it wasn't found
        """
        try:
            self.sub_agents.remove(agent)
            return True
        except ValueError:
            return False
    
    def get_sub_agent(self, name: str) -> Optional['BaseAgent']:
        """Get a sub-agent by name.
        
        Args:
            name: The name of the sub-agent
            
        Returns:
            The sub-agent if found, None otherwise
        """
        for agent in self.sub_agents:
            if agent.name == name:
                return agent
        return None
    
    def set_config(self, key: str, value: Any) -> None:
        """Set a configuration value.
        
        Args:
            key: The configuration key
            value: The configuration value
        """
        self.config[key] = value
    
    def get_config(self, key: str, default: Any = None) -> Any:
        """Get a configuration value.
        
        Args:
            key: The configuration key
            default: Default value if key not found
            
        Returns:
            The configuration value or default
        """
        return self.config.get(key, default)
    
    def __str__(self) -> str:
        """String representation of the agent."""
        return f"{self.__class__.__name__}(name='{self.name}')"
    
    def __repr__(self) -> str:
        """Detailed string representation of the agent."""
        return (
            f"{self.__class__.__name__}("
            f"name='{self.name}', "
            f"description='{self.description}', "
            f"enabled={self.enabled}, "
            f"sub_agents={len(self.sub_agents)}"
            f")"
        )