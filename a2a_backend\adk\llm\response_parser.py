#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A2A多智能体系统 响应解析和格式化

提供LLM响应的解析、格式化和后处理功能
"""

import re
import json
import asyncio
import logging
from typing import Dict, List, Any, Optional, Union, Callable, Type
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime
import xml.etree.ElementTree as ET
from abc import ABC, abstractmethod
import markdown
from bs4 import BeautifulSoup
import yaml


class ResponseFormat(Enum):
    """响应格式枚举"""
    TEXT = "text"
    JSON = "json"
    XML = "xml"
    MARKDOWN = "markdown"
    HTML = "html"
    YAML = "yaml"
    CODE = "code"
    STRUCTURED = "structured"


class ParseStatus(Enum):
    """解析状态枚举"""
    SUCCESS = "success"
    PARTIAL = "partial"
    FAILED = "failed"
    INVALID_FORMAT = "invalid_format"


@dataclass
class ParsedContent:
    """解析后的内容"""
    content: Any
    format_type: ResponseFormat
    confidence: float = 1.0
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ParseResult:
    """解析结果"""
    status: ParseStatus
    parsed_content: Optional[ParsedContent] = None
    raw_content: str = ""
    error_message: Optional[str] = None
    parse_time: float = 0.0
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        result = {
            "status": self.status.value,
            "raw_content": self.raw_content,
            "error_message": self.error_message,
            "parse_time": self.parse_time,
            "metadata": self.metadata
        }
        
        if self.parsed_content:
            result["parsed_content"] = {
                "content": self.parsed_content.content,
                "format_type": self.parsed_content.format_type.value,
                "confidence": self.parsed_content.confidence,
                "metadata": self.parsed_content.metadata
            }
        
        return result


class BaseParser(ABC):
    """解析器基类"""
    
    def __init__(self, name: str):
        self.name = name
        self.logger = logging.getLogger(f"{__name__}.{name}")
    
    @abstractmethod
    async def can_parse(self, content: str) -> bool:
        """检查是否可以解析内容"""
        pass
    
    @abstractmethod
    async def parse(self, content: str) -> ParseResult:
        """解析内容"""
        pass
    
    def get_confidence(self, content: str) -> float:
        """获取解析置信度"""
        return 1.0


class JSONParser(BaseParser):
    """JSON解析器"""
    
    def __init__(self):
        super().__init__("JSONParser")
        self.json_pattern = re.compile(r'```json\s*([\s\S]*?)\s*```|```\s*([\s\S]*?)\s*```|({[\s\S]*}|\[[\s\S]*\])')
    
    async def can_parse(self, content: str) -> bool:
        """检查是否包含JSON内容"""
        try:
            # 检查是否有JSON代码块
            if self.json_pattern.search(content):
                return True
            
            # 尝试直接解析
            content_stripped = content.strip()
            if content_stripped.startswith(('{', '[')) and content_stripped.endswith(('}', ']')):
                json.loads(content_stripped)
                return True
            
            return False
            
        except Exception:
            return False
    
    async def parse(self, content: str) -> ParseResult:
        """解析JSON内容"""
        start_time = datetime.now()
        
        try:
            # 提取JSON内容
            json_content = self._extract_json(content)
            
            if not json_content:
                return ParseResult(
                    status=ParseStatus.FAILED,
                    raw_content=content,
                    error_message="未找到有效的JSON内容",
                    parse_time=(datetime.now() - start_time).total_seconds()
                )
            
            # 解析JSON
            parsed_data = json.loads(json_content)
            
            # 计算置信度
            confidence = self.get_confidence(json_content)
            
            return ParseResult(
                status=ParseStatus.SUCCESS,
                parsed_content=ParsedContent(
                    content=parsed_data,
                    format_type=ResponseFormat.JSON,
                    confidence=confidence
                ),
                raw_content=content,
                parse_time=(datetime.now() - start_time).total_seconds()
            )
            
        except json.JSONDecodeError as e:
            return ParseResult(
                status=ParseStatus.INVALID_FORMAT,
                raw_content=content,
                error_message=f"JSON格式错误: {e}",
                parse_time=(datetime.now() - start_time).total_seconds()
            )
        except Exception as e:
            return ParseResult(
                status=ParseStatus.FAILED,
                raw_content=content,
                error_message=f"解析失败: {e}",
                parse_time=(datetime.now() - start_time).total_seconds()
            )
    
    def _extract_json(self, content: str) -> Optional[str]:
        """提取JSON内容"""
        # 查找JSON代码块
        match = self.json_pattern.search(content)
        if match:
            # 返回第一个非空匹配组
            for group in match.groups():
                if group and group.strip():
                    return group.strip()
        
        # 尝试直接提取
        content_stripped = content.strip()
        if content_stripped.startswith(('{', '[')) and content_stripped.endswith(('}', ']')):
            return content_stripped
        
        return None
    
    def get_confidence(self, content: str) -> float:
        """计算JSON解析置信度"""
        try:
            # 基础置信度
            confidence = 0.8
            
            # 如果在代码块中，置信度更高
            if '```json' in content or '```' in content:
                confidence += 0.15
            
            # 检查JSON结构完整性
            parsed = json.loads(content)
            if isinstance(parsed, dict) and len(parsed) > 0:
                confidence += 0.05
            
            return min(1.0, confidence)
            
        except Exception:
            return 0.5


class XMLParser(BaseParser):
    """XML解析器"""
    
    def __init__(self):
        super().__init__("XMLParser")
        self.xml_pattern = re.compile(r'```xml\s*([\s\S]*?)\s*```|<[^>]+>[\s\S]*</[^>]+>')
    
    async def can_parse(self, content: str) -> bool:
        """检查是否包含XML内容"""
        try:
            if self.xml_pattern.search(content):
                xml_content = self._extract_xml(content)
                if xml_content:
                    ET.fromstring(xml_content)
                    return True
            return False
            
        except Exception:
            return False
    
    async def parse(self, content: str) -> ParseResult:
        """解析XML内容"""
        start_time = datetime.now()
        
        try:
            xml_content = self._extract_xml(content)
            
            if not xml_content:
                return ParseResult(
                    status=ParseStatus.FAILED,
                    raw_content=content,
                    error_message="未找到有效的XML内容",
                    parse_time=(datetime.now() - start_time).total_seconds()
                )
            
            # 解析XML
            root = ET.fromstring(xml_content)
            parsed_data = self._xml_to_dict(root)
            
            return ParseResult(
                status=ParseStatus.SUCCESS,
                parsed_content=ParsedContent(
                    content=parsed_data,
                    format_type=ResponseFormat.XML,
                    confidence=self.get_confidence(xml_content)
                ),
                raw_content=content,
                parse_time=(datetime.now() - start_time).total_seconds()
            )
            
        except ET.ParseError as e:
            return ParseResult(
                status=ParseStatus.INVALID_FORMAT,
                raw_content=content,
                error_message=f"XML格式错误: {e}",
                parse_time=(datetime.now() - start_time).total_seconds()
            )
        except Exception as e:
            return ParseResult(
                status=ParseStatus.FAILED,
                raw_content=content,
                error_message=f"解析失败: {e}",
                parse_time=(datetime.now() - start_time).total_seconds()
            )
    
    def _extract_xml(self, content: str) -> Optional[str]:
        """提取XML内容"""
        # 查找XML代码块
        if '```xml' in content:
            match = re.search(r'```xml\s*([\s\S]*?)\s*```', content)
            if match:
                return match.group(1).strip()
        
        # 查找XML标签
        match = re.search(r'<[^>]+>[\s\S]*</[^>]+>', content)
        if match:
            return match.group(0)
        
        return None
    
    def _xml_to_dict(self, element: ET.Element) -> Dict[str, Any]:
        """将XML元素转换为字典"""
        result = {}
        
        # 添加属性
        if element.attrib:
            result['@attributes'] = element.attrib
        
        # 添加文本内容
        if element.text and element.text.strip():
            if len(element) == 0:  # 叶子节点
                return element.text.strip()
            else:
                result['#text'] = element.text.strip()
        
        # 添加子元素
        for child in element:
            child_data = self._xml_to_dict(child)
            
            if child.tag in result:
                # 如果已存在同名标签，转换为列表
                if not isinstance(result[child.tag], list):
                    result[child.tag] = [result[child.tag]]
                result[child.tag].append(child_data)
            else:
                result[child.tag] = child_data
        
        return result if result else element.text


class MarkdownParser(BaseParser):
    """Markdown解析器"""
    
    def __init__(self):
        super().__init__("MarkdownParser")
        self.md_patterns = [
            r'#{1,6}\s+.+',  # 标题
            r'\*\*.*?\*\*',  # 粗体
            r'\*.*?\*',      # 斜体
            r'```[\s\S]*?```',  # 代码块
            r'`.*?`',        # 行内代码
            r'\[.*?\]\(.*?\)',  # 链接
        ]
    
    async def can_parse(self, content: str) -> bool:
        """检查是否包含Markdown内容"""
        try:
            # 检查Markdown语法
            for pattern in self.md_patterns:
                if re.search(pattern, content):
                    return True
            return False
            
        except Exception:
            return False
    
    async def parse(self, content: str) -> ParseResult:
        """解析Markdown内容"""
        start_time = datetime.now()
        
        try:
            # 解析Markdown
            html_content = markdown.markdown(
                content,
                extensions=['tables', 'fenced_code', 'toc']
            )
            
            # 提取结构化信息
            structured_data = self._extract_structure(content)
            
            return ParseResult(
                status=ParseStatus.SUCCESS,
                parsed_content=ParsedContent(
                    content={
                        "html": html_content,
                        "structure": structured_data,
                        "raw": content
                    },
                    format_type=ResponseFormat.MARKDOWN,
                    confidence=self.get_confidence(content)
                ),
                raw_content=content,
                parse_time=(datetime.now() - start_time).total_seconds()
            )
            
        except Exception as e:
            return ParseResult(
                status=ParseStatus.FAILED,
                raw_content=content,
                error_message=f"解析失败: {e}",
                parse_time=(datetime.now() - start_time).total_seconds()
            )
    
    def _extract_structure(self, content: str) -> Dict[str, Any]:
        """提取Markdown结构"""
        structure = {
            "headings": [],
            "code_blocks": [],
            "links": [],
            "images": []
        }
        
        # 提取标题
        headings = re.findall(r'^(#{1,6})\s+(.+)$', content, re.MULTILINE)
        for level, title in headings:
            structure["headings"].append({
                "level": len(level),
                "title": title.strip()
            })
        
        # 提取代码块
        code_blocks = re.findall(r'```(\w*)\n([\s\S]*?)```', content)
        for language, code in code_blocks:
            structure["code_blocks"].append({
                "language": language or "text",
                "code": code.strip()
            })
        
        # 提取链接
        links = re.findall(r'\[([^\]]+)\]\(([^\)]+)\)', content)
        for text, url in links:
            structure["links"].append({
                "text": text,
                "url": url
            })
        
        # 提取图片
        images = re.findall(r'!\[([^\]]*)\]\(([^\)]+)\)', content)
        for alt, src in images:
            structure["images"].append({
                "alt": alt,
                "src": src
            })
        
        return structure


class CodeParser(BaseParser):
    """代码解析器"""
    
    def __init__(self):
        super().__init__("CodeParser")
        self.code_pattern = re.compile(r'```(\w*)\s*([\s\S]*?)\s*```')
    
    async def can_parse(self, content: str) -> bool:
        """检查是否包含代码块"""
        return bool(self.code_pattern.search(content))
    
    async def parse(self, content: str) -> ParseResult:
        """解析代码内容"""
        start_time = datetime.now()
        
        try:
            code_blocks = []
            
            for match in self.code_pattern.finditer(content):
                language = match.group(1) or "text"
                code = match.group(2).strip()
                
                code_blocks.append({
                    "language": language,
                    "code": code,
                    "lines": len(code.split('\n')),
                    "characters": len(code)
                })
            
            if not code_blocks:
                return ParseResult(
                    status=ParseStatus.FAILED,
                    raw_content=content,
                    error_message="未找到代码块",
                    parse_time=(datetime.now() - start_time).total_seconds()
                )
            
            return ParseResult(
                status=ParseStatus.SUCCESS,
                parsed_content=ParsedContent(
                    content=code_blocks,
                    format_type=ResponseFormat.CODE,
                    confidence=1.0
                ),
                raw_content=content,
                parse_time=(datetime.now() - start_time).total_seconds()
            )
            
        except Exception as e:
            return ParseResult(
                status=ParseStatus.FAILED,
                raw_content=content,
                error_message=f"解析失败: {e}",
                parse_time=(datetime.now() - start_time).total_seconds()
            )


class YAMLParser(BaseParser):
    """YAML解析器"""
    
    def __init__(self):
        super().__init__("YAMLParser")
        self.yaml_pattern = re.compile(r'```ya?ml\s*([\s\S]*?)\s*```')
    
    async def can_parse(self, content: str) -> bool:
        """检查是否包含YAML内容"""
        try:
            if self.yaml_pattern.search(content):
                return True
            
            # 简单的YAML格式检查
            lines = content.strip().split('\n')
            yaml_indicators = 0
            for line in lines[:10]:  # 只检查前10行
                if ':' in line and not line.strip().startswith('#'):
                    yaml_indicators += 1
            
            return yaml_indicators >= 2
            
        except Exception:
            return False
    
    async def parse(self, content: str) -> ParseResult:
        """解析YAML内容"""
        start_time = datetime.now()
        
        try:
            yaml_content = self._extract_yaml(content)
            
            if not yaml_content:
                return ParseResult(
                    status=ParseStatus.FAILED,
                    raw_content=content,
                    error_message="未找到有效的YAML内容",
                    parse_time=(datetime.now() - start_time).total_seconds()
                )
            
            # 解析YAML
            parsed_data = yaml.safe_load(yaml_content)
            
            return ParseResult(
                status=ParseStatus.SUCCESS,
                parsed_content=ParsedContent(
                    content=parsed_data,
                    format_type=ResponseFormat.YAML,
                    confidence=self.get_confidence(yaml_content)
                ),
                raw_content=content,
                parse_time=(datetime.now() - start_time).total_seconds()
            )
            
        except yaml.YAMLError as e:
            return ParseResult(
                status=ParseStatus.INVALID_FORMAT,
                raw_content=content,
                error_message=f"YAML格式错误: {e}",
                parse_time=(datetime.now() - start_time).total_seconds()
            )
        except Exception as e:
            return ParseResult(
                status=ParseStatus.FAILED,
                raw_content=content,
                error_message=f"解析失败: {e}",
                parse_time=(datetime.now() - start_time).total_seconds()
            )
    
    def _extract_yaml(self, content: str) -> Optional[str]:
        """提取YAML内容"""
        # 查找YAML代码块
        match = self.yaml_pattern.search(content)
        if match:
            return match.group(1).strip()
        
        # 尝试直接解析
        return content.strip()


class ResponseParser:
    """响应解析器主类"""
    
    def __init__(self):
        """
        初始化响应解析器
        """
        self.logger = logging.getLogger(__name__)
        
        # 注册解析器
        self.parsers: List[BaseParser] = [
            JSONParser(),
            XMLParser(),
            CodeParser(),
            YAMLParser(),
            MarkdownParser(),
        ]
        
        # 解析历史
        self.parse_history: List[ParseResult] = []
        
        # 最大历史记录数
        self._max_history = 1000
        
        # 自定义解析器
        self.custom_parsers: Dict[str, BaseParser] = {}
        
        # 格式化器
        self.formatters: Dict[ResponseFormat, Callable] = {
            ResponseFormat.JSON: self._format_json,
            ResponseFormat.XML: self._format_xml,
            ResponseFormat.MARKDOWN: self._format_markdown,
            ResponseFormat.HTML: self._format_html,
            ResponseFormat.TEXT: self._format_text,
            ResponseFormat.YAML: self._format_yaml,
        }
        
        self.logger.info("响应解析器初始化完成")
    
    async def parse_response(
        self,
        content: str,
        expected_format: Optional[ResponseFormat] = None,
        fallback_to_text: bool = True
    ) -> ParseResult:
        """
        解析响应内容
        
        Args:
            content: 响应内容
            expected_format: 期望的格式
            fallback_to_text: 是否回退到文本格式
        
        Returns:
            ParseResult: 解析结果
        """
        start_time = datetime.now()
        
        try:
            # 如果指定了期望格式，优先使用对应解析器
            if expected_format:
                parser = self._get_parser_by_format(expected_format)
                if parser and await parser.can_parse(content):
                    result = await parser.parse(content)
                    self._add_to_history(result)
                    return result
            
            # 尝试所有解析器
            best_result = None
            best_confidence = 0.0
            
            for parser in self.parsers:
                try:
                    if await parser.can_parse(content):
                        result = await parser.parse(content)
                        
                        if result.status == ParseStatus.SUCCESS:
                            confidence = result.parsed_content.confidence if result.parsed_content else 0.0
                            
                            if confidence > best_confidence:
                                best_result = result
                                best_confidence = confidence
                            
                            # 如果置信度很高，直接返回
                            if confidence >= 0.9:
                                break
                                
                except Exception as e:
                    self.logger.warning(f"解析器 {parser.name} 执行失败: {e}")
                    continue
            
            # 返回最佳结果
            if best_result:
                self._add_to_history(best_result)
                return best_result
            
            # 如果没有找到合适的解析器，回退到文本格式
            if fallback_to_text:
                result = ParseResult(
                    status=ParseStatus.SUCCESS,
                    parsed_content=ParsedContent(
                        content=content,
                        format_type=ResponseFormat.TEXT,
                        confidence=0.5
                    ),
                    raw_content=content,
                    parse_time=(datetime.now() - start_time).total_seconds()
                )
                
                self._add_to_history(result)
                return result
            
            # 解析失败
            result = ParseResult(
                status=ParseStatus.FAILED,
                raw_content=content,
                error_message="未找到合适的解析器",
                parse_time=(datetime.now() - start_time).total_seconds()
            )
            
            self._add_to_history(result)
            return result
            
        except Exception as e:
            self.logger.error(f"解析响应失败: {e}")
            
            result = ParseResult(
                status=ParseStatus.FAILED,
                raw_content=content,
                error_message=f"解析异常: {e}",
                parse_time=(datetime.now() - start_time).total_seconds()
            )
            
            self._add_to_history(result)
            return result
    
    async def format_response(
        self,
        parse_result: ParseResult,
        target_format: ResponseFormat,
        **format_options
    ) -> str:
        """
        格式化响应内容
        
        Args:
            parse_result: 解析结果
            target_format: 目标格式
            **format_options: 格式化选项
        
        Returns:
            str: 格式化后的内容
        """
        try:
            if parse_result.status != ParseStatus.SUCCESS or not parse_result.parsed_content:
                return parse_result.raw_content
            
            formatter = self.formatters.get(target_format)
            if not formatter:
                self.logger.warning(f"未找到格式化器: {target_format}")
                return parse_result.raw_content
            
            return await formatter(parse_result.parsed_content, **format_options)
            
        except Exception as e:
            self.logger.error(f"格式化响应失败: {e}")
            return parse_result.raw_content
    
    def register_parser(self, parser: BaseParser) -> None:
        """
        注册自定义解析器
        
        Args:
            parser: 解析器实例
        """
        try:
            self.custom_parsers[parser.name] = parser
            self.parsers.append(parser)
            
            self.logger.info(f"注册自定义解析器: {parser.name}")
            
        except Exception as e:
            self.logger.error(f"注册解析器失败: {e}")
    
    def register_formatter(
        self,
        format_type: ResponseFormat,
        formatter: Callable
    ) -> None:
        """
        注册自定义格式化器
        
        Args:
            format_type: 格式类型
            formatter: 格式化函数
        """
        try:
            self.formatters[format_type] = formatter
            
            self.logger.info(f"注册自定义格式化器: {format_type.value}")
            
        except Exception as e:
            self.logger.error(f"注册格式化器失败: {e}")
    
    def get_parse_statistics(self) -> Dict[str, Any]:
        """
        获取解析统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            total_parses = len(self.parse_history)
            successful_parses = sum(
                1 for result in self.parse_history
                if result.status == ParseStatus.SUCCESS
            )
            
            # 按格式统计
            format_stats = {}
            for result in self.parse_history:
                if result.parsed_content:
                    format_type = result.parsed_content.format_type.value
                    if format_type not in format_stats:
                        format_stats[format_type] = 0
                    format_stats[format_type] += 1
            
            # 平均解析时间
            avg_parse_time = 0.0
            if self.parse_history:
                avg_parse_time = sum(
                    result.parse_time for result in self.parse_history
                ) / len(self.parse_history)
            
            return {
                "total_parses": total_parses,
                "successful_parses": successful_parses,
                "success_rate": successful_parses / total_parses if total_parses > 0 else 0,
                "avg_parse_time": avg_parse_time,
                "format_stats": format_stats,
                "registered_parsers": len(self.parsers),
                "custom_parsers": len(self.custom_parsers)
            }
            
        except Exception as e:
            self.logger.error(f"获取统计信息失败: {e}")
            return {}
    
    def clear_history(self) -> None:
        """
        清空解析历史
        """
        self.parse_history.clear()
        self.logger.info("解析历史已清空")
    
    def _get_parser_by_format(self, format_type: ResponseFormat) -> Optional[BaseParser]:
        """
        根据格式类型获取解析器
        
        Args:
            format_type: 格式类型
        
        Returns:
            Optional[BaseParser]: 解析器实例
        """
        format_parser_map = {
            ResponseFormat.JSON: JSONParser,
            ResponseFormat.XML: XMLParser,
            ResponseFormat.MARKDOWN: MarkdownParser,
            ResponseFormat.CODE: CodeParser,
            ResponseFormat.YAML: YAMLParser,
        }
        
        parser_class = format_parser_map.get(format_type)
        if parser_class:
            for parser in self.parsers:
                if isinstance(parser, parser_class):
                    return parser
        
        return None
    
    def _add_to_history(self, result: ParseResult) -> None:
        """
        添加到解析历史
        
        Args:
            result: 解析结果
        """
        self.parse_history.append(result)
        
        # 限制历史记录数量
        if len(self.parse_history) > self._max_history:
            self.parse_history = self.parse_history[-self._max_history:]
    
    async def _format_json(self, content: ParsedContent, **options) -> str:
        """格式化JSON"""
        try:
            indent = options.get('indent', 2)
            ensure_ascii = options.get('ensure_ascii', False)
            
            return json.dumps(
                content.content,
                indent=indent,
                ensure_ascii=ensure_ascii
            )
            
        except Exception as e:
            self.logger.error(f"JSON格式化失败: {e}")
            return str(content.content)
    
    async def _format_xml(self, content: ParsedContent, **options) -> str:
        """格式化XML"""
        try:
            # 简单的XML格式化
            return str(content.content)
            
        except Exception as e:
            self.logger.error(f"XML格式化失败: {e}")
            return str(content.content)
    
    async def _format_markdown(self, content: ParsedContent, **options) -> str:
        """格式化Markdown"""
        try:
            if isinstance(content.content, dict):
                return content.content.get('raw', str(content.content))
            return str(content.content)
            
        except Exception as e:
            self.logger.error(f"Markdown格式化失败: {e}")
            return str(content.content)
    
    async def _format_html(self, content: ParsedContent, **options) -> str:
        """格式化HTML"""
        try:
            if isinstance(content.content, dict) and 'html' in content.content:
                return content.content['html']
            return str(content.content)
            
        except Exception as e:
            self.logger.error(f"HTML格式化失败: {e}")
            return str(content.content)
    
    async def _format_text(self, content: ParsedContent, **options) -> str:
        """格式化文本"""
        try:
            return str(content.content)
            
        except Exception as e:
            self.logger.error(f"文本格式化失败: {e}")
            return str(content.content)
    
    async def _format_yaml(self, content: ParsedContent, **options) -> str:
        """格式化YAML"""
        try:
            return yaml.dump(
                content.content,
                allow_unicode=True,
                default_flow_style=False
            )
            
        except Exception as e:
            self.logger.error(f"YAML格式化失败: {e}")
            return str(content.content)