#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A2A多智能体系统 自定义LLM智能体

支持多种LLM提供商，包含用户权限验证
"""

import logging
from typing import Dict, List, Optional, Any, Union, AsyncGenerator
from datetime import datetime
import json

from google.adk.agents.llm_agent import Agent
from google.adk.models.base_llm import BaseLlm
from google.ai.generativelanguage import Content, Part
from google.genai import types

from app.models.user import User
from app.models.agent import Agent as AgentModel
from app.models.config import LLMConfig
from app.core.logging import get_logger
from app.auth.permissions import check_user_permission

class CustomLLMAgent(Agent):
    """
    自定义LLM智能体，支持多种LLM提供商，包含用户权限验证
    
    支持的LLM提供商：
    1. Google Gemini
    2. OpenAI GPT
    3. Anthropic Claude
    4. Azure OpenAI
    5. 本地模型
    
    提供以下功能：
    1. 多LLM提供商支持
    2. 用户权限验证
    3. 配置管理
    4. 错误处理和重试
    5. 使用统计和监控
    """
    
    def __init__(
        self,
        user_id: str,
        owner_id: str,
        agent_model: AgentModel,
        llm_config: LLMConfig,
        logger: Optional[logging.Logger] = None,
        **kwargs
    ):
        """
        初始化自定义LLM智能体
        
        Args:
            user_id: 用户ID
            owner_id: 拥有者ID
            agent_model: 智能体模型
            llm_config: LLM配置
            options: ADK Agent选项
            logger: 日志记录器
        """
        self.user_id = user_id
        self.owner_id = owner_id
        self.agent_model = agent_model
        self.llm_config = llm_config
        self.logger = logger or get_logger(f"custom_llm_agent_{agent_model.id}")
        
        # 创建LLM实例
        self.llm = self._create_llm()
        
        # 初始化基类
        super().__init__(
            name=agent_model.name,
            description=agent_model.description,
            model=self.llm
        )
        
        # 使用统计
        self.usage_stats = {
            "total_requests": 0,
            "total_tokens": 0,
            "prompt_tokens": 0,
            "completion_tokens": 0,
            "total_cost": 0.0,
            "error_count": 0,
            "last_used": None
        }
        
        self.logger.info(f"CustomLLMAgent已初始化，智能体ID: {agent_model.id}, LLM提供商: {llm_config.provider}")
    
    def _create_llm(self) -> BaseLlm:
        """
        根据配置创建LLM实例
        
        Returns:
            BaseLlm: LLM实例
        """
        try:
            # 根据提供商创建不同的LLM实例
            if self.llm_config.provider == "google":
                return self._create_google_llm()
            elif self.llm_config.provider == "openai":
                return self._create_openai_llm()
            elif self.llm_config.provider == "anthropic":
                return self._create_anthropic_llm()
            elif self.llm_config.provider == "azure":
                return self._create_azure_llm()
            elif self.llm_config.provider == "local":
                return self._create_local_llm()
            else:
                raise ValueError(f"不支持的LLM提供商: {self.llm_config.provider}")
        except Exception as e:
            self.logger.error(f"创建LLM实例错误: {str(e)}")
            raise e
    
    def _create_google_llm(self) -> BaseLlm:
        """
        创建Google Gemini LLM实例
        
        Returns:
            LLM: Google LLM实例
        """
        # 这里应该使用实际的Google LLM类
        # 暂时使用基础LLM类作为占位符
        return BaseLlm(model=self.llm_config.model_name)
    
    def _create_openai_llm(self) -> BaseLlm:
        """
        创建OpenAI GPT LLM实例
        
        Returns:
            LLM: OpenAI LLM实例
        """
        # 这里需要实现OpenAI LLM适配器
        # 暂时抛出异常，在实际使用时需要实现
        raise NotImplementedError("OpenAI LLM适配器尚未实现")
    
    def _create_anthropic_llm(self) -> BaseLlm:
        """
        创建Anthropic Claude LLM实例
        
        Returns:
            LLM: Anthropic LLM实例
        """
        # 这里需要实现Anthropic LLM适配器
        # 暂时抛出异常，在实际使用时需要实现
        raise NotImplementedError("Anthropic LLM适配器尚未实现")
    
    def _create_azure_llm(self) -> BaseLlm:
        """
        创建Azure OpenAI LLM实例
        
        Returns:
            LLM: Azure LLM实例
        """
        # 这里需要实现Azure OpenAI LLM适配器
        # 暂时抛出异常，在实际使用时需要实现
        raise NotImplementedError("Azure OpenAI LLM适配器尚未实现")
    
    def _create_local_llm(self) -> BaseLlm:
        """
        创建本地LLM实例
        
        Returns:
            LLM: 本地LLM实例
        """
        # 这里需要实现本地LLM适配器
        # 暂时抛出异常，在实际使用时需要实现
        raise NotImplementedError("本地LLM适配器尚未实现")
    
    async def _check_permission(self) -> bool:
        """
        检查用户权限
        
        Returns:
            bool: 是否有权限
        """
        try:
            # 检查用户是否存在
            user = await User.get_by_id(self.user_id)
            if not user:
                self.logger.error(f"用户不存在: {self.user_id}")
                return False
            
            # 检查用户是否有权限使用此智能体
            has_permission = await check_user_permission(
                user_id=self.user_id,
                owner_id=self.owner_id,
                resource_type="agent",
                action="use",
                resource_id=str(self.agent_model.id)
            )
            
            if not has_permission:
                self.logger.error(f"用户 {self.user_id} 没有权限使用智能体 {self.agent_model.id}")
            
            return has_permission
        except Exception as e:
            self.logger.error(f"权限检查错误: {str(e)}")
            return False
    
    async def _update_usage_stats(self, response: Any) -> None:
        """
        更新使用统计
        
        Args:
            response: LLM响应
        """
        try:
            self.usage_stats["total_requests"] += 1
            self.usage_stats["last_used"] = datetime.now().isoformat()
            
            # 更新令牌统计
            if hasattr(response, "usage") and response.usage:
                prompt_tokens = getattr(response.usage, "prompt_tokens", 0)
                completion_tokens = getattr(response.usage, "completion_tokens", 0)
                total_tokens = getattr(response.usage, "total_tokens", 0)
                
                self.usage_stats["prompt_tokens"] += prompt_tokens
                self.usage_stats["completion_tokens"] += completion_tokens
                self.usage_stats["total_tokens"] += total_tokens
                
                # 计算成本（这里需要根据实际的定价模型实现）
                cost = self._calculate_cost(prompt_tokens, completion_tokens)
                self.usage_stats["total_cost"] += cost
        except Exception as e:
            self.logger.error(f"更新使用统计错误: {str(e)}")
    
    def _calculate_cost(self, prompt_tokens: int, completion_tokens: int) -> float:
        """
        计算使用成本
        
        Args:
            prompt_tokens: 提示令牌数
            completion_tokens: 完成令牌数
            
        Returns:
            float: 成本
        """
        # 这里需要根据不同LLM提供商的定价模型实现
        # 暂时返回0，在实际使用时需要实现
        return 0.0
    
    async def generate(
        self, 
        content: Union[str, Content, types.Content],
        stream: bool = False
    ) -> Union[Any, AsyncGenerator[Any, None]]:
        """
        生成响应
        
        Args:
            content: 输入内容
            stream: 是否使用流式输出
            
        Returns:
            Union[Any, AsyncGenerator[Any, None]]: 生成的响应
        """
        # 检查权限
        has_permission = await self._check_permission()
        if not has_permission:
            raise PermissionError(f"用户 {self.user_id} 没有权限使用智能体 {self.agent_model.id}")
        
        try:
            # 转换输入内容
            if isinstance(content, str):
                adk_content = Content(parts=[Part(text=content)])
            else:
                adk_content = content
            
            # 调用LLM生成响应
            if stream:
                return self._generate_stream(adk_content)
            else:
                return await self._generate_sync(adk_content)
        except Exception as e:
            self.usage_stats["error_count"] += 1
            self.logger.error(f"生成响应错误: {str(e)}")
            raise e
    
    async def _generate_sync(self, content: Content) -> Any:
        """
        同步生成响应
        
        Args:
            content: ADK Content对象
            
        Returns:
            Any: 生成的响应
        """
        # 调用LLM生成响应
        response = await self.llm.generate_async(content)
        
        # 更新使用统计
        await self._update_usage_stats(response)
        
        return response
    
    async def _generate_stream(self, content: Content) -> AsyncGenerator[Any, None]:
        """
        流式生成响应
        
        Args:
            content: ADK Content对象
            
        Yields:
            Any: 流式响应
        """
        # 调用LLM生成流式响应
        async for response in self.llm.generate_stream_async(content):
            # 更新使用统计（只在最后一个响应时更新）
            if hasattr(response, "is_final") and response.is_final:
                await self._update_usage_stats(response)
            
            yield response
    
    async def chat(
        self, 
        messages: List[Dict[str, Any]],
        stream: bool = False
    ) -> Union[Any, AsyncGenerator[Any, None]]:
        """
        聊天对话
        
        Args:
            messages: 消息列表
            stream: 是否使用流式输出
            
        Returns:
            Union[Any, AsyncGenerator[Any, None]]: 聊天响应
        """
        # 检查权限
        has_permission = await self._check_permission()
        if not has_permission:
            raise PermissionError(f"用户 {self.user_id} 没有权限使用智能体 {self.agent_model.id}")
        
        try:
            # 转换消息格式为ADK Content
            content_parts = []
            for message in messages:
                role = message.get("role", "user")
                text = message.get("content", "")
                content_parts.append(Part(text=f"{role}: {text}"))
            
            content = Content(parts=content_parts)
            
            # 生成响应
            return await self.generate(content, stream)
        except Exception as e:
            self.logger.error(f"聊天对话错误: {str(e)}")
            raise e
    
    def get_usage_stats(self) -> Dict[str, Any]:
        """
        获取使用统计
        
        Returns:
            Dict[str, Any]: 使用统计信息
        """
        return self.usage_stats.copy()
    
    def get_config(self) -> Dict[str, Any]:
        """
        获取智能体配置
        
        Returns:
            Dict[str, Any]: 智能体配置
        """
        return {
            "agent_id": self.agent_model.id,
            "agent_name": self.agent_model.name,
            "agent_type": self.agent_model.type,
            "llm_provider": self.llm_config.provider,
            "llm_model": self.llm_config.model_name,
            "temperature": self.llm_config.temperature,
            "max_tokens": self.llm_config.max_tokens,
            "user_id": self.user_id,
            "owner_id": self.owner_id
        }
    
    async def update_config(self, new_config: Dict[str, Any]) -> None:
        """
        更新智能体配置
        
        Args:
            new_config: 新配置
        """
        # 检查权限
        has_permission = await self._check_permission()
        if not has_permission:
            raise PermissionError(f"用户 {self.user_id} 没有权限更新智能体配置")
        
        try:
            # 更新LLM配置
            if "temperature" in new_config:
                self.llm_config.temperature = new_config["temperature"]
            if "max_tokens" in new_config:
                self.llm_config.max_tokens = new_config["max_tokens"]
            if "top_p" in new_config:
                self.llm_config.top_p = new_config["top_p"]
            if "top_k" in new_config:
                self.llm_config.top_k = new_config["top_k"]
            
            # 保存配置到数据库
            await self.llm_config.save()
            
            # 重新创建LLM实例
            self.llm = self._create_llm()
            
            self.logger.info(f"智能体配置已更新: {self.agent_model.id}")
        except Exception as e:
            self.logger.error(f"更新智能体配置错误: {str(e)}")
            raise e
    
    @classmethod
    async def create(
        cls,
        user_id: int,
        owner_id: int,
        agent_model: AgentModel,
        llm_config: LLMConfig,
        **kwargs
    ) -> "CustomLLMAgent":
        """
        创建CustomLLMAgent实例的工厂方法
        
        Args:
            user_id: 用户ID
            owner_id: 拥有者ID
            agent_model: 智能体模型
            llm_config: LLM配置
            **kwargs: 其他参数
            
        Returns:
            CustomLLMAgent: CustomLLMAgent实例
        """
        # 检查用户权限
        user = await User.get_by_id(user_id)
        if not user:
            raise ValueError(f"用户不存在: {user_id}")
        
        has_permission = await check_user_permission(
            user_id=user_id,
            owner_id=owner_id,
            resource_type="agent",
            action="create",
            resource_id=str(agent_model.id)
        )
        if not has_permission:
            raise PermissionError(f"用户 {user_id} 没有权限创建智能体")
        
        # 创建实例
        instance = cls(
            user_id=user_id,
            owner_id=owner_id,
            agent_model=agent_model,
            llm_config=llm_config,
            **kwargs
        )
        
        return instance