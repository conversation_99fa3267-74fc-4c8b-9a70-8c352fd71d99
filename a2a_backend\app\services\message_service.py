# -*- coding: utf-8 -*-
"""
A2A多智能体系统消息服务

提供消息的发送、接收、存储、流式处理和实时推送功能
"""

import json
import uuid
import asyncio
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Union, AsyncGenerator
from sqlalchemy import and_, or_, desc, func
from sqlalchemy.orm import Session, selectinload
from loguru import logger

from app.core.database import get_database_manager
from app.models.message import Message, MessageAttachment, MessageReaction
from app.models.session import Session as SessionModel
from app.models.user import User
from app.services.auth_service import AuthService
from app.services.session_service import SessionService
from app.core.config import get_settings


class MessageService:
    """
    消息服务
    
    提供消息的发送、接收、存储、流式处理和实时推送功能
    """
    
    def __init__(self):
        self.db_manager = get_database_manager()
        self.auth_service = AuthService()
        self.session_service = SessionService()
        self.settings = get_settings()
        self._message_subscribers = {}  # 消息订阅者
    
    async def send_message(self, user_id: int, message_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        发送消息
        
        Args:
            user_id: 发送者用户ID
            message_data: 消息数据
            
        Returns:
            Dict[str, Any]: 发送结果
            
        Raises:
            ValueError: 参数无效
            Exception: 发送失败
        """
        async with self.db_manager.get_session() as session:
            try:
                # 验证必需字段
                required_fields = ["session_id", "content", "type"]
                for field in required_fields:
                    if field not in message_data:
                        raise ValueError(f"缺少必需字段: {field}")
                
                session_id = message_data["session_id"]
                
                # 检查会话权限
                has_write_permission = await self.session_service._check_session_permission(
                    session, user_id, session_id, "write"
                )
                if not has_write_permission:
                    raise ValueError("没有发送消息的权限")
                
                # 验证会话状态
                session_result = await session.execute(
                    session.query(SessionModel).filter(
                        and_(
                            SessionModel.id == session_id,
                            SessionModel.deleted_at.is_(None),
                            SessionModel.status.in_(["active", "paused"])
                        )
                    )
                )
                session_obj = session_result.scalar_one_or_none()
                
                if not session_obj:
                    raise ValueError("会话不存在或不可用")
                
                # 验证消息类型
                valid_types = ["text", "image", "file", "audio", "video", "system", "tool_call", "tool_result"]
                if message_data["type"] not in valid_types:
                    raise ValueError(f"无效的消息类型: {message_data['type']}")
                
                # 验证消息内容
                content = message_data["content"]
                if message_data["type"] == "text" and not content.strip():
                    raise ValueError("文本消息内容不能为空")
                
                # 创建消息记录
                message_id = str(uuid.uuid4())
                new_message = Message(
                    id=message_id,
                    session_id=session_id,
                    sender_id=user_id,
                    content=content,
                    type=message_data["type"],
                    role=message_data.get("role", "user"),
                    metadata=json.dumps(message_data.get("metadata", {})),
                    parent_id=message_data.get("parent_id"),
                    thread_id=message_data.get("thread_id"),
                    status="sent",
                    created_at=datetime.utcnow(),
                    updated_at=datetime.utcnow()
                )
                
                session.add(new_message)
                await session.flush()  # 获取message.id
                
                # 处理附件
                attachments = message_data.get("attachments", [])
                attachment_records = []
                for attachment in attachments:
                    attachment_record = MessageAttachment(
                        id=str(uuid.uuid4()),
                        message_id=message_id,
                        filename=attachment["filename"],
                        file_type=attachment["file_type"],
                        file_size=attachment["file_size"],
                        file_url=attachment["file_url"],
                        thumbnail_url=attachment.get("thumbnail_url"),
                        metadata=json.dumps(attachment.get("metadata", {})),
                        created_at=datetime.utcnow()
                    )
                    session.add(attachment_record)
                    attachment_records.append(attachment_record)
                
                # 更新会话的最后消息时间
                session_obj.last_message_at = datetime.utcnow()
                session_obj.updated_at = datetime.utcnow()
                
                await session.commit()
                
                # 构建消息数据用于推送
                message_dict = {
                    "message_id": message_id,
                    "session_id": session_id,
                    "sender_id": user_id,
                    "content": content,
                    "type": new_message.type,
                    "role": new_message.role,
                    "metadata": json.loads(new_message.metadata) if new_message.metadata else {},
                    "parent_id": new_message.parent_id,
                    "thread_id": new_message.thread_id,
                    "attachments": [
                        {
                            "attachment_id": att.id,
                            "filename": att.filename,
                            "file_type": att.file_type,
                            "file_size": att.file_size,
                            "file_url": att.file_url,
                            "thumbnail_url": att.thumbnail_url
                        } for att in attachment_records
                    ],
                    "created_at": new_message.created_at.isoformat(),
                    "status": new_message.status
                }
                
                # 实时推送消息
                await self._broadcast_message(session_id, message_dict)
                
                logger.info(f"消息发送成功", extra={
                    "user_id": user_id,
                    "session_id": session_id,
                    "message_id": message_id,
                    "message_type": new_message.type
                })
                
                return {
                    "success": True,
                    "message": "消息发送成功",
                    "data": message_dict
                }
                
            except ValueError as e:
                await session.rollback()
                logger.warning(f"消息发送失败: {str(e)}")
                raise
            except Exception as e:
                await session.rollback()
                logger.error(f"消息发送异常: {str(e)}")
                raise Exception(f"消息发送失败: {str(e)}")
    
    async def get_message(self, user_id: int, message_id: str) -> Dict[str, Any]:
        """
        获取消息详情
        
        Args:
            user_id: 用户ID
            message_id: 消息ID
            
        Returns:
            Dict[str, Any]: 消息详情
            
        Raises:
            ValueError: 消息不存在或无权限
        """
        async with self.db_manager.get_session() as session:
            try:
                # 查询消息
                result = await session.execute(
                    session.query(Message)
                    .options(
                        selectinload(Message.attachments),
                        selectinload(Message.reactions),
                        selectinload(Message.sender)
                    )
                    .filter(
                        and_(
                            Message.id == message_id,
                            Message.deleted_at.is_(None)
                        )
                    )
                )
                message = result.scalar_one_or_none()
                
                if not message:
                    raise ValueError("消息不存在")
                
                # 检查会话访问权限
                has_read_permission = await self.session_service._check_session_permission(
                    session, user_id, message.session_id, "read"
                )
                if not has_read_permission:
                    raise ValueError("没有访问权限")
                
                # 格式化消息数据
                message_dict = {
                    "message_id": message.id,
                    "session_id": message.session_id,
                    "sender": {
                        "user_id": message.sender.id,
                        "username": message.sender.username,
                        "full_name": message.sender.full_name,
                        "avatar_url": message.sender.avatar_url
                    } if message.sender else None,
                    "content": message.content,
                    "type": message.type,
                    "role": message.role,
                    "metadata": json.loads(message.metadata) if message.metadata else {},
                    "parent_id": message.parent_id,
                    "thread_id": message.thread_id,
                    "attachments": [
                        {
                            "attachment_id": att.id,
                            "filename": att.filename,
                            "file_type": att.file_type,
                            "file_size": att.file_size,
                            "file_url": att.file_url,
                            "thumbnail_url": att.thumbnail_url,
                            "metadata": json.loads(att.metadata) if att.metadata else {}
                        } for att in message.attachments
                    ],
                    "reactions": [
                        {
                            "reaction_id": reaction.id,
                            "user_id": reaction.user_id,
                            "emoji": reaction.emoji,
                            "created_at": reaction.created_at.isoformat()
                        } for reaction in message.reactions
                    ],
                    "status": message.status,
                    "created_at": message.created_at.isoformat(),
                    "updated_at": message.updated_at.isoformat(),
                    "edited_at": message.edited_at.isoformat() if message.edited_at else None
                }
                
                return message_dict
                
            except ValueError as e:
                logger.warning(f"获取消息失败: {str(e)}")
                raise
            except Exception as e:
                logger.error(f"获取消息异常: {str(e)}")
                raise Exception(f"获取消息失败: {str(e)}")
    
    async def update_message(self, user_id: int, message_id: str, update_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        更新消息
        
        Args:
            user_id: 用户ID
            message_id: 消息ID
            update_data: 更新数据
            
        Returns:
            Dict[str, Any]: 更新结果
        """
        async with self.db_manager.get_session() as session:
            try:
                # 查询消息
                result = await session.execute(
                    session.query(Message).filter(
                        and_(
                            Message.id == message_id,
                            Message.deleted_at.is_(None)
                        )
                    )
                )
                message = result.scalar_one_or_none()
                
                if not message:
                    raise ValueError("消息不存在")
                
                # 检查权限：只有发送者或管理员可以编辑
                if message.sender_id != user_id:
                    has_manage_permission = await self.session_service._check_session_permission(
                        session, user_id, message.session_id, "manage"
                    )
                    if not has_manage_permission:
                        raise ValueError("没有编辑权限")
                
                # 检查消息是否可编辑（例如，发送后一定时间内）
                edit_time_limit = timedelta(minutes=self.settings.message_edit_time_limit_minutes)
                if datetime.utcnow() - message.created_at > edit_time_limit:
                    raise ValueError("消息编辑时间已过")
                
                # 更新消息内容
                if "content" in update_data:
                    message.content = update_data["content"]
                    message.edited_at = datetime.utcnow()
                
                if "metadata" in update_data:
                    message.metadata = json.dumps(update_data["metadata"])
                
                message.updated_at = datetime.utcnow()
                await session.commit()
                
                # 推送消息更新事件
                update_event = {
                    "type": "message_updated",
                    "message_id": message_id,
                    "session_id": message.session_id,
                    "content": message.content,
                    "edited_at": message.edited_at.isoformat() if message.edited_at else None,
                    "updated_at": message.updated_at.isoformat()
                }
                await self._broadcast_message(message.session_id, update_event)
                
                logger.info(f"消息更新成功", extra={
                    "user_id": user_id,
                    "message_id": message_id
                })
                
                return {
                    "success": True,
                    "message": "消息更新成功",
                    "data": {
                        "message_id": message_id,
                        "updated_at": message.updated_at.isoformat(),
                        "edited_at": message.edited_at.isoformat() if message.edited_at else None
                    }
                }
                
            except ValueError as e:
                await session.rollback()
                logger.warning(f"消息更新失败: {str(e)}")
                raise
            except Exception as e:
                await session.rollback()
                logger.error(f"消息更新异常: {str(e)}")
                raise Exception(f"消息更新失败: {str(e)}")
    
    async def delete_message(self, user_id: int, message_id: str, hard_delete: bool = False) -> Dict[str, Any]:
        """
        删除消息
        
        Args:
            user_id: 用户ID
            message_id: 消息ID
            hard_delete: 是否硬删除
            
        Returns:
            Dict[str, Any]: 删除结果
        """
        async with self.db_manager.get_session() as session:
            try:
                # 查询消息
                result = await session.execute(
                    session.query(Message).filter(
                        and_(
                            Message.id == message_id,
                            Message.deleted_at.is_(None) if not hard_delete else True
                        )
                    )
                )
                message = result.scalar_one_or_none()
                
                if not message:
                    raise ValueError("消息不存在")
                
                # 检查权限：只有发送者或管理员可以删除
                if message.sender_id != user_id:
                    has_manage_permission = await self.session_service._check_session_permission(
                        session, user_id, message.session_id, "manage"
                    )
                    if not has_manage_permission:
                        raise ValueError("没有删除权限")
                
                if hard_delete:
                    # 硬删除：删除所有相关记录
                    await session.execute(
                        session.query(MessageAttachment).filter(
                            MessageAttachment.message_id == message_id
                        ).delete()
                    )
                    
                    await session.execute(
                        session.query(MessageReaction).filter(
                            MessageReaction.message_id == message_id
                        ).delete()
                    )
                    
                    await session.delete(message)
                    message_text = "消息已永久删除"
                else:
                    # 软删除：标记删除时间
                    message.deleted_at = datetime.utcnow()
                    message.status = "deleted"
                    message.updated_at = datetime.utcnow()
                    message_text = "消息已删除"
                
                await session.commit()
                
                # 推送消息删除事件
                delete_event = {
                    "type": "message_deleted",
                    "message_id": message_id,
                    "session_id": message.session_id,
                    "hard_delete": hard_delete,
                    "deleted_at": datetime.utcnow().isoformat()
                }
                await self._broadcast_message(message.session_id, delete_event)
                
                logger.info(f"消息删除成功", extra={
                    "user_id": user_id,
                    "message_id": message_id,
                    "hard_delete": hard_delete
                })
                
                return {
                    "success": True,
                    "message": message_text
                }
                
            except ValueError as e:
                await session.rollback()
                logger.warning(f"消息删除失败: {str(e)}")
                raise
            except Exception as e:
                await session.rollback()
                logger.error(f"消息删除异常: {str(e)}")
                raise Exception(f"消息删除失败: {str(e)}")
    
    async def list_messages(self, user_id: int, session_id: str, 
                           filters: Optional[Dict[str, Any]] = None,
                           page: int = 1, page_size: int = 50) -> Dict[str, Any]:
        """
        获取消息列表
        
        Args:
            user_id: 用户ID
            session_id: 会话ID
            filters: 过滤条件
            page: 页码
            page_size: 页面大小
            
        Returns:
            Dict[str, Any]: 消息列表
        """
        async with self.db_manager.get_session() as session:
            try:
                # 检查会话访问权限
                has_read_permission = await self.session_service._check_session_permission(
                    session, user_id, session_id, "read"
                )
                if not has_read_permission:
                    raise ValueError("没有访问权限")
                
                # 构建查询条件
                query_conditions = [
                    Message.session_id == session_id,
                    Message.deleted_at.is_(None)
                ]
                
                # 应用过滤条件
                if filters:
                    if "type" in filters:
                        query_conditions.append(Message.type == filters["type"])
                    
                    if "role" in filters:
                        query_conditions.append(Message.role == filters["role"])
                    
                    if "sender_id" in filters:
                        query_conditions.append(Message.sender_id == filters["sender_id"])
                    
                    if "content" in filters:
                        query_conditions.append(
                            Message.content.ilike(f"%{filters['content']}%")
                        )
                    
                    if "thread_id" in filters:
                        query_conditions.append(Message.thread_id == filters["thread_id"])
                    
                    if "created_after" in filters:
                        query_conditions.append(
                            Message.created_at >= datetime.fromisoformat(filters["created_after"])
                        )
                    
                    if "created_before" in filters:
                        query_conditions.append(
                            Message.created_at <= datetime.fromisoformat(filters["created_before"])
                        )
                
                # 计算总数
                count_result = await session.execute(
                    session.query(func.count(Message.id)).filter(
                        and_(*query_conditions)
                    )
                )
                total = count_result.scalar()
                
                # 分页查询
                offset = (page - 1) * page_size
                result = await session.execute(
                    session.query(Message)
                    .options(
                        selectinload(Message.attachments),
                        selectinload(Message.reactions),
                        selectinload(Message.sender)
                    )
                    .filter(and_(*query_conditions))
                    .order_by(Message.created_at)
                    .offset(offset)
                    .limit(page_size)
                )
                messages = result.scalars().all()
                
                # 格式化结果
                message_list = []
                for message in messages:
                    message_dict = {
                        "message_id": message.id,
                        "sender": {
                            "user_id": message.sender.id,
                            "username": message.sender.username,
                            "full_name": message.sender.full_name,
                            "avatar_url": message.sender.avatar_url
                        } if message.sender else None,
                        "content": message.content,
                        "type": message.type,
                        "role": message.role,
                        "metadata": json.loads(message.metadata) if message.metadata else {},
                        "parent_id": message.parent_id,
                        "thread_id": message.thread_id,
                        "attachment_count": len(message.attachments),
                        "reaction_count": len(message.reactions),
                        "status": message.status,
                        "created_at": message.created_at.isoformat(),
                        "updated_at": message.updated_at.isoformat(),
                        "edited_at": message.edited_at.isoformat() if message.edited_at else None
                    }
                    message_list.append(message_dict)
                
                return {
                    "messages": message_list,
                    "pagination": {
                        "page": page,
                        "page_size": page_size,
                        "total": total,
                        "pages": (total + page_size - 1) // page_size
                    }
                }
                
            except ValueError as e:
                logger.warning(f"获取消息列表失败: {str(e)}")
                raise
            except Exception as e:
                logger.error(f"获取消息列表异常: {str(e)}")
                raise Exception(f"获取消息列表失败: {str(e)}")
    
    async def add_reaction(self, user_id: int, message_id: str, emoji: str) -> Dict[str, Any]:
        """
        添加消息反应
        
        Args:
            user_id: 用户ID
            message_id: 消息ID
            emoji: 表情符号
            
        Returns:
            Dict[str, Any]: 添加结果
        """
        async with self.db_manager.get_session() as session:
            try:
                # 查询消息
                message_result = await session.execute(
                    session.query(Message).filter(
                        and_(
                            Message.id == message_id,
                            Message.deleted_at.is_(None)
                        )
                    )
                )
                message = message_result.scalar_one_or_none()
                
                if not message:
                    raise ValueError("消息不存在")
                
                # 检查会话访问权限
                has_read_permission = await self.session_service._check_session_permission(
                    session, user_id, message.session_id, "read"
                )
                if not has_read_permission:
                    raise ValueError("没有访问权限")
                
                # 检查是否已经有相同的反应
                existing_reaction_result = await session.execute(
                    session.query(MessageReaction).filter(
                        and_(
                            MessageReaction.message_id == message_id,
                            MessageReaction.user_id == user_id,
                            MessageReaction.emoji == emoji
                        )
                    )
                )
                if existing_reaction_result.scalar_one_or_none():
                    raise ValueError("已经添加过相同的反应")
                
                # 添加反应
                reaction = MessageReaction(
                    id=str(uuid.uuid4()),
                    message_id=message_id,
                    user_id=user_id,
                    emoji=emoji,
                    created_at=datetime.utcnow()
                )
                
                session.add(reaction)
                await session.commit()
                
                # 推送反应事件
                reaction_event = {
                    "type": "reaction_added",
                    "message_id": message_id,
                    "session_id": message.session_id,
                    "user_id": user_id,
                    "emoji": emoji,
                    "reaction_id": reaction.id,
                    "created_at": reaction.created_at.isoformat()
                }
                await self._broadcast_message(message.session_id, reaction_event)
                
                logger.info(f"消息反应添加成功", extra={
                    "user_id": user_id,
                    "message_id": message_id,
                    "emoji": emoji
                })
                
                return {
                    "success": True,
                    "message": "反应添加成功",
                    "data": {
                        "reaction_id": reaction.id,
                        "emoji": emoji,
                        "created_at": reaction.created_at.isoformat()
                    }
                }
                
            except ValueError as e:
                await session.rollback()
                logger.warning(f"添加反应失败: {str(e)}")
                raise
            except Exception as e:
                await session.rollback()
                logger.error(f"添加反应异常: {str(e)}")
                raise Exception(f"添加反应失败: {str(e)}")
    
    async def remove_reaction(self, user_id: int, message_id: str, emoji: str) -> Dict[str, Any]:
        """
        移除消息反应
        
        Args:
            user_id: 用户ID
            message_id: 消息ID
            emoji: 表情符号
            
        Returns:
            Dict[str, Any]: 移除结果
        """
        async with self.db_manager.get_session() as session:
            try:
                # 查询消息
                message_result = await session.execute(
                    session.query(Message).filter(
                        and_(
                            Message.id == message_id,
                            Message.deleted_at.is_(None)
                        )
                    )
                )
                message = message_result.scalar_one_or_none()
                
                if not message:
                    raise ValueError("消息不存在")
                
                # 查找反应
                reaction_result = await session.execute(
                    session.query(MessageReaction).filter(
                        and_(
                            MessageReaction.message_id == message_id,
                            MessageReaction.user_id == user_id,
                            MessageReaction.emoji == emoji
                        )
                    )
                )
                reaction = reaction_result.scalar_one_or_none()
                
                if not reaction:
                    raise ValueError("反应不存在")
                
                # 删除反应
                await session.delete(reaction)
                await session.commit()
                
                # 推送反应移除事件
                reaction_event = {
                    "type": "reaction_removed",
                    "message_id": message_id,
                    "session_id": message.session_id,
                    "user_id": user_id,
                    "emoji": emoji,
                    "reaction_id": reaction.id
                }
                await self._broadcast_message(message.session_id, reaction_event)
                
                logger.info(f"消息反应移除成功", extra={
                    "user_id": user_id,
                    "message_id": message_id,
                    "emoji": emoji
                })
                
                return {
                    "success": True,
                    "message": "反应移除成功"
                }
                
            except ValueError as e:
                await session.rollback()
                logger.warning(f"移除反应失败: {str(e)}")
                raise
            except Exception as e:
                await session.rollback()
                logger.error(f"移除反应异常: {str(e)}")
                raise Exception(f"移除反应失败: {str(e)}")
    
    async def stream_messages(self, user_id: int, session_id: str) -> AsyncGenerator[Dict[str, Any], None]:
        """
        流式获取消息
        
        Args:
            user_id: 用户ID
            session_id: 会话ID
            
        Yields:
            Dict[str, Any]: 消息数据
        """
        try:
            # 检查会话访问权限
            async with self.db_manager.get_session() as session:
                has_read_permission = await self.session_service._check_session_permission(
                    session, user_id, session_id, "read"
                )
                if not has_read_permission:
                    raise ValueError("没有访问权限")
            
            # 创建消息队列
            message_queue = asyncio.Queue()
            
            # 订阅消息
            subscription_id = str(uuid.uuid4())
            if session_id not in self._message_subscribers:
                self._message_subscribers[session_id] = {}
            
            self._message_subscribers[session_id][subscription_id] = {
                "user_id": user_id,
                "queue": message_queue
            }
            
            try:
                # 发送连接确认
                yield {
                    "type": "connection_established",
                    "session_id": session_id,
                    "subscription_id": subscription_id,
                    "timestamp": datetime.utcnow().isoformat()
                }
                
                # 持续监听消息
                while True:
                    try:
                        # 等待消息，设置超时以便定期检查连接状态
                        message = await asyncio.wait_for(message_queue.get(), timeout=30.0)
                        yield message
                    except asyncio.TimeoutError:
                        # 发送心跳
                        yield {
                            "type": "heartbeat",
                            "timestamp": datetime.utcnow().isoformat()
                        }
                    
            finally:
                # 清理订阅
                if (session_id in self._message_subscribers and 
                    subscription_id in self._message_subscribers[session_id]):
                    del self._message_subscribers[session_id][subscription_id]
                    
                    if not self._message_subscribers[session_id]:
                        del self._message_subscribers[session_id]
                
                logger.info(f"消息流连接关闭", extra={
                    "user_id": user_id,
                    "session_id": session_id,
                    "subscription_id": subscription_id
                })
                
        except Exception as e:
            logger.error(f"消息流异常: {str(e)}")
            yield {
                "type": "error",
                "message": str(e),
                "timestamp": datetime.utcnow().isoformat()
            }
    
    async def search_messages(self, user_id: int, search_params: Dict[str, Any]) -> Dict[str, Any]:
        """
        搜索消息
        
        Args:
            user_id: 用户ID
            search_params: 搜索参数
            
        Returns:
            Dict[str, Any]: 搜索结果
        """
        async with self.db_manager.get_session() as session:
            try:
                # 验证必需参数
                if "query" not in search_params:
                    raise ValueError("缺少搜索关键词")
                
                query = search_params["query"]
                session_ids = search_params.get("session_ids", [])
                page = search_params.get("page", 1)
                page_size = min(search_params.get("page_size", 20), 100)  # 限制最大页面大小
                
                # 构建查询条件
                query_conditions = [
                    Message.deleted_at.is_(None),
                    Message.content.ilike(f"%{query}%")
                ]
                
                # 权限过滤：只搜索用户有权限访问的会话
                if session_ids:
                    # 验证用户对指定会话的访问权限
                    accessible_sessions = []
                    for session_id in session_ids:
                        has_permission = await self.session_service._check_session_permission(
                            session, user_id, session_id, "read"
                        )
                        if has_permission:
                            accessible_sessions.append(session_id)
                    
                    if not accessible_sessions:
                        return {
                            "messages": [],
                            "pagination": {
                                "page": page,
                                "page_size": page_size,
                                "total": 0,
                                "pages": 0
                            }
                        }
                    
                    query_conditions.append(Message.session_id.in_(accessible_sessions))
                else:
                    # 获取用户有权限的所有会话
                    from app.models.session import SessionParticipant
                    
                    participant_sessions = await session.execute(
                        session.query(SessionParticipant.session_id).filter(
                            and_(
                                SessionParticipant.user_id == user_id,
                                SessionParticipant.left_at.is_(None)
                            )
                        )
                    )
                    accessible_session_ids = [row[0] for row in participant_sessions.fetchall()]
                    
                    if not accessible_session_ids:
                        return {
                            "messages": [],
                            "pagination": {
                                "page": page,
                                "page_size": page_size,
                                "total": 0,
                                "pages": 0
                            }
                        }
                    
                    query_conditions.append(Message.session_id.in_(accessible_session_ids))
                
                # 应用其他过滤条件
                if "type" in search_params:
                    query_conditions.append(Message.type == search_params["type"])
                
                if "sender_id" in search_params:
                    query_conditions.append(Message.sender_id == search_params["sender_id"])
                
                if "date_from" in search_params:
                    query_conditions.append(
                        Message.created_at >= datetime.fromisoformat(search_params["date_from"])
                    )
                
                if "date_to" in search_params:
                    query_conditions.append(
                        Message.created_at <= datetime.fromisoformat(search_params["date_to"])
                    )
                
                # 计算总数
                count_result = await session.execute(
                    session.query(func.count(Message.id)).filter(
                        and_(*query_conditions)
                    )
                )
                total = count_result.scalar()
                
                # 分页查询
                offset = (page - 1) * page_size
                result = await session.execute(
                    session.query(Message)
                    .options(
                        selectinload(Message.sender),
                        selectinload(Message.session)
                    )
                    .filter(and_(*query_conditions))
                    .order_by(desc(Message.created_at))
                    .offset(offset)
                    .limit(page_size)
                )
                messages = result.scalars().all()
                
                # 格式化结果
                message_list = []
                for message in messages:
                    # 高亮搜索关键词
                    highlighted_content = self._highlight_search_term(message.content, query)
                    
                    message_dict = {
                        "message_id": message.id,
                        "session_id": message.session_id,
                        "session_title": message.session.title if message.session else None,
                        "sender": {
                            "user_id": message.sender.id,
                            "username": message.sender.username,
                            "full_name": message.sender.full_name
                        } if message.sender else None,
                        "content": message.content,
                        "highlighted_content": highlighted_content,
                        "type": message.type,
                        "role": message.role,
                        "created_at": message.created_at.isoformat()
                    }
                    message_list.append(message_dict)
                
                logger.info(f"消息搜索完成", extra={
                    "user_id": user_id,
                    "query": query,
                    "total_results": total
                })
                
                return {
                    "messages": message_list,
                    "pagination": {
                        "page": page,
                        "page_size": page_size,
                        "total": total,
                        "pages": (total + page_size - 1) // page_size
                    },
                    "search_info": {
                        "query": query,
                        "searched_sessions": len(accessible_session_ids) if not session_ids else len(accessible_sessions)
                    }
                }
                
            except ValueError as e:
                logger.warning(f"消息搜索失败: {str(e)}")
                raise
            except Exception as e:
                logger.error(f"消息搜索异常: {str(e)}")
                raise Exception(f"消息搜索失败: {str(e)}")
    
    async def _broadcast_message(self, session_id: str, message_data: Dict[str, Any]):
        """
        广播消息到会话的所有订阅者
        
        Args:
            session_id: 会话ID
            message_data: 消息数据
        """
        if session_id in self._message_subscribers:
            subscribers = self._message_subscribers[session_id].copy()
            
            for subscription_id, subscriber in subscribers.items():
                try:
                    await subscriber["queue"].put(message_data)
                except Exception as e:
                    logger.warning(f"消息推送失败", extra={
                        "session_id": session_id,
                        "subscription_id": subscription_id,
                        "error": str(e)
                    })
                    # 移除失效的订阅者
                    if (session_id in self._message_subscribers and 
                        subscription_id in self._message_subscribers[session_id]):
                        del self._message_subscribers[session_id][subscription_id]
    
    def _highlight_search_term(self, content: str, search_term: str) -> str:
        """
        在内容中高亮搜索关键词
        
        Args:
            content: 原始内容
            search_term: 搜索关键词
            
        Returns:
            str: 高亮后的内容
        """
        import re
        
        # 使用正则表达式进行不区分大小写的替换
        pattern = re.compile(re.escape(search_term), re.IGNORECASE)
        highlighted = pattern.sub(f"<mark>{search_term}</mark>", content)
        
        return highlighted
    
    async def get_message_statistics(self, user_id: int, session_id: str, 
                                   date_range: Optional[Dict[str, str]] = None) -> Dict[str, Any]:
        """
        获取消息统计信息
        
        Args:
            user_id: 用户ID
            session_id: 会话ID
            date_range: 日期范围
            
        Returns:
            Dict[str, Any]: 统计信息
        """
        async with self.db_manager.get_session() as session:
            try:
                # 检查会话访问权限
                has_read_permission = await self.session_service._check_session_permission(
                    session, user_id, session_id, "read"
                )
                if not has_read_permission:
                    raise ValueError("没有访问权限")
                
                # 构建查询条件
                query_conditions = [
                    Message.session_id == session_id,
                    Message.deleted_at.is_(None)
                ]
                
                if date_range:
                    if "start_date" in date_range:
                        query_conditions.append(
                            Message.created_at >= datetime.fromisoformat(date_range["start_date"])
                        )
                    if "end_date" in date_range:
                        query_conditions.append(
                            Message.created_at <= datetime.fromisoformat(date_range["end_date"])
                        )
                
                # 总消息数
                total_result = await session.execute(
                    session.query(func.count(Message.id)).filter(
                        and_(*query_conditions)
                    )
                )
                total_messages = total_result.scalar()
                
                # 按类型统计
                type_result = await session.execute(
                    session.query(Message.type, func.count(Message.id))
                    .filter(and_(*query_conditions))
                    .group_by(Message.type)
                )
                message_by_type = {row[0]: row[1] for row in type_result.fetchall()}
                
                # 按发送者统计
                sender_result = await session.execute(
                    session.query(Message.sender_id, func.count(Message.id))
                    .filter(and_(*query_conditions))
                    .group_by(Message.sender_id)
                )
                message_by_sender = {row[0]: row[1] for row in sender_result.fetchall()}
                
                # 按日期统计（最近7天）
                daily_stats = []
                for i in range(7):
                    date = datetime.utcnow().date() - timedelta(days=i)
                    start_time = datetime.combine(date, datetime.min.time())
                    end_time = datetime.combine(date, datetime.max.time())
                    
                    daily_result = await session.execute(
                        session.query(func.count(Message.id)).filter(
                            and_(
                                Message.session_id == session_id,
                                Message.deleted_at.is_(None),
                                Message.created_at >= start_time,
                                Message.created_at <= end_time
                            )
                        )
                    )
                    count = daily_result.scalar()
                    
                    daily_stats.append({
                        "date": date.isoformat(),
                        "count": count
                    })
                
                return {
                    "total_messages": total_messages,
                    "message_by_type": message_by_type,
                    "message_by_sender": message_by_sender,
                    "daily_statistics": daily_stats,
                    "date_range": date_range,
                    "generated_at": datetime.utcnow().isoformat()
                }
                
            except ValueError as e:
                logger.warning(f"获取消息统计失败: {str(e)}")
                raise
            except Exception as e:
                logger.error(f"获取消息统计异常: {str(e)}")
                raise Exception(f"获取消息统计失败: {str(e)}")