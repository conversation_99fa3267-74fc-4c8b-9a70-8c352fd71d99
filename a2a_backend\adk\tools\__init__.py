#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A2A多智能体系统 工具包

提供完整的工具生态系统，包括基础工具、工具管理、执行引擎、安全控制、
插件系统、缓存、监控、配置管理、API服务等功能。
"""

# 版本信息
__version__ = "1.0.0"
__author__ = "A2A Team"
__email__ = "<EMAIL>"
__description__ = "A2A多智能体系统工具包"

# 基础工具模块
from .base_tool import (
    BaseTool,
    ToolConfig,
    ToolResult,
    ToolError,
    ToolStatus,
    ToolExecutionContext,
    ToolPermission,
    ToolCategory,
    ToolPriority,
    ToolMetadata,
    ToolUsageStats,
    IToolValidator,
    DefaultToolValidator
)

# 具体工具实现
from .web_tool import (
    WebTool,
    HttpMethod,
    ResponseFormat,
    SecurityLevel,
    RequestConfig,
    NetworkResponse,
    WebToolConfig
)

from .calculation_tool import (
    CalculationTool,
    CalculationType,
    OutputFormat,
    CalculationConfig,
    CalculationRequest,
    CalculationResult,
    UsageStats
)

# 工具管理
from .tool_registry import (
    ToolRegistry,
    ToolCategory as RegistryToolCategory,
    RegistrationStatus,
    ToolRegistration,
    ToolFilter,
    ToolStats,
    IToolValidator as RegistryIToolValidator,
    DefaultToolValidator as RegistryDefaultToolValidator,
    get_global_tool_registry,
    set_global_tool_registry
)

# 工具执行引擎
from .tool_executor import (
    ToolExecutor,
    ExecutionMode,
    ExecutionRequest,
    ExecutionResult,
    ExecutionConfig,
    ExecutionStats,
    BatchExecutionRequest,
    BatchExecutionResult
)

# 工具API服务
from .tool_api import (
    ToolAPI,
    APIConfig,
    WebSocketConnection,
    WebSocketMessage,
    MessageType
)

# 工具安全
from .tool_security import (
    SecurityManager,
    SecurityLevel as SecuritySecurityLevel,
    ThreatLevel,
    SecurityAction,
    AuthenticationMethod,
    SecurityRule,
    SecurityPolicy,
    SecurityIncident,
    SecurityAuditLog,
    RateLimitRule,
    SecurityConfig,
    SecurityError,
    ISecurityProvider,
    DefaultSecurityProvider,
    RateLimiter,
    get_global_security_manager,
    set_global_security_manager
)

# 工具缓存
from .tool_cache import (
    ToolCache,
    CacheStrategy,
    StorageBackend,
    SerializationFormat,
    CacheKey,
    CacheEntry,
    CacheConfig,
    CacheStats,
    CacheError
)

# 工具监控
from .tool_monitor import (
    ToolMonitor,
    MonitorLevel,
    AlertLevel,
    MetricType,
    HealthStatus,
    MetricValue,
    PerformanceMetrics,
    HealthCheck,
    Alert,
    AlertRule,
    MonitorConfig,
    MetricsCollector,
    AlertManager
)

# 工具配置
from .tool_config import (
    ConfigManager,
    ConfigScope,
    ConfigFormat,
    ConfigSource,
    ValidationResult,
    ConfigSchema,
    ConfigFile,
    ConfigInheritance,
    ConfigError
)

# 工具插件系统
from .tool_plugin import (
    PluginManager,
    PluginStatus,
    PluginType,
    PluginPriority,
    PluginDependency,
    PluginMetadata,
    PluginConfig as PluginPluginConfig,
    PluginInfo,
    PluginEvent,
    PluginManagerConfig,
    PluginError,
    IPlugin,
    IPluginLoader,
    PythonPluginLoader,
    PluginDiscovery,
    get_global_plugin_manager,
    set_global_plugin_manager
)

# MCP协议支持
from .mcp_protocol import (
    MCPVersion,
    MCPErrorCode,
    MCPError,
    MCPCapabilities,
    MCPClientInfo,
    MCPServerInfo,
    MCPToolSchema,
    MCPTool,
    MCPResourceTemplate,
    MCPResourceContent,
    MCPPromptArgument,
    MCPPromptMessage,
    MCPPrompt,
    MCPLogLevel,
    MCPLogEntry,
    MCPMessageValidator,
    MCPMessageBuilder
)

from .mcp_client import (
    MCPClient,
    ConnectionState,
    TransportType,
    ClientConfig,
    ConnectionStats,
    MCPClientError
)

from .mcp_server import (
    MCPServer,
    ServerState,
    ServerConfig,
    ClientConnection,
    ServerStats,
    MCPServerError
)

from .mcp_manager import (
    MCPManager,
    ManagerState,
    ConnectionType,
    EndpointConfig,
    ManagerConfig,
    EndpointStatus,
    ManagerError
)

# 导出所有主要类和函数
__all__ = [
    # 版本信息
    "__version__",
    "__author__",
    "__email__",
    "__description__",
    
    # 基础工具
    "BaseTool",
    "ToolConfig",
    "ToolResult",
    "ToolError",
    "ToolStatus",
    "ToolExecutionContext",
    "ToolPermission",
    "ToolCategory",
    "ToolPriority",
    "ToolMetadata",
    "ToolUsageStats",
    "IToolValidator",
    "DefaultToolValidator",
    
    # 具体工具
    "WebTool",
    "HttpMethod",
    "ResponseFormat",
    "SecurityLevel",
    "RequestConfig",
    "NetworkResponse",
    "WebToolConfig",
    "CalculationTool",
    "CalculationType",
    "OutputFormat",
    "CalculationConfig",
    "CalculationRequest",
    "CalculationResult",
    "UsageStats",
    
    # 工具管理
    "ToolRegistry",
    "RegistryToolCategory",
    "RegistrationStatus",
    "ToolRegistration",
    "ToolFilter",
    "ToolStats",
    "RegistryIToolValidator",
    "RegistryDefaultToolValidator",
    "get_global_tool_registry",
    "set_global_tool_registry",
    
    # 工具执行
    "ToolExecutor",
    "ExecutionMode",
    "ExecutionRequest",
    "ExecutionResult",
    "ExecutionConfig",
    "ExecutionStats",
    "BatchExecutionRequest",
    "BatchExecutionResult",
    
    # 工具API
    "ToolAPI",
    "APIConfig",
    "WebSocketConnection",
    "WebSocketMessage",
    "MessageType",
    
    # 工具安全
    "SecurityManager",
    "SecuritySecurityLevel",
    "ThreatLevel",
    "SecurityAction",
    "AuthenticationMethod",
    "SecurityRule",
    "SecurityPolicy",
    "SecurityIncident",
    "SecurityAuditLog",
    "RateLimitRule",
    "SecurityConfig",
    "SecurityError",
    "ISecurityProvider",
    "DefaultSecurityProvider",
    "RateLimiter",
    "get_global_security_manager",
    "set_global_security_manager",
    
    # 工具缓存
    "ToolCache",
    "CacheStrategy",
    "StorageBackend",
    "SerializationFormat",
    "CacheKey",
    "CacheEntry",
    "CacheConfig",
    "CacheStats",
    "CacheError",
    
    # 工具监控
    "ToolMonitor",
    "MonitorLevel",
    "AlertLevel",
    "MetricType",
    "HealthStatus",
    "MetricValue",
    "PerformanceMetrics",
    "HealthCheck",
    "Alert",
    "AlertRule",
    "MonitorConfig",
    "MetricsCollector",
    "AlertManager",
    
    # 工具配置
    "ConfigManager",
    "ConfigScope",
    "ConfigFormat",
    "ConfigSource",
    "ValidationResult",
    "ConfigSchema",
    "ConfigFile",
    "ConfigInheritance",
    "ConfigError",
    
    # 工具插件
    "PluginManager",
    "PluginStatus",
    "PluginType",
    "PluginPriority",
    "PluginDependency",
    "PluginMetadata",
    "PluginPluginConfig",
    "PluginInfo",
    "PluginEvent",
    "PluginManagerConfig",
    "PluginError",
    "IPlugin",
    "IPluginLoader",
    "PythonPluginLoader",
    "PluginDiscovery",
    "get_global_plugin_manager",
    "set_global_plugin_manager",
    
    # MCP协议
    "MCPVersion",
    "MCPErrorCode",
    "MCPError",
    "MCPCapabilities",
    "MCPClientInfo",
    "MCPServerInfo",
    "MCPToolSchema",
    "MCPTool",
    "MCPResourceTemplate",
    "MCPResourceContent",
    "MCPPromptArgument",
    "MCPPromptMessage",
    "MCPPrompt",
    "MCPLogLevel",
    "MCPLogEntry",
    "MCPMessageValidator",
    "MCPMessageBuilder",
    "MCPClient",
    "ConnectionState",
    "TransportType",
    "ClientConfig",
    "ConnectionStats",
    "MCPClientError",
    "MCPServer",
    "ServerState",
    "ServerConfig",
    "ClientConnection",
    "ServerStats",
    "MCPServerError",
    "MCPManager",
    "ManagerState",
    "ConnectionType",
    "EndpointConfig",
    "ManagerConfig",
    "EndpointStatus",
    "ManagerError",
]

# 便利函数
def create_tool_ecosystem(config: dict = None) -> dict:
    """
    创建完整的工具生态系统
    
    Args:
        config: 配置字典（可选）
    
    Returns:
        dict: 包含所有组件的字典
    """
    from .tool_config import ConfigManager, ConfigScope
    from .tool_security import SecurityConfig
    from .tool_cache import CacheConfig
    from .tool_monitor import MonitorConfig
    from .tool_plugin import PluginManagerConfig
    
    # 默认配置
    default_config = {
        'security': SecurityConfig(),
        'cache': CacheConfig(),
        'monitor': MonitorConfig(),
        'plugin': PluginManagerConfig()
    }
    
    if config:
        default_config.update(config)
    
    # 创建组件
    components = {
        'config_manager': ConfigManager(),
        'tool_registry': ToolRegistry(),
        'security_manager': SecurityManager(default_config['security']),
        'cache': ToolCache(default_config['cache']),
        'monitor': ToolMonitor(default_config['monitor']),
        'plugin_manager': PluginManager(default_config['plugin']),
        'executor': ToolExecutor(),
        'api': ToolAPI()
    }
    
    # 设置全局实例
    set_global_tool_registry(components['tool_registry'])
    set_global_security_manager(components['security_manager'])
    set_global_plugin_manager(components['plugin_manager'])
    
    return components


def get_tool_ecosystem() -> dict:
    """
    获取当前的工具生态系统
    
    Returns:
        dict: 包含所有全局组件的字典
    """
    return {
        'tool_registry': get_global_tool_registry(),
        'security_manager': get_global_security_manager(),
        'plugin_manager': get_global_plugin_manager()
    }


def initialize_tools(config_file: str = None) -> dict:
    """
    从配置文件初始化工具系统
    
    Args:
        config_file: 配置文件路径（可选）
    
    Returns:
        dict: 初始化的组件字典
    """
    config = {}
    
    if config_file:
        try:
            import json
            import yaml
            
            with open(config_file, 'r', encoding='utf-8') as f:
                if config_file.endswith('.json'):
                    config = json.load(f)
                elif config_file.endswith(('.yml', '.yaml')):
                    config = yaml.safe_load(f)
        except Exception as e:
            print(f"加载配置文件失败: {e}")
    
    return create_tool_ecosystem(config)


# 模块级别的初始化
def _initialize_logging():
    """
    初始化日志配置
    """
    import logging
    
    # 设置默认日志格式
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # 设置工具包日志级别
    logger = logging.getLogger('adk.tools')
    logger.setLevel(logging.INFO)


# 执行模块初始化
_initialize_logging()

# 模块信息
print(f"A2A工具包 v{__version__} 已加载")
print(f"作者: {__author__}")
print(f"描述: {__description__}")