#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A2A多智能体系统 工具插件系统

提供动态加载、管理和扩展工具功能的插件系统
"""

import asyncio
import importlib
import importlib.util
import inspect
import json
import logging
import os
import sys
import threading
import time
import zipfile
from typing import Dict, List, Any, Optional, Union, Callable, Type, Set, Tuple
from dataclasses import dataclass, field, asdict
from enum import Enum
from datetime import datetime, timedelta
from abc import ABC, abstractmethod
from pathlib import Path
import hashlib
import shutil
import tempfile

# Google ADK imports
from google.ai.generativelanguage import FunctionDeclaration, Schema, Type as SchemaType

from .base_tool import (
    BaseTool, ToolConfig, ToolResult, ToolError, ToolStatus,
    ToolExecutionContext, ToolPermission
)


class PluginStatus(Enum):
    """插件状态枚举"""
    UNKNOWN = "unknown"          # 未知状态
    LOADING = "loading"          # 加载中
    LOADED = "loaded"            # 已加载
    ACTIVE = "active"            # 活跃
    INACTIVE = "inactive"        # 非活跃
    ERROR = "error"              # 错误
    UNLOADING = "unloading"      # 卸载中
    UNLOADED = "unloaded"        # 已卸载


class PluginType(Enum):
    """插件类型枚举"""
    TOOL = "tool"                # 工具插件
    EXTENSION = "extension"      # 扩展插件
    MIDDLEWARE = "middleware"    # 中间件插件
    PROVIDER = "provider"        # 提供者插件
    ADAPTER = "adapter"          # 适配器插件
    FILTER = "filter"            # 过滤器插件
    VALIDATOR = "validator"      # 验证器插件
    TRANSFORMER = "transformer"  # 转换器插件


class PluginPriority(Enum):
    """插件优先级枚举"""
    LOWEST = 0
    LOW = 25
    NORMAL = 50
    HIGH = 75
    HIGHEST = 100


@dataclass
class PluginDependency:
    """插件依赖"""
    name: str
    version: Optional[str] = None
    optional: bool = False
    min_version: Optional[str] = None
    max_version: Optional[str] = None
    source: Optional[str] = None  # 依赖来源（pip, conda, git等）


@dataclass
class PluginMetadata:
    """插件元数据"""
    name: str
    version: str
    description: str
    author: str
    email: Optional[str] = None
    license: Optional[str] = None
    homepage: Optional[str] = None
    repository: Optional[str] = None
    keywords: List[str] = field(default_factory=list)
    category: Optional[str] = None
    plugin_type: PluginType = PluginType.TOOL
    priority: PluginPriority = PluginPriority.NORMAL
    dependencies: List[PluginDependency] = field(default_factory=list)
    python_requires: Optional[str] = None
    platform: List[str] = field(default_factory=list)
    entry_point: str = "main"
    config_schema: Optional[Dict[str, Any]] = None
    permissions: List[str] = field(default_factory=list)
    api_version: str = "1.0"
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class PluginConfig:
    """插件配置"""
    enabled: bool = True
    auto_start: bool = True
    config: Dict[str, Any] = field(default_factory=dict)
    environment: Dict[str, str] = field(default_factory=dict)
    resources: Dict[str, Any] = field(default_factory=dict)
    limits: Dict[str, Any] = field(default_factory=dict)
    security: Dict[str, Any] = field(default_factory=dict)
    logging: Dict[str, Any] = field(default_factory=dict)
    monitoring: Dict[str, Any] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class PluginInfo:
    """插件信息"""
    id: str
    metadata: PluginMetadata
    config: PluginConfig
    status: PluginStatus = PluginStatus.UNKNOWN
    module_path: Optional[str] = None
    file_path: Optional[str] = None
    checksum: Optional[str] = None
    size: Optional[int] = None
    load_time: Optional[datetime] = None
    last_used: Optional[datetime] = None
    usage_count: int = 0
    error_count: int = 0
    last_error: Optional[str] = None
    instance: Optional[Any] = None
    hooks: Dict[str, List[Callable]] = field(default_factory=dict)
    stats: Dict[str, Any] = field(default_factory=dict)


@dataclass
class PluginEvent:
    """插件事件"""
    id: str
    plugin_id: str
    event_type: str
    data: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.now)
    source: Optional[str] = None
    target: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class PluginManagerConfig:
    """插件管理器配置"""
    # 基础配置
    plugin_directories: List[str] = field(default_factory=list)
    auto_discover: bool = True
    auto_load: bool = True
    auto_reload: bool = False
    reload_interval: int = 60  # 重载检查间隔（秒）
    
    # 安全配置
    enable_security: bool = True
    allowed_modules: List[str] = field(default_factory=list)
    blocked_modules: List[str] = field(default_factory=list)
    sandbox_mode: bool = False
    max_memory_usage: int = 100 * 1024 * 1024  # 100MB
    max_execution_time: int = 30  # 30秒
    
    # 依赖管理
    auto_install_dependencies: bool = False
    dependency_cache_dir: Optional[str] = None
    pip_index_url: Optional[str] = None
    
    # 存储配置
    storage_backend: str = "file"  # file, database, memory
    storage_config: Dict[str, Any] = field(default_factory=dict)
    cache_enabled: bool = True
    cache_ttl: int = 3600  # 缓存TTL（秒）
    
    # 日志配置
    log_level: str = "INFO"
    log_file: Optional[str] = None
    log_format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    # 监控配置
    enable_monitoring: bool = True
    monitoring_interval: int = 30  # 监控间隔（秒）
    health_check_interval: int = 60  # 健康检查间隔（秒）
    
    # 事件配置
    enable_events: bool = True
    event_queue_size: int = 1000
    event_retention: int = 24  # 事件保留时间（小时）


class PluginError(Exception):
    """插件异常"""
    
    def __init__(self, message: str, plugin_id: Optional[str] = None,
                 error_code: Optional[str] = None, details: Optional[Dict[str, Any]] = None):
        super().__init__(message)
        self.plugin_id = plugin_id
        self.error_code = error_code
        self.details = details or {}


class IPlugin(ABC):
    """插件接口"""
    
    @abstractmethod
    def get_metadata(self) -> PluginMetadata:
        """获取插件元数据"""
        pass
    
    @abstractmethod
    async def initialize(self, config: Dict[str, Any]) -> None:
        """初始化插件"""
        pass
    
    @abstractmethod
    async def start(self) -> None:
        """启动插件"""
        pass
    
    @abstractmethod
    async def stop(self) -> None:
        """停止插件"""
        pass
    
    @abstractmethod
    async def cleanup(self) -> None:
        """清理插件"""
        pass
    
    def get_status(self) -> PluginStatus:
        """获取插件状态"""
        return PluginStatus.UNKNOWN
    
    def get_health(self) -> Dict[str, Any]:
        """获取插件健康状态"""
        return {'status': 'unknown'}
    
    def get_stats(self) -> Dict[str, Any]:
        """获取插件统计信息"""
        return {}


class IPluginLoader(ABC):
    """插件加载器接口"""
    
    @abstractmethod
    def can_load(self, file_path: str) -> bool:
        """检查是否可以加载指定文件"""
        pass
    
    @abstractmethod
    async def load_plugin(self, file_path: str) -> PluginInfo:
        """加载插件"""
        pass
    
    @abstractmethod
    async def unload_plugin(self, plugin_info: PluginInfo) -> None:
        """卸载插件"""
        pass


class PythonPluginLoader(IPluginLoader):
    """Python插件加载器"""
    
    def __init__(self):
        """
        初始化Python插件加载器
        """
        self.logger = logging.getLogger(self.__class__.__name__)
        self.loaded_modules: Dict[str, Any] = {}
    
    def can_load(self, file_path: str) -> bool:
        """检查是否可以加载指定文件"""
        return file_path.endswith('.py') or file_path.endswith('.zip')
    
    async def load_plugin(self, file_path: str) -> PluginInfo:
        """加载插件"""
        try:
            # 计算文件校验和
            checksum = self._calculate_checksum(file_path)
            file_size = os.path.getsize(file_path)
            
            # 加载模块
            if file_path.endswith('.zip'):
                module = await self._load_zip_plugin(file_path)
            else:
                module = await self._load_python_plugin(file_path)
            
            # 获取插件实例
            plugin_instance = await self._create_plugin_instance(module)
            
            # 获取元数据
            metadata = plugin_instance.get_metadata()
            
            # 创建插件信息
            plugin_info = PluginInfo(
                id=metadata.name,
                metadata=metadata,
                config=PluginConfig(),
                status=PluginStatus.LOADED,
                module_path=module.__name__,
                file_path=file_path,
                checksum=checksum,
                size=file_size,
                load_time=datetime.now(),
                instance=plugin_instance
            )
            
            # 存储模块引用
            self.loaded_modules[plugin_info.id] = module
            
            self.logger.info(f"插件加载成功: {metadata.name} v{metadata.version}")
            return plugin_info
            
        except Exception as e:
            raise PluginError(
                f"加载插件失败: {e}",
                error_code="LOAD_FAILED",
                details={'file_path': file_path}
            )
    
    async def _load_python_plugin(self, file_path: str) -> Any:
        """加载Python插件文件"""
        try:
            # 生成模块名
            module_name = f"plugin_{int(time.time() * 1000000)}"
            
            # 加载模块
            spec = importlib.util.spec_from_file_location(module_name, file_path)
            if not spec or not spec.loader:
                raise PluginError("无法创建模块规范")
            
            module = importlib.util.module_from_spec(spec)
            sys.modules[module_name] = module
            spec.loader.exec_module(module)
            
            return module
            
        except Exception as e:
            raise PluginError(f"加载Python模块失败: {e}")
    
    async def _load_zip_plugin(self, file_path: str) -> Any:
        """加载ZIP插件包"""
        try:
            # 创建临时目录
            temp_dir = tempfile.mkdtemp()
            
            try:
                # 解压ZIP文件
                with zipfile.ZipFile(file_path, 'r') as zip_file:
                    zip_file.extractall(temp_dir)
                
                # 查找主模块文件
                main_file = None
                for root, dirs, files in os.walk(temp_dir):
                    for file in files:
                        if file == '__init__.py' or file == 'main.py':
                            main_file = os.path.join(root, file)
                            break
                    if main_file:
                        break
                
                if not main_file:
                    raise PluginError("ZIP插件包中未找到主模块文件")
                
                # 添加到Python路径
                if temp_dir not in sys.path:
                    sys.path.insert(0, temp_dir)
                
                # 加载模块
                return await self._load_python_plugin(main_file)
                
            finally:
                # 清理临时目录
                try:
                    shutil.rmtree(temp_dir)
                except Exception as e:
                    self.logger.warning(f"清理临时目录失败: {e}")
                
        except Exception as e:
            raise PluginError(f"加载ZIP插件包失败: {e}")
    
    async def _create_plugin_instance(self, module: Any) -> IPlugin:
        """创建插件实例"""
        try:
            # 查找插件类
            plugin_class = None
            
            # 首先查找实现了IPlugin接口的类
            for name in dir(module):
                obj = getattr(module, name)
                if (inspect.isclass(obj) and 
                    issubclass(obj, IPlugin) and 
                    obj != IPlugin):
                    plugin_class = obj
                    break
            
            # 如果没找到，查找名为Plugin的类
            if not plugin_class and hasattr(module, 'Plugin'):
                plugin_class = module.Plugin
            
            # 如果还没找到，查找主函数
            if not plugin_class and hasattr(module, 'main'):
                # 创建一个包装类
                class FunctionPlugin(IPlugin):
                    def __init__(self, func):
                        self.func = func
                    
                    def get_metadata(self) -> PluginMetadata:
                        return PluginMetadata(
                            name=getattr(module, '__name__', 'unknown'),
                            version="1.0.0",
                            description=getattr(module, '__doc__', ''),
                            author="unknown"
                        )
                    
                    async def initialize(self, config: Dict[str, Any]) -> None:
                        pass
                    
                    async def start(self) -> None:
                        pass
                    
                    async def stop(self) -> None:
                        pass
                    
                    async def cleanup(self) -> None:
                        pass
                
                plugin_class = FunctionPlugin
                return plugin_class(module.main)
            
            if not plugin_class:
                raise PluginError("模块中未找到有效的插件类")
            
            # 创建实例
            return plugin_class()
            
        except Exception as e:
            raise PluginError(f"创建插件实例失败: {e}")
    
    async def unload_plugin(self, plugin_info: PluginInfo) -> None:
        """卸载插件"""
        try:
            # 停止插件
            if plugin_info.instance:
                await plugin_info.instance.stop()
                await plugin_info.instance.cleanup()
            
            # 移除模块引用
            if plugin_info.id in self.loaded_modules:
                module = self.loaded_modules[plugin_info.id]
                
                # 从sys.modules中移除
                if hasattr(module, '__name__') and module.__name__ in sys.modules:
                    del sys.modules[module.__name__]
                
                del self.loaded_modules[plugin_info.id]
            
            plugin_info.status = PluginStatus.UNLOADED
            self.logger.info(f"插件卸载成功: {plugin_info.metadata.name}")
            
        except Exception as e:
            raise PluginError(
                f"卸载插件失败: {e}",
                plugin_id=plugin_info.id,
                error_code="UNLOAD_FAILED"
            )
    
    def _calculate_checksum(self, file_path: str) -> str:
        """计算文件校验和"""
        try:
            hash_md5 = hashlib.md5()
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception:
            return ""


class PluginDiscovery:
    """插件发现器"""
    
    def __init__(self, config: PluginManagerConfig):
        """
        初始化插件发现器
        
        Args:
            config: 插件管理器配置
        """
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
        self.discovered_plugins: Dict[str, str] = {}  # plugin_id -> file_path
    
    async def discover_plugins(self) -> List[str]:
        """
        发现插件
        
        Returns:
            List[str]: 插件文件路径列表
        """
        plugin_files = []
        
        for directory in self.config.plugin_directories:
            if not os.path.exists(directory):
                self.logger.warning(f"插件目录不存在: {directory}")
                continue
            
            try:
                # 递归搜索插件文件
                for root, dirs, files in os.walk(directory):
                    for file in files:
                        file_path = os.path.join(root, file)
                        
                        # 检查文件扩展名
                        if self._is_plugin_file(file_path):
                            plugin_files.append(file_path)
                            self.logger.debug(f"发现插件文件: {file_path}")
                
            except Exception as e:
                self.logger.error(f"搜索插件目录失败 {directory}: {e}")
        
        return plugin_files
    
    def _is_plugin_file(self, file_path: str) -> bool:
        """
        检查是否为插件文件
        
        Args:
            file_path: 文件路径
        
        Returns:
            bool: 是否为插件文件
        """
        # 检查文件扩展名
        valid_extensions = ['.py', '.zip']
        if not any(file_path.endswith(ext) for ext in valid_extensions):
            return False
        
        # 排除特殊文件
        filename = os.path.basename(file_path)
        if filename.startswith('__') or filename.startswith('.'):
            return False
        
        # 检查是否为测试文件
        if 'test' in filename.lower():
            return False
        
        return True
    
    async def watch_directories(self, callback: Callable[[str, str], None]) -> None:
        """
        监控插件目录变化
        
        Args:
            callback: 变化回调函数 (action, file_path)
        """
        try:
            import watchdog
            from watchdog.observers import Observer
            from watchdog.events import FileSystemEventHandler
            
            class PluginEventHandler(FileSystemEventHandler):
                def __init__(self, discovery):
                    self.discovery = discovery
                
                def on_created(self, event):
                    if not event.is_directory and self.discovery._is_plugin_file(event.src_path):
                        asyncio.create_task(callback('created', event.src_path))
                
                def on_modified(self, event):
                    if not event.is_directory and self.discovery._is_plugin_file(event.src_path):
                        asyncio.create_task(callback('modified', event.src_path))
                
                def on_deleted(self, event):
                    if not event.is_directory and self.discovery._is_plugin_file(event.src_path):
                        asyncio.create_task(callback('deleted', event.src_path))
            
            observer = Observer()
            event_handler = PluginEventHandler(self)
            
            for directory in self.config.plugin_directories:
                if os.path.exists(directory):
                    observer.schedule(event_handler, directory, recursive=True)
            
            observer.start()
            self.logger.info("插件目录监控已启动")
            
            try:
                while True:
                    await asyncio.sleep(1)
            except KeyboardInterrupt:
                observer.stop()
            
            observer.join()
            
        except ImportError:
            self.logger.warning("watchdog库未安装，无法监控插件目录变化")
        except Exception as e:
            self.logger.error(f"监控插件目录失败: {e}")


class PluginManager:
    """插件管理器
    
    提供动态加载、管理和扩展工具功能的插件系统
    """
    
    def __init__(self, config: PluginManagerConfig):
        """
        初始化插件管理器
        
        Args:
            config: 插件管理器配置
        """
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
        self.logger.setLevel(getattr(logging, config.log_level.upper()))
        
        # 插件存储
        self.plugins: Dict[str, PluginInfo] = {}
        self.plugin_types: Dict[PluginType, List[str]] = {}
        
        # 组件
        self.loaders: List[IPluginLoader] = [PythonPluginLoader()]
        self.discovery = PluginDiscovery(config)
        
        # 事件系统
        self.events: List[PluginEvent] = []
        self.event_handlers: Dict[str, List[Callable]] = {}
        
        # 钩子系统
        self.hooks: Dict[str, List[Callable]] = {}
        
        # 状态
        self.is_running = False
        self.start_time: Optional[datetime] = None
        
        # 锁
        self.lock = threading.RLock()
        
        # 监控任务
        self.monitor_task: Optional[asyncio.Task] = None
        self.reload_task: Optional[asyncio.Task] = None
    
    async def start(self) -> None:
        """
        启动插件管理器
        """
        if self.is_running:
            return
        
        try:
            self.logger.info("启动插件管理器...")
            
            # 发现插件
            if self.config.auto_discover:
                await self.discover_plugins()
            
            # 自动加载插件
            if self.config.auto_load:
                await self.load_all_plugins()
            
            # 启动监控
            if self.config.enable_monitoring:
                self.monitor_task = asyncio.create_task(self._monitor_loop())
            
            # 启动自动重载
            if self.config.auto_reload:
                self.reload_task = asyncio.create_task(self._reload_loop())
            
            self.is_running = True
            self.start_time = datetime.now()
            
            await self._emit_event("manager_started", {})
            self.logger.info("插件管理器启动完成")
            
        except Exception as e:
            self.logger.error(f"启动插件管理器失败: {e}")
            raise PluginError(f"启动插件管理器失败: {e}")
    
    async def stop(self) -> None:
        """
        停止插件管理器
        """
        if not self.is_running:
            return
        
        try:
            self.logger.info("停止插件管理器...")
            
            # 停止监控任务
            if self.monitor_task:
                self.monitor_task.cancel()
                try:
                    await self.monitor_task
                except asyncio.CancelledError:
                    pass
            
            if self.reload_task:
                self.reload_task.cancel()
                try:
                    await self.reload_task
                except asyncio.CancelledError:
                    pass
            
            # 卸载所有插件
            await self.unload_all_plugins()
            
            self.is_running = False
            
            await self._emit_event("manager_stopped", {})
            self.logger.info("插件管理器停止完成")
            
        except Exception as e:
            self.logger.error(f"停止插件管理器失败: {e}")
            raise PluginError(f"停止插件管理器失败: {e}")
    
    async def discover_plugins(self) -> List[str]:
        """
        发现插件
        
        Returns:
            List[str]: 发现的插件文件路径列表
        """
        try:
            plugin_files = await self.discovery.discover_plugins()
            self.logger.info(f"发现 {len(plugin_files)} 个插件文件")
            return plugin_files
        except Exception as e:
            self.logger.error(f"发现插件失败: {e}")
            return []
    
    async def load_plugin(self, file_path: str, config: Optional[PluginConfig] = None) -> str:
        """
        加载插件
        
        Args:
            file_path: 插件文件路径
            config: 插件配置（可选）
        
        Returns:
            str: 插件ID
        """
        try:
            # 查找合适的加载器
            loader = None
            for l in self.loaders:
                if l.can_load(file_path):
                    loader = l
                    break
            
            if not loader:
                raise PluginError(f"没有找到适合的加载器: {file_path}")
            
            # 加载插件
            plugin_info = await loader.load_plugin(file_path)
            
            # 应用配置
            if config:
                plugin_info.config = config
            
            # 检查依赖
            await self._check_dependencies(plugin_info)
            
            # 初始化插件
            if plugin_info.instance:
                await plugin_info.instance.initialize(plugin_info.config.config)
            
            # 存储插件信息
            with self.lock:
                self.plugins[plugin_info.id] = plugin_info
                
                # 按类型分类
                plugin_type = plugin_info.metadata.plugin_type
                if plugin_type not in self.plugin_types:
                    self.plugin_types[plugin_type] = []
                self.plugin_types[plugin_type].append(plugin_info.id)
            
            # 自动启动
            if plugin_info.config.auto_start:
                await self.start_plugin(plugin_info.id)
            
            await self._emit_event("plugin_loaded", {
                'plugin_id': plugin_info.id,
                'file_path': file_path
            })
            
            self.logger.info(f"插件加载成功: {plugin_info.metadata.name} ({plugin_info.id})")
            return plugin_info.id
            
        except Exception as e:
            self.logger.error(f"加载插件失败 {file_path}: {e}")
            raise PluginError(f"加载插件失败: {e}", details={'file_path': file_path})
    
    async def unload_plugin(self, plugin_id: str) -> None:
        """
        卸载插件
        
        Args:
            plugin_id: 插件ID
        """
        try:
            with self.lock:
                if plugin_id not in self.plugins:
                    raise PluginError(f"插件不存在: {plugin_id}")
                
                plugin_info = self.plugins[plugin_id]
            
            # 停止插件
            if plugin_info.status == PluginStatus.ACTIVE:
                await self.stop_plugin(plugin_id)
            
            # 查找加载器并卸载
            for loader in self.loaders:
                if loader.can_load(plugin_info.file_path or ""):
                    await loader.unload_plugin(plugin_info)
                    break
            
            # 移除插件信息
            with self.lock:
                del self.plugins[plugin_id]
                
                # 从类型分类中移除
                plugin_type = plugin_info.metadata.plugin_type
                if plugin_type in self.plugin_types:
                    if plugin_id in self.plugin_types[plugin_type]:
                        self.plugin_types[plugin_type].remove(plugin_id)
            
            await self._emit_event("plugin_unloaded", {
                'plugin_id': plugin_id
            })
            
            self.logger.info(f"插件卸载成功: {plugin_info.metadata.name} ({plugin_id})")
            
        except Exception as e:
            self.logger.error(f"卸载插件失败 {plugin_id}: {e}")
            raise PluginError(f"卸载插件失败: {e}", plugin_id=plugin_id)
    
    async def start_plugin(self, plugin_id: str) -> None:
        """
        启动插件
        
        Args:
            plugin_id: 插件ID
        """
        try:
            with self.lock:
                if plugin_id not in self.plugins:
                    raise PluginError(f"插件不存在: {plugin_id}")
                
                plugin_info = self.plugins[plugin_id]
            
            if plugin_info.status == PluginStatus.ACTIVE:
                return
            
            if not plugin_info.config.enabled:
                raise PluginError(f"插件已禁用: {plugin_id}")
            
            # 启动插件
            if plugin_info.instance:
                await plugin_info.instance.start()
            
            plugin_info.status = PluginStatus.ACTIVE
            plugin_info.last_used = datetime.now()
            
            await self._emit_event("plugin_started", {
                'plugin_id': plugin_id
            })
            
            self.logger.info(f"插件启动成功: {plugin_info.metadata.name} ({plugin_id})")
            
        except Exception as e:
            self.logger.error(f"启动插件失败 {plugin_id}: {e}")
            
            # 更新错误信息
            if plugin_id in self.plugins:
                self.plugins[plugin_id].status = PluginStatus.ERROR
                self.plugins[plugin_id].last_error = str(e)
                self.plugins[plugin_id].error_count += 1
            
            raise PluginError(f"启动插件失败: {e}", plugin_id=plugin_id)
    
    async def stop_plugin(self, plugin_id: str) -> None:
        """
        停止插件
        
        Args:
            plugin_id: 插件ID
        """
        try:
            with self.lock:
                if plugin_id not in self.plugins:
                    raise PluginError(f"插件不存在: {plugin_id}")
                
                plugin_info = self.plugins[plugin_id]
            
            if plugin_info.status != PluginStatus.ACTIVE:
                return
            
            # 停止插件
            if plugin_info.instance:
                await plugin_info.instance.stop()
            
            plugin_info.status = PluginStatus.INACTIVE
            
            await self._emit_event("plugin_stopped", {
                'plugin_id': plugin_id
            })
            
            self.logger.info(f"插件停止成功: {plugin_info.metadata.name} ({plugin_id})")
            
        except Exception as e:
            self.logger.error(f"停止插件失败 {plugin_id}: {e}")
            
            # 更新错误信息
            if plugin_id in self.plugins:
                self.plugins[plugin_id].status = PluginStatus.ERROR
                self.plugins[plugin_id].last_error = str(e)
                self.plugins[plugin_id].error_count += 1
            
            raise PluginError(f"停止插件失败: {e}", plugin_id=plugin_id)
    
    async def reload_plugin(self, plugin_id: str) -> None:
        """
        重载插件
        
        Args:
            plugin_id: 插件ID
        """
        try:
            with self.lock:
                if plugin_id not in self.plugins:
                    raise PluginError(f"插件不存在: {plugin_id}")
                
                plugin_info = self.plugins[plugin_id]
                file_path = plugin_info.file_path
                config = plugin_info.config
            
            # 卸载插件
            await self.unload_plugin(plugin_id)
            
            # 重新加载插件
            if file_path:
                new_plugin_id = await self.load_plugin(file_path, config)
                
                await self._emit_event("plugin_reloaded", {
                    'old_plugin_id': plugin_id,
                    'new_plugin_id': new_plugin_id
                })
                
                self.logger.info(f"插件重载成功: {plugin_id} -> {new_plugin_id}")
            
        except Exception as e:
            self.logger.error(f"重载插件失败 {plugin_id}: {e}")
            raise PluginError(f"重载插件失败: {e}", plugin_id=plugin_id)
    
    async def load_all_plugins(self) -> List[str]:
        """
        加载所有发现的插件
        
        Returns:
            List[str]: 成功加载的插件ID列表
        """
        plugin_files = await self.discover_plugins()
        loaded_plugins = []
        
        for file_path in plugin_files:
            try:
                plugin_id = await self.load_plugin(file_path)
                loaded_plugins.append(plugin_id)
            except Exception as e:
                self.logger.error(f"加载插件失败 {file_path}: {e}")
        
        self.logger.info(f"成功加载 {len(loaded_plugins)} 个插件")
        return loaded_plugins
    
    async def unload_all_plugins(self) -> None:
        """
        卸载所有插件
        """
        plugin_ids = list(self.plugins.keys())
        
        for plugin_id in plugin_ids:
            try:
                await self.unload_plugin(plugin_id)
            except Exception as e:
                self.logger.error(f"卸载插件失败 {plugin_id}: {e}")
        
        self.logger.info("所有插件已卸载")
    
    async def _check_dependencies(self, plugin_info: PluginInfo) -> None:
        """
        检查插件依赖
        
        Args:
            plugin_info: 插件信息
        """
        for dependency in plugin_info.metadata.dependencies:
            try:
                # 检查Python包依赖
                if dependency.source in [None, 'pip']:
                    try:
                        importlib.import_module(dependency.name)
                    except ImportError:
                        if not dependency.optional:
                            raise PluginError(
                                f"缺少必需的依赖: {dependency.name}",
                                plugin_id=plugin_info.id,
                                error_code="MISSING_DEPENDENCY"
                            )
                        else:
                            self.logger.warning(
                                f"缺少可选依赖: {dependency.name} (插件: {plugin_info.id})"
                            )
                
                # 检查插件依赖
                elif dependency.source == 'plugin':
                    if dependency.name not in self.plugins:
                        if not dependency.optional:
                            raise PluginError(
                                f"缺少必需的插件依赖: {dependency.name}",
                                plugin_id=plugin_info.id,
                                error_code="MISSING_PLUGIN_DEPENDENCY"
                            )
                        else:
                            self.logger.warning(
                                f"缺少可选插件依赖: {dependency.name} (插件: {plugin_info.id})"
                            )
                
            except Exception as e:
                self.logger.error(f"检查依赖失败 {dependency.name}: {e}")
                if not dependency.optional:
                    raise
    
    def get_plugin(self, plugin_id: str) -> Optional[PluginInfo]:
        """
        获取插件信息
        
        Args:
            plugin_id: 插件ID
        
        Returns:
            Optional[PluginInfo]: 插件信息
        """
        return self.plugins.get(plugin_id)
    
    def list_plugins(self, plugin_type: Optional[PluginType] = None,
                    status: Optional[PluginStatus] = None) -> List[PluginInfo]:
        """
        列出插件
        
        Args:
            plugin_type: 插件类型过滤（可选）
            status: 状态过滤（可选）
        
        Returns:
            List[PluginInfo]: 插件信息列表
        """
        plugins = list(self.plugins.values())
        
        # 按类型过滤
        if plugin_type:
            plugins = [p for p in plugins if p.metadata.plugin_type == plugin_type]
        
        # 按状态过滤
        if status:
            plugins = [p for p in plugins if p.status == status]
        
        return plugins
    
    def get_plugin_by_name(self, name: str) -> Optional[PluginInfo]:
        """
        根据名称获取插件
        
        Args:
            name: 插件名称
        
        Returns:
            Optional[PluginInfo]: 插件信息
        """
        for plugin in self.plugins.values():
            if plugin.metadata.name == name:
                return plugin
        return None
    
    async def call_plugin(self, plugin_id: str, method: str, *args, **kwargs) -> Any:
        """
        调用插件方法
        
        Args:
            plugin_id: 插件ID
            method: 方法名
            *args: 位置参数
            **kwargs: 关键字参数
        
        Returns:
            Any: 方法返回值
        """
        try:
            plugin_info = self.get_plugin(plugin_id)
            if not plugin_info:
                raise PluginError(f"插件不存在: {plugin_id}")
            
            if plugin_info.status != PluginStatus.ACTIVE:
                raise PluginError(f"插件未激活: {plugin_id}")
            
            if not plugin_info.instance:
                raise PluginError(f"插件实例不存在: {plugin_id}")
            
            # 检查方法是否存在
            if not hasattr(plugin_info.instance, method):
                raise PluginError(f"插件方法不存在: {method}")
            
            # 调用方法
            func = getattr(plugin_info.instance, method)
            if asyncio.iscoroutinefunction(func):
                result = await func(*args, **kwargs)
            else:
                result = func(*args, **kwargs)
            
            # 更新使用统计
            plugin_info.usage_count += 1
            plugin_info.last_used = datetime.now()
            
            return result
            
        except Exception as e:
            self.logger.error(f"调用插件方法失败 {plugin_id}.{method}: {e}")
            
            # 更新错误统计
            if plugin_id in self.plugins:
                self.plugins[plugin_id].error_count += 1
                self.plugins[plugin_id].last_error = str(e)
            
            raise PluginError(
                f"调用插件方法失败: {e}",
                plugin_id=plugin_id,
                error_code="CALL_FAILED"
            )
    
    def register_hook(self, hook_name: str, callback: Callable) -> None:
        """
        注册钩子
        
        Args:
            hook_name: 钩子名称
            callback: 回调函数
        """
        if hook_name not in self.hooks:
            self.hooks[hook_name] = []
        self.hooks[hook_name].append(callback)
        
        self.logger.debug(f"钩子已注册: {hook_name}")
    
    def unregister_hook(self, hook_name: str, callback: Callable) -> None:
        """
        注销钩子
        
        Args:
            hook_name: 钩子名称
            callback: 回调函数
        """
        if hook_name in self.hooks and callback in self.hooks[hook_name]:
            self.hooks[hook_name].remove(callback)
            
            if not self.hooks[hook_name]:
                del self.hooks[hook_name]
            
            self.logger.debug(f"钩子已注销: {hook_name}")
    
    async def call_hook(self, hook_name: str, *args, **kwargs) -> List[Any]:
        """
        调用钩子
        
        Args:
            hook_name: 钩子名称
            *args: 位置参数
            **kwargs: 关键字参数
        
        Returns:
            List[Any]: 钩子返回值列表
        """
        results = []
        
        if hook_name in self.hooks:
            for callback in self.hooks[hook_name]:
                try:
                    if asyncio.iscoroutinefunction(callback):
                        result = await callback(*args, **kwargs)
                    else:
                        result = callback(*args, **kwargs)
                    results.append(result)
                except Exception as e:
                    self.logger.error(f"调用钩子失败 {hook_name}: {e}")
        
        return results
    
    def register_event_handler(self, event_type: str, handler: Callable) -> None:
        """
        注册事件处理器
        
        Args:
            event_type: 事件类型
            handler: 处理器函数
        """
        if event_type not in self.event_handlers:
            self.event_handlers[event_type] = []
        self.event_handlers[event_type].append(handler)
        
        self.logger.debug(f"事件处理器已注册: {event_type}")
    
    def unregister_event_handler(self, event_type: str, handler: Callable) -> None:
        """
        注销事件处理器
        
        Args:
            event_type: 事件类型
            handler: 处理器函数
        """
        if event_type in self.event_handlers and handler in self.event_handlers[event_type]:
            self.event_handlers[event_type].remove(handler)
            
            if not self.event_handlers[event_type]:
                del self.event_handlers[event_type]
            
            self.logger.debug(f"事件处理器已注销: {event_type}")
    
    async def _emit_event(self, event_type: str, data: Dict[str, Any]) -> None:
        """
        发出事件
        
        Args:
            event_type: 事件类型
            data: 事件数据
        """
        if not self.config.enable_events:
            return
        
        try:
            # 创建事件
            event = PluginEvent(
                id=f"event_{int(time.time() * 1000000)}",
                plugin_id="manager",
                event_type=event_type,
                data=data
            )
            
            # 存储事件
            with self.lock:
                self.events.append(event)
                
                # 清理过期事件
                cutoff_time = datetime.now() - timedelta(hours=self.config.event_retention)
                self.events = [
                    e for e in self.events
                    if e.timestamp > cutoff_time
                ]
                
                # 限制事件队列大小
                if len(self.events) > self.config.event_queue_size:
                    self.events = self.events[-self.config.event_queue_size:]
            
            # 调用事件处理器
            if event_type in self.event_handlers:
                for handler in self.event_handlers[event_type]:
                    try:
                        if asyncio.iscoroutinefunction(handler):
                            await handler(event)
                        else:
                            handler(event)
                    except Exception as e:
                        self.logger.error(f"事件处理器执行失败 {event_type}: {e}")
            
        except Exception as e:
            self.logger.error(f"发出事件失败 {event_type}: {e}")
    
    async def _monitor_loop(self) -> None:
        """
        监控循环
        """
        while self.is_running:
            try:
                await self._check_plugin_health()
                await asyncio.sleep(self.config.monitoring_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"监控循环异常: {e}")
                await asyncio.sleep(self.config.monitoring_interval)
    
    async def _reload_loop(self) -> None:
        """
        重载循环
        """
        while self.is_running:
            try:
                await self._check_plugin_changes()
                await asyncio.sleep(self.config.reload_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"重载循环异常: {e}")
                await asyncio.sleep(self.config.reload_interval)
    
    async def _check_plugin_health(self) -> None:
        """
        检查插件健康状态
        """
        for plugin_id, plugin_info in self.plugins.items():
            try:
                if plugin_info.status == PluginStatus.ACTIVE and plugin_info.instance:
                    # 获取健康状态
                    health = plugin_info.instance.get_health()
                    
                    # 检查健康状态
                    if health.get('status') == 'unhealthy':
                        self.logger.warning(f"插件健康状态异常: {plugin_id}")
                        
                        # 可以选择重启插件
                        # await self.restart_plugin(plugin_id)
                
            except Exception as e:
                self.logger.error(f"检查插件健康状态失败 {plugin_id}: {e}")
    
    async def _check_plugin_changes(self) -> None:
        """
        检查插件文件变化
        """
        for plugin_id, plugin_info in list(self.plugins.items()):
            try:
                if not plugin_info.file_path or not os.path.exists(plugin_info.file_path):
                    continue
                
                # 计算当前文件校验和
                current_checksum = self._calculate_checksum(plugin_info.file_path)
                
                # 检查是否有变化
                if current_checksum != plugin_info.checksum:
                    self.logger.info(f"检测到插件文件变化: {plugin_id}")
                    await self.reload_plugin(plugin_id)
                
            except Exception as e:
                self.logger.error(f"检查插件变化失败 {plugin_id}: {e}")
    
    def _calculate_checksum(self, file_path: str) -> str:
        """
        计算文件校验和
        
        Args:
            file_path: 文件路径
        
        Returns:
            str: 校验和
        """
        try:
            hash_md5 = hashlib.md5()
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception:
            return ""
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取插件管理器统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        with self.lock:
            # 统计插件状态
            status_stats = {}
            for status in PluginStatus:
                status_stats[status.value] = len([
                    p for p in self.plugins.values()
                    if p.status == status
                ])
            
            # 统计插件类型
            type_stats = {}
            for plugin_type in PluginType:
                type_stats[plugin_type.value] = len(
                    self.plugin_types.get(plugin_type, [])
                )
            
            # 统计使用情况
            total_usage = sum(p.usage_count for p in self.plugins.values())
            total_errors = sum(p.error_count for p in self.plugins.values())
            
            return {
                'config': asdict(self.config),
                'is_running': self.is_running,
                'start_time': self.start_time.isoformat() if self.start_time else None,
                'plugins': {
                    'total': len(self.plugins),
                    'by_status': status_stats,
                    'by_type': type_stats
                },
                'usage': {
                    'total_calls': total_usage,
                    'total_errors': total_errors,
                    'error_rate': total_errors / max(total_usage, 1)
                },
                'events': {
                    'total': len(self.events),
                    'handlers': len(self.event_handlers)
                },
                'hooks': {
                    'total': len(self.hooks)
                },
                'loaders': len(self.loaders)
            }


# 全局插件管理器实例
_global_plugin_manager: Optional[PluginManager] = None


def get_global_plugin_manager() -> Optional[PluginManager]:
    """
    获取全局插件管理器实例
    
    Returns:
        Optional[PluginManager]: 插件管理器实例
    """
    return _global_plugin_manager


def set_global_plugin_manager(manager: PluginManager) -> None:
    """
    设置全局插件管理器实例
    
    Args:
        manager: 插件管理器实例
    """
    global _global_plugin_manager
    _global_plugin_manager = manager