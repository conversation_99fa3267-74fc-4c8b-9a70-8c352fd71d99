#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A2A多智能体系统 工具缓存

提供工具执行结果的缓存功能，支持多种缓存策略和存储后端
"""

import asyncio
import hashlib
import json
import logging
import pickle
import time
import threading
from typing import Dict, List, Any, Optional, Union, Callable, Tuple
from dataclasses import dataclass, field, asdict
from enum import Enum
from datetime import datetime, timedelta
from abc import ABC, abstractmethod
import weakref

# Google ADK imports
from google.ai.generativelanguage import FunctionDeclaration, Schema, Type as SchemaType

from .base_tool import (
    BaseTool, ToolConfig, ToolResult, ToolError, ToolStatus,
    ToolExecutionContext, ToolPermission
)


class CacheStrategy(Enum):
    """缓存策略枚举"""
    LRU = "lru"          # 最近最少使用
    LFU = "lfu"          # 最少使用频率
    FIFO = "fifo"        # 先进先出
    TTL = "ttl"          # 生存时间
    ADAPTIVE = "adaptive"  # 自适应


class CacheBackend(Enum):
    """缓存后端枚举"""
    MEMORY = "memory"    # 内存缓存
    REDIS = "redis"      # Redis缓存
    FILE = "file"        # 文件缓存
    DATABASE = "database"  # 数据库缓存


class SerializationFormat(Enum):
    """序列化格式枚举"""
    JSON = "json"
    PICKLE = "pickle"
    MSGPACK = "msgpack"
    PROTOBUF = "protobuf"


@dataclass
class CacheKey:
    """缓存键"""
    tool_name: str
    method_name: str
    args_hash: str
    kwargs_hash: str
    user_id: Optional[str] = None
    context_hash: Optional[str] = None
    
    def __str__(self) -> str:
        """生成缓存键字符串"""
        parts = [self.tool_name, self.method_name, self.args_hash, self.kwargs_hash]
        if self.user_id:
            parts.append(f"user:{self.user_id}")
        if self.context_hash:
            parts.append(f"ctx:{self.context_hash}")
        return ":".join(parts)
    
    def __hash__(self) -> int:
        """生成哈希值"""
        return hash(str(self))


@dataclass
class CacheEntry:
    """缓存条目"""
    key: CacheKey
    value: Any
    created_at: datetime = field(default_factory=datetime.now)
    last_accessed: datetime = field(default_factory=datetime.now)
    access_count: int = 0
    ttl: Optional[int] = None  # 生存时间（秒）
    size: int = 0  # 条目大小（字节）
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def is_expired(self) -> bool:
        """检查是否过期"""
        if self.ttl is None:
            return False
        return (datetime.now() - self.created_at).seconds > self.ttl
    
    def touch(self) -> None:
        """更新访问时间和计数"""
        self.last_accessed = datetime.now()
        self.access_count += 1
    
    def age_seconds(self) -> int:
        """获取条目年龄（秒）"""
        return (datetime.now() - self.created_at).seconds


@dataclass
class CacheConfig:
    """缓存配置"""
    # 基础配置
    strategy: CacheStrategy = CacheStrategy.LRU
    backend: CacheBackend = CacheBackend.MEMORY
    max_size: int = 1000  # 最大条目数
    max_memory: int = 100 * 1024 * 1024  # 最大内存使用（字节）
    
    # TTL配置
    default_ttl: Optional[int] = None  # 默认TTL（秒）
    max_ttl: int = 86400  # 最大TTL（秒）
    
    # 序列化配置
    serialization_format: SerializationFormat = SerializationFormat.PICKLE
    compression_enabled: bool = False
    compression_threshold: int = 1024  # 压缩阈值（字节）
    
    # 性能配置
    enable_async: bool = True
    batch_size: int = 100
    cleanup_interval: int = 300  # 清理间隔（秒）
    
    # 持久化配置
    enable_persistence: bool = False
    persistence_file: Optional[str] = None
    persistence_interval: int = 600  # 持久化间隔（秒）
    
    # Redis配置（当backend为REDIS时）
    redis_host: str = "localhost"
    redis_port: int = 6379
    redis_db: int = 0
    redis_password: Optional[str] = None
    redis_prefix: str = "a2a:tool_cache:"
    
    # 文件缓存配置（当backend为FILE时）
    file_cache_dir: str = "./cache"
    file_cache_max_files: int = 10000
    
    # 数据库配置（当backend为DATABASE时）
    database_url: Optional[str] = None
    database_table: str = "tool_cache"
    
    # 监控配置
    enable_metrics: bool = True
    enable_logging: bool = True
    log_level: str = "INFO"


@dataclass
class CacheStats:
    """缓存统计"""
    hits: int = 0
    misses: int = 0
    evictions: int = 0
    expired: int = 0
    size: int = 0
    memory_usage: int = 0
    hit_rate: float = 0.0
    
    def update_hit_rate(self) -> None:
        """更新命中率"""
        total = self.hits + self.misses
        self.hit_rate = self.hits / total if total > 0 else 0.0


class CacheError(Exception):
    """缓存异常"""
    pass


class ICacheBackend(ABC):
    """缓存后端接口"""
    
    @abstractmethod
    async def get(self, key: str) -> Optional[bytes]:
        """获取缓存值"""
        pass
    
    @abstractmethod
    async def set(self, key: str, value: bytes, ttl: Optional[int] = None) -> bool:
        """设置缓存值"""
        pass
    
    @abstractmethod
    async def delete(self, key: str) -> bool:
        """删除缓存值"""
        pass
    
    @abstractmethod
    async def exists(self, key: str) -> bool:
        """检查键是否存在"""
        pass
    
    @abstractmethod
    async def clear(self) -> bool:
        """清空缓存"""
        pass
    
    @abstractmethod
    async def keys(self, pattern: Optional[str] = None) -> List[str]:
        """获取键列表"""
        pass
    
    @abstractmethod
    async def size(self) -> int:
        """获取缓存大小"""
        pass


class MemoryCacheBackend(ICacheBackend):
    """内存缓存后端"""
    
    def __init__(self, config: CacheConfig):
        """
        初始化内存缓存后端
        
        Args:
            config: 缓存配置
        """
        self.config = config
        self.data: Dict[str, bytes] = {}
        self.ttl_data: Dict[str, datetime] = {}
        self.lock = asyncio.Lock()
    
    async def get(self, key: str) -> Optional[bytes]:
        """获取缓存值"""
        async with self.lock:
            # 检查TTL
            if key in self.ttl_data:
                if datetime.now() > self.ttl_data[key]:
                    await self._remove_key(key)
                    return None
            
            return self.data.get(key)
    
    async def set(self, key: str, value: bytes, ttl: Optional[int] = None) -> bool:
        """设置缓存值"""
        async with self.lock:
            self.data[key] = value
            
            if ttl is not None:
                self.ttl_data[key] = datetime.now() + timedelta(seconds=ttl)
            elif key in self.ttl_data:
                del self.ttl_data[key]
            
            return True
    
    async def delete(self, key: str) -> bool:
        """删除缓存值"""
        async with self.lock:
            return await self._remove_key(key)
    
    async def exists(self, key: str) -> bool:
        """检查键是否存在"""
        return await self.get(key) is not None
    
    async def clear(self) -> bool:
        """清空缓存"""
        async with self.lock:
            self.data.clear()
            self.ttl_data.clear()
            return True
    
    async def keys(self, pattern: Optional[str] = None) -> List[str]:
        """获取键列表"""
        async with self.lock:
            if pattern is None:
                return list(self.data.keys())
            
            import fnmatch
            return [key for key in self.data.keys() if fnmatch.fnmatch(key, pattern)]
    
    async def size(self) -> int:
        """获取缓存大小"""
        return len(self.data)
    
    async def _remove_key(self, key: str) -> bool:
        """移除键"""
        removed = False
        if key in self.data:
            del self.data[key]
            removed = True
        if key in self.ttl_data:
            del self.ttl_data[key]
        return removed


class RedisCacheBackend(ICacheBackend):
    """Redis缓存后端"""
    
    def __init__(self, config: CacheConfig):
        """
        初始化Redis缓存后端
        
        Args:
            config: 缓存配置
        """
        self.config = config
        self.redis = None
        self._init_redis()
    
    def _init_redis(self) -> None:
        """初始化Redis连接"""
        try:
            import redis.asyncio as redis
            
            self.redis = redis.Redis(
                host=self.config.redis_host,
                port=self.config.redis_port,
                db=self.config.redis_db,
                password=self.config.redis_password,
                decode_responses=False
            )
        except ImportError:
            raise CacheError("Redis库未安装，请安装redis包")
    
    def _get_key(self, key: str) -> str:
        """获取带前缀的键"""
        return f"{self.config.redis_prefix}{key}"
    
    async def get(self, key: str) -> Optional[bytes]:
        """获取缓存值"""
        if not self.redis:
            return None
        
        try:
            return await self.redis.get(self._get_key(key))
        except Exception as e:
            raise CacheError(f"Redis获取失败: {e}")
    
    async def set(self, key: str, value: bytes, ttl: Optional[int] = None) -> bool:
        """设置缓存值"""
        if not self.redis:
            return False
        
        try:
            redis_key = self._get_key(key)
            if ttl is not None:
                await self.redis.setex(redis_key, ttl, value)
            else:
                await self.redis.set(redis_key, value)
            return True
        except Exception as e:
            raise CacheError(f"Redis设置失败: {e}")
    
    async def delete(self, key: str) -> bool:
        """删除缓存值"""
        if not self.redis:
            return False
        
        try:
            result = await self.redis.delete(self._get_key(key))
            return result > 0
        except Exception as e:
            raise CacheError(f"Redis删除失败: {e}")
    
    async def exists(self, key: str) -> bool:
        """检查键是否存在"""
        if not self.redis:
            return False
        
        try:
            return await self.redis.exists(self._get_key(key)) > 0
        except Exception as e:
            raise CacheError(f"Redis检查存在失败: {e}")
    
    async def clear(self) -> bool:
        """清空缓存"""
        if not self.redis:
            return False
        
        try:
            keys = await self.keys()
            if keys:
                redis_keys = [self._get_key(key) for key in keys]
                await self.redis.delete(*redis_keys)
            return True
        except Exception as e:
            raise CacheError(f"Redis清空失败: {e}")
    
    async def keys(self, pattern: Optional[str] = None) -> List[str]:
        """获取键列表"""
        if not self.redis:
            return []
        
        try:
            if pattern:
                redis_pattern = f"{self.config.redis_prefix}{pattern}"
            else:
                redis_pattern = f"{self.config.redis_prefix}*"
            
            redis_keys = await self.redis.keys(redis_pattern)
            prefix_len = len(self.config.redis_prefix)
            return [key.decode('utf-8')[prefix_len:] for key in redis_keys]
        except Exception as e:
            raise CacheError(f"Redis获取键列表失败: {e}")
    
    async def size(self) -> int:
        """获取缓存大小"""
        keys = await self.keys()
        return len(keys)


class FileCacheBackend(ICacheBackend):
    """文件缓存后端"""
    
    def __init__(self, config: CacheConfig):
        """
        初始化文件缓存后端
        
        Args:
            config: 缓存配置
        """
        self.config = config
        self.cache_dir = config.file_cache_dir
        self.lock = asyncio.Lock()
        self._ensure_cache_dir()
    
    def _ensure_cache_dir(self) -> None:
        """确保缓存目录存在"""
        import os
        os.makedirs(self.cache_dir, exist_ok=True)
    
    def _get_file_path(self, key: str) -> str:
        """获取缓存文件路径"""
        import os
        # 使用哈希避免文件名过长或包含特殊字符
        key_hash = hashlib.md5(key.encode()).hexdigest()
        return os.path.join(self.cache_dir, f"{key_hash}.cache")
    
    async def get(self, key: str) -> Optional[bytes]:
        """获取缓存值"""
        async with self.lock:
            file_path = self._get_file_path(key)
            
            try:
                import aiofiles
                async with aiofiles.open(file_path, 'rb') as f:
                    return await f.read()
            except FileNotFoundError:
                return None
            except Exception as e:
                raise CacheError(f"文件缓存读取失败: {e}")
    
    async def set(self, key: str, value: bytes, ttl: Optional[int] = None) -> bool:
        """设置缓存值"""
        async with self.lock:
            file_path = self._get_file_path(key)
            
            try:
                import aiofiles
                async with aiofiles.open(file_path, 'wb') as f:
                    await f.write(value)
                
                # 设置TTL（通过文件修改时间）
                if ttl is not None:
                    import os
                    expire_time = time.time() + ttl
                    os.utime(file_path, (expire_time, expire_time))
                
                return True
            except Exception as e:
                raise CacheError(f"文件缓存写入失败: {e}")
    
    async def delete(self, key: str) -> bool:
        """删除缓存值"""
        async with self.lock:
            file_path = self._get_file_path(key)
            
            try:
                import os
                if os.path.exists(file_path):
                    os.remove(file_path)
                    return True
                return False
            except Exception as e:
                raise CacheError(f"文件缓存删除失败: {e}")
    
    async def exists(self, key: str) -> bool:
        """检查键是否存在"""
        file_path = self._get_file_path(key)
        import os
        return os.path.exists(file_path)
    
    async def clear(self) -> bool:
        """清空缓存"""
        async with self.lock:
            try:
                import os
                import glob
                
                cache_files = glob.glob(os.path.join(self.cache_dir, "*.cache"))
                for file_path in cache_files:
                    os.remove(file_path)
                
                return True
            except Exception as e:
                raise CacheError(f"文件缓存清空失败: {e}")
    
    async def keys(self, pattern: Optional[str] = None) -> List[str]:
        """获取键列表"""
        # 文件缓存无法直接获取原始键名，返回空列表
        return []
    
    async def size(self) -> int:
        """获取缓存大小"""
        try:
            import os
            import glob
            
            cache_files = glob.glob(os.path.join(self.cache_dir, "*.cache"))
            return len(cache_files)
        except Exception:
            return 0


class CacheSerializer:
    """缓存序列化器"""
    
    def __init__(self, format: SerializationFormat, 
                 compression_enabled: bool = False,
                 compression_threshold: int = 1024):
        """
        初始化序列化器
        
        Args:
            format: 序列化格式
            compression_enabled: 是否启用压缩
            compression_threshold: 压缩阈值
        """
        self.format = format
        self.compression_enabled = compression_enabled
        self.compression_threshold = compression_threshold
    
    def serialize(self, obj: Any) -> bytes:
        """
        序列化对象
        
        Args:
            obj: 要序列化的对象
        
        Returns:
            bytes: 序列化后的字节数据
        """
        try:
            if self.format == SerializationFormat.JSON:
                data = json.dumps(obj, default=str).encode('utf-8')
            elif self.format == SerializationFormat.PICKLE:
                data = pickle.dumps(obj)
            elif self.format == SerializationFormat.MSGPACK:
                import msgpack
                data = msgpack.packb(obj)
            else:
                raise CacheError(f"不支持的序列化格式: {self.format}")
            
            # 压缩
            if (self.compression_enabled and 
                len(data) >= self.compression_threshold):
                import gzip
                data = gzip.compress(data)
                # 添加压缩标记
                data = b'\x01' + data
            else:
                # 添加未压缩标记
                data = b'\x00' + data
            
            return data
            
        except Exception as e:
            raise CacheError(f"序列化失败: {e}")
    
    def deserialize(self, data: bytes) -> Any:
        """
        反序列化对象
        
        Args:
            data: 序列化的字节数据
        
        Returns:
            Any: 反序列化后的对象
        """
        try:
            if not data:
                return None
            
            # 检查压缩标记
            is_compressed = data[0] == 1
            data = data[1:]
            
            # 解压缩
            if is_compressed:
                import gzip
                data = gzip.decompress(data)
            
            # 反序列化
            if self.format == SerializationFormat.JSON:
                return json.loads(data.decode('utf-8'))
            elif self.format == SerializationFormat.PICKLE:
                return pickle.loads(data)
            elif self.format == SerializationFormat.MSGPACK:
                import msgpack
                return msgpack.unpackb(data)
            else:
                raise CacheError(f"不支持的序列化格式: {self.format}")
                
        except Exception as e:
            raise CacheError(f"反序列化失败: {e}")


class ToolCache:
    """工具缓存
    
    提供工具执行结果的缓存功能，支持多种缓存策略和存储后端
    """
    
    def __init__(self, config: CacheConfig):
        """
        初始化工具缓存
        
        Args:
            config: 缓存配置
        """
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
        self.logger.setLevel(getattr(logging, config.log_level.upper()))
        
        # 初始化组件
        self.backend = self._create_backend()
        self.serializer = CacheSerializer(
            config.serialization_format,
            config.compression_enabled,
            config.compression_threshold
        )
        
        # 缓存条目（用于策略管理）
        self.entries: Dict[str, CacheEntry] = {}
        
        # 统计信息
        self.stats = CacheStats()
        
        # 锁
        self.lock = asyncio.Lock()
        
        # 清理任务
        self.cleanup_task: Optional[asyncio.Task] = None
        self.persistence_task: Optional[asyncio.Task] = None
        
        # 启动后台任务
        if config.cleanup_interval > 0:
            self.cleanup_task = asyncio.create_task(self._cleanup_loop())
        
        if config.enable_persistence and config.persistence_interval > 0:
            self.persistence_task = asyncio.create_task(self._persistence_loop())
    
    def _create_backend(self) -> ICacheBackend:
        """
        创建缓存后端
        
        Returns:
            ICacheBackend: 缓存后端实例
        """
        if self.config.backend == CacheBackend.MEMORY:
            return MemoryCacheBackend(self.config)
        elif self.config.backend == CacheBackend.REDIS:
            return RedisCacheBackend(self.config)
        elif self.config.backend == CacheBackend.FILE:
            return FileCacheBackend(self.config)
        else:
            raise CacheError(f"不支持的缓存后端: {self.config.backend}")
    
    def generate_cache_key(self, tool_name: str, method_name: str,
                          args: tuple, kwargs: dict,
                          user_id: Optional[str] = None,
                          context: Optional[ToolExecutionContext] = None) -> CacheKey:
        """
        生成缓存键
        
        Args:
            tool_name: 工具名称
            method_name: 方法名称
            args: 位置参数
            kwargs: 关键字参数
            user_id: 用户ID
            context: 执行上下文
        
        Returns:
            CacheKey: 缓存键
        """
        # 生成参数哈希
        args_str = json.dumps(args, sort_keys=True, default=str)
        kwargs_str = json.dumps(kwargs, sort_keys=True, default=str)
        
        args_hash = hashlib.md5(args_str.encode()).hexdigest()[:16]
        kwargs_hash = hashlib.md5(kwargs_str.encode()).hexdigest()[:16]
        
        # 生成上下文哈希
        context_hash = None
        if context:
            context_data = {
                'session_id': context.session_id,
                'request_id': context.request_id,
                'permissions': [p.value for p in context.permissions] if context.permissions else []
            }
            context_str = json.dumps(context_data, sort_keys=True)
            context_hash = hashlib.md5(context_str.encode()).hexdigest()[:16]
        
        return CacheKey(
            tool_name=tool_name,
            method_name=method_name,
            args_hash=args_hash,
            kwargs_hash=kwargs_hash,
            user_id=user_id,
            context_hash=context_hash
        )
    
    async def get(self, key: CacheKey) -> Optional[Any]:
        """
        获取缓存值
        
        Args:
            key: 缓存键
        
        Returns:
            Optional[Any]: 缓存值
        """
        async with self.lock:
            key_str = str(key)
            
            try:
                # 从后端获取数据
                data = await self.backend.get(key_str)
                if data is None:
                    self.stats.misses += 1
                    self.stats.update_hit_rate()
                    return None
                
                # 反序列化
                value = self.serializer.deserialize(data)
                
                # 更新条目信息
                if key_str in self.entries:
                    entry = self.entries[key_str]
                    entry.touch()
                    
                    # 检查是否过期
                    if entry.is_expired():
                        await self._remove_entry(key_str)
                        self.stats.expired += 1
                        self.stats.misses += 1
                        self.stats.update_hit_rate()
                        return None
                
                self.stats.hits += 1
                self.stats.update_hit_rate()
                
                if self.config.enable_logging:
                    self.logger.debug(f"缓存命中: {key_str}")
                
                return value
                
            except Exception as e:
                self.logger.error(f"获取缓存失败 {key_str}: {e}")
                self.stats.misses += 1
                self.stats.update_hit_rate()
                return None
    
    async def set(self, key: CacheKey, value: Any, ttl: Optional[int] = None) -> bool:
        """
        设置缓存值
        
        Args:
            key: 缓存键
            value: 缓存值
            ttl: 生存时间（秒）
        
        Returns:
            bool: 是否设置成功
        """
        async with self.lock:
            key_str = str(key)
            
            try:
                # 序列化
                data = self.serializer.serialize(value)
                data_size = len(data)
                
                # 检查内存限制
                if self.stats.memory_usage + data_size > self.config.max_memory:
                    await self._evict_entries(data_size)
                
                # 检查大小限制
                if len(self.entries) >= self.config.max_size:
                    await self._evict_entries()
                
                # 设置TTL
                if ttl is None:
                    ttl = self.config.default_ttl
                if ttl is not None and ttl > self.config.max_ttl:
                    ttl = self.config.max_ttl
                
                # 存储到后端
                success = await self.backend.set(key_str, data, ttl)
                if not success:
                    return False
                
                # 创建缓存条目
                entry = CacheEntry(
                    key=key,
                    value=value,
                    ttl=ttl,
                    size=data_size
                )
                
                # 移除旧条目（如果存在）
                if key_str in self.entries:
                    old_entry = self.entries[key_str]
                    self.stats.memory_usage -= old_entry.size
                
                # 添加新条目
                self.entries[key_str] = entry
                self.stats.size = len(self.entries)
                self.stats.memory_usage += data_size
                
                if self.config.enable_logging:
                    self.logger.debug(f"缓存设置: {key_str}, TTL: {ttl}")
                
                return True
                
            except Exception as e:
                self.logger.error(f"设置缓存失败 {key_str}: {e}")
                return False
    
    async def delete(self, key: CacheKey) -> bool:
        """
        删除缓存值
        
        Args:
            key: 缓存键
        
        Returns:
            bool: 是否删除成功
        """
        async with self.lock:
            key_str = str(key)
            return await self._remove_entry(key_str)
    
    async def exists(self, key: CacheKey) -> bool:
        """
        检查缓存是否存在
        
        Args:
            key: 缓存键
        
        Returns:
            bool: 是否存在
        """
        key_str = str(key)
        
        # 检查条目是否过期
        if key_str in self.entries:
            entry = self.entries[key_str]
            if entry.is_expired():
                await self._remove_entry(key_str)
                return False
        
        return await self.backend.exists(key_str)
    
    async def clear(self) -> bool:
        """
        清空缓存
        
        Returns:
            bool: 是否清空成功
        """
        async with self.lock:
            try:
                success = await self.backend.clear()
                if success:
                    self.entries.clear()
                    self.stats = CacheStats()
                    
                    if self.config.enable_logging:
                        self.logger.info("缓存已清空")
                
                return success
                
            except Exception as e:
                self.logger.error(f"清空缓存失败: {e}")
                return False
    
    async def _remove_entry(self, key_str: str) -> bool:
        """
        移除缓存条目
        
        Args:
            key_str: 缓存键字符串
        
        Returns:
            bool: 是否移除成功
        """
        try:
            # 从后端删除
            backend_success = await self.backend.delete(key_str)
            
            # 从内存中移除
            if key_str in self.entries:
                entry = self.entries[key_str]
                self.stats.memory_usage -= entry.size
                del self.entries[key_str]
                self.stats.size = len(self.entries)
            
            return backend_success
            
        except Exception as e:
            self.logger.error(f"移除缓存条目失败 {key_str}: {e}")
            return False
    
    async def _evict_entries(self, required_space: int = 0) -> None:
        """
        驱逐缓存条目
        
        Args:
            required_space: 需要的空间大小
        """
        if not self.entries:
            return
        
        evicted_count = 0
        freed_space = 0
        
        # 根据策略选择要驱逐的条目
        if self.config.strategy == CacheStrategy.LRU:
            # 最近最少使用
            sorted_entries = sorted(
                self.entries.items(),
                key=lambda x: x[1].last_accessed
            )
        elif self.config.strategy == CacheStrategy.LFU:
            # 最少使用频率
            sorted_entries = sorted(
                self.entries.items(),
                key=lambda x: x[1].access_count
            )
        elif self.config.strategy == CacheStrategy.FIFO:
            # 先进先出
            sorted_entries = sorted(
                self.entries.items(),
                key=lambda x: x[1].created_at
            )
        else:
            # 默认使用LRU
            sorted_entries = sorted(
                self.entries.items(),
                key=lambda x: x[1].last_accessed
            )
        
        # 驱逐条目
        for key_str, entry in sorted_entries:
            if (required_space == 0 and 
                len(self.entries) < self.config.max_size and
                self.stats.memory_usage < self.config.max_memory):
                break
            
            if (required_space > 0 and freed_space >= required_space):
                break
            
            await self._remove_entry(key_str)
            evicted_count += 1
            freed_space += entry.size
        
        self.stats.evictions += evicted_count
        
        if evicted_count > 0 and self.config.enable_logging:
            self.logger.info(f"驱逐了 {evicted_count} 个缓存条目，释放 {freed_space} 字节")
    
    async def _cleanup_loop(self) -> None:
        """
        清理循环
        """
        while True:
            try:
                await asyncio.sleep(self.config.cleanup_interval)
                await self._cleanup_expired_entries()
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"清理循环异常: {e}")
    
    async def _cleanup_expired_entries(self) -> None:
        """
        清理过期条目
        """
        async with self.lock:
            expired_keys = []
            
            for key_str, entry in self.entries.items():
                if entry.is_expired():
                    expired_keys.append(key_str)
            
            for key_str in expired_keys:
                await self._remove_entry(key_str)
                self.stats.expired += 1
            
            if expired_keys and self.config.enable_logging:
                self.logger.info(f"清理了 {len(expired_keys)} 个过期缓存条目")
    
    async def _persistence_loop(self) -> None:
        """
        持久化循环
        """
        while True:
            try:
                await asyncio.sleep(self.config.persistence_interval)
                await self._persist_cache()
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"持久化循环异常: {e}")
    
    async def _persist_cache(self) -> None:
        """
        持久化缓存
        """
        if not self.config.persistence_file:
            return
        
        try:
            # 导出缓存数据
            cache_data = {
                'entries': {},
                'stats': asdict(self.stats),
                'timestamp': datetime.now().isoformat()
            }
            
            for key_str, entry in self.entries.items():
                cache_data['entries'][key_str] = {
                    'key': asdict(entry.key),
                    'value': entry.value,
                    'created_at': entry.created_at.isoformat(),
                    'last_accessed': entry.last_accessed.isoformat(),
                    'access_count': entry.access_count,
                    'ttl': entry.ttl,
                    'size': entry.size,
                    'metadata': entry.metadata
                }
            
            # 写入文件
            import aiofiles
            async with aiofiles.open(self.config.persistence_file, 'w') as f:
                await f.write(json.dumps(cache_data, indent=2))
            
            if self.config.enable_logging:
                self.logger.info(f"缓存已持久化到 {self.config.persistence_file}")
                
        except Exception as e:
            self.logger.error(f"持久化缓存失败: {e}")
    
    async def load_cache(self, file_path: Optional[str] = None) -> bool:
        """
        加载缓存
        
        Args:
            file_path: 文件路径（可选）
        
        Returns:
            bool: 是否加载成功
        """
        file_path = file_path or self.config.persistence_file
        if not file_path:
            return False
        
        try:
            import aiofiles
            import os
            
            if not os.path.exists(file_path):
                return False
            
            async with aiofiles.open(file_path, 'r') as f:
                content = await f.read()
                cache_data = json.loads(content)
            
            # 恢复缓存条目
            for key_str, entry_data in cache_data.get('entries', {}).items():
                try:
                    # 重建缓存键
                    key_data = entry_data['key']
                    cache_key = CacheKey(**key_data)
                    
                    # 重建缓存条目
                    entry = CacheEntry(
                        key=cache_key,
                        value=entry_data['value'],
                        created_at=datetime.fromisoformat(entry_data['created_at']),
                        last_accessed=datetime.fromisoformat(entry_data['last_accessed']),
                        access_count=entry_data['access_count'],
                        ttl=entry_data.get('ttl'),
                        size=entry_data['size'],
                        metadata=entry_data.get('metadata', {})
                    )
                    
                    # 检查是否过期
                    if not entry.is_expired():
                        # 序列化并存储到后端
                        data = self.serializer.serialize(entry.value)
                        await self.backend.set(key_str, data, entry.ttl)
                        
                        # 添加到内存
                        self.entries[key_str] = entry
                        self.stats.memory_usage += entry.size
                
                except Exception as e:
                    self.logger.warning(f"恢复缓存条目失败 {key_str}: {e}")
            
            self.stats.size = len(self.entries)
            
            if self.config.enable_logging:
                self.logger.info(f"从 {file_path} 加载了 {len(self.entries)} 个缓存条目")
            
            return True
            
        except Exception as e:
            self.logger.error(f"加载缓存失败: {e}")
            return False
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取缓存统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        return {
            **asdict(self.stats),
            'config': asdict(self.config),
            'backend_type': self.config.backend.value,
            'entries_count': len(self.entries)
        }
    
    async def close(self) -> None:
        """
        关闭缓存
        """
        # 取消后台任务
        if self.cleanup_task:
            self.cleanup_task.cancel()
            try:
                await self.cleanup_task
            except asyncio.CancelledError:
                pass
        
        if self.persistence_task:
            self.persistence_task.cancel()
            try:
                await self.persistence_task
            except asyncio.CancelledError:
                pass
        
        # 最后一次持久化
        if self.config.enable_persistence:
            await self._persist_cache()
        
        # 关闭后端连接
        if hasattr(self.backend, 'close'):
            await self.backend.close()
        
        if self.config.enable_logging:
            self.logger.info("工具缓存已关闭")


# 全局缓存实例
_global_cache: Optional[ToolCache] = None


def get_global_cache() -> Optional[ToolCache]:
    """
    获取全局缓存实例
    
    Returns:
        Optional[ToolCache]: 缓存实例
    """
    return _global_cache


def set_global_cache(cache: ToolCache) -> None:
    """
    设置全局缓存实例
    
    Args:
        cache: 缓存实例
    """
    global _global_cache
    _global_cache = cache