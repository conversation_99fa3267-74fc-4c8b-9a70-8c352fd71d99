# -*- coding: utf-8 -*-
"""
A2A多智能体系统流式数据处理服务

基于Google ADK的流式数据处理和分析系统
"""

import asyncio
import json
import time
import uuid
import gzip
import zlib
import pickle
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable, Union, Set, AsyncGenerator, Tuple
from dataclasses import dataclass, field
from enum import Enum
import logging
from collections import defaultdict, deque
import weakref
from concurrent.futures import ThreadPoolExecutor
import hashlib
import base64

from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc, func, text
from cachetools import TTLCache, LRUCache
from loguru import logger
import numpy as np
import pandas as pd
from pydantic import BaseModel, validator

from ..models import User, Session as SessionModel, Message, Agent
from ..core.database import get_db
from ..auth.permissions import check_user_permission
from .user_service import UserService
from .event_service import EventService, EventType, EventPriority
from .monitoring_service import MonitoringService, MetricType, MonitoringScope


class DataFormat(str, Enum):
    """数据格式枚举"""
    JSON = "json"
    XML = "xml"
    CSV = "csv"
    BINARY = "binary"
    TEXT = "text"
    PROTOBUF = "protobuf"
    AVRO = "avro"
    PARQUET = "parquet"


class CompressionType(str, Enum):
    """压缩类型枚举"""
    NONE = "none"
    GZIP = "gzip"
    ZLIB = "zlib"
    LZ4 = "lz4"
    SNAPPY = "snappy"
    BROTLI = "brotli"


class ProcessingMode(str, Enum):
    """处理模式枚举"""
    REAL_TIME = "real_time"
    BATCH = "batch"
    MICRO_BATCH = "micro_batch"
    WINDOWED = "windowed"


class AggregationType(str, Enum):
    """聚合类型枚举"""
    COUNT = "count"
    SUM = "sum"
    AVG = "avg"
    MIN = "min"
    MAX = "max"
    MEDIAN = "median"
    PERCENTILE = "percentile"
    DISTINCT_COUNT = "distinct_count"
    FIRST = "first"
    LAST = "last"


class WindowType(str, Enum):
    """窗口类型枚举"""
    TUMBLING = "tumbling"  # 滚动窗口
    SLIDING = "sliding"    # 滑动窗口
    SESSION = "session"    # 会话窗口
    GLOBAL = "global"      # 全局窗口


@dataclass
class StreamData:
    """流式数据"""
    data_id: str
    source: str
    data: Any
    timestamp: datetime = field(default_factory=datetime.utcnow)
    format: DataFormat = DataFormat.JSON
    compression: CompressionType = CompressionType.NONE
    user_id: Optional[int] = None
    session_id: Optional[str] = None
    agent_id: Optional[str] = None
    tags: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    size_bytes: int = 0
    checksum: Optional[str] = None
    
    def __post_init__(self):
        if not self.data_id:
            self.data_id = str(uuid.uuid4())
        
        # 计算数据大小和校验和
        if self.data:
            data_str = json.dumps(self.data) if isinstance(self.data, (dict, list)) else str(self.data)
            self.size_bytes = len(data_str.encode('utf-8'))
            self.checksum = hashlib.md5(data_str.encode('utf-8')).hexdigest()
    
    def compress_data(self, compression_type: CompressionType = CompressionType.GZIP) -> bytes:
        """压缩数据"""
        if not self.data:
            return b''
        
        data_bytes = json.dumps(self.data).encode('utf-8')
        
        if compression_type == CompressionType.GZIP:
            return gzip.compress(data_bytes)
        elif compression_type == CompressionType.ZLIB:
            return zlib.compress(data_bytes)
        else:
            return data_bytes
    
    def decompress_data(self, compressed_data: bytes, compression_type: CompressionType) -> Any:
        """解压数据"""
        if compression_type == CompressionType.GZIP:
            data_bytes = gzip.decompress(compressed_data)
        elif compression_type == CompressionType.ZLIB:
            data_bytes = zlib.decompress(compressed_data)
        else:
            data_bytes = compressed_data
        
        return json.loads(data_bytes.decode('utf-8'))
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "data_id": self.data_id,
            "source": self.source,
            "data": self.data,
            "timestamp": self.timestamp.isoformat(),
            "format": self.format.value,
            "compression": self.compression.value,
            "user_id": self.user_id,
            "session_id": self.session_id,
            "agent_id": self.agent_id,
            "tags": self.tags,
            "metadata": self.metadata,
            "size_bytes": self.size_bytes,
            "checksum": self.checksum
        }


@dataclass
class ProcessingWindow:
    """处理窗口"""
    window_id: str
    window_type: WindowType
    size_seconds: int
    slide_seconds: Optional[int] = None
    session_timeout_seconds: Optional[int] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    data_count: int = 0
    
    def __post_init__(self):
        if not self.window_id:
            self.window_id = str(uuid.uuid4())
    
    def is_in_window(self, timestamp: datetime) -> bool:
        """检查时间戳是否在窗口内"""
        if self.window_type == WindowType.GLOBAL:
            return True
        
        if not self.start_time or not self.end_time:
            return False
        
        return self.start_time <= timestamp < self.end_time
    
    def should_trigger(self, current_time: datetime) -> bool:
        """检查是否应该触发窗口处理"""
        if self.window_type == WindowType.GLOBAL:
            return False
        
        if not self.end_time:
            return False
        
        return current_time >= self.end_time


@dataclass
class AggregationRule:
    """聚合规则"""
    rule_id: str
    name: str
    source_pattern: str
    aggregation_type: AggregationType
    field_path: str
    window: ProcessingWindow
    group_by: List[str] = field(default_factory=list)
    filter_condition: Optional[str] = None
    output_target: Optional[str] = None
    enabled: bool = True
    
    def __post_init__(self):
        if not self.rule_id:
            self.rule_id = str(uuid.uuid4())
    
    def matches_source(self, source: str) -> bool:
        """检查源是否匹配"""
        import re
        return bool(re.match(self.source_pattern, source))
    
    def extract_field_value(self, data: Any) -> Any:
        """提取字段值"""
        try:
            if not self.field_path:
                return data
            
            value = data
            for part in self.field_path.split('.'):
                if isinstance(value, dict):
                    value = value.get(part)
                elif isinstance(value, list) and part.isdigit():
                    value = value[int(part)]
                else:
                    return None
            
            return value
        except Exception:
            return None
    
    def evaluate_filter(self, data: Any) -> bool:
        """评估过滤条件"""
        if not self.filter_condition:
            return True
        
        try:
            # 简单的条件评估（实际应用中可能需要更复杂的表达式解析器）
            # 这里只是示例实现
            return True
        except Exception:
            return False


class DataValidator:
    """数据验证器"""
    
    def __init__(self):
        self.schemas: Dict[str, BaseModel] = {}
        self.validation_rules: Dict[str, List[Callable]] = defaultdict(list)
    
    def register_schema(self, source: str, schema: BaseModel):
        """注册数据模式"""
        self.schemas[source] = schema
    
    def register_validation_rule(self, source: str, rule: Callable[[Any], bool]):
        """注册验证规则"""
        self.validation_rules[source].append(rule)
    
    def validate_data(self, stream_data: StreamData) -> Tuple[bool, List[str]]:
        """验证数据"""
        errors = []
        
        try:
            # 模式验证
            if stream_data.source in self.schemas:
                schema = self.schemas[stream_data.source]
                try:
                    schema.parse_obj(stream_data.data)
                except Exception as e:
                    errors.append(f"模式验证失败: {e}")
            
            # 自定义规则验证
            if stream_data.source in self.validation_rules:
                for rule in self.validation_rules[stream_data.source]:
                    try:
                        if not rule(stream_data.data):
                            errors.append(f"自定义规则验证失败: {rule.__name__}")
                    except Exception as e:
                        errors.append(f"规则执行失败: {e}")
            
            # 基本数据完整性检查
            if not stream_data.data:
                errors.append("数据为空")
            
            if stream_data.checksum:
                # 验证校验和
                data_str = json.dumps(stream_data.data) if isinstance(stream_data.data, (dict, list)) else str(stream_data.data)
                calculated_checksum = hashlib.md5(data_str.encode('utf-8')).hexdigest()
                if calculated_checksum != stream_data.checksum:
                    errors.append("数据校验和不匹配")
            
            return len(errors) == 0, errors
            
        except Exception as e:
            return False, [f"验证过程异常: {e}"]


class DataAggregator:
    """数据聚合器"""
    
    def __init__(self):
        self.aggregation_cache: Dict[str, Dict[str, Any]] = defaultdict(dict)
        self.window_data: Dict[str, List[StreamData]] = defaultdict(list)
        self._lock = asyncio.Lock()
    
    async def aggregate_data(
        self,
        rule: AggregationRule,
        data_batch: List[StreamData]
    ) -> Dict[str, Any]:
        """聚合数据"""
        async with self._lock:
            try:
                # 过滤匹配的数据
                matching_data = []
                for stream_data in data_batch:
                    if (rule.matches_source(stream_data.source) and 
                        rule.evaluate_filter(stream_data.data)):
                        matching_data.append(stream_data)
                
                if not matching_data:
                    return {}
                
                # 提取字段值
                values = []
                for stream_data in matching_data:
                    value = rule.extract_field_value(stream_data.data)
                    if value is not None:
                        values.append(value)
                
                if not values:
                    return {}
                
                # 执行聚合
                result = await self._perform_aggregation(rule.aggregation_type, values)
                
                # 分组聚合
                if rule.group_by:
                    grouped_results = await self._perform_grouped_aggregation(
                        rule, matching_data
                    )
                    return grouped_results
                
                return {
                    "rule_id": rule.rule_id,
                    "rule_name": rule.name,
                    "aggregation_type": rule.aggregation_type.value,
                    "result": result,
                    "data_count": len(values),
                    "timestamp": datetime.utcnow().isoformat(),
                    "window_id": rule.window.window_id
                }
                
            except Exception as e:
                logger.error(f"数据聚合失败: {e}")
                return {}
    
    async def _perform_aggregation(self, agg_type: AggregationType, values: List[Any]) -> Any:
        """执行聚合计算"""
        try:
            # 转换为数值类型（如果可能）
            numeric_values = []
            for value in values:
                try:
                    if isinstance(value, (int, float)):
                        numeric_values.append(value)
                    elif isinstance(value, str) and value.replace('.', '').replace('-', '').isdigit():
                        numeric_values.append(float(value))
                except Exception:
                    pass
            
            if agg_type == AggregationType.COUNT:
                return len(values)
            elif agg_type == AggregationType.DISTINCT_COUNT:
                return len(set(str(v) for v in values))
            elif agg_type == AggregationType.FIRST:
                return values[0] if values else None
            elif agg_type == AggregationType.LAST:
                return values[-1] if values else None
            
            # 数值聚合
            if not numeric_values:
                return None
            
            if agg_type == AggregationType.SUM:
                return sum(numeric_values)
            elif agg_type == AggregationType.AVG:
                return sum(numeric_values) / len(numeric_values)
            elif agg_type == AggregationType.MIN:
                return min(numeric_values)
            elif agg_type == AggregationType.MAX:
                return max(numeric_values)
            elif agg_type == AggregationType.MEDIAN:
                sorted_values = sorted(numeric_values)
                n = len(sorted_values)
                if n % 2 == 0:
                    return (sorted_values[n//2-1] + sorted_values[n//2]) / 2
                else:
                    return sorted_values[n//2]
            elif agg_type == AggregationType.PERCENTILE:
                # 默认95百分位
                sorted_values = sorted(numeric_values)
                index = int(0.95 * len(sorted_values))
                return sorted_values[min(index, len(sorted_values)-1)]
            
            return None
            
        except Exception as e:
            logger.error(f"聚合计算失败: {e}")
            return None
    
    async def _perform_grouped_aggregation(
        self,
        rule: AggregationRule,
        data_batch: List[StreamData]
    ) -> Dict[str, Any]:
        """执行分组聚合"""
        try:
            # 按分组字段分组
            groups = defaultdict(list)
            
            for stream_data in data_batch:
                group_key_parts = []
                for group_field in rule.group_by:
                    value = self._extract_group_value(stream_data.data, group_field)
                    group_key_parts.append(str(value))
                
                group_key = "|".join(group_key_parts)
                groups[group_key].append(stream_data)
            
            # 对每个分组执行聚合
            grouped_results = {}
            for group_key, group_data in groups.items():
                values = []
                for stream_data in group_data:
                    value = rule.extract_field_value(stream_data.data)
                    if value is not None:
                        values.append(value)
                
                if values:
                    result = await self._perform_aggregation(rule.aggregation_type, values)
                    grouped_results[group_key] = {
                        "result": result,
                        "count": len(values)
                    }
            
            return {
                "rule_id": rule.rule_id,
                "rule_name": rule.name,
                "aggregation_type": rule.aggregation_type.value,
                "grouped_results": grouped_results,
                "total_groups": len(grouped_results),
                "timestamp": datetime.utcnow().isoformat(),
                "window_id": rule.window.window_id
            }
            
        except Exception as e:
            logger.error(f"分组聚合失败: {e}")
            return {}
    
    def _extract_group_value(self, data: Any, field_path: str) -> Any:
        """提取分组字段值"""
        try:
            value = data
            for part in field_path.split('.'):
                if isinstance(value, dict):
                    value = value.get(part)
                elif isinstance(value, list) and part.isdigit():
                    value = value[int(part)]
                else:
                    return "unknown"
            return value if value is not None else "null"
        except Exception:
            return "error"


class StreamStorage:
    """流式数据存储"""
    
    def __init__(self, max_memory_items: int = 100000):
        self.max_memory_items = max_memory_items
        self.memory_storage: Dict[str, StreamData] = {}
        self.time_index: List[Tuple[datetime, str]] = []
        self.source_index: Dict[str, Set[str]] = defaultdict(set)
        self.user_index: Dict[int, Set[str]] = defaultdict(set)
        self.session_index: Dict[str, Set[str]] = defaultdict(set)
        self.agent_index: Dict[str, Set[str]] = defaultdict(set)
        self.tag_index: Dict[str, Set[str]] = defaultdict(set)
        
        # 压缩存储
        self.compressed_storage: Dict[str, bytes] = {}
        self.compression_cache = LRUCache(maxsize=10000)
        
        self._lock = asyncio.Lock()
    
    async def store_data(self, stream_data: StreamData, compress: bool = False) -> bool:
        """存储流式数据"""
        async with self._lock:
            try:
                # 检查存储限制
                if len(self.memory_storage) >= self.max_memory_items:
                    await self._cleanup_old_data()
                
                # 存储数据
                if compress:
                    compressed_data = stream_data.compress_data(CompressionType.GZIP)
                    self.compressed_storage[stream_data.data_id] = compressed_data
                else:
                    self.memory_storage[stream_data.data_id] = stream_data
                
                # 更新索引
                self.time_index.append((stream_data.timestamp, stream_data.data_id))
                self.time_index.sort(key=lambda x: x[0])
                
                self.source_index[stream_data.source].add(stream_data.data_id)
                
                if stream_data.user_id:
                    self.user_index[stream_data.user_id].add(stream_data.data_id)
                
                if stream_data.session_id:
                    self.session_index[stream_data.session_id].add(stream_data.data_id)
                
                if stream_data.agent_id:
                    self.agent_index[stream_data.agent_id].add(stream_data.data_id)
                
                for tag in stream_data.tags:
                    self.tag_index[tag].add(stream_data.data_id)
                
                return True
                
            except Exception as e:
                logger.error(f"存储流式数据失败: {e}")
                return False
    
    async def get_data(self, data_id: str) -> Optional[StreamData]:
        """获取流式数据"""
        try:
            # 从内存存储获取
            if data_id in self.memory_storage:
                return self.memory_storage[data_id]
            
            # 从压缩存储获取
            if data_id in self.compressed_storage:
                if data_id in self.compression_cache:
                    return self.compression_cache[data_id]
                
                compressed_data = self.compressed_storage[data_id]
                # 这里需要重构StreamData以支持从压缩数据恢复
                # 简化实现，实际应用中需要更完整的序列化/反序列化
                return None
            
            return None
            
        except Exception as e:
            logger.error(f"获取流式数据失败: {e}")
            return None
    
    async def query_data(
        self,
        source_pattern: Optional[str] = None,
        user_id: Optional[int] = None,
        session_id: Optional[str] = None,
        agent_id: Optional[str] = None,
        tags: Optional[List[str]] = None,
        time_range: Optional[Tuple[datetime, datetime]] = None,
        limit: int = 1000
    ) -> List[StreamData]:
        """查询流式数据"""
        async with self._lock:
            try:
                candidate_ids = set()
                
                # 使用索引优化查询
                if source_pattern:
                    import re
                    for source, ids in self.source_index.items():
                        if re.match(source_pattern, source):
                            candidate_ids.update(ids)
                elif user_id and user_id in self.user_index:
                    candidate_ids = self.user_index[user_id].copy()
                elif session_id and session_id in self.session_index:
                    candidate_ids = self.session_index[session_id].copy()
                elif agent_id and agent_id in self.agent_index:
                    candidate_ids = self.agent_index[agent_id].copy()
                elif tags:
                    for tag in tags:
                        if tag in self.tag_index:
                            if not candidate_ids:
                                candidate_ids = self.tag_index[tag].copy()
                            else:
                                candidate_ids &= self.tag_index[tag]
                else:
                    candidate_ids = set(self.memory_storage.keys())
                
                # 获取候选数据
                candidate_data = []
                for data_id in candidate_ids:
                    data = await self.get_data(data_id)
                    if data:
                        candidate_data.append(data)
                
                # 应用过滤器
                filtered_data = []
                for data in candidate_data:
                    # 时间范围过滤
                    if time_range:
                        start_time, end_time = time_range
                        if data.timestamp < start_time or data.timestamp > end_time:
                            continue
                    
                    # 其他过滤条件
                    if user_id and data.user_id != user_id:
                        continue
                    if session_id and data.session_id != session_id:
                        continue
                    if agent_id and data.agent_id != agent_id:
                        continue
                    if tags and not any(tag in data.tags for tag in tags):
                        continue
                    
                    filtered_data.append(data)
                
                # 按时间排序并限制数量
                filtered_data.sort(key=lambda x: x.timestamp, reverse=True)
                return filtered_data[:limit]
                
            except Exception as e:
                logger.error(f"查询流式数据失败: {e}")
                return []
    
    async def _cleanup_old_data(self):
        """清理旧数据"""
        try:
            # 删除最旧的10%数据
            cleanup_count = max(1, len(self.memory_storage) // 10)
            
            # 按时间排序，删除最旧的数据
            old_items = self.time_index[:cleanup_count]
            
            for timestamp, data_id in old_items:
                await self._remove_data(data_id)
            
            # 更新时间索引
            self.time_index = self.time_index[cleanup_count:]
            
        except Exception as e:
            logger.error(f"清理旧数据失败: {e}")
    
    async def _remove_data(self, data_id: str):
        """移除数据"""
        try:
            # 获取数据以清理索引
            data = self.memory_storage.get(data_id)
            if data:
                # 清理索引
                self.source_index[data.source].discard(data_id)
                if data.user_id:
                    self.user_index[data.user_id].discard(data_id)
                if data.session_id:
                    self.session_index[data.session_id].discard(data_id)
                if data.agent_id:
                    self.agent_index[data.agent_id].discard(data_id)
                for tag in data.tags:
                    self.tag_index[tag].discard(data_id)
                
                # 删除数据
                del self.memory_storage[data_id]
            
            # 删除压缩数据
            if data_id in self.compressed_storage:
                del self.compressed_storage[data_id]
            
            # 清理缓存
            if data_id in self.compression_cache:
                del self.compression_cache[data_id]
                
        except Exception as e:
            logger.error(f"移除数据失败: {e}")
    
    def get_storage_stats(self) -> Dict[str, Any]:
        """获取存储统计"""
        try:
            memory_size = sum(data.size_bytes for data in self.memory_storage.values())
            compressed_size = sum(len(data) for data in self.compressed_storage.values())
            
            return {
                "memory_items": len(self.memory_storage),
                "compressed_items": len(self.compressed_storage),
                "total_items": len(self.memory_storage) + len(self.compressed_storage),
                "memory_size_bytes": memory_size,
                "compressed_size_bytes": compressed_size,
                "compression_ratio": compressed_size / memory_size if memory_size > 0 else 0,
                "sources_count": len(self.source_index),
                "users_count": len(self.user_index),
                "sessions_count": len(self.session_index),
                "agents_count": len(self.agent_index),
                "tags_count": len(self.tag_index)
            }
            
        except Exception as e:
            logger.error(f"获取存储统计失败: {e}")
            return {}


class StreamProcessor:
    """
    流式数据处理服务
    
    基于Google ADK的流式数据处理和分析系统
    """
    
    def __init__(self):
        """初始化流式数据处理服务"""
        self.logger = logger
        
        # 组件初始化
        self.data_validator = DataValidator()
        self.data_aggregator = DataAggregator()
        self.stream_storage = StreamStorage(max_memory_items=100000)
        
        # 处理配置
        self.processing_mode = ProcessingMode.REAL_TIME
        self.batch_size = 1000
        self.batch_timeout_seconds = 30
        self.max_concurrent_processors = 10
        
        # 数据队列
        self.input_queue: asyncio.Queue = asyncio.Queue(maxsize=50000)
        self.processing_queue: asyncio.Queue = asyncio.Queue(maxsize=10000)
        self.output_queue: asyncio.Queue = asyncio.Queue(maxsize=10000)
        
        # 聚合规则
        self.aggregation_rules: Dict[str, AggregationRule] = {}
        self.active_windows: Dict[str, ProcessingWindow] = {}
        
        # 处理器和订阅者
        self.data_processors: Dict[str, Callable] = {}
        self.result_subscribers: Dict[str, Set[Callable]] = defaultdict(set)
        
        # 性能统计
        self.stats = {
            "total_processed": 0,
            "total_validated": 0,
            "total_aggregated": 0,
            "total_stored": 0,
            "validation_errors": 0,
            "processing_errors": 0,
            "average_processing_time": 0.0,
            "throughput_per_second": 0.0
        }
        
        # 后台任务
        self._input_processor_task = None
        self._batch_processor_task = None
        self._window_processor_task = None
        self._output_processor_task = None
        self._stats_task = None
        
        # 线程池
        self.thread_pool = ThreadPoolExecutor(max_workers=self.max_concurrent_processors)
        
        # 服务依赖
        self.user_service = UserService()
        self.event_service = None  # 延迟初始化
        self.monitoring_service = None  # 延迟初始化
        
        self.logger.info("流式数据处理服务初始化完成")
    
    async def start(self):
        """启动流式数据处理服务"""
        try:
            # 启动后台任务
            self._input_processor_task = asyncio.create_task(self._process_input_data())
            self._batch_processor_task = asyncio.create_task(self._process_batch_data())
            self._window_processor_task = asyncio.create_task(self._process_windows())
            self._output_processor_task = asyncio.create_task(self._process_output_data())
            self._stats_task = asyncio.create_task(self._update_stats())
            
            # 初始化服务依赖
            from .event_service import event_service
            from .monitoring_service import monitoring_service
            self.event_service = event_service
            self.monitoring_service = monitoring_service
            
            self.logger.info("流式数据处理服务启动成功")
            
        except Exception as e:
            self.logger.error(f"流式数据处理服务启动失败: {e}")
            raise
    
    async def stop(self):
        """停止流式数据处理服务"""
        try:
            # 取消后台任务
            if self._input_processor_task:
                self._input_processor_task.cancel()
            if self._batch_processor_task:
                self._batch_processor_task.cancel()
            if self._window_processor_task:
                self._window_processor_task.cancel()
            if self._output_processor_task:
                self._output_processor_task.cancel()
            if self._stats_task:
                self._stats_task.cancel()
            
            # 关闭线程池
            self.thread_pool.shutdown(wait=True)
            
            self.logger.info("流式数据处理服务停止成功")
            
        except Exception as e:
            self.logger.error(f"流式数据处理服务停止失败: {e}")
    
    async def process_stream_data(
        self,
        source: str,
        data: Any,
        format: DataFormat = DataFormat.JSON,
        compression: CompressionType = CompressionType.NONE,
        user_id: Optional[int] = None,
        session_id: Optional[str] = None,
        agent_id: Optional[str] = None,
        tags: Optional[List[str]] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        处理流式数据
        
        Args:
            source: 数据源
            data: 数据内容
            format: 数据格式
            compression: 压缩类型
            user_id: 用户ID
            session_id: 会话ID
            agent_id: 智能体ID
            tags: 标签
            metadata: 元数据
        
        Returns:
            str: 数据ID
        """
        try:
            # 创建流式数据
            stream_data = StreamData(
                data_id=str(uuid.uuid4()),
                source=source,
                data=data,
                format=format,
                compression=compression,
                user_id=user_id,
                session_id=session_id,
                agent_id=agent_id,
                tags=tags or [],
                metadata=metadata or {}
            )
            
            # 加入输入队列
            await self.input_queue.put(stream_data)
            
            # 更新统计
            self.stats["total_processed"] += 1
            
            # 收集指标
            if self.monitoring_service:
                await self.monitoring_service.collect_metric(
                    "stream_data_received",
                    MetricType.COUNTER,
                    1,
                    labels={"source": source, "format": format.value},
                    scope=MonitoringScope.STREAM
                )
            
            return stream_data.data_id
            
        except Exception as e:
            self.logger.error(f"处理流式数据失败: {e}")
            self.stats["processing_errors"] += 1
            raise
    
    async def register_aggregation_rule(
        self,
        name: str,
        source_pattern: str,
        aggregation_type: AggregationType,
        field_path: str,
        window_type: WindowType,
        window_size_seconds: int,
        slide_seconds: Optional[int] = None,
        group_by: Optional[List[str]] = None,
        filter_condition: Optional[str] = None,
        output_target: Optional[str] = None
    ) -> str:
        """
        注册聚合规则
        
        Args:
            name: 规则名称
            source_pattern: 源模式
            aggregation_type: 聚合类型
            field_path: 字段路径
            window_type: 窗口类型
            window_size_seconds: 窗口大小（秒）
            slide_seconds: 滑动间隔（秒）
            group_by: 分组字段
            filter_condition: 过滤条件
            output_target: 输出目标
        
        Returns:
            str: 规则ID
        """
        try:
            # 创建处理窗口
            window = ProcessingWindow(
                window_id=str(uuid.uuid4()),
                window_type=window_type,
                size_seconds=window_size_seconds,
                slide_seconds=slide_seconds
            )
            
            # 创建聚合规则
            rule = AggregationRule(
                rule_id=str(uuid.uuid4()),
                name=name,
                source_pattern=source_pattern,
                aggregation_type=aggregation_type,
                field_path=field_path,
                window=window,
                group_by=group_by or [],
                filter_condition=filter_condition,
                output_target=output_target
            )
            
            # 注册规则
            self.aggregation_rules[rule.rule_id] = rule
            self.active_windows[window.window_id] = window
            
            self.logger.info(f"注册聚合规则: {name} ({rule.rule_id})")
            return rule.rule_id
            
        except Exception as e:
            self.logger.error(f"注册聚合规则失败: {e}")
            raise
    
    async def register_data_processor(
        self,
        processor_id: str,
        processor: Callable[[StreamData], Any]
    ):
        """
        注册数据处理器
        
        Args:
            processor_id: 处理器ID
            processor: 处理器函数
        """
        self.data_processors[processor_id] = processor
        self.logger.info(f"注册数据处理器: {processor_id}")
    
    async def subscribe_to_results(
        self,
        subscriber_id: str,
        callback: Callable[[Dict[str, Any]], None],
        result_type: Optional[str] = None
    ):
        """
        订阅处理结果
        
        Args:
            subscriber_id: 订阅者ID
            callback: 回调函数
            result_type: 结果类型
        """
        key = f"{subscriber_id}:{result_type or 'all'}"
        self.result_subscribers[key].add(callback)
        self.logger.info(f"订阅处理结果: {subscriber_id} -> {result_type or 'all'}")
    
    async def query_stream_data(
        self,
        source_pattern: Optional[str] = None,
        user_id: Optional[int] = None,
        session_id: Optional[str] = None,
        agent_id: Optional[str] = None,
        tags: Optional[List[str]] = None,
        time_range: Optional[Tuple[datetime, datetime]] = None,
        limit: int = 1000
    ) -> List[StreamData]:
        """
        查询流式数据
        
        Args:
            source_pattern: 源模式
            user_id: 用户ID
            session_id: 会话ID
            agent_id: 智能体ID
            tags: 标签
            time_range: 时间范围
            limit: 限制数量
        
        Returns:
            List[StreamData]: 流式数据列表
        """
        return await self.stream_storage.query_data(
            source_pattern, user_id, session_id, agent_id, tags, time_range, limit
        )
    
    async def get_processing_stats(self) -> Dict[str, Any]:
        """
        获取处理统计
        
        Returns:
            Dict[str, Any]: 处理统计
        """
        try:
            storage_stats = self.stream_storage.get_storage_stats()
            
            queue_stats = {
                "input_queue_size": self.input_queue.qsize(),
                "processing_queue_size": self.processing_queue.qsize(),
                "output_queue_size": self.output_queue.qsize()
            }
            
            rule_stats = {
                "total_rules": len(self.aggregation_rules),
                "active_windows": len(self.active_windows),
                "registered_processors": len(self.data_processors)
            }
            
            return {
                **self.stats,
                "storage_stats": storage_stats,
                "queue_stats": queue_stats,
                "rule_stats": rule_stats,
                "timestamp": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"获取处理统计失败: {e}")
            return {}
    
    # 私有方法
    
    async def _process_input_data(self):
        """处理输入数据（后台任务）"""
        while True:
            try:
                # 获取输入数据
                stream_data = await self.input_queue.get()
                
                start_time = time.time()
                
                # 验证数据
                is_valid, errors = self.data_validator.validate_data(stream_data)
                
                if is_valid:
                    self.stats["total_validated"] += 1
                    
                    # 存储数据
                    stored = await self.stream_storage.store_data(stream_data)
                    if stored:
                        self.stats["total_stored"] += 1
                    
                    # 加入处理队列
                    await self.processing_queue.put(stream_data)
                    
                else:
                    self.stats["validation_errors"] += 1
                    self.logger.warning(f"数据验证失败: {stream_data.data_id} - {errors}")
                
                # 更新处理时间
                processing_time = time.time() - start_time
                self.stats["average_processing_time"] = (
                    (self.stats["average_processing_time"] * (self.stats["total_validated"] - 1) + processing_time) /
                    self.stats["total_validated"]
                ) if self.stats["total_validated"] > 0 else processing_time
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"处理输入数据失败: {e}")
                self.stats["processing_errors"] += 1
    
    async def _process_batch_data(self):
        """批量处理数据（后台任务）"""
        batch = []
        last_batch_time = time.time()
        
        while True:
            try:
                # 收集批量数据
                try:
                    stream_data = await asyncio.wait_for(
                        self.processing_queue.get(),
                        timeout=1.0
                    )
                    batch.append(stream_data)
                except asyncio.TimeoutError:
                    pass
                
                current_time = time.time()
                
                # 检查是否应该处理批量
                should_process = (
                    len(batch) >= self.batch_size or
                    (batch and current_time - last_batch_time >= self.batch_timeout_seconds)
                )
                
                if should_process and batch:
                    # 处理批量数据
                    await self._process_data_batch(batch)
                    batch = []
                    last_batch_time = current_time
                
            except asyncio.CancelledError:
                # 处理剩余批量数据
                if batch:
                    await self._process_data_batch(batch)
                break
            except Exception as e:
                self.logger.error(f"批量处理数据失败: {e}")
                batch = []  # 清空批量以避免重复处理错误数据
    
    async def _process_data_batch(self, batch: List[StreamData]):
        """处理数据批量"""
        try:
            # 执行自定义处理器
            for processor_id, processor in self.data_processors.items():
                try:
                    for stream_data in batch:
                        if asyncio.iscoroutinefunction(processor):
                            await processor(stream_data)
                        else:
                            await asyncio.get_event_loop().run_in_executor(
                                self.thread_pool, processor, stream_data
                            )
                except Exception as e:
                    self.logger.error(f"数据处理器执行失败: {processor_id} - {e}")
            
            # 执行聚合规则
            for rule in self.aggregation_rules.values():
                if rule.enabled:
                    try:
                        result = await self.data_aggregator.aggregate_data(rule, batch)
                        if result:
                            self.stats["total_aggregated"] += 1
                            await self.output_queue.put({
                                "type": "aggregation_result",
                                "data": result
                            })
                    except Exception as e:
                        self.logger.error(f"聚合规则执行失败: {rule.rule_id} - {e}")
            
            # 发布事件
            if self.event_service:
                await self.event_service.publish_event(
                    EventType.STREAM,
                    "stream_processor",
                    {"batch_size": len(batch), "processed_at": datetime.utcnow().isoformat()},
                    tags=["batch_processed"],
                    metadata={"batch_size": len(batch)}
                )
            
        except Exception as e:
            self.logger.error(f"处理数据批量失败: {e}")
    
    async def _process_windows(self):
        """处理窗口（后台任务）"""
        while True:
            try:
                current_time = datetime.utcnow()
                
                # 检查需要触发的窗口
                triggered_windows = []
                for window in list(self.active_windows.values()):
                    if window.should_trigger(current_time):
                        triggered_windows.append(window)
                
                # 处理触发的窗口
                for window in triggered_windows:
                    await self._process_window(window)
                
                await asyncio.sleep(1)  # 每秒检查一次
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"处理窗口失败: {e}")
                await asyncio.sleep(1)
    
    async def _process_window(self, window: ProcessingWindow):
        """处理单个窗口"""
        try:
            # 获取窗口内的数据
            if window.start_time and window.end_time:
                window_data = await self.stream_storage.query_data(
                    time_range=(window.start_time, window.end_time),
                    limit=10000
                )
                
                # 查找使用此窗口的规则
                window_rules = [
                    rule for rule in self.aggregation_rules.values()
                    if rule.window.window_id == window.window_id and rule.enabled
                ]
                
                # 执行窗口聚合
                for rule in window_rules:
                    try:
                        result = await self.data_aggregator.aggregate_data(rule, window_data)
                        if result:
                            await self.output_queue.put({
                                "type": "window_result",
                                "data": result
                            })
                    except Exception as e:
                        self.logger.error(f"窗口聚合失败: {rule.rule_id} - {e}")
                
                # 更新窗口（滑动窗口）
                if window.window_type == WindowType.SLIDING and window.slide_seconds:
                    window.start_time += timedelta(seconds=window.slide_seconds)
                    window.end_time += timedelta(seconds=window.slide_seconds)
                else:
                    # 移除已完成的窗口
                    if window.window_id in self.active_windows:
                        del self.active_windows[window.window_id]
            
        except Exception as e:
            self.logger.error(f"处理窗口失败: {window.window_id} - {e}")
    
    async def _process_output_data(self):
        """处理输出数据（后台任务）"""
        while True:
            try:
                # 获取输出数据
                output_data = await self.output_queue.get()
                
                # 通知订阅者
                await self._notify_result_subscribers(output_data)
                
                # 发布事件
                if self.event_service:
                    await self.event_service.publish_event(
                        EventType.STREAM,
                        "stream_processor",
                        output_data,
                        tags=["processing_result", output_data.get("type", "unknown")]
                    )
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"处理输出数据失败: {e}")
    
    async def _notify_result_subscribers(self, result: Dict[str, Any]):
        """通知结果订阅者"""
        try:
            result_type = result.get("type", "unknown")
            
            # 通知所有订阅者和特定类型订阅者
            for key, callbacks in self.result_subscribers.items():
                subscriber_id, subscribed_type = key.split(":", 1)
                
                if subscribed_type == "all" or subscribed_type == result_type:
                    for callback in callbacks:
                        try:
                            if asyncio.iscoroutinefunction(callback):
                                await callback(result)
                            else:
                                callback(result)
                        except Exception as e:
                            self.logger.error(f"结果订阅者回调失败: {subscriber_id} - {e}")
                            
        except Exception as e:
            self.logger.error(f"通知结果订阅者失败: {e}")
    
    async def _update_stats(self):
        """更新统计（后台任务）"""
        last_processed = 0
        last_time = time.time()
        
        while True:
            try:
                current_time = time.time()
                current_processed = self.stats["total_processed"]
                
                # 计算吞吐量
                time_diff = current_time - last_time
                if time_diff > 0:
                    processed_diff = current_processed - last_processed
                    self.stats["throughput_per_second"] = processed_diff / time_diff
                
                last_processed = current_processed
                last_time = current_time
                
                # 收集指标
                if self.monitoring_service:
                    await self.monitoring_service.collect_metric(
                        "stream_processing_throughput",
                        MetricType.GAUGE,
                        self.stats["throughput_per_second"],
                        scope=MonitoringScope.STREAM,
                        unit="items/second"
                    )
                    
                    await self.monitoring_service.collect_metric(
                        "stream_processing_errors",
                        MetricType.COUNTER,
                        self.stats["processing_errors"],
                        scope=MonitoringScope.STREAM
                    )
                
                await asyncio.sleep(60)  # 每分钟更新一次
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"更新统计失败: {e}")
                await asyncio.sleep(60)


# 全局流式数据处理服务实例
stream_processor = StreamProcessor()