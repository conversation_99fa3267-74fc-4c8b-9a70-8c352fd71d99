#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A2A多智能体系统 工具配置管理

提供工具配置的加载、验证、更新和持久化功能
"""

import os
import json
import yaml
import logging
from typing import Dict, List, Any, Optional, Union, Type, Set
from dataclasses import dataclass, field, asdict
from enum import Enum
from pathlib import Path
from datetime import datetime
import copy

# Google ADK imports
from google.ai.generativelanguage import FunctionDeclaration, Schema, Type as SchemaType

from .base_tool import ToolConfig, ToolPermission


class ConfigFormat(Enum):
    """配置文件格式枚举"""
    JSON = "json"
    YAML = "yaml"
    YML = "yml"
    TOML = "toml"
    INI = "ini"


class ConfigScope(Enum):
    """配置作用域枚举"""
    GLOBAL = "global"  # 全局配置
    TOOL = "tool"      # 工具配置
    USER = "user"      # 用户配置
    SESSION = "session"  # 会话配置


class ConfigSource(Enum):
    """配置来源枚举"""
    FILE = "file"          # 文件配置
    ENVIRONMENT = "env"    # 环境变量
    DATABASE = "database"  # 数据库配置
    REMOTE = "remote"      # 远程配置
    DEFAULT = "default"    # 默认配置


@dataclass
class ConfigEntry:
    """配置条目"""
    key: str
    value: Any
    scope: ConfigScope
    source: ConfigSource
    description: Optional[str] = None
    required: bool = False
    sensitive: bool = False  # 敏感信息（如密码、密钥）
    validation_rule: Optional[str] = None
    default_value: Any = None
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ConfigSchema:
    """配置模式定义"""
    name: str
    version: str
    description: Optional[str] = None
    entries: List[ConfigEntry] = field(default_factory=list)
    dependencies: List[str] = field(default_factory=list)
    validation_rules: Dict[str, str] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ConfigProfile:
    """配置配置文件"""
    name: str
    description: Optional[str] = None
    configs: Dict[str, Any] = field(default_factory=dict)
    parent_profile: Optional[str] = None
    active: bool = True
    created_at: datetime = field(default_factory=datetime.now)
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ConfigValidationResult:
    """配置验证结果"""
    valid: bool
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    missing_required: List[str] = field(default_factory=list)
    invalid_values: Dict[str, str] = field(default_factory=dict)


@dataclass
class ConfigManagerConfig:
    """配置管理器配置"""
    # 配置文件路径
    config_dir: str = "./configs"
    global_config_file: str = "global.yaml"
    tool_config_dir: str = "tools"
    user_config_dir: str = "users"
    
    # 配置格式
    default_format: ConfigFormat = ConfigFormat.YAML
    supported_formats: Set[ConfigFormat] = field(default_factory=lambda: {
        ConfigFormat.JSON, ConfigFormat.YAML, ConfigFormat.YML
    })
    
    # 环境变量
    env_prefix: str = "A2A_"
    env_separator: str = "__"
    
    # 验证配置
    enable_validation: bool = True
    strict_validation: bool = False
    
    # 缓存配置
    enable_cache: bool = True
    cache_ttl: int = 300  # 5分钟
    
    # 备份配置
    enable_backup: bool = True
    backup_dir: str = "./backups"
    max_backups: int = 10
    
    # 监控配置
    enable_file_watch: bool = True
    auto_reload: bool = True
    
    # 安全配置
    encrypt_sensitive: bool = True
    encryption_key: Optional[str] = None


class ConfigManagerError(Exception):
    """配置管理器异常"""
    pass


class ConfigManager:
    """工具配置管理器
    
    提供工具配置的加载、验证、更新和持久化功能
    """
    
    def __init__(self, config: ConfigManagerConfig):
        """
        初始化配置管理器
        
        Args:
            config: 配置管理器配置
        """
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 配置存储
        self._configs: Dict[str, Dict[str, Any]] = {}
        self._schemas: Dict[str, ConfigSchema] = {}
        self._profiles: Dict[str, ConfigProfile] = {}
        
        # 缓存
        self._cache: Dict[str, Any] = {}
        self._cache_timestamps: Dict[str, datetime] = {}
        
        # 文件监控
        self._file_watchers: Dict[str, Any] = {}
        
        # 初始化目录
        self._init_directories()
        
        # 加载配置
        self._load_all_configs()
    
    def _init_directories(self) -> None:
        """
        初始化配置目录
        """
        directories = [
            self.config.config_dir,
            os.path.join(self.config.config_dir, self.config.tool_config_dir),
            os.path.join(self.config.config_dir, self.config.user_config_dir),
            self.config.backup_dir
        ]
        
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)
    
    def _load_all_configs(self) -> None:
        """
        加载所有配置
        """
        try:
            # 加载全局配置
            self._load_global_config()
            
            # 加载工具配置
            self._load_tool_configs()
            
            # 加载用户配置
            self._load_user_configs()
            
            # 加载配置文件
            self._load_profiles()
            
            self.logger.info("所有配置加载完成")
            
        except Exception as e:
            self.logger.error(f"加载配置失败: {e}")
            raise ConfigManagerError(f"加载配置失败: {e}")
    
    def _load_global_config(self) -> None:
        """
        加载全局配置
        """
        global_config_path = os.path.join(
            self.config.config_dir,
            self.config.global_config_file
        )
        
        if os.path.exists(global_config_path):
            config_data = self._load_config_file(global_config_path)
            self._configs["global"] = config_data
            self.logger.info(f"全局配置已加载: {global_config_path}")
        else:
            # 创建默认全局配置
            default_config = self._get_default_global_config()
            self._configs["global"] = default_config
            self._save_config_file(global_config_path, default_config)
            self.logger.info(f"创建默认全局配置: {global_config_path}")
    
    def _load_tool_configs(self) -> None:
        """
        加载工具配置
        """
        tool_config_dir = os.path.join(
            self.config.config_dir,
            self.config.tool_config_dir
        )
        
        if not os.path.exists(tool_config_dir):
            return
        
        for config_file in os.listdir(tool_config_dir):
            if self._is_config_file(config_file):
                config_path = os.path.join(tool_config_dir, config_file)
                tool_name = os.path.splitext(config_file)[0]
                
                try:
                    config_data = self._load_config_file(config_path)
                    self._configs[f"tool:{tool_name}"] = config_data
                    self.logger.debug(f"工具配置已加载: {tool_name}")
                except Exception as e:
                    self.logger.error(f"加载工具配置失败 {tool_name}: {e}")
    
    def _load_user_configs(self) -> None:
        """
        加载用户配置
        """
        user_config_dir = os.path.join(
            self.config.config_dir,
            self.config.user_config_dir
        )
        
        if not os.path.exists(user_config_dir):
            return
        
        for config_file in os.listdir(user_config_dir):
            if self._is_config_file(config_file):
                config_path = os.path.join(user_config_dir, config_file)
                user_id = os.path.splitext(config_file)[0]
                
                try:
                    config_data = self._load_config_file(config_path)
                    self._configs[f"user:{user_id}"] = config_data
                    self.logger.debug(f"用户配置已加载: {user_id}")
                except Exception as e:
                    self.logger.error(f"加载用户配置失败 {user_id}: {e}")
    
    def _load_profiles(self) -> None:
        """
        加载配置文件
        """
        profiles_file = os.path.join(self.config.config_dir, "profiles.yaml")
        
        if os.path.exists(profiles_file):
            try:
                profiles_data = self._load_config_file(profiles_file)
                for profile_name, profile_data in profiles_data.items():
                    profile = ConfigProfile(
                        name=profile_name,
                        **profile_data
                    )
                    self._profiles[profile_name] = profile
                self.logger.info(f"配置文件已加载: {len(self._profiles)}个")
            except Exception as e:
                self.logger.error(f"加载配置文件失败: {e}")
    
    def _load_config_file(self, file_path: str) -> Dict[str, Any]:
        """
        加载配置文件
        
        Args:
            file_path: 文件路径
        
        Returns:
            Dict[str, Any]: 配置数据
        """
        file_ext = Path(file_path).suffix.lower()
        
        with open(file_path, 'r', encoding='utf-8') as f:
            if file_ext == '.json':
                return json.load(f)
            elif file_ext in ['.yaml', '.yml']:
                return yaml.safe_load(f) or {}
            else:
                raise ConfigManagerError(f"不支持的配置文件格式: {file_ext}")
    
    def _save_config_file(self, file_path: str, config_data: Dict[str, Any]) -> None:
        """
        保存配置文件
        
        Args:
            file_path: 文件路径
            config_data: 配置数据
        """
        # 创建备份
        if self.config.enable_backup and os.path.exists(file_path):
            self._create_backup(file_path)
        
        file_ext = Path(file_path).suffix.lower()
        
        with open(file_path, 'w', encoding='utf-8') as f:
            if file_ext == '.json':
                json.dump(config_data, f, indent=2, ensure_ascii=False)
            elif file_ext in ['.yaml', '.yml']:
                yaml.dump(config_data, f, default_flow_style=False, allow_unicode=True)
            else:
                raise ConfigManagerError(f"不支持的配置文件格式: {file_ext}")
    
    def _is_config_file(self, filename: str) -> bool:
        """
        检查是否为配置文件
        
        Args:
            filename: 文件名
        
        Returns:
            bool: 是否为配置文件
        """
        file_ext = Path(filename).suffix.lower()
        return file_ext in ['.json', '.yaml', '.yml']
    
    def _get_default_global_config(self) -> Dict[str, Any]:
        """
        获取默认全局配置
        
        Returns:
            Dict[str, Any]: 默认配置
        """
        return {
            'version': '1.0.0',
            'created_at': datetime.now().isoformat(),
            'logging': {
                'level': 'INFO',
                'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            },
            'security': {
                'enable_auth': True,
                'session_timeout': 3600
            },
            'performance': {
                'max_concurrent_executions': 10,
                'default_timeout': 30,
                'enable_cache': True,
                'cache_ttl': 300
            },
            'features': {
                'enable_mcp': True,
                'enable_websocket': True,
                'enable_file_operations': True
            }
        }
    
    def _create_backup(self, file_path: str) -> None:
        """
        创建配置文件备份
        
        Args:
            file_path: 文件路径
        """
        try:
            backup_name = f"{Path(file_path).stem}_{datetime.now().strftime('%Y%m%d_%H%M%S')}{Path(file_path).suffix}"
            backup_path = os.path.join(self.config.backup_dir, backup_name)
            
            import shutil
            shutil.copy2(file_path, backup_path)
            
            # 清理旧备份
            self._cleanup_old_backups(Path(file_path).stem)
            
            self.logger.debug(f"配置备份已创建: {backup_path}")
            
        except Exception as e:
            self.logger.warning(f"创建配置备份失败: {e}")
    
    def _cleanup_old_backups(self, file_stem: str) -> None:
        """
        清理旧备份文件
        
        Args:
            file_stem: 文件名（不含扩展名）
        """
        try:
            backup_files = []
            for backup_file in os.listdir(self.config.backup_dir):
                if backup_file.startswith(file_stem + '_'):
                    backup_path = os.path.join(self.config.backup_dir, backup_file)
                    backup_files.append((backup_path, os.path.getmtime(backup_path)))
            
            # 按修改时间排序
            backup_files.sort(key=lambda x: x[1], reverse=True)
            
            # 删除超出限制的备份
            for backup_path, _ in backup_files[self.config.max_backups:]:
                os.remove(backup_path)
                self.logger.debug(f"删除旧备份: {backup_path}")
                
        except Exception as e:
            self.logger.warning(f"清理旧备份失败: {e}")
    
    def get_config(self, key: str, scope: ConfigScope = ConfigScope.GLOBAL, 
                   user_id: Optional[str] = None, tool_name: Optional[str] = None,
                   default: Any = None) -> Any:
        """
        获取配置值
        
        Args:
            key: 配置键
            scope: 配置作用域
            user_id: 用户ID（用户作用域时需要）
            tool_name: 工具名称（工具作用域时需要）
            default: 默认值
        
        Returns:
            Any: 配置值
        """
        try:
            # 检查缓存
            cache_key = f"{scope.value}:{user_id or ''}:{tool_name or ''}:{key}"
            if self.config.enable_cache and cache_key in self._cache:
                cache_time = self._cache_timestamps.get(cache_key)
                if cache_time and (datetime.now() - cache_time).seconds < self.config.cache_ttl:
                    return self._cache[cache_key]
            
            # 构建配置键
            if scope == ConfigScope.GLOBAL:
                config_key = "global"
            elif scope == ConfigScope.TOOL:
                if not tool_name:
                    raise ConfigManagerError("工具作用域需要提供tool_name")
                config_key = f"tool:{tool_name}"
            elif scope == ConfigScope.USER:
                if not user_id:
                    raise ConfigManagerError("用户作用域需要提供user_id")
                config_key = f"user:{user_id}"
            else:
                raise ConfigManagerError(f"不支持的配置作用域: {scope}")
            
            # 获取配置
            config_data = self._configs.get(config_key, {})
            
            # 解析嵌套键
            value = self._get_nested_value(config_data, key, default)
            
            # 检查环境变量覆盖
            env_value = self._get_env_value(key, scope, user_id, tool_name)
            if env_value is not None:
                value = env_value
            
            # 更新缓存
            if self.config.enable_cache:
                self._cache[cache_key] = value
                self._cache_timestamps[cache_key] = datetime.now()
            
            return value
            
        except Exception as e:
            self.logger.error(f"获取配置失败 {key}: {e}")
            return default
    
    def set_config(self, key: str, value: Any, scope: ConfigScope = ConfigScope.GLOBAL,
                   user_id: Optional[str] = None, tool_name: Optional[str] = None,
                   save: bool = True) -> None:
        """
        设置配置值
        
        Args:
            key: 配置键
            value: 配置值
            scope: 配置作用域
            user_id: 用户ID（用户作用域时需要）
            tool_name: 工具名称（工具作用域时需要）
            save: 是否立即保存到文件
        """
        try:
            # 构建配置键
            if scope == ConfigScope.GLOBAL:
                config_key = "global"
                file_path = os.path.join(self.config.config_dir, self.config.global_config_file)
            elif scope == ConfigScope.TOOL:
                if not tool_name:
                    raise ConfigManagerError("工具作用域需要提供tool_name")
                config_key = f"tool:{tool_name}"
                file_path = os.path.join(
                    self.config.config_dir,
                    self.config.tool_config_dir,
                    f"{tool_name}.yaml"
                )
            elif scope == ConfigScope.USER:
                if not user_id:
                    raise ConfigManagerError("用户作用域需要提供user_id")
                config_key = f"user:{user_id}"
                file_path = os.path.join(
                    self.config.config_dir,
                    self.config.user_config_dir,
                    f"{user_id}.yaml"
                )
            else:
                raise ConfigManagerError(f"不支持的配置作用域: {scope}")
            
            # 获取或创建配置
            if config_key not in self._configs:
                self._configs[config_key] = {}
            
            # 设置嵌套值
            self._set_nested_value(self._configs[config_key], key, value)
            
            # 清除缓存
            if self.config.enable_cache:
                cache_key = f"{scope.value}:{user_id or ''}:{tool_name or ''}:{key}"
                if cache_key in self._cache:
                    del self._cache[cache_key]
                if cache_key in self._cache_timestamps:
                    del self._cache_timestamps[cache_key]
            
            # 保存到文件
            if save:
                self._save_config_file(file_path, self._configs[config_key])
            
            self.logger.debug(f"配置已设置: {key} = {value}")
            
        except Exception as e:
            self.logger.error(f"设置配置失败 {key}: {e}")
            raise ConfigManagerError(f"设置配置失败: {e}")
    
    def _get_nested_value(self, data: Dict[str, Any], key: str, default: Any = None) -> Any:
        """
        获取嵌套配置值
        
        Args:
            data: 配置数据
            key: 配置键（支持点分隔的嵌套键）
            default: 默认值
        
        Returns:
            Any: 配置值
        """
        keys = key.split('.')
        current = data
        
        for k in keys:
            if isinstance(current, dict) and k in current:
                current = current[k]
            else:
                return default
        
        return current
    
    def _set_nested_value(self, data: Dict[str, Any], key: str, value: Any) -> None:
        """
        设置嵌套配置值
        
        Args:
            data: 配置数据
            key: 配置键（支持点分隔的嵌套键）
            value: 配置值
        """
        keys = key.split('.')
        current = data
        
        for k in keys[:-1]:
            if k not in current:
                current[k] = {}
            current = current[k]
        
        current[keys[-1]] = value
    
    def _get_env_value(self, key: str, scope: ConfigScope, 
                       user_id: Optional[str] = None, 
                       tool_name: Optional[str] = None) -> Any:
        """
        获取环境变量值
        
        Args:
            key: 配置键
            scope: 配置作用域
            user_id: 用户ID
            tool_name: 工具名称
        
        Returns:
            Any: 环境变量值
        """
        # 构建环境变量名
        env_key_parts = [self.config.env_prefix.rstrip('_')]
        
        if scope == ConfigScope.TOOL and tool_name:
            env_key_parts.append("TOOL")
            env_key_parts.append(tool_name.upper())
        elif scope == ConfigScope.USER and user_id:
            env_key_parts.append("USER")
            env_key_parts.append(user_id.upper())
        
        # 转换配置键
        config_key = key.replace('.', self.config.env_separator).upper()
        env_key_parts.append(config_key)
        
        env_key = '_'.join(env_key_parts)
        
        # 获取环境变量
        env_value = os.getenv(env_key)
        if env_value is None:
            return None
        
        # 尝试转换类型
        try:
            # 尝试JSON解析
            return json.loads(env_value)
        except (json.JSONDecodeError, ValueError):
            # 尝试布尔值
            if env_value.lower() in ['true', 'false']:
                return env_value.lower() == 'true'
            
            # 尝试数字
            try:
                if '.' in env_value:
                    return float(env_value)
                else:
                    return int(env_value)
            except ValueError:
                pass
            
            # 返回字符串
            return env_value
    
    def get_tool_config(self, tool_name: str) -> ToolConfig:
        """
        获取工具配置
        
        Args:
            tool_name: 工具名称
        
        Returns:
            ToolConfig: 工具配置
        """
        try:
            # 获取工具配置数据
            config_data = self._configs.get(f"tool:{tool_name}", {})
            
            # 构建ToolConfig
            tool_config = ToolConfig(
                max_retries=config_data.get('max_retries', 3),
                timeout=config_data.get('timeout', 30),
                rate_limit=config_data.get('rate_limit', 100),
                cache_enabled=config_data.get('cache_enabled', True),
                cache_ttl=config_data.get('cache_ttl', 300),
                log_enabled=config_data.get('log_enabled', True),
                log_level=config_data.get('log_level', 'INFO'),
                custom_settings=config_data.get('custom_settings', {})
            )
            
            return tool_config
            
        except Exception as e:
            self.logger.error(f"获取工具配置失败 {tool_name}: {e}")
            # 返回默认配置
            return ToolConfig()
    
    def set_tool_config(self, tool_name: str, tool_config: ToolConfig) -> None:
        """
        设置工具配置
        
        Args:
            tool_name: 工具名称
            tool_config: 工具配置
        """
        try:
            config_data = asdict(tool_config)
            
            # 设置配置
            config_key = f"tool:{tool_name}"
            self._configs[config_key] = config_data
            
            # 保存到文件
            file_path = os.path.join(
                self.config.config_dir,
                self.config.tool_config_dir,
                f"{tool_name}.yaml"
            )
            self._save_config_file(file_path, config_data)
            
            self.logger.info(f"工具配置已保存: {tool_name}")
            
        except Exception as e:
            self.logger.error(f"设置工具配置失败 {tool_name}: {e}")
            raise ConfigManagerError(f"设置工具配置失败: {e}")
    
    def validate_config(self, config_data: Dict[str, Any], 
                       schema: Optional[ConfigSchema] = None) -> ConfigValidationResult:
        """
        验证配置
        
        Args:
            config_data: 配置数据
            schema: 配置模式（可选）
        
        Returns:
            ConfigValidationResult: 验证结果
        """
        result = ConfigValidationResult(valid=True)
        
        if not self.config.enable_validation:
            return result
        
        try:
            if schema:
                # 使用提供的模式验证
                result = self._validate_with_schema(config_data, schema)
            else:
                # 基本验证
                result = self._basic_validation(config_data)
            
            return result
            
        except Exception as e:
            self.logger.error(f"配置验证失败: {e}")
            result.valid = False
            result.errors.append(f"验证异常: {e}")
            return result
    
    def _validate_with_schema(self, config_data: Dict[str, Any], 
                             schema: ConfigSchema) -> ConfigValidationResult:
        """
        使用模式验证配置
        
        Args:
            config_data: 配置数据
            schema: 配置模式
        
        Returns:
            ConfigValidationResult: 验证结果
        """
        result = ConfigValidationResult(valid=True)
        
        # 检查必需字段
        for entry in schema.entries:
            if entry.required:
                value = self._get_nested_value(config_data, entry.key)
                if value is None:
                    result.missing_required.append(entry.key)
                    result.valid = False
        
        # 验证字段值
        for entry in schema.entries:
            value = self._get_nested_value(config_data, entry.key)
            if value is not None and entry.validation_rule:
                if not self._validate_value(value, entry.validation_rule):
                    result.invalid_values[entry.key] = f"值 '{value}' 不符合规则 '{entry.validation_rule}'"
                    result.valid = False
        
        return result
    
    def _basic_validation(self, config_data: Dict[str, Any]) -> ConfigValidationResult:
        """
        基本配置验证
        
        Args:
            config_data: 配置数据
        
        Returns:
            ConfigValidationResult: 验证结果
        """
        result = ConfigValidationResult(valid=True)
        
        # 检查基本结构
        if not isinstance(config_data, dict):
            result.errors.append("配置数据必须是字典类型")
            result.valid = False
            return result
        
        # 检查常见配置项
        common_checks = {
            'timeout': lambda x: isinstance(x, (int, float)) and x > 0,
            'max_retries': lambda x: isinstance(x, int) and x >= 0,
            'rate_limit': lambda x: isinstance(x, int) and x > 0,
            'cache_ttl': lambda x: isinstance(x, int) and x > 0
        }
        
        for key, validator in common_checks.items():
            value = self._get_nested_value(config_data, key)
            if value is not None and not validator(value):
                result.invalid_values[key] = f"无效的 {key} 值: {value}"
                result.valid = False
        
        return result
    
    def _validate_value(self, value: Any, rule: str) -> bool:
        """
        验证配置值
        
        Args:
            value: 配置值
            rule: 验证规则
        
        Returns:
            bool: 是否有效
        """
        try:
            # 简单的规则验证（可以扩展为更复杂的规则引擎）
            if rule.startswith('type:'):
                expected_type = rule[5:]
                if expected_type == 'int':
                    return isinstance(value, int)
                elif expected_type == 'float':
                    return isinstance(value, (int, float))
                elif expected_type == 'str':
                    return isinstance(value, str)
                elif expected_type == 'bool':
                    return isinstance(value, bool)
                elif expected_type == 'list':
                    return isinstance(value, list)
                elif expected_type == 'dict':
                    return isinstance(value, dict)
            
            elif rule.startswith('range:'):
                # 范围验证：range:1-100
                range_str = rule[6:]
                min_val, max_val = map(int, range_str.split('-'))
                return min_val <= value <= max_val
            
            elif rule.startswith('regex:'):
                # 正则验证
                import re
                pattern = rule[6:]
                return bool(re.match(pattern, str(value)))
            
            return True
            
        except Exception:
            return False
    
    def create_profile(self, name: str, configs: Dict[str, Any], 
                      description: Optional[str] = None,
                      parent_profile: Optional[str] = None) -> ConfigProfile:
        """
        创建配置文件
        
        Args:
            name: 配置文件名称
            configs: 配置数据
            description: 描述
            parent_profile: 父配置文件
        
        Returns:
            ConfigProfile: 配置文件
        """
        try:
            profile = ConfigProfile(
                name=name,
                description=description,
                configs=configs,
                parent_profile=parent_profile
            )
            
            self._profiles[name] = profile
            
            # 保存配置文件
            self._save_profiles()
            
            self.logger.info(f"配置文件已创建: {name}")
            return profile
            
        except Exception as e:
            self.logger.error(f"创建配置文件失败 {name}: {e}")
            raise ConfigManagerError(f"创建配置文件失败: {e}")
    
    def apply_profile(self, profile_name: str, scope: ConfigScope = ConfigScope.GLOBAL,
                     user_id: Optional[str] = None, tool_name: Optional[str] = None) -> None:
        """
        应用配置文件
        
        Args:
            profile_name: 配置文件名称
            scope: 应用作用域
            user_id: 用户ID
            tool_name: 工具名称
        """
        try:
            profile = self._profiles.get(profile_name)
            if not profile:
                raise ConfigManagerError(f"配置文件不存在: {profile_name}")
            
            # 获取完整配置（包括继承）
            full_configs = self._resolve_profile_configs(profile)
            
            # 应用配置
            for key, value in full_configs.items():
                self.set_config(key, value, scope, user_id, tool_name, save=False)
            
            # 批量保存
            self._save_scope_config(scope, user_id, tool_name)
            
            self.logger.info(f"配置文件已应用: {profile_name}")
            
        except Exception as e:
            self.logger.error(f"应用配置文件失败 {profile_name}: {e}")
            raise ConfigManagerError(f"应用配置文件失败: {e}")
    
    def _resolve_profile_configs(self, profile: ConfigProfile) -> Dict[str, Any]:
        """
        解析配置文件配置（处理继承）
        
        Args:
            profile: 配置文件
        
        Returns:
            Dict[str, Any]: 完整配置
        """
        configs = {}
        
        # 处理父配置文件
        if profile.parent_profile:
            parent_profile = self._profiles.get(profile.parent_profile)
            if parent_profile:
                parent_configs = self._resolve_profile_configs(parent_profile)
                configs.update(parent_configs)
        
        # 应用当前配置文件
        configs.update(profile.configs)
        
        return configs
    
    def _save_profiles(self) -> None:
        """
        保存配置文件
        """
        try:
            profiles_data = {}
            for name, profile in self._profiles.items():
                profiles_data[name] = {
                    'description': profile.description,
                    'configs': profile.configs,
                    'parent_profile': profile.parent_profile,
                    'active': profile.active,
                    'created_at': profile.created_at.isoformat(),
                    'metadata': profile.metadata
                }
            
            profiles_file = os.path.join(self.config.config_dir, "profiles.yaml")
            self._save_config_file(profiles_file, profiles_data)
            
        except Exception as e:
            self.logger.error(f"保存配置文件失败: {e}")
    
    def _save_scope_config(self, scope: ConfigScope, 
                          user_id: Optional[str] = None, 
                          tool_name: Optional[str] = None) -> None:
        """
        保存作用域配置
        
        Args:
            scope: 配置作用域
            user_id: 用户ID
            tool_name: 工具名称
        """
        try:
            if scope == ConfigScope.GLOBAL:
                config_key = "global"
                file_path = os.path.join(self.config.config_dir, self.config.global_config_file)
            elif scope == ConfigScope.TOOL and tool_name:
                config_key = f"tool:{tool_name}"
                file_path = os.path.join(
                    self.config.config_dir,
                    self.config.tool_config_dir,
                    f"{tool_name}.yaml"
                )
            elif scope == ConfigScope.USER and user_id:
                config_key = f"user:{user_id}"
                file_path = os.path.join(
                    self.config.config_dir,
                    self.config.user_config_dir,
                    f"{user_id}.yaml"
                )
            else:
                return
            
            config_data = self._configs.get(config_key, {})
            self._save_config_file(file_path, config_data)
            
        except Exception as e:
            self.logger.error(f"保存作用域配置失败: {e}")
    
    def get_all_configs(self) -> Dict[str, Dict[str, Any]]:
        """
        获取所有配置
        
        Returns:
            Dict[str, Dict[str, Any]]: 所有配置
        """
        return copy.deepcopy(self._configs)
    
    def get_all_profiles(self) -> Dict[str, ConfigProfile]:
        """
        获取所有配置文件
        
        Returns:
            Dict[str, ConfigProfile]: 所有配置文件
        """
        return copy.deepcopy(self._profiles)
    
    def reload_configs(self) -> None:
        """
        重新加载所有配置
        """
        try:
            # 清除缓存
            self._cache.clear()
            self._cache_timestamps.clear()
            
            # 重新加载
            self._load_all_configs()
            
            self.logger.info("配置重新加载完成")
            
        except Exception as e:
            self.logger.error(f"重新加载配置失败: {e}")
            raise ConfigManagerError(f"重新加载配置失败: {e}")


# 全局配置管理器实例
_global_config_manager: Optional[ConfigManager] = None


def get_global_config_manager() -> Optional[ConfigManager]:
    """
    获取全局配置管理器实例
    
    Returns:
        Optional[ConfigManager]: 配置管理器实例
    """
    return _global_config_manager


def set_global_config_manager(manager: ConfigManager) -> None:
    """
    设置全局配置管理器实例
    
    Args:
        manager: 配置管理器实例
    """
    global _global_config_manager
    _global_config_manager = manager