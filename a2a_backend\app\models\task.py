#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A2A多智能体系统任务模型

定义任务相关的数据模型
"""

from datetime import datetime
from typing import Optional, List, Dict, Any
from sqlalchemy import Column, Integer, String, Text, Boolean, DateTime, ForeignKey, Index, Float
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from .base import BaseModel


class Task(BaseModel):
    """
    任务模型
    
    存储智能体执行的任务信息
    """
    
    __tablename__ = "tasks"
    
    # 用户和智能体关联
    user_id = Column(
        Integer,
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        comment="创建者用户ID"
    )
    
    owner_id = Column(
        Integer,
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        comment="拥有者用户ID"
    )
    
    agent_id = Column(
        Integer,
        ForeignKey("agents.id", ondelete="CASCADE"),
        nullable=False,
        comment="执行智能体ID"
    )
    
    # 任务基本信息
    task_id = Column(
        String(36),
        nullable=False,
        unique=True,
        comment="任务唯一标识"
    )
    
    title = Column(
        String(200),
        nullable=False,
        comment="任务标题"
    )
    
    description = Column(
        Text,
        nullable=True,
        comment="任务描述"
    )
    
    # 任务类型和优先级
    task_type = Column(
        String(50),
        nullable=False,
        default="general",
        comment="任务类型"
    )
    
    priority = Column(
        String(20),
        nullable=False,
        default="medium",
        comment="任务优先级"
    )
    
    # 任务状态
    status = Column(
        String(20),
        nullable=False,
        default="pending",
        comment="任务状态"
    )
    
    progress = Column(
        Float,
        nullable=False,
        default=0.0,
        comment="任务进度（0-100）"
    )
    
    # 任务配置
    config = Column(
        Text,
        nullable=True,
        comment="任务配置（JSON格式）"
    )
    
    # 输入和输出
    input_data = Column(
        Text,
        nullable=True,
        comment="输入数据（JSON格式）"
    )
    
    output_data = Column(
        Text,
        nullable=True,
        comment="输出数据（JSON格式）"
    )
    
    # 执行信息
    execution_log = Column(
        Text,
        nullable=True,
        comment="执行日志（JSON格式）"
    )
    
    error_message = Column(
        Text,
        nullable=True,
        comment="错误信息"
    )
    
    # 时间信息
    scheduled_at = Column(
        DateTime,
        nullable=True,
        comment="计划执行时间"
    )
    
    started_at = Column(
        DateTime,
        nullable=True,
        comment="开始执行时间"
    )
    
    completed_at = Column(
        DateTime,
        nullable=True,
        comment="完成时间"
    )
    
    # 性能指标
    execution_time = Column(
        Float,
        nullable=True,
        comment="执行时间（秒）"
    )
    
    # 资源使用
    tokens_used = Column(
        Integer,
        nullable=False,
        default=0,
        comment="使用的token数"
    )
    
    cost = Column(
        Float,
        nullable=False,
        default=0.0,
        comment="执行费用"
    )
    
    # 依赖关系
    parent_task_id = Column(
        Integer,
        ForeignKey("tasks.id", ondelete="SET NULL"),
        nullable=True,
        comment="父任务ID"
    )
    
    # 关联关系
    user = relationship("User", back_populates="tasks")
    agent = relationship("Agent", back_populates="tasks")
    parent_task = relationship("Task", remote_side="Task.id")
    subtasks = relationship("Task", back_populates="parent_task")
    executions = relationship("TaskExecution", back_populates="task", cascade="all, delete-orphan")
    
    # 索引
    __table_args__ = (
        Index("idx_tasks_user_id", "user_id"),
        Index("idx_tasks_agent_id", "agent_id"),
        Index("idx_tasks_task_id", "task_id"),
        Index("idx_tasks_type", "task_type"),
        Index("idx_tasks_status", "status"),
        Index("idx_tasks_priority", "priority"),
        Index("idx_tasks_scheduled_at", "scheduled_at"),
        Index("idx_tasks_parent", "parent_task_id"),
        Index("idx_tasks_is_active", "is_active"),
    )
    
    def set_config(self, config: Dict[str, Any]) -> None:
        """
        设置任务配置
        
        Args:
            config: 配置字典
        """
        import json
        self.config = json.dumps(config, ensure_ascii=False)
    
    def get_config(self) -> Dict[str, Any]:
        """
        获取任务配置
        
        Returns:
            Dict[str, Any]: 配置字典
        """
        if not self.config:
            return {}
        
        try:
            import json
            return json.loads(self.config)
        except (json.JSONDecodeError, TypeError):
            return {}
    
    def set_input_data(self, data: Dict[str, Any]) -> None:
        """
        设置输入数据
        
        Args:
            data: 输入数据字典
        """
        import json
        self.input_data = json.dumps(data, ensure_ascii=False)
    
    def get_input_data(self) -> Dict[str, Any]:
        """
        获取输入数据
        
        Returns:
            Dict[str, Any]: 输入数据字典
        """
        if not self.input_data:
            return {}
        
        try:
            import json
            return json.loads(self.input_data)
        except (json.JSONDecodeError, TypeError):
            return {}
    
    def set_output_data(self, data: Dict[str, Any]) -> None:
        """
        设置输出数据
        
        Args:
            data: 输出数据字典
        """
        import json
        self.output_data = json.dumps(data, ensure_ascii=False)
    
    def get_output_data(self) -> Dict[str, Any]:
        """
        获取输出数据
        
        Returns:
            Dict[str, Any]: 输出数据字典
        """
        if not self.output_data:
            return {}
        
        try:
            import json
            return json.loads(self.output_data)
        except (json.JSONDecodeError, TypeError):
            return {}
    
    def set_execution_log(self, log: List[Dict[str, Any]]) -> None:
        """
        设置执行日志
        
        Args:
            log: 执行日志列表
        """
        import json
        self.execution_log = json.dumps(log, ensure_ascii=False)
    
    def get_execution_log(self) -> List[Dict[str, Any]]:
        """
        获取执行日志
        
        Returns:
            List[Dict[str, Any]]: 执行日志列表
        """
        if not self.execution_log:
            return []
        
        try:
            import json
            return json.loads(self.execution_log)
        except (json.JSONDecodeError, TypeError):
            return []
    
    def add_log_entry(self, entry: Dict[str, Any]) -> None:
        """
        添加日志条目
        
        Args:
            entry: 日志条目
        """
        log = self.get_execution_log()
        entry["timestamp"] = datetime.now().isoformat()
        log.append(entry)
        self.set_execution_log(log)
    
    def start_execution(self) -> None:
        """
        开始执行任务
        """
        self.status = "running"
        self.started_at = datetime.now()
        self.add_log_entry({
            "action": "start",
            "message": "任务开始执行"
        })
    
    def complete_task(self, output_data: Dict[str, Any] = None) -> None:
        """
        完成任务
        
        Args:
            output_data: 输出数据
        """
        self.status = "completed"
        self.progress = 100.0
        self.completed_at = datetime.now()
        
        if self.started_at:
            self.execution_time = (self.completed_at - self.started_at).total_seconds()
        
        if output_data:
            self.set_output_data(output_data)
        
        self.add_log_entry({
            "action": "complete",
            "message": "任务执行完成"
        })
    
    def fail_task(self, error_message: str) -> None:
        """
        任务失败
        
        Args:
            error_message: 错误信息
        """
        self.status = "failed"
        self.error_message = error_message
        self.completed_at = datetime.now()
        
        if self.started_at:
            self.execution_time = (self.completed_at - self.started_at).total_seconds()
        
        self.add_log_entry({
            "action": "fail",
            "message": f"任务执行失败: {error_message}"
        })
    
    def pause_task(self) -> None:
        """
        暂停任务
        """
        self.status = "paused"
        self.add_log_entry({
            "action": "pause",
            "message": "任务已暂停"
        })
    
    def resume_task(self) -> None:
        """
        恢复任务
        """
        self.status = "running"
        self.add_log_entry({
            "action": "resume",
            "message": "任务已恢复"
        })
    
    def cancel_task(self) -> None:
        """
        取消任务
        """
        self.status = "cancelled"
        self.completed_at = datetime.now()
        self.add_log_entry({
            "action": "cancel",
            "message": "任务已取消"
        })
    
    def update_progress(self, progress: float) -> None:
        """
        更新任务进度
        
        Args:
            progress: 进度值（0-100）
        """
        self.progress = max(0.0, min(100.0, progress))
        self.add_log_entry({
            "action": "progress",
            "message": f"任务进度更新: {self.progress}%"
        })
    
    @property
    def is_pending(self) -> bool:
        """
        检查是否为待执行状态
        
        Returns:
            bool: 是否为待执行状态
        """
        return self.status == "pending"
    
    @property
    def is_running(self) -> bool:
        """
        检查是否正在执行
        
        Returns:
            bool: 是否正在执行
        """
        return self.status == "running"
    
    @property
    def is_completed(self) -> bool:
        """
        检查是否已完成
        
        Returns:
            bool: 是否已完成
        """
        return self.status == "completed"
    
    @property
    def is_failed(self) -> bool:
        """
        检查是否失败
        
        Returns:
            bool: 是否失败
        """
        return self.status == "failed"
    
    @property
    def is_cancelled(self) -> bool:
        """
        检查是否已取消
        
        Returns:
            bool: 是否已取消
        """
        return self.status == "cancelled"
    
    @property
    def is_finished(self) -> bool:
        """
        检查是否已结束（完成、失败或取消）
        
        Returns:
            bool: 是否已结束
        """
        return self.status in ["completed", "failed", "cancelled"]
    
    def __repr__(self) -> str:
        return f"<Task(id={self.id}, task_id='{self.task_id}', title='{self.title}')>"


class TaskExecution(BaseModel):
    """
    任务执行记录模型
    
    记录任务的每次执行详情
    """
    
    __tablename__ = "task_executions"
    
    # 任务关联
    task_id = Column(
        Integer,
        ForeignKey("tasks.id", ondelete="CASCADE"),
        nullable=False,
        comment="任务ID"
    )
    
    # 用户关联
    user_id = Column(
        Integer,
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        comment="创建者用户ID"
    )
    
    owner_id = Column(
        Integer,
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        comment="拥有者用户ID"
    )
    
    # 执行信息
    execution_id = Column(
        String(36),
        nullable=False,
        unique=True,
        comment="执行唯一标识"
    )
    
    # 执行状态
    status = Column(
        String(20),
        nullable=False,
        default="running",
        comment="执行状态"
    )
    
    # 执行结果
    result = Column(
        Text,
        nullable=True,
        comment="执行结果（JSON格式）"
    )
    
    error_details = Column(
        Text,
        nullable=True,
        comment="错误详情（JSON格式）"
    )
    
    # 性能指标
    start_time = Column(
        DateTime,
        nullable=False,
        default=func.now(),
        comment="开始时间"
    )
    
    end_time = Column(
        DateTime,
        nullable=True,
        comment="结束时间"
    )
    
    duration = Column(
        Float,
        nullable=True,
        comment="执行时长（秒）"
    )
    
    # 资源使用
    memory_usage = Column(
        Float,
        nullable=True,
        comment="内存使用（MB）"
    )
    
    cpu_usage = Column(
        Float,
        nullable=True,
        comment="CPU使用率（%）"
    )
    
    tokens_consumed = Column(
        Integer,
        nullable=False,
        default=0,
        comment="消耗的token数"
    )
    
    api_calls = Column(
        Integer,
        nullable=False,
        default=0,
        comment="API调用次数"
    )
    
    # 执行环境
    environment = Column(
        Text,
        nullable=True,
        comment="执行环境信息（JSON格式）"
    )
    
    # 关联关系
    task = relationship("Task", back_populates="executions")
    
    # 索引
    __table_args__ = (
        Index("idx_task_executions_task_id", "task_id"),
        Index("idx_task_executions_execution_id", "execution_id"),
        Index("idx_task_executions_status", "status"),
        Index("idx_task_executions_start_time", "start_time"),
    )
    
    def set_result(self, result: Dict[str, Any]) -> None:
        """
        设置执行结果
        
        Args:
            result: 结果字典
        """
        import json
        self.result = json.dumps(result, ensure_ascii=False)
    
    def get_result(self) -> Dict[str, Any]:
        """
        获取执行结果
        
        Returns:
            Dict[str, Any]: 结果字典
        """
        if not self.result:
            return {}
        
        try:
            import json
            return json.loads(self.result)
        except (json.JSONDecodeError, TypeError):
            return {}
    
    def set_error_details(self, error: Dict[str, Any]) -> None:
        """
        设置错误详情
        
        Args:
            error: 错误详情字典
        """
        import json
        self.error_details = json.dumps(error, ensure_ascii=False)
    
    def get_error_details(self) -> Dict[str, Any]:
        """
        获取错误详情
        
        Returns:
            Dict[str, Any]: 错误详情字典
        """
        if not self.error_details:
            return {}
        
        try:
            import json
            return json.loads(self.error_details)
        except (json.JSONDecodeError, TypeError):
            return {}
    
    def set_environment(self, env: Dict[str, Any]) -> None:
        """
        设置执行环境
        
        Args:
            env: 环境信息字典
        """
        import json
        self.environment = json.dumps(env, ensure_ascii=False)
    
    def get_environment(self) -> Dict[str, Any]:
        """
        获取执行环境
        
        Returns:
            Dict[str, Any]: 环境信息字典
        """
        if not self.environment:
            return {}
        
        try:
            import json
            return json.loads(self.environment)
        except (json.JSONDecodeError, TypeError):
            return {}
    
    def complete_execution(self, result: Dict[str, Any] = None) -> None:
        """
        完成执行
        
        Args:
            result: 执行结果
        """
        self.status = "completed"
        self.end_time = datetime.now()
        self.duration = (self.end_time - self.start_time).total_seconds()
        
        if result:
            self.set_result(result)
    
    def fail_execution(self, error: Dict[str, Any]) -> None:
        """
        执行失败
        
        Args:
            error: 错误信息
        """
        self.status = "failed"
        self.end_time = datetime.now()
        self.duration = (self.end_time - self.start_time).total_seconds()
        self.set_error_details(error)
    
    def __repr__(self) -> str:
        return f"<TaskExecution(id={self.id}, execution_id='{self.execution_id}', status='{self.status}')>"


class TaskTemplate(BaseModel):
    """
    任务模板模型
    
    存储常用的任务模板
    """
    
    __tablename__ = "task_templates"
    
    # 模板信息
    name = Column(
        String(100),
        nullable=False,
        comment="模板名称"
    )
    
    description = Column(
        Text,
        nullable=True,
        comment="模板描述"
    )
    
    category = Column(
        String(50),
        nullable=False,
        default="general",
        comment="模板分类"
    )
    
    # 模板配置
    template_config = Column(
        Text,
        nullable=False,
        comment="模板配置（JSON格式）"
    )
    
    # 输入输出定义
    input_schema = Column(
        Text,
        nullable=True,
        comment="输入数据结构定义（JSON Schema）"
    )
    
    output_schema = Column(
        Text,
        nullable=True,
        comment="输出数据结构定义（JSON Schema）"
    )
    
    # 状态信息
    is_public = Column(
        Boolean,
        nullable=False,
        default=False,
        comment="是否公开"
    )
    
    is_system = Column(
        Boolean,
        nullable=False,
        default=False,
        comment="是否为系统模板"
    )
    
    # 统计信息
    usage_count = Column(
        Integer,
        nullable=False,
        default=0,
        comment="使用次数"
    )
    
    success_rate = Column(
        Float,
        nullable=False,
        default=0.0,
        comment="成功率"
    )
    
    # 创建者信息
    created_by = Column(
        Integer,
        nullable=True,
        comment="创建者ID"
    )
    
    # 索引
    __table_args__ = (
        Index("idx_task_templates_name", "name"),
        Index("idx_task_templates_category", "category"),
        Index("idx_task_templates_is_public", "is_public"),
        Index("idx_task_templates_is_system", "is_system"),
        Index("idx_task_templates_created_by", "created_by"),
    )
    
    def set_template_config(self, config: Dict[str, Any]) -> None:
        """
        设置模板配置
        
        Args:
            config: 配置字典
        """
        import json
        self.template_config = json.dumps(config, ensure_ascii=False)
    
    def get_template_config(self) -> Dict[str, Any]:
        """
        获取模板配置
        
        Returns:
            Dict[str, Any]: 配置字典
        """
        if not self.template_config:
            return {}
        
        try:
            import json
            return json.loads(self.template_config)
        except (json.JSONDecodeError, TypeError):
            return {}
    
    def increment_usage(self, success: bool = True) -> None:
        """
        增加使用次数并更新成功率
        
        Args:
            success: 是否成功
        """
        old_count = self.usage_count
        self.usage_count += 1
        
        if success:
            success_count = int(self.success_rate * old_count) + 1
        else:
            success_count = int(self.success_rate * old_count)
        
        self.success_rate = success_count / self.usage_count
    
    def __repr__(self) -> str:
        return f"<TaskTemplate(id={self.id}, name='{self.name}', category='{self.category}')>"


class TaskStep(BaseModel):
    """
    任务步骤模型
    
    存储任务执行的详细步骤信息
    """
    
    __tablename__ = "task_steps"
    
    # 关联关系
    task_id = Column(
        Integer,
        ForeignKey("tasks.id", ondelete="CASCADE"),
        nullable=False,
        comment="任务ID"
    )
    
    user_id = Column(
        Integer,
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        comment="用户ID"
    )
    
    owner_id = Column(
        Integer,
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        comment="拥有者用户ID"
    )
    
    execution_id = Column(
        Integer,
        ForeignKey("task_executions.id", ondelete="SET NULL"),
        nullable=True,
        comment="执行ID"
    )
    
    # 步骤基本信息
    step_id = Column(
        String(36),
        nullable=False,
        unique=True,
        comment="步骤唯一标识"
    )
    
    step_name = Column(
        String(200),
        nullable=False,
        comment="步骤名称"
    )
    
    description = Column(
        Text,
        nullable=True,
        comment="步骤描述"
    )
    
    # 步骤顺序和层级
    step_order = Column(
        Integer,
        nullable=False,
        comment="步骤顺序"
    )
    
    parent_step_id = Column(
        Integer,
        ForeignKey("task_steps.id", ondelete="SET NULL"),
        nullable=True,
        comment="父步骤ID"
    )
    
    level = Column(
        Integer,
        nullable=False,
        default=0,
        comment="步骤层级"
    )
    
    # 步骤类型和状态
    step_type = Column(
        String(50),
        nullable=False,
        comment="步骤类型"
    )
    
    status = Column(
        String(20),
        nullable=False,
        default="pending",
        comment="步骤状态"
    )
    
    # 执行信息
    agent_id = Column(
        Integer,
        ForeignKey("agents.id", ondelete="SET NULL"),
        nullable=True,
        comment="执行智能体ID"
    )
    
    tool_name = Column(
        String(100),
        nullable=True,
        comment="使用的工具名称"
    )
    
    # 输入输出
    input_data = Column(
        Text,
        nullable=True,
        comment="输入数据（JSON格式）"
    )
    
    output_data = Column(
        Text,
        nullable=True,
        comment="输出数据（JSON格式）"
    )
    
    # 执行配置
    config = Column(
        Text,
        nullable=True,
        comment="步骤配置（JSON格式）"
    )
    
    # 时间信息
    start_time = Column(
        DateTime,
        nullable=True,
        comment="开始时间"
    )
    
    end_time = Column(
        DateTime,
        nullable=True,
        comment="结束时间"
    )
    
    duration = Column(
        Float,
        nullable=True,
        comment="执行时长（秒）"
    )
    
    # 结果和错误
    result = Column(
        Text,
        nullable=True,
        comment="执行结果（JSON格式）"
    )
    
    error_message = Column(
        Text,
        nullable=True,
        comment="错误信息"
    )
    
    error_details = Column(
        Text,
        nullable=True,
        comment="错误详情（JSON格式）"
    )
    
    # 重试信息
    retry_count = Column(
        Integer,
        nullable=False,
        default=0,
        comment="重试次数"
    )
    
    max_retries = Column(
        Integer,
        nullable=False,
        default=3,
        comment="最大重试次数"
    )
    
    # 依赖关系
    dependencies = Column(
        Text,
        nullable=True,
        comment="依赖步骤（JSON格式）"
    )
    
    # 元数据
    meta_data = Column(
        "metadata",
        Text,
        nullable=True,
        comment="步骤元数据（JSON格式）"
    )
    
    # 关联关系
    task = relationship("Task", back_populates="steps")
    user = relationship("User", back_populates="task_steps")
    owner = relationship("User", foreign_keys=[owner_id])
    execution = relationship("TaskExecution", back_populates="steps")
    agent = relationship("Agent", back_populates="task_steps")
    parent_step = relationship("TaskStep", remote_side="TaskStep.id")
    child_steps = relationship("TaskStep", back_populates="parent_step")
    
    # 索引
    __table_args__ = (
        Index("idx_task_steps_task_id", "task_id"),
        Index("idx_task_steps_user_id", "user_id"),
        Index("idx_task_steps_owner_id", "owner_id"),
        Index("idx_task_steps_execution_id", "execution_id"),
        Index("idx_task_steps_step_id", "step_id"),
        Index("idx_task_steps_step_order", "step_order"),
        Index("idx_task_steps_parent_step_id", "parent_step_id"),
        Index("idx_task_steps_step_type", "step_type"),
        Index("idx_task_steps_status", "status"),
        Index("idx_task_steps_agent_id", "agent_id"),
        Index("idx_task_steps_start_time", "start_time"),
        Index("idx_task_steps_created_at", "created_at"),
    )
    
    def set_input_data(self, data: Dict[str, Any]) -> None:
        """设置输入数据"""
        import json
        self.input_data = json.dumps(data, ensure_ascii=False) if data else None
    
    def get_input_data(self) -> Dict[str, Any]:
        """获取输入数据"""
        if not self.input_data:
            return {}
        
        try:
            import json
            return json.loads(self.input_data)
        except (json.JSONDecodeError, TypeError):
            return {}
    
    def set_output_data(self, data: Dict[str, Any]) -> None:
        """设置输出数据"""
        import json
        self.output_data = json.dumps(data, ensure_ascii=False) if data else None
    
    def get_output_data(self) -> Dict[str, Any]:
        """获取输出数据"""
        if not self.output_data:
            return {}
        
        try:
            import json
            return json.loads(self.output_data)
        except (json.JSONDecodeError, TypeError):
            return {}
    
    def set_config(self, config: Dict[str, Any]) -> None:
        """设置步骤配置"""
        import json
        self.config = json.dumps(config, ensure_ascii=False) if config else None
    
    def get_config(self) -> Dict[str, Any]:
        """获取步骤配置"""
        if not self.config:
            return {}
        
        try:
            import json
            return json.loads(self.config)
        except (json.JSONDecodeError, TypeError):
            return {}
    
    def set_result(self, result: Dict[str, Any]) -> None:
        """设置执行结果"""
        import json
        self.result = json.dumps(result, ensure_ascii=False) if result else None
    
    def get_result(self) -> Dict[str, Any]:
        """获取执行结果"""
        if not self.result:
            return {}
        
        try:
            import json
            return json.loads(self.result)
        except (json.JSONDecodeError, TypeError):
            return {}
    
    def set_error_details(self, error: Dict[str, Any]) -> None:
        """设置错误详情"""
        import json
        self.error_details = json.dumps(error, ensure_ascii=False) if error else None
    
    def get_error_details(self) -> Dict[str, Any]:
        """获取错误详情"""
        if not self.error_details:
            return {}
        
        try:
            import json
            return json.loads(self.error_details)
        except (json.JSONDecodeError, TypeError):
            return {}
    
    def set_dependencies(self, deps: List[str]) -> None:
        """设置依赖步骤"""
        import json
        self.dependencies = json.dumps(deps, ensure_ascii=False) if deps else None
    
    def get_dependencies(self) -> List[str]:
        """获取依赖步骤"""
        if not self.dependencies:
            return []
        
        try:
            import json
            return json.loads(self.dependencies)
        except (json.JSONDecodeError, TypeError):
            return []
    
    def set_metadata(self, metadata: Dict[str, Any]) -> None:
        """设置元数据"""
        import json
        self.meta_data = json.dumps(metadata, ensure_ascii=False) if metadata else None
    
    def get_metadata(self) -> Dict[str, Any]:
        """获取元数据"""
        if not self.meta_data:
            return {}
        
        try:
            import json
            return json.loads(self.meta_data)
        except (json.JSONDecodeError, TypeError):
            return {}
    
    def start_execution(self) -> None:
        """开始执行"""
        self.status = "running"
        self.start_time = datetime.now()
    
    def complete_execution(self, result: Dict[str, Any] = None) -> None:
        """完成执行"""
        self.status = "completed"
        self.end_time = datetime.now()
        if self.start_time:
            self.duration = (self.end_time - self.start_time).total_seconds()
        
        if result:
            self.set_result(result)
    
    def fail_execution(self, error_message: str, error_details: Dict[str, Any] = None) -> None:
        """执行失败"""
        self.status = "failed"
        self.end_time = datetime.now()
        if self.start_time:
            self.duration = (self.end_time - self.start_time).total_seconds()
        
        self.error_message = error_message
        if error_details:
            self.set_error_details(error_details)
    
    def can_retry(self) -> bool:
        """检查是否可以重试"""
        return self.retry_count < self.max_retries
    
    def increment_retry(self) -> None:
        """增加重试次数"""
        self.retry_count += 1
        self.status = "pending"
        self.error_message = None
        self.error_details = None
    
    def is_pending(self) -> bool:
        """检查是否为待执行状态"""
        return self.status == "pending"
    
    def is_running(self) -> bool:
        """检查是否为执行中状态"""
        return self.status == "running"
    
    def is_completed(self) -> bool:
        """检查是否为已完成状态"""
        return self.status == "completed"
    
    def is_failed(self) -> bool:
        """检查是否为失败状态"""
        return self.status == "failed"
    
    def is_skipped(self) -> bool:
        """检查是否为跳过状态"""
        return self.status == "skipped"
    
    def __repr__(self) -> str:
        return f"<TaskStep(id={self.id}, step_name='{self.step_name}', step_type='{self.step_type}', status='{self.status}')>"


class TaskResult(BaseModel):
    """
    任务结果模型
    
    存储任务执行的结果信息
    """
    
    __tablename__ = "task_results"
    
    # 任务关联
    task_id = Column(
        String(36),
        nullable=False,
        comment="任务ID"
    )
    
    # 结果信息
    result_type = Column(
        String(50),
        nullable=False,
        comment="结果类型"
    )
    
    result_data = Column(
        Text,
        nullable=True,
        comment="结果数据（JSON格式）"
    )
    
    status = Column(
        String(20),
        nullable=False,
        default="success",
        comment="结果状态"
    )
    
    # 统计信息
    execution_time = Column(
        Float,
        nullable=True,
        comment="执行时间（秒）"
    )
    
    tokens_used = Column(
        Integer,
        nullable=False,
        default=0,
        comment="使用的token数"
    )
    
    cost = Column(
        Float,
        nullable=False,
        default=0.0,
        comment="执行费用"
    )
    
    # 错误信息
    error_message = Column(
        Text,
        nullable=True,
        comment="错误信息"
    )
    
    error_details = Column(
        Text,
        nullable=True,
        comment="错误详情（JSON格式）"
    )
    
    # 索引
    __table_args__ = (
        Index("idx_task_results_task_id", "task_id"),
        Index("idx_task_results_type", "result_type"),
        Index("idx_task_results_status", "status"),
    )
    
    def set_result_data(self, data: Dict[str, Any]) -> None:
        """
        设置结果数据
        
        Args:
            data: 结果数据字典
        """
        import json
        self.result_data = json.dumps(data, ensure_ascii=False)
    
    def get_result_data(self) -> Dict[str, Any]:
        """
        获取结果数据
        
        Returns:
            Dict[str, Any]: 结果数据字典
        """
        if not self.result_data:
            return {}
        
        try:
            import json
            return json.loads(self.result_data)
        except (json.JSONDecodeError, TypeError):
            return {}
    
    def set_error_details(self, details: Dict[str, Any]) -> None:
        """
        设置错误详情
        
        Args:
            details: 错误详情字典
        """
        import json
        self.error_details = json.dumps(details, ensure_ascii=False)
    
    def get_error_details(self) -> Dict[str, Any]:
        """
        获取错误详情
        
        Returns:
            Dict[str, Any]: 错误详情字典
        """
        if not self.error_details:
            return {}
        
        try:
            import json
            return json.loads(self.error_details)
        except (json.JSONDecodeError, TypeError):
            return {}
    
    def __repr__(self) -> str:
        return f"<TaskResult(id={self.id}, task_id={self.task_id}, result_type='{self.result_type}', status='{self.status}')>"


class TaskDependency(BaseModel):
    """
    任务依赖模型
    
    存储任务之间的依赖关系
    """
    
    __tablename__ = "task_dependencies"
    
    # 任务关联
    task_id = Column(
        String(36),
        nullable=False,
        comment="任务ID"
    )
    
    depends_on_task_id = Column(
        String(36),
        nullable=False,
        comment="依赖的任务ID"
    )
    
    # 依赖类型
    dependency_type = Column(
        String(20),
        nullable=False,
        default="sequential",
        comment="依赖类型"
    )
    
    # 依赖条件
    condition = Column(
        Text,
        nullable=True,
        comment="依赖条件（JSON格式）"
    )
    
    # 状态信息
    is_satisfied = Column(
        Boolean,
        nullable=False,
        default=False,
        comment="依赖是否已满足"
    )
    
    satisfied_at = Column(
        DateTime,
        nullable=True,
        comment="依赖满足时间"
    )
    
    # 索引
    __table_args__ = (
        Index("idx_task_dependencies_task_id", "task_id"),
        Index("idx_task_dependencies_depends_on", "depends_on_task_id"),
        Index("idx_task_dependencies_type", "dependency_type"),
        Index("idx_task_dependencies_satisfied", "is_satisfied"),
        # 复合唯一索引
        Index("idx_task_dependencies_unique", "task_id", "depends_on_task_id", unique=True),
    )
    
    def set_condition(self, condition: Dict[str, Any]) -> None:
        """
        设置依赖条件
        
        Args:
            condition: 依赖条件字典
        """
        import json
        self.condition = json.dumps(condition, ensure_ascii=False)
    
    def get_condition(self) -> Dict[str, Any]:
        """
        获取依赖条件
        
        Returns:
            Dict[str, Any]: 依赖条件字典
        """
        if not self.condition:
            return {}
        
        try:
            import json
            return json.loads(self.condition)
        except (json.JSONDecodeError, TypeError):
            return {}
    
    def satisfy(self) -> None:
        """
        标记依赖为已满足
        """
        self.is_satisfied = True
        self.satisfied_at = datetime.now()
    
    def __repr__(self) -> str:
        return f"<TaskDependency(id={self.id}, task_id={self.task_id}, depends_on_task_id={self.depends_on_task_id}, is_satisfied={self.is_satisfied})>"