#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A2A多智能体系统 数据库操作工具

支持用户数据隔离的数据库操作工具，包含权限验证和安全控制
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional, Union, Tuple
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime
import json
import hashlib
from sqlalchemy import text, select, insert, update, delete
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.exc import SQLAlchemyError

# Google ADK imports
from google.ai.generativelanguage import FunctionDeclaration, Schema, Type

from .base_tool import (
    BaseTool, ToolConfig, ToolResult, ToolError, ToolStatus,
    ToolExecutionContext, ToolPermission
)
from ..services.database_service import DatabaseService


class DatabaseOperation(Enum):
    """数据库操作类型枚举"""
    SELECT = "select"
    INSERT = "insert"
    UPDATE = "update"
    DELETE = "delete"
    COUNT = "count"
    EXISTS = "exists"
    AGGREGATE = "aggregate"


class DataAccessLevel(Enum):
    """数据访问级别枚举"""
    OWNER = "owner"  # 只能访问自己的数据
    SHARED = "shared"  # 可以访问共享数据
    PUBLIC = "public"  # 可以访问公共数据
    ADMIN = "admin"  # 管理员权限


@dataclass
class DatabaseToolConfig(ToolConfig):
    """数据库工具配置"""
    allowed_tables: List[str] = field(default_factory=list)
    allowed_operations: List[DatabaseOperation] = field(default_factory=lambda: list(DatabaseOperation))
    max_rows_per_query: int = 1000
    query_timeout: float = 30.0
    enable_raw_sql: bool = False
    data_access_level: DataAccessLevel = DataAccessLevel.OWNER
    table_permissions: Dict[str, List[str]] = field(default_factory=dict)
    column_permissions: Dict[str, List[str]] = field(default_factory=dict)
    
    def __post_init__(self):
        """配置后处理"""
        if not self.allowed_tables:
            raise ValueError("必须指定允许访问的表")
        
        # 验证操作权限
        if not self.allowed_operations:
            self.allowed_operations = [DatabaseOperation.SELECT]


@dataclass
class QueryFilter:
    """查询过滤器"""
    column: str
    operator: str  # =, !=, >, <, >=, <=, LIKE, IN, NOT IN
    value: Any
    logical_operator: str = "AND"  # AND, OR


@dataclass
class QueryParams:
    """查询参数"""
    table: str
    operation: DatabaseOperation
    columns: Optional[List[str]] = None
    filters: List[QueryFilter] = field(default_factory=list)
    data: Optional[Dict[str, Any]] = None
    order_by: Optional[List[str]] = None
    limit: Optional[int] = None
    offset: Optional[int] = None
    group_by: Optional[List[str]] = None
    having: Optional[List[QueryFilter]] = None
    raw_sql: Optional[str] = None


class DatabaseTool(BaseTool):
    """数据库操作工具"""
    
    def __init__(self, config: DatabaseToolConfig):
        """
        初始化数据库工具
        
        Args:
            config: 数据库工具配置
        """
        super().__init__(config)
        self.db_service = DatabaseService()
        self._user_data_cache: Dict[str, Dict[str, Any]] = {}
    
    def get_function_declaration(self) -> FunctionDeclaration:
        """
        获取工具的函数声明
        
        Returns:
            FunctionDeclaration: ADK函数声明
        """
        # 定义查询参数schema
        filter_schema = Schema(
            type=Type.OBJECT,
            properties={
                "column": Schema(type=Type.STRING, description="过滤列名"),
                "operator": Schema(type=Type.STRING, description="操作符 (=, !=, >, <, >=, <=, LIKE, IN, NOT IN)"),
                "value": Schema(type=Type.STRING, description="过滤值"),
                "logical_operator": Schema(type=Type.STRING, description="逻辑操作符 (AND, OR)")
            },
            required=["column", "operator", "value"]
        )
        
        parameters_schema = Schema(
            type=Type.OBJECT,
            properties={
                "table": Schema(type=Type.STRING, description="表名"),
                "operation": Schema(type=Type.STRING, description="操作类型 (select, insert, update, delete, count, exists, aggregate)"),
                "columns": Schema(
                    type=Type.ARRAY,
                    items=Schema(type=Type.STRING),
                    description="查询列名列表"
                ),
                "filters": Schema(
                    type=Type.ARRAY,
                    items=filter_schema,
                    description="过滤条件列表"
                ),
                "data": Schema(type=Type.OBJECT, description="插入或更新的数据"),
                "order_by": Schema(
                    type=Type.ARRAY,
                    items=Schema(type=Type.STRING),
                    description="排序字段列表"
                ),
                "limit": Schema(type=Type.INTEGER, description="限制返回行数"),
                "offset": Schema(type=Type.INTEGER, description="偏移量"),
                "group_by": Schema(
                    type=Type.ARRAY,
                    items=Schema(type=Type.STRING),
                    description="分组字段列表"
                ),
                "raw_sql": Schema(type=Type.STRING, description="原始SQL查询（需要特殊权限）")
            },
            required=["table", "operation"]
        )
        
        return FunctionDeclaration(
            name=self.name,
            description=self.description,
            parameters=parameters_schema
        )
    
    async def execute(
        self,
        context: ToolExecutionContext,
        **kwargs
    ) -> ToolResult:
        """
        执行数据库操作
        
        Args:
            context: 执行上下文
            **kwargs: 额外参数
        
        Returns:
            ToolResult: 执行结果
        """
        async with self._execution_context(context):
            try:
                # 解析查询参数
                query_params = self._parse_query_params(context.parameters)
                
                # 验证权限
                permission_error = await self._validate_permissions(
                    query_params, context.user_id
                )
                if permission_error:
                    return ToolResult(
                        tool_name=self.name,
                        execution_id=context.execution_id,
                        status=ToolStatus.FAILED,
                        error=permission_error,
                        user_id=context.user_id
                    )
                
                # 执行数据库操作
                result = await self._execute_database_operation(
                    query_params, context.user_id
                )
                
                return ToolResult(
                    tool_name=self.name,
                    execution_id=context.execution_id,
                    status=ToolStatus.SUCCESS,
                    result=result,
                    user_id=context.user_id,
                    metadata={
                        "table": query_params.table,
                        "operation": query_params.operation.value,
                        "rows_affected": result.get("rows_affected", 0)
                    }
                )
                
            except Exception as e:
                self.logger.error(f"数据库操作失败: {e}")
                
                return ToolResult(
                    tool_name=self.name,
                    execution_id=context.execution_id,
                    status=ToolStatus.FAILED,
                    error=ToolError(
                        code="DATABASE_OPERATION_ERROR",
                        message=f"数据库操作失败: {str(e)}"
                    ),
                    user_id=context.user_id
                )
    
    def _parse_query_params(self, parameters: Dict[str, Any]) -> QueryParams:
        """
        解析查询参数
        
        Args:
            parameters: 原始参数
        
        Returns:
            QueryParams: 解析后的查询参数
        """
        # 解析过滤条件
        filters = []
        for filter_data in parameters.get("filters", []):
            filters.append(QueryFilter(
                column=filter_data["column"],
                operator=filter_data["operator"],
                value=filter_data["value"],
                logical_operator=filter_data.get("logical_operator", "AND")
            ))
        
        return QueryParams(
            table=parameters["table"],
            operation=DatabaseOperation(parameters["operation"]),
            columns=parameters.get("columns"),
            filters=filters,
            data=parameters.get("data"),
            order_by=parameters.get("order_by"),
            limit=parameters.get("limit"),
            offset=parameters.get("offset"),
            group_by=parameters.get("group_by"),
            raw_sql=parameters.get("raw_sql")
        )
    
    async def _validate_permissions(
        self,
        query_params: QueryParams,
        user_id: str
    ) -> Optional[ToolError]:
        """
        验证数据库操作权限
        
        Args:
            query_params: 查询参数
            user_id: 用户ID
        
        Returns:
            Optional[ToolError]: 权限验证错误
        """
        try:
            # 检查表访问权限
            if query_params.table not in self.config.allowed_tables:
                return ToolError(
                    code="TABLE_ACCESS_DENIED",
                    message=f"无权访问表: {query_params.table}"
                )
            
            # 检查操作权限
            if query_params.operation not in self.config.allowed_operations:
                return ToolError(
                    code="OPERATION_NOT_ALLOWED",
                    message=f"不允许的操作: {query_params.operation.value}"
                )
            
            # 检查原始SQL权限
            if query_params.raw_sql and not self.config.enable_raw_sql:
                return ToolError(
                    code="RAW_SQL_NOT_ALLOWED",
                    message="不允许执行原始SQL查询"
                )
            
            # 检查表级权限
            table_perms = self.config.table_permissions.get(query_params.table, [])
            if table_perms and query_params.operation.value not in table_perms:
                return ToolError(
                    code="TABLE_OPERATION_DENIED",
                    message=f"表 {query_params.table} 不允许 {query_params.operation.value} 操作"
                )
            
            # 检查列级权限
            if query_params.columns:
                allowed_columns = self.config.column_permissions.get(query_params.table, [])
                if allowed_columns:
                    for column in query_params.columns:
                        if column not in allowed_columns:
                            return ToolError(
                                code="COLUMN_ACCESS_DENIED",
                                message=f"无权访问列: {column}"
                            )
            
            # 检查行数限制
            if query_params.limit and query_params.limit > self.config.max_rows_per_query:
                return ToolError(
                    code="ROW_LIMIT_EXCEEDED",
                    message=f"查询行数超过限制: {self.config.max_rows_per_query}"
                )
            
            return None
            
        except Exception as e:
            return ToolError(
                code="PERMISSION_VALIDATION_ERROR",
                message=f"权限验证异常: {str(e)}"
            )
    
    async def _execute_database_operation(
        self,
        query_params: QueryParams,
        user_id: str
    ) -> Dict[str, Any]:
        """
        执行数据库操作
        
        Args:
            query_params: 查询参数
            user_id: 用户ID
        
        Returns:
            Dict[str, Any]: 操作结果
        """
        async with self.db_service.get_session() as session:
            try:
                if query_params.raw_sql:
                    return await self._execute_raw_sql(query_params.raw_sql, session, user_id)
                
                if query_params.operation == DatabaseOperation.SELECT:
                    return await self._execute_select(query_params, session, user_id)
                elif query_params.operation == DatabaseOperation.INSERT:
                    return await self._execute_insert(query_params, session, user_id)
                elif query_params.operation == DatabaseOperation.UPDATE:
                    return await self._execute_update(query_params, session, user_id)
                elif query_params.operation == DatabaseOperation.DELETE:
                    return await self._execute_delete(query_params, session, user_id)
                elif query_params.operation == DatabaseOperation.COUNT:
                    return await self._execute_count(query_params, session, user_id)
                elif query_params.operation == DatabaseOperation.EXISTS:
                    return await self._execute_exists(query_params, session, user_id)
                elif query_params.operation == DatabaseOperation.AGGREGATE:
                    return await self._execute_aggregate(query_params, session, user_id)
                else:
                    raise ValueError(f"不支持的操作: {query_params.operation}")
                
            except SQLAlchemyError as e:
                self.logger.error(f"数据库操作异常: {e}")
                raise Exception(f"数据库操作失败: {str(e)}")
    
    async def _execute_select(
        self,
        query_params: QueryParams,
        session: AsyncSession,
        user_id: str
    ) -> Dict[str, Any]:
        """
        执行SELECT查询
        
        Args:
            query_params: 查询参数
            session: 数据库会话
            user_id: 用户ID
        
        Returns:
            Dict[str, Any]: 查询结果
        """
        # 构建查询
        columns = query_params.columns or ["*"]
        query = f"SELECT {', '.join(columns)} FROM {query_params.table}"
        
        # 添加用户数据隔离条件
        where_conditions = self._build_user_isolation_conditions(user_id)
        
        # 添加过滤条件
        if query_params.filters:
            filter_conditions = self._build_filter_conditions(query_params.filters)
            if where_conditions:
                where_conditions += f" AND ({filter_conditions})"
            else:
                where_conditions = filter_conditions
        
        if where_conditions:
            query += f" WHERE {where_conditions}"
        
        # 添加分组
        if query_params.group_by:
            query += f" GROUP BY {', '.join(query_params.group_by)}"
        
        # 添加排序
        if query_params.order_by:
            query += f" ORDER BY {', '.join(query_params.order_by)}"
        
        # 添加限制
        limit = query_params.limit or self.config.max_rows_per_query
        query += f" LIMIT {limit}"
        
        if query_params.offset:
            query += f" OFFSET {query_params.offset}"
        
        # 执行查询
        result = await session.execute(text(query))
        rows = result.fetchall()
        
        # 转换结果
        data = []
        if rows:
            column_names = result.keys()
            for row in rows:
                data.append(dict(zip(column_names, row)))
        
        return {
            "data": data,
            "rows_count": len(data),
            "query": query
        }
    
    async def _execute_insert(
        self,
        query_params: QueryParams,
        session: AsyncSession,
        user_id: str
    ) -> Dict[str, Any]:
        """
        执行INSERT操作
        
        Args:
            query_params: 查询参数
            session: 数据库会话
            user_id: 用户ID
        
        Returns:
            Dict[str, Any]: 插入结果
        """
        if not query_params.data:
            raise ValueError("插入操作需要提供数据")
        
        # 添加用户ID到数据中（如果表支持）
        data = query_params.data.copy()
        if "user_id" not in data and self._table_has_user_id_column(query_params.table):
            data["user_id"] = user_id
        
        # 添加时间戳
        if "created_at" not in data and self._table_has_created_at_column(query_params.table):
            data["created_at"] = datetime.now()
        
        # 构建插入语句
        columns = list(data.keys())
        placeholders = [f":{col}" for col in columns]
        
        query = f"INSERT INTO {query_params.table} ({', '.join(columns)}) VALUES ({', '.join(placeholders)})"
        
        # 执行插入
        result = await session.execute(text(query), data)
        await session.commit()
        
        return {
            "rows_affected": result.rowcount,
            "inserted_id": result.lastrowid,
            "query": query
        }
    
    async def _execute_update(
        self,
        query_params: QueryParams,
        session: AsyncSession,
        user_id: str
    ) -> Dict[str, Any]:
        """
        执行UPDATE操作
        
        Args:
            query_params: 查询参数
            session: 数据库会话
            user_id: 用户ID
        
        Returns:
            Dict[str, Any]: 更新结果
        """
        if not query_params.data:
            raise ValueError("更新操作需要提供数据")
        
        if not query_params.filters:
            raise ValueError("更新操作需要提供过滤条件")
        
        # 添加更新时间戳
        data = query_params.data.copy()
        if "updated_at" not in data and self._table_has_updated_at_column(query_params.table):
            data["updated_at"] = datetime.now()
        
        # 构建更新语句
        set_clauses = [f"{col} = :{col}" for col in data.keys()]
        query = f"UPDATE {query_params.table} SET {', '.join(set_clauses)}"
        
        # 添加用户数据隔离条件
        where_conditions = self._build_user_isolation_conditions(user_id)
        
        # 添加过滤条件
        filter_conditions = self._build_filter_conditions(query_params.filters)
        if where_conditions:
            where_conditions += f" AND ({filter_conditions})"
        else:
            where_conditions = filter_conditions
        
        query += f" WHERE {where_conditions}"
        
        # 执行更新
        result = await session.execute(text(query), data)
        await session.commit()
        
        return {
            "rows_affected": result.rowcount,
            "query": query
        }
    
    async def _execute_delete(
        self,
        query_params: QueryParams,
        session: AsyncSession,
        user_id: str
    ) -> Dict[str, Any]:
        """
        执行DELETE操作
        
        Args:
            query_params: 查询参数
            session: 数据库会话
            user_id: 用户ID
        
        Returns:
            Dict[str, Any]: 删除结果
        """
        if not query_params.filters:
            raise ValueError("删除操作需要提供过滤条件")
        
        # 构建删除语句
        query = f"DELETE FROM {query_params.table}"
        
        # 添加用户数据隔离条件
        where_conditions = self._build_user_isolation_conditions(user_id)
        
        # 添加过滤条件
        filter_conditions = self._build_filter_conditions(query_params.filters)
        if where_conditions:
            where_conditions += f" AND ({filter_conditions})"
        else:
            where_conditions = filter_conditions
        
        query += f" WHERE {where_conditions}"
        
        # 执行删除
        result = await session.execute(text(query))
        await session.commit()
        
        return {
            "rows_affected": result.rowcount,
            "query": query
        }
    
    async def _execute_count(
        self,
        query_params: QueryParams,
        session: AsyncSession,
        user_id: str
    ) -> Dict[str, Any]:
        """
        执行COUNT查询
        
        Args:
            query_params: 查询参数
            session: 数据库会话
            user_id: 用户ID
        
        Returns:
            Dict[str, Any]: 计数结果
        """
        # 构建计数查询
        query = f"SELECT COUNT(*) as count FROM {query_params.table}"
        
        # 添加用户数据隔离条件
        where_conditions = self._build_user_isolation_conditions(user_id)
        
        # 添加过滤条件
        if query_params.filters:
            filter_conditions = self._build_filter_conditions(query_params.filters)
            if where_conditions:
                where_conditions += f" AND ({filter_conditions})"
            else:
                where_conditions = filter_conditions
        
        if where_conditions:
            query += f" WHERE {where_conditions}"
        
        # 执行查询
        result = await session.execute(text(query))
        count = result.scalar()
        
        return {
            "count": count,
            "query": query
        }
    
    async def _execute_exists(
        self,
        query_params: QueryParams,
        session: AsyncSession,
        user_id: str
    ) -> Dict[str, Any]:
        """
        执行EXISTS查询
        
        Args:
            query_params: 查询参数
            session: 数据库会话
            user_id: 用户ID
        
        Returns:
            Dict[str, Any]: 存在性检查结果
        """
        # 构建存在性查询
        query = f"SELECT EXISTS(SELECT 1 FROM {query_params.table}"
        
        # 添加用户数据隔离条件
        where_conditions = self._build_user_isolation_conditions(user_id)
        
        # 添加过滤条件
        if query_params.filters:
            filter_conditions = self._build_filter_conditions(query_params.filters)
            if where_conditions:
                where_conditions += f" AND ({filter_conditions})"
            else:
                where_conditions = filter_conditions
        
        if where_conditions:
            query += f" WHERE {where_conditions}"
        
        query += ") as exists"
        
        # 执行查询
        result = await session.execute(text(query))
        exists = result.scalar()
        
        return {
            "exists": bool(exists),
            "query": query
        }
    
    async def _execute_aggregate(
        self,
        query_params: QueryParams,
        session: AsyncSession,
        user_id: str
    ) -> Dict[str, Any]:
        """
        执行聚合查询
        
        Args:
            query_params: 查询参数
            session: 数据库会话
            user_id: 用户ID
        
        Returns:
            Dict[str, Any]: 聚合结果
        """
        if not query_params.columns:
            raise ValueError("聚合查询需要指定聚合列")
        
        # 构建聚合查询
        query = f"SELECT {', '.join(query_params.columns)} FROM {query_params.table}"
        
        # 添加用户数据隔离条件
        where_conditions = self._build_user_isolation_conditions(user_id)
        
        # 添加过滤条件
        if query_params.filters:
            filter_conditions = self._build_filter_conditions(query_params.filters)
            if where_conditions:
                where_conditions += f" AND ({filter_conditions})"
            else:
                where_conditions = filter_conditions
        
        if where_conditions:
            query += f" WHERE {where_conditions}"
        
        # 添加分组
        if query_params.group_by:
            query += f" GROUP BY {', '.join(query_params.group_by)}"
        
        # 执行查询
        result = await session.execute(text(query))
        rows = result.fetchall()
        
        # 转换结果
        data = []
        if rows:
            column_names = result.keys()
            for row in rows:
                data.append(dict(zip(column_names, row)))
        
        return {
            "data": data,
            "rows_count": len(data),
            "query": query
        }
    
    async def _execute_raw_sql(
        self,
        sql: str,
        session: AsyncSession,
        user_id: str
    ) -> Dict[str, Any]:
        """
        执行原始SQL查询
        
        Args:
            sql: SQL语句
            session: 数据库会话
            user_id: 用户ID
        
        Returns:
            Dict[str, Any]: 查询结果
        """
        # 安全检查：防止危险操作
        dangerous_keywords = ['DROP', 'TRUNCATE', 'ALTER', 'CREATE', 'GRANT', 'REVOKE']
        sql_upper = sql.upper()
        for keyword in dangerous_keywords:
            if keyword in sql_upper:
                raise ValueError(f"不允许执行包含 {keyword} 的SQL语句")
        
        # 执行查询
        result = await session.execute(text(sql))
        
        # 处理结果
        if result.returns_rows:
            rows = result.fetchall()
            data = []
            if rows:
                column_names = result.keys()
                for row in rows:
                    data.append(dict(zip(column_names, row)))
            
            return {
                "data": data,
                "rows_count": len(data),
                "query": sql
            }
        else:
            await session.commit()
            return {
                "rows_affected": result.rowcount,
                "query": sql
            }
    
    def _build_user_isolation_conditions(self, user_id: str) -> str:
        """
        构建用户数据隔离条件
        
        Args:
            user_id: 用户ID
        
        Returns:
            str: WHERE条件
        """
        if self.config.data_access_level == DataAccessLevel.ADMIN:
            return ""  # 管理员可以访问所有数据
        elif self.config.data_access_level == DataAccessLevel.PUBLIC:
            return ""  # 公共访问级别
        elif self.config.data_access_level == DataAccessLevel.SHARED:
            return f"(user_id = '{user_id}' OR is_shared = 1)"
        else:  # OWNER
            return f"user_id = '{user_id}'"
    
    def _build_filter_conditions(self, filters: List[QueryFilter]) -> str:
        """
        构建过滤条件
        
        Args:
            filters: 过滤器列表
        
        Returns:
            str: WHERE条件
        """
        conditions = []
        
        for filter_item in filters:
            condition = self._build_single_filter_condition(filter_item)
            if conditions:
                conditions.append(f" {filter_item.logical_operator} {condition}")
            else:
                conditions.append(condition)
        
        return "".join(conditions)
    
    def _build_single_filter_condition(self, filter_item: QueryFilter) -> str:
        """
        构建单个过滤条件
        
        Args:
            filter_item: 过滤器
        
        Returns:
            str: 条件字符串
        """
        column = filter_item.column
        operator = filter_item.operator.upper()
        value = filter_item.value
        
        if operator in ['IN', 'NOT IN']:
            if isinstance(value, list):
                value_str = "', '".join(str(v) for v in value)
                return f"{column} {operator} ('{value_str}')"
            else:
                return f"{column} {operator} ('{value}')"
        elif operator == 'LIKE':
            return f"{column} LIKE '%{value}%'"
        else:
            return f"{column} {operator} '{value}'"
    
    def _table_has_user_id_column(self, table: str) -> bool:
        """
        检查表是否有user_id列
        
        Args:
            table: 表名
        
        Returns:
            bool: 是否有user_id列
        """
        # 这里可以通过查询数据库schema来实现
        # 简化实现，假设大部分表都有user_id列
        return True
    
    def _table_has_created_at_column(self, table: str) -> bool:
        """
        检查表是否有created_at列
        
        Args:
            table: 表名
        
        Returns:
            bool: 是否有created_at列
        """
        # 简化实现
        return True
    
    def _table_has_updated_at_column(self, table: str) -> bool:
        """
        检查表是否有updated_at列
        
        Args:
            table: 表名
        
        Returns:
            bool: 是否有updated_at列
        """
        # 简化实现
        return True
    
    async def _validate_specific_parameters(self, parameters: Dict[str, Any]) -> Optional[ToolError]:
        """
        验证数据库工具参数
        
        Args:
            parameters: 参数字典
        
        Returns:
            Optional[ToolError]: 验证错误
        """
        try:
            # 检查必需参数
            if "table" not in parameters:
                return ToolError(
                    code="MISSING_REQUIRED_PARAMETER",
                    message="缺少必需参数: table"
                )
            
            if "operation" not in parameters:
                return ToolError(
                    code="MISSING_REQUIRED_PARAMETER",
                    message="缺少必需参数: operation"
                )
            
            # 验证操作类型
            try:
                operation = DatabaseOperation(parameters["operation"])
            except ValueError:
                return ToolError(
                    code="INVALID_OPERATION",
                    message=f"无效的操作类型: {parameters['operation']}"
                )
            
            # 验证插入/更新操作的数据
            if operation in [DatabaseOperation.INSERT, DatabaseOperation.UPDATE]:
                if "data" not in parameters or not parameters["data"]:
                    return ToolError(
                        code="MISSING_DATA",
                        message=f"{operation.value} 操作需要提供数据"
                    )
            
            # 验证删除/更新操作的过滤条件
            if operation in [DatabaseOperation.DELETE, DatabaseOperation.UPDATE]:
                if "filters" not in parameters or not parameters["filters"]:
                    return ToolError(
                        code="MISSING_FILTERS",
                        message=f"{operation.value} 操作需要提供过滤条件"
                    )
            
            return None
            
        except Exception as e:
            return ToolError(
                code="PARAMETER_VALIDATION_ERROR",
                message=f"参数验证异常: {str(e)}"
            )