#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A2A多智能体系统基础模型

定义所有模型的基类和通用字段
"""

from datetime import datetime
from typing import Any, Dict, Optional
from sqlalchemy import Column, Integer, DateTime, Boolean, Text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import declared_attr
from sqlalchemy.sql import func

# 创建基础模型类
Base = declarative_base()


class BaseModel(Base):
    """
    基础模型类
    
    所有数据模型的基类，包含通用字段和方法
    """
    
    __abstract__ = True
    
    # 主键ID
    id = Column(Integer, primary_key=True, autoincrement=True, comment="主键ID")
    
    # 时间戳字段
    created_at = Column(
        DateTime,
        nullable=False,
        default=func.now(),
        comment="创建时间"
    )
    
    updated_at = Column(
        DateTime,
        nullable=False,
        default=func.now(),
        onupdate=func.now(),
        comment="更新时间"
    )
    
    # 软删除字段
    deleted_at = Column(
        DateTime,
        nullable=True,
        comment="删除时间（软删除）"
    )
    
    # 是否激活字段
    is_active = Column(
        Boolean,
        nullable=False,
        default=True,
        comment="是否激活"
    )
    
    @declared_attr
    def __tablename__(cls):
        """
        自动生成表名（类名转换为下划线格式）
        
        Returns:
            str: 表名
        """
        # 将驼峰命名转换为下划线命名
        import re
        name = re.sub('(.)([A-Z][a-z]+)', r'\1_\2', cls.__name__)
        return re.sub('([a-z0-9])([A-Z])', r'\1_\2', name).lower()
    
    def to_dict(self, exclude_fields: Optional[list] = None) -> Dict[str, Any]:
        """
        将模型转换为字典
        
        Args:
            exclude_fields: 要排除的字段列表
            
        Returns:
            Dict[str, Any]: 模型字典
        """
        exclude_fields = exclude_fields or []
        result = {}
        
        for column in self.__table__.columns:
            field_name = column.name
            if field_name not in exclude_fields:
                value = getattr(self, field_name)
                
                # 处理datetime类型
                if isinstance(value, datetime):
                    result[field_name] = value.isoformat()
                else:
                    result[field_name] = value
        
        return result
    
    def update_from_dict(self, data: Dict[str, Any], exclude_fields: Optional[list] = None) -> None:
        """
        从字典更新模型
        
        Args:
            data: 更新数据字典
            exclude_fields: 要排除的字段列表
        """
        exclude_fields = exclude_fields or ['id', 'created_at', 'updated_at']
        
        for key, value in data.items():
            if key not in exclude_fields and hasattr(self, key):
                setattr(self, key, value)
    
    def soft_delete(self) -> None:
        """
        软删除记录
        """
        self.deleted_at = datetime.now()
        self.is_active = False
    
    def restore(self) -> None:
        """
        恢复软删除的记录
        """
        self.deleted_at = None
        self.is_active = True
    
    @property
    def is_deleted(self) -> bool:
        """
        检查记录是否已被软删除
        
        Returns:
            bool: 是否已删除
        """
        return self.deleted_at is not None
    
    def __repr__(self) -> str:
        """
        模型的字符串表示
        
        Returns:
            str: 模型字符串表示
        """
        return f"<{self.__class__.__name__}(id={self.id})>"


class TimestampMixin:
    """
    时间戳混入类
    
    为模型添加创建时间和更新时间字段
    """
    
    created_at = Column(
        DateTime,
        nullable=False,
        default=func.now(),
        comment="创建时间"
    )
    
    updated_at = Column(
        DateTime,
        nullable=False,
        default=func.now(),
        onupdate=func.now(),
        comment="更新时间"
    )


class SoftDeleteMixin:
    """
    软删除混入类
    
    为模型添加软删除功能
    """
    
    deleted_at = Column(
        DateTime,
        nullable=True,
        comment="删除时间（软删除）"
    )
    
    def soft_delete(self) -> None:
        """
        软删除记录
        """
        self.deleted_at = datetime.now()
    
    def restore(self) -> None:
        """
        恢复软删除的记录
        """
        self.deleted_at = None
    
    @property
    def is_deleted(self) -> bool:
        """
        检查记录是否已被软删除
        
        Returns:
            bool: 是否已删除
        """
        return self.deleted_at is not None


class ActiveMixin:
    """
    激活状态混入类
    
    为模型添加激活状态字段
    """
    
    is_active = Column(
        Boolean,
        nullable=False,
        default=True,
        comment="是否激活"
    )
    
    def activate(self) -> None:
        """
        激活记录
        """
        self.is_active = True
    
    def deactivate(self) -> None:
        """
        停用记录
        """
        self.is_active = False


class UserTrackingMixin:
    """
    用户跟踪混入类
    
    为模型添加创建者和更新者字段
    """
    
    created_by = Column(
        Integer,
        nullable=True,
        comment="创建者ID"
    )
    
    updated_by = Column(
        Integer,
        nullable=True,
        comment="更新者ID"
    )


class VersionMixin:
    """
    版本控制混入类
    
    为模型添加版本控制字段
    """
    
    version = Column(
        Integer,
        nullable=False,
        default=1,
        comment="版本号"
    )
    
    def increment_version(self) -> None:
        """
        增加版本号
        """
        self.version += 1


class MetadataMixin:
    """
    元数据混入类
    
    为模型添加元数据字段
    """
    
    meta_data = Column(
        "metadata",
        Text,
        nullable=True,
        comment="元数据（JSON格式）"
    )
    
    def set_metadata(self, data: Dict[str, Any]) -> None:
        """
        设置元数据
        
        Args:
            data: 元数据字典
        """
        import json
        self.meta_data = json.dumps(data, ensure_ascii=False)
    
    def get_metadata(self) -> Dict[str, Any]:
        """
        获取元数据
        
        Returns:
            Dict[str, Any]: 元数据字典
        """
        if not self.meta_data:
            return {}
        
        try:
            import json
            return json.loads(self.meta_data)
        except (json.JSONDecodeError, TypeError):
            return {}