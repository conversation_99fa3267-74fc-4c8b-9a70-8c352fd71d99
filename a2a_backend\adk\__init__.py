#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A2A多智能体系统 Google ADK集成层

提供Google ADK功能的完整封装，包括：
- Runner封装和管理
- Agent扩展和工厂
- 数据库服务替代
- 数据转换器
"""

__version__ = "1.0.0"
__author__ = "A2A Development Team"

# 导入主要组件
from .runners import (
    BaseRunner,
    AgentRunner,
    WorkflowRunner,
    RunnerManager,
    RunnerFactory
)

from .agents import (
    CustomLLMAgent,
    WorkflowAgent,
    ToolAgent,
    AgentRegistry,
    AgentFactory
)

from .services import (
    DatabaseSessionService,
    DatabaseMemoryService,
    DatabaseArtifactService,
    ServiceFactory
)

from .converters import (
    EventConverter,
    MessageConverter,
    ResponseConverter
)

__all__ = [
    # Runners
    "BaseRunner",
    "AgentRunner", 
    "WorkflowRunner",
    "RunnerManager",
    "RunnerFactory",
    
    # Agents
    "CustomLLMAgent",
    "WorkflowAgent",
    "ToolAgent",
    "AgentRegistry",
    "AgentFactory",
    
    # Services
    "DatabaseSessionService",
    "DatabaseMemoryService",
    "DatabaseArtifactService",
    "ServiceFactory",
    
    # Converters
    "EventConverter",
    "MessageConverter",
    "ResponseConverter"
]