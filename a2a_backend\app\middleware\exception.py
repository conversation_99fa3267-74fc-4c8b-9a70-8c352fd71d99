#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A2A多智能体系统异常处理中间件

处理全局异常和错误响应
"""

import json
from typing import Optional
from datetime import datetime

from fastapi import Request, Response, HTTPException, status
from starlette.middleware.base import BaseHTTPMiddleware
from fastapi.responses import JSONResponse
from starlette.middleware.base import RequestResponseEndpoint
from loguru import logger
from sqlalchemy import text

from app.core.database import get_database_manager


class ExceptionMiddleware(BaseHTTPMiddleware):
    """
    全局异常处理中间件
    
    捕获并处理所有未处理的异常
    """
    
    async def dispatch(self, request: Request, call_next: RequestResponseEndpoint) -> Response:
        """
        处理全局异常
        
        Args:
            request: HTTP请求
            call_next: 下一个中间件或路由处理器
            
        Returns:
            Response: HTTP响应
        """
        try:
            return await call_next(request)
        
        except HTTPException as e:
            # HTTP异常直接返回
            return JSONResponse(
                status_code=e.status_code,
                content={
                    "error": {
                        "code": e.status_code,
                        "message": e.detail,
                        "type": "http_exception",
                        "request_id": getattr(request.state, "request_id", None)
                    }
                }
            )
        
        except ValueError as e:
            # 值错误
            logger.error(f"值错误: {str(e)}", extra={"request_id": getattr(request.state, "request_id", None)})
            return JSONResponse(
                status_code=status.HTTP_400_BAD_REQUEST,
                content={
                    "error": {
                        "code": 400,
                        "message": "请求参数错误",
                        "detail": str(e),
                        "type": "value_error",
                        "request_id": getattr(request.state, "request_id", None)
                    }
                }
            )
        
        except PermissionError as e:
            # 权限错误
            logger.warning(f"权限错误: {str(e)}", extra={"request_id": getattr(request.state, "request_id", None)})
            return JSONResponse(
                status_code=status.HTTP_403_FORBIDDEN,
                content={
                    "error": {
                        "code": 403,
                        "message": "权限不足",
                        "detail": str(e),
                        "type": "permission_error",
                        "request_id": getattr(request.state, "request_id", None)
                    }
                }
            )
        
        except Exception as e:
            # 其他未知异常
            request_id = getattr(request.state, "request_id", None)
            logger.error(
                f"未处理的异常: {str(e)}",
                extra={
                    "request_id": request_id,
                    "exception_type": type(e).__name__,
                    "traceback": True
                }
            )
            
            # 记录错误到数据库
            await self._log_error_to_database(request, e, request_id)
            
            return JSONResponse(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                content={
                    "error": {
                        "code": 500,
                        "message": "服务器内部错误",
                        "type": "internal_error",
                        "request_id": request_id
                    }
                }
            )
    
    async def _log_error_to_database(self, request: Request, exception: Exception, request_id: Optional[str]) -> None:
        """
        记录错误到数据库
        
        Args:
            request: HTTP请求
            exception: 异常对象
            request_id: 请求ID
        """
        try:
            db_manager = get_database_manager()
            engine = db_manager.get_engine()
            
            error_data = {
                "error_type": type(exception).__name__,
                "error_message": str(exception),
                "request_method": request.method,
                "request_path": request.url.path,
                "request_query": str(request.query_params),
                "request_headers": dict(request.headers),
                "client_ip": request.client.host if request.client else "unknown",
                "user_agent": request.headers.get("user-agent", ""),
                "request_id": request_id
            }
            
            sql = """
                INSERT INTO error_logs (
                    error_type, error_message, stack_trace, request_data,
                    user_id, session_id, request_id, timestamp
                ) VALUES (
                    :error_type, :error_message, :stack_trace, :request_data,
                    :user_id, :session_id, :request_id, :timestamp
                )
            """
            
            async with engine.begin() as conn:
                await conn.execute(text(sql), {
                    "error_type": error_data["error_type"],
                    "error_message": error_data["error_message"][:500],
                    "stack_trace": "",  # 可以添加堆栈跟踪
                    "request_data": json.dumps(error_data, ensure_ascii=False)[:2000],
                    "user_id": getattr(request.state, "user_id", None),
                    "session_id": getattr(request.state, "session_id", None),
                    "request_id": request_id,
                    "timestamp": datetime.now()
                })
        
        except Exception as e:
            logger.error(f"记录错误到数据库失败: {str(e)}")