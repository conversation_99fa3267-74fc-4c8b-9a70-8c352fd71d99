#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A2A多智能体系统 数据库工件服务

替代GCS存储，包含用户权限控制
"""

import logging
import os
import hashlib
import mimetypes
from typing import Dict, List, Optional, Any, Union, BinaryIO
from datetime import datetime, timedelta
import uuid
from pathlib import Path

from google.adk.artifacts.base_artifact_service import BaseArtifactService

from app.models.user import User
from app.models.artifact import Artifact as ArtifactModel, ArtifactVersion
from app.core.logging import get_logger
from app.auth.permissions import check_user_permission
from app.core.database import get_db
from app.core.config import get_settings

class DatabaseArtifact:
    """
    数据库工件实现
    
    基于数据库和本地文件系统的工件存储
    """
    
    def __init__(
        self,
        artifact_id: str,
        user_id: int,
        owner_id: int,
        artifact_model: ArtifactModel,
        logger: Optional[logging.Logger] = None
    ):
        """
        初始化数据库工件
        
        Args:
            artifact_id: 工件ID
            user_id: 用户ID
            owner_id: 拥有者ID
            artifact_model: 工件模型
            logger: 日志记录器
        """
        self.artifact_id = artifact_id
        self.user_id = user_id
        self.owner_id = owner_id
        self.artifact_model = artifact_model
        self.logger = logger or get_logger(f"db_artifact_{artifact_id}")
        
        # 工件版本缓存
        self._version_cache: Dict[str, ArtifactVersion] = {}
        self._cache_dirty = False
        
        # 工件状态
        self._is_active = True
        self._last_access = datetime.now()
        
        # 存储路径
        self.storage_path = Path(get_settings().ARTIFACT_STORAGE_PATH) / str(owner_id) / artifact_id
        self.storage_path.mkdir(parents=True, exist_ok=True)
        
        self.logger.info(f"DatabaseArtifact已初始化: {artifact_id}")
    
    async def _load_versions(self) -> None:
        """
        从数据库加载工件版本
        """
        try:
            versions = await ArtifactVersion.get_by_artifact_id(self.artifact_model.id)
            self._version_cache = {version.version: version for version in versions or []}
            self._cache_dirty = False
            
            self.logger.debug(f"已加载 {len(self._version_cache)} 个工件版本")
        except Exception as e:
            self.logger.error(f"加载工件版本错误: {str(e)}")
            self._version_cache = {}
    
    def _calculate_file_hash(self, file_path: Path) -> str:
        """
        计算文件哈希值
        
        Args:
            file_path: 文件路径
            
        Returns:
            str: 文件哈希值
        """
        try:
            hash_md5 = hashlib.md5()
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception as e:
            self.logger.error(f"计算文件哈希错误: {str(e)}")
            return ""
    
    def _get_file_info(self, file_path: Path) -> Dict[str, Any]:
        """
        获取文件信息
        
        Args:
            file_path: 文件路径
            
        Returns:
            Dict[str, Any]: 文件信息
        """
        try:
            stat = file_path.stat()
            mime_type, _ = mimetypes.guess_type(str(file_path))
            
            return {
                "size": stat.st_size,
                "mime_type": mime_type or "application/octet-stream",
                "created_at": datetime.fromtimestamp(stat.st_ctime),
                "modified_at": datetime.fromtimestamp(stat.st_mtime),
                "hash": self._calculate_file_hash(file_path)
            }
        except Exception as e:
            self.logger.error(f"获取文件信息错误: {str(e)}")
            return {}
    
    async def upload(
        self,
        content: Union[bytes, BinaryIO],
        filename: str,
        version: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        上传工件内容
        
        Args:
            content: 文件内容
            filename: 文件名
            version: 版本号
            metadata: 元数据
            
        Returns:
            str: 版本号
        """
        try:
            # 更新最后访问时间
            self._last_access = datetime.now()
            
            # 生成版本号
            if not version:
                version = f"v{len(self._version_cache) + 1}_{int(self._last_access.timestamp())}"
            
            # 创建版本目录
            version_path = self.storage_path / version
            version_path.mkdir(parents=True, exist_ok=True)
            
            # 保存文件
            file_path = version_path / filename
            if isinstance(content, bytes):
                with open(file_path, "wb") as f:
                    f.write(content)
            else:
                # BinaryIO
                with open(file_path, "wb") as f:
                    content.seek(0)
                    f.write(content.read())
            
            # 获取文件信息
            file_info = self._get_file_info(file_path)
            
            # 创建版本记录
            artifact_version = ArtifactVersion(
                artifact_id=self.artifact_model.id,
                user_id=self.user_id,
                owner_id=self.owner_id,
                version=version,
                filename=filename,
                file_path=str(file_path),
                file_size=file_info.get("size", 0),
                mime_type=file_info.get("mime_type", "application/octet-stream"),
                file_hash=file_info.get("hash", ""),
                metadata=metadata or {},
                download_count=0
            )
            
            await artifact_version.save()
            
            # 缓存版本
            self._version_cache[version] = artifact_version
            self._cache_dirty = True
            
            # 更新工件模型
            self.artifact_model.version_count = len(self._version_cache)
            self.artifact_model.latest_version = version
            self.artifact_model.total_size += file_info.get("size", 0)
            self.artifact_model.last_access = self._last_access
            await self.artifact_model.save()
            
            self.logger.info(f"工件已上传: {filename} (版本: {version})")
            return version
        except Exception as e:
            self.logger.error(f"上传工件错误: {str(e)}")
            raise e
    
    async def download(
        self,
        version: Optional[str] = None
    ) -> Optional[bytes]:
        """
        下载工件内容
        
        Args:
            version: 版本号，如果为None则下载最新版本
            
        Returns:
            Optional[bytes]: 文件内容
        """
        try:
            # 更新最后访问时间
            self._last_access = datetime.now()
            
            # 如果缓存为空，从数据库加载
            if not self._version_cache:
                await self._load_versions()
            
            # 确定版本
            if not version:
                version = self.artifact_model.latest_version
            
            if not version or version not in self._version_cache:
                self.logger.error(f"版本不存在: {version}")
                return None
            
            # 获取版本记录
            artifact_version = self._version_cache[version]
            file_path = Path(artifact_version.file_path)
            
            if not file_path.exists():
                self.logger.error(f"文件不存在: {file_path}")
                return None
            
            # 读取文件内容
            with open(file_path, "rb") as f:
                content = f.read()
            
            # 更新下载统计
            artifact_version.download_count += 1
            artifact_version.last_downloaded = self._last_access
            await artifact_version.save()
            
            # 更新工件模型
            self.artifact_model.download_count += 1
            self.artifact_model.last_access = self._last_access
            await self.artifact_model.save()
            
            self.logger.info(f"工件已下载: {artifact_version.filename} (版本: {version})")
            return content
        except Exception as e:
            self.logger.error(f"下载工件错误: {str(e)}")
            return None
    
    async def get_download_url(
        self,
        version: Optional[str] = None,
        expires_in: int = 3600
    ) -> Optional[str]:
        """
        获取下载URL（临时访问链接）
        
        Args:
            version: 版本号
            expires_in: 过期时间（秒）
            
        Returns:
            Optional[str]: 下载URL
        """
        try:
            # 如果缓存为空，从数据库加载
            if not self._version_cache:
                await self._load_versions()
            
            # 确定版本
            if not version:
                version = self.artifact_model.latest_version
            
            if not version or version not in self._version_cache:
                return None
            
            # 生成临时访问令牌
            access_token = str(uuid.uuid4())
            expires_at = datetime.now() + timedelta(seconds=expires_in)
            
            # 这里可以实现临时访问令牌的存储和验证逻辑
            # 为简化，直接返回一个包含必要信息的URL
            download_url = f"/api/artifacts/{self.artifact_id}/download/{version}?token={access_token}&expires={int(expires_at.timestamp())}"
            
            return download_url
        except Exception as e:
            self.logger.error(f"获取下载URL错误: {str(e)}")
            return None
    
    async def list_versions(self) -> List[Dict[str, Any]]:
        """
        列出所有版本
        
        Returns:
            List[Dict[str, Any]]: 版本列表
        """
        try:
            # 如果缓存为空，从数据库加载
            if not self._version_cache:
                await self._load_versions()
            
            versions = []
            for version_record in self._version_cache.values():
                version_info = {
                    "version": version_record.version,
                    "filename": version_record.filename,
                    "file_size": version_record.file_size,
                    "mime_type": version_record.mime_type,
                    "file_hash": version_record.file_hash,
                    "download_count": version_record.download_count,
                    "created_at": version_record.created_at.isoformat() if version_record.created_at else None,
                    "last_downloaded": version_record.last_downloaded.isoformat() if version_record.last_downloaded else None,
                    "metadata": version_record.metadata
                }
                versions.append(version_info)
            
            # 按创建时间排序
            versions.sort(key=lambda x: x["created_at"] or "", reverse=True)
            
            return versions
        except Exception as e:
            self.logger.error(f"列出版本错误: {str(e)}")
            return []
    
    async def delete_version(self, version: str) -> bool:
        """
        删除指定版本
        
        Args:
            version: 版本号
            
        Returns:
            bool: 是否删除成功
        """
        try:
            # 如果缓存为空，从数据库加载
            if not self._version_cache:
                await self._load_versions()
            
            if version not in self._version_cache:
                return False
            
            # 获取版本记录
            artifact_version = self._version_cache[version]
            file_path = Path(artifact_version.file_path)
            
            # 删除文件
            if file_path.exists():
                file_path.unlink()
            
            # 删除版本目录（如果为空）
            version_dir = file_path.parent
            if version_dir.exists() and not any(version_dir.iterdir()):
                version_dir.rmdir()
            
            # 删除数据库记录
            await artifact_version.delete()
            
            # 从缓存中移除
            del self._version_cache[version]
            self._cache_dirty = True
            
            # 更新工件模型
            self.artifact_model.version_count = len(self._version_cache)
            self.artifact_model.total_size -= artifact_version.file_size
            
            # 如果删除的是最新版本，更新最新版本
            if self.artifact_model.latest_version == version:
                if self._version_cache:
                    # 找到最新的版本
                    latest_version = max(
                        self._version_cache.values(),
                        key=lambda v: v.created_at or datetime.min
                    )
                    self.artifact_model.latest_version = latest_version.version
                else:
                    self.artifact_model.latest_version = None
            
            await self.artifact_model.save()
            
            self.logger.info(f"版本已删除: {version}")
            return True
        except Exception as e:
            self.logger.error(f"删除版本错误: {str(e)}")
            return False
    
    async def get_metadata(self, version: Optional[str] = None) -> Dict[str, Any]:
        """
        获取工件元数据
        
        Args:
            version: 版本号
            
        Returns:
            Dict[str, Any]: 元数据
        """
        try:
            # 如果缓存为空，从数据库加载
            if not self._version_cache:
                await self._load_versions()
            
            # 确定版本
            if not version:
                version = self.artifact_model.latest_version
            
            if version and version in self._version_cache:
                artifact_version = self._version_cache[version]
                return artifact_version.metadata or {}
            
            return self.artifact_model.metadata or {}
        except Exception as e:
            self.logger.error(f"获取元数据错误: {str(e)}")
            return {}
    
    async def update_metadata(
        self,
        metadata: Dict[str, Any],
        version: Optional[str] = None
    ) -> None:
        """
        更新工件元数据
        
        Args:
            metadata: 元数据
            version: 版本号，如果为None则更新工件级别的元数据
        """
        try:
            if version:
                # 更新版本级别的元数据
                if not self._version_cache:
                    await self._load_versions()
                
                if version in self._version_cache:
                    artifact_version = self._version_cache[version]
                    current_metadata = artifact_version.metadata or {}
                    current_metadata.update(metadata)
                    artifact_version.metadata = current_metadata
                    await artifact_version.save()
            else:
                # 更新工件级别的元数据
                current_metadata = self.artifact_model.metadata or {}
                current_metadata.update(metadata)
                self.artifact_model.metadata = current_metadata
                await self.artifact_model.save()
            
            self.logger.info(f"元数据已更新: {version or 'artifact'}")
        except Exception as e:
            self.logger.error(f"更新元数据错误: {str(e)}")
            raise e
    
    async def get_stats(self) -> Dict[str, Any]:
        """
        获取工件统计
        
        Returns:
            Dict[str, Any]: 工件统计信息
        """
        try:
            # 如果缓存为空，从数据库加载
            if not self._version_cache:
                await self._load_versions()
            
            total_downloads = sum(v.download_count for v in self._version_cache.values())
            
            return {
                "artifact_id": self.artifact_id,
                "version_count": len(self._version_cache),
                "total_size": self.artifact_model.total_size,
                "total_downloads": total_downloads,
                "latest_version": self.artifact_model.latest_version,
                "last_access": self._last_access.isoformat(),
                "is_active": self._is_active,
                "created_at": self.artifact_model.created_at.isoformat() if self.artifact_model.created_at else None
            }
        except Exception as e:
            self.logger.error(f"获取工件统计错误: {str(e)}")
            return {}
    
    def is_active(self) -> bool:
        """
        检查工件是否活跃
        
        Returns:
            bool: 是否活跃
        """
        return self._is_active
    
    async def close(self) -> None:
        """
        关闭工件
        """
        try:
            self._is_active = False
            
            # 更新工件状态
            self.artifact_model.status = "archived"
            await self.artifact_model.save()
            
            self.logger.info("工件已关闭")
        except Exception as e:
            self.logger.error(f"关闭工件错误: {str(e)}")
            raise e

class DatabaseArtifactService(BaseArtifactService):
    """
    数据库工件服务，替代GCS存储，包含用户权限控制
    
    提供以下功能：
    1. 基于数据库和本地文件系统的工件存储
    2. 用户权限验证和数据隔离
    3. 工件生命周期管理
    4. 版本控制和历史管理
    5. 文件完整性验证
    6. 工件统计和监控
    7. 批量操作支持
    8. 临时访问链接生成
    """
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        """
        初始化数据库工件服务
        
        Args:
            logger: 日志记录器
        """
        super().__init__()
        
        self.logger = logger or get_logger("database_artifact_service")
        
        # 工件缓存
        self._artifact_cache: Dict[str, DatabaseArtifact] = {}
        
        # 服务统计
        self.service_stats = {
            "total_artifacts_created": 0,
            "active_artifacts": 0,
            "total_versions": 0,
            "total_storage_size": 0,
            "total_downloads": 0,
            "users_served": set(),
            "last_activity": None
        }
        
        # 确保存储目录存在
        storage_path = Path(get_settings().ARTIFACT_STORAGE_PATH)
        storage_path.mkdir(parents=True, exist_ok=True)
        
        self.logger.info("DatabaseArtifactService已初始化")
    
    async def _check_permission(
        self,
        user_id: int,
        owner_id: int,
        action: str = "create"
    ) -> bool:
        """
        检查用户权限
        
        Args:
            user_id: 用户ID
            owner_id: 拥有者ID
            action: 操作类型
            
        Returns:
            bool: 是否有权限
        """
        try:
            # 检查用户是否存在
            user = await User.get_by_id(user_id)
            if not user:
                self.logger.error(f"用户不存在: {user_id}")
                return False
            
            # 检查用户权限
            has_permission = await check_user_permission(
                user_id=user_id,
                owner_id=owner_id,
                resource_type="artifact",
                action=action
            )
            
            if not has_permission:
                self.logger.error(f"用户 {user_id} 没有权限{action}工件")
            
            return has_permission
        except Exception as e:
            self.logger.error(f"权限检查错误: {str(e)}")
            return False
    
    async def create_artifact(
        self,
        user_id: int,
        owner_id: int,
        artifact_name: str,
        artifact_type: str = "file",
        description: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Optional[DatabaseArtifact]:
        """
        创建新工件
        
        Args:
            user_id: 用户ID
            owner_id: 拥有者ID
            artifact_name: 工件名称
            artifact_type: 工件类型
            description: 工件描述
            metadata: 工件元数据
            
        Returns:
            Optional[DatabaseArtifact]: 数据库工件实例
        """
        try:
            # 检查权限
            has_permission = await self._check_permission(user_id, owner_id, "create")
            if not has_permission:
                raise PermissionError(f"用户 {user_id} 没有权限创建工件")
            
            # 生成工件ID
            artifact_id = str(uuid.uuid4())
            
            # 创建工件模型
            artifact_model = ArtifactModel(
                artifact_id=artifact_id,
                user_id=user_id,
                owner_id=owner_id,
                name=artifact_name,
                artifact_type=artifact_type,
                description=description,
                status="active",
                metadata=metadata or {},
                version_count=0,
                total_size=0,
                download_count=0
            )
            
            await artifact_model.save()
            
            # 创建工件实例
            artifact = DatabaseArtifact(
                artifact_id=artifact_id,
                user_id=user_id,
                owner_id=owner_id,
                artifact_model=artifact_model,
                logger=self.logger
            )
            
            # 缓存工件
            self._artifact_cache[artifact_id] = artifact
            
            # 更新统计
            self.service_stats["total_artifacts_created"] += 1
            self.service_stats["active_artifacts"] += 1
            self.service_stats["users_served"].add(user_id)
            self.service_stats["last_activity"] = datetime.now().isoformat()
            
            self.logger.info(f"工件已创建: {artifact_id} (用户: {user_id})")
            return artifact
        except Exception as e:
            self.logger.error(f"创建工件错误: {str(e)}")
            raise e
    
    async def get_artifact(
        self,
        artifact_id: str,
        user_id: int
    ) -> Optional[DatabaseArtifact]:
        """
        获取工件
        
        Args:
            artifact_id: 工件ID
            user_id: 用户ID
            
        Returns:
            Optional[DatabaseArtifact]: 数据库工件实例
        """
        try:
            # 检查缓存
            if artifact_id in self._artifact_cache:
                artifact = self._artifact_cache[artifact_id]
                # 验证用户权限
                if artifact.user_id == user_id or await self._check_permission(user_id, artifact.owner_id, "read"):
                    return artifact
                else:
                    self.logger.error(f"用户 {user_id} 没有权限访问工件 {artifact_id}")
                    return None
            
            # 从数据库加载
            artifact_model = await ArtifactModel.get_by_artifact_id(artifact_id)
            if not artifact_model:
                self.logger.error(f"工件不存在: {artifact_id}")
                return None
            
            # 验证用户权限
            if artifact_model.user_id != user_id and not await self._check_permission(user_id, artifact_model.owner_id, "read"):
                self.logger.error(f"用户 {user_id} 没有权限访问工件 {artifact_id}")
                return None
            
            # 创建工件实例
            artifact = DatabaseArtifact(
                artifact_id=artifact_id,
                user_id=artifact_model.user_id,
                owner_id=artifact_model.owner_id,
                artifact_model=artifact_model,
                logger=self.logger
            )
            
            # 缓存工件
            self._artifact_cache[artifact_id] = artifact
            
            return artifact
        except Exception as e:
            self.logger.error(f"获取工件错误: {str(e)}")
            return None
    
    async def list_user_artifacts(
        self,
        user_id: int,
        owner_id: int,
        artifact_type: Optional[str] = None,
        limit: int = 50,
        offset: int = 0
    ) -> List[Dict[str, Any]]:
        """
        列出用户工件
        
        Args:
            user_id: 用户ID
            owner_id: 拥有者ID
            artifact_type: 工件类型过滤
            limit: 数量限制
            offset: 偏移量
            
        Returns:
            List[Dict[str, Any]]: 工件列表
        """
        try:
            # 检查权限
            has_permission = await self._check_permission(user_id, owner_id, "list")
            if not has_permission:
                raise PermissionError(f"用户 {user_id} 没有权限列出工件")
            
            # 从数据库获取工件
            artifacts = await ArtifactModel.get_by_user_id(
                user_id,
                artifact_type=artifact_type,
                limit=limit,
                offset=offset
            )
            
            artifact_list = []
            for artifact_model in artifacts:
                artifact_info = {
                    "artifact_id": artifact_model.artifact_id,
                    "name": artifact_model.name,
                    "artifact_type": artifact_model.artifact_type,
                    "description": artifact_model.description,
                    "status": artifact_model.status,
                    "version_count": artifact_model.version_count,
                    "latest_version": artifact_model.latest_version,
                    "total_size": artifact_model.total_size,
                    "download_count": artifact_model.download_count,
                    "created_at": artifact_model.created_at.isoformat() if artifact_model.created_at else None,
                    "last_access": artifact_model.last_access.isoformat() if artifact_model.last_access else None,
                    "metadata": artifact_model.metadata
                }
                artifact_list.append(artifact_info)
            
            return artifact_list
        except Exception as e:
            self.logger.error(f"列出用户工件错误: {str(e)}")
            return []
    
    async def delete_artifact(
        self,
        artifact_id: str,
        user_id: int
    ) -> bool:
        """
        删除工件
        
        Args:
            artifact_id: 工件ID
            user_id: 用户ID
            
        Returns:
            bool: 是否删除成功
        """
        try:
            # 获取工件
            artifact = await self.get_artifact(artifact_id, user_id)
            if not artifact:
                return False
            
            # 检查删除权限
            has_permission = await self._check_permission(user_id, artifact.owner_id, "delete")
            if not has_permission:
                raise PermissionError(f"用户 {user_id} 没有权限删除工件 {artifact_id}")
            
            # 关闭工件
            await artifact.close()
            
            # 删除所有版本文件
            import shutil
            if artifact.storage_path.exists():
                shutil.rmtree(artifact.storage_path)
            
            # 删除版本记录
            await ArtifactVersion.delete_by_artifact_id(artifact.artifact_model.id)
            
            # 删除工件模型
            await artifact.artifact_model.delete()
            
            # 从缓存中移除
            if artifact_id in self._artifact_cache:
                del self._artifact_cache[artifact_id]
            
            # 更新统计
            self.service_stats["active_artifacts"] = max(0, self.service_stats["active_artifacts"] - 1)
            self.service_stats["total_storage_size"] -= artifact.artifact_model.total_size
            
            self.logger.info(f"工件已删除: {artifact_id}")
            return True
        except Exception as e:
            self.logger.error(f"删除工件错误: {str(e)}")
            return False
    
    async def cleanup_orphaned_files(self) -> int:
        """
        清理孤立文件
        
        Returns:
            int: 清理的文件数量
        """
        try:
            storage_path = Path(get_settings().ARTIFACT_STORAGE_PATH)
            if not storage_path.exists():
                return 0
            
            cleaned_count = 0
            
            # 获取所有数据库中的工件路径
            db_artifacts = await ArtifactModel.get_all_active()
            db_artifact_ids = {artifact.artifact_id for artifact in db_artifacts}
            
            # 遍历存储目录
            for owner_dir in storage_path.iterdir():
                if not owner_dir.is_dir():
                    continue
                
                for artifact_dir in owner_dir.iterdir():
                    if not artifact_dir.is_dir():
                        continue
                    
                    artifact_id = artifact_dir.name
                    if artifact_id not in db_artifact_ids:
                        # 孤立目录，删除
                        import shutil
                        shutil.rmtree(artifact_dir)
                        cleaned_count += 1
                        self.logger.info(f"已清理孤立工件目录: {artifact_id}")
            
            if cleaned_count > 0:
                self.logger.info(f"已清理 {cleaned_count} 个孤立文件/目录")
            
            return cleaned_count
        except Exception as e:
            self.logger.error(f"清理孤立文件错误: {str(e)}")
            return 0
    
    async def get_storage_stats(self) -> Dict[str, Any]:
        """
        获取存储统计
        
        Returns:
            Dict[str, Any]: 存储统计信息
        """
        try:
            storage_path = Path(get_settings().ARTIFACT_STORAGE_PATH)
            
            total_size = 0
            file_count = 0
            
            if storage_path.exists():
                for file_path in storage_path.rglob("*"):
                    if file_path.is_file():
                        total_size += file_path.stat().st_size
                        file_count += 1
            
            return {
                "storage_path": str(storage_path),
                "total_size": total_size,
                "file_count": file_count,
                "total_artifacts": len(self._artifact_cache),
                "active_artifacts": self.service_stats["active_artifacts"]
            }
        except Exception as e:
            self.logger.error(f"获取存储统计错误: {str(e)}")
            return {}
    
    def get_service_stats(self) -> Dict[str, Any]:
        """
        获取服务统计
        
        Returns:
            Dict[str, Any]: 服务统计信息
        """
        stats = self.service_stats.copy()
        stats["users_served"] = len(stats["users_served"])
        stats["cached_artifacts"] = len(self._artifact_cache)
        return stats
    
    async def close(self) -> None:
        """
        关闭服务
        """
        try:
            # 关闭所有活跃工件
            for artifact in self._artifact_cache.values():
                await artifact.close()
            
            # 清空缓存
            self._artifact_cache.clear()
            
            self.logger.info("DatabaseArtifactService已关闭")
        except Exception as e:
            self.logger.error(f"关闭服务错误: {str(e)}")