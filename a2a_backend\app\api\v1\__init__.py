# -*- coding: utf-8 -*-
"""
A2A多智能体系统API v1模块

提供RESTful API接口
"""

from fastapi import APIRouter
from . import auth, users, agents, sessions, messages, tasks, workflows, stream, websocket, configs, artifacts, monitoring

# 创建API路由器
api_router = APIRouter(prefix="/api/v1")

# 注册各模块路由
api_router.include_router(auth.router, tags=["认证"])
api_router.include_router(users.router, tags=["用户管理"])
api_router.include_router(agents.router, tags=["智能体管理"])
api_router.include_router(sessions.router, tags=["会话管理"])
api_router.include_router(messages.router, tags=["消息管理"])
api_router.include_router(tasks.router, tags=["任务管理"])
api_router.include_router(workflows.router, tags=["工作流管理"])
api_router.include_router(stream.router, tags=["流式输出"])
api_router.include_router(websocket.router, tags=["WebSocket通信"])
api_router.include_router(configs.router, tags=["配置管理"])
api_router.include_router(artifacts.router, tags=["工件管理"])
api_router.include_router(monitoring.router, tags=["监控管理"])

__all__ = [
    "api_router"
]