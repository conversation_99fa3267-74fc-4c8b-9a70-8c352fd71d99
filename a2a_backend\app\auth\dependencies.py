# -*- coding: utf-8 -*-
"""
A2A多智能体系统认证依赖模块

提供FastAPI依赖注入的认证相关函数
"""

from typing import Optional
from fastapi import Depends, HTTPException, status
from fastapi.security import H<PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.models.user import User
from app.auth.jwt_handler import <PERSON><PERSON><PERSON><PERSON><PERSON>
from app.core.logging import get_logger


logger = get_logger("auth_dependencies")
security = HTTPBearer()
jwt_handler = JWTHandler()


async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: AsyncSession = Depends(get_db)
) -> User:
    """
    获取当前认证用户
    
    Args:
        credentials: JWT凭证
        db: 数据库会话
        
    Returns:
        User: 当前用户对象
        
    Raises:
        HTTPException: 认证失败时抛出401错误
    """
    try:
        # 验证JWT令牌
        payload = jwt_handler.decode_token(credentials.credentials)
        user_id = payload.get("sub")
        
        if user_id is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="无效的认证凭证",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        # 从数据库获取用户
        user = await User.get_by_id(db, int(user_id))
        if user is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户不存在",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        # 检查用户状态
        if not user.is_active:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户账户已被禁用",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        return user
        
    except Exception as e:
        logger.error(f"用户认证失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="认证失败",
            headers={"WWW-Authenticate": "Bearer"},
        )


async def get_current_active_user(
    current_user: User = Depends(get_current_user)
) -> User:
    """
    获取当前活跃用户（已验证且活跃）
    
    Args:
        current_user: 当前用户
        
    Returns:
        User: 当前活跃用户对象
        
    Raises:
        HTTPException: 用户不活跃时抛出400错误
    """
    if not current_user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户账户已被禁用"
        )
    return current_user


async def get_current_admin_user(
    current_user: User = Depends(get_current_user)
) -> User:
    """
    获取当前管理员用户
    
    Args:
        current_user: 当前用户
        
    Returns:
        User: 当前管理员用户对象
        
    Raises:
        HTTPException: 用户不是管理员时抛出403错误
    """
    if not current_user.is_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要管理员权限"
        )
    return current_user


async def get_optional_current_user(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security),
    db: AsyncSession = Depends(get_db)
) -> Optional[User]:
    """
    获取可选的当前用户（允许匿名访问）
    
    Args:
        credentials: 可选的JWT凭证
        db: 数据库会话
        
    Returns:
        Optional[User]: 当前用户对象或None
    """
    if credentials is None:
        return None
    
    try:
        return await get_current_user(credentials, db)
    except HTTPException:
        return None


def require_permissions(*permissions: str):
    """
    权限检查装饰器
    
    Args:
        *permissions: 需要的权限列表
        
    Returns:
        function: 依赖函数
    """
    async def check_permissions(
        current_user: User = Depends(get_current_user)
    ) -> User:
        """
        检查用户权限
        
        Args:
            current_user: 当前用户
            
        Returns:
            User: 当前用户对象
            
        Raises:
            HTTPException: 权限不足时抛出403错误
        """
        # 管理员拥有所有权限
        if current_user.is_admin:
            return current_user
        
        # 检查用户权限（这里需要根据实际的权限系统实现）
        # 暂时简化处理
        user_permissions = getattr(current_user, 'permissions', [])
        
        for permission in permissions:
            if permission not in user_permissions:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"缺少权限: {permission}"
                )
        
        return current_user
    
    return check_permissions