# -*- coding: utf-8 -*-
"""
A2A多智能体系统存储优化模块

提供大文件分块存储、压缩算法、数据库连接池优化等功能
"""

import os
import gzip
import zlib
import lzma
import hashlib
import asyncio
import aiofiles
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any, Union, Tuple, AsyncGenerator
from pathlib import Path
from contextlib import asynccontextmanager
from sqlalchemy import create_engine, text, pool
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import QueuePool, StaticPool
from sqlalchemy.engine import Engine
from concurrent.futures import ThreadPoolExecutor
import threading
import time

from app.core.config import get_settings
from app.core.logging import get_logger
from app.models.artifact import Artifact, ArtifactVersion

logger = get_logger(__name__)


# ==================== 压缩算法管理 ====================

class CompressionManager:
    """
    压缩算法管理器
    
    支持多种压缩算法的选择和优化
    """
    
    ALGORITHMS = {
        "gzip": {
            "compress": lambda data, level=6: gzip.compress(data, compresslevel=level),
            "decompress": gzip.decompress,
            "extension": ".gz",
            "ratio_threshold": 0.8  # 压缩率阈值
        },
        "zlib": {
            "compress": lambda data, level=6: zlib.compress(data, level=level),
            "decompress": zlib.decompress,
            "extension": ".zlib",
            "ratio_threshold": 0.8
        },
        "lzma": {
            "compress": lambda data, level=6: lzma.compress(data, preset=level),
            "decompress": lzma.decompress,
            "extension": ".xz",
            "ratio_threshold": 0.7
        }
    }
    
    def __init__(self):
        """初始化压缩管理器"""
        self.default_algorithm = "gzip"
        self.compression_level = 6
        self.min_size_for_compression = 1024  # 1KB以上才压缩
    
    def should_compress(self, data: bytes, content_type: str = None) -> bool:
        """
        判断是否应该压缩数据
        
        Args:
            data: 原始数据
            content_type: 内容类型
            
        Returns:
            是否应该压缩
        """
        # 小文件不压缩
        if len(data) < self.min_size_for_compression:
            return False
        
        # 已压缩的文件类型不再压缩
        if content_type:
            compressed_types = {
                "image/jpeg", "image/png", "image/gif", "image/webp",
                "video/mp4", "video/avi", "video/mkv",
                "audio/mp3", "audio/aac", "audio/ogg",
                "application/zip", "application/rar", "application/7z",
                "application/gzip", "application/x-compressed"
            }
            if content_type.lower() in compressed_types:
                return False
        
        return True
    
    def choose_algorithm(self, data: bytes, content_type: str = None) -> str:
        """
        选择最佳压缩算法
        
        Args:
            data: 原始数据
            content_type: 内容类型
            
        Returns:
            压缩算法名称
        """
        # 对于文本类型，使用gzip
        if content_type and content_type.startswith("text/"):
            return "gzip"
        
        # 对于JSON、XML等结构化数据，使用lzma
        if content_type and content_type in ["application/json", "application/xml", "text/xml"]:
            return "lzma"
        
        # 默认使用gzip
        return self.default_algorithm
    
    def compress(self, data: bytes, algorithm: str = None, level: int = None) -> Tuple[bytes, str, float]:
        """
        压缩数据
        
        Args:
            data: 原始数据
            algorithm: 压缩算法
            level: 压缩级别
            
        Returns:
            (压缩后数据, 算法名称, 压缩率)
        """
        if not algorithm:
            algorithm = self.default_algorithm
        
        if not level:
            level = self.compression_level
        
        if algorithm not in self.ALGORITHMS:
            raise ValueError(f"不支持的压缩算法: {algorithm}")
        
        try:
            start_time = time.time()
            compressed_data = self.ALGORITHMS[algorithm]["compress"](data, level)
            compression_time = time.time() - start_time
            
            compression_ratio = len(compressed_data) / len(data)
            
            logger.debug(
                f"压缩完成: {algorithm}, 原始大小: {len(data)}, "
                f"压缩后大小: {len(compressed_data)}, 压缩率: {compression_ratio:.2f}, "
                f"耗时: {compression_time:.3f}s"
            )
            
            # 如果压缩效果不好，返回原始数据
            threshold = self.ALGORITHMS[algorithm]["ratio_threshold"]
            if compression_ratio > threshold:
                logger.debug(f"压缩效果不佳，返回原始数据")
                return data, "none", 1.0
            
            return compressed_data, algorithm, compression_ratio
            
        except Exception as e:
            logger.error(f"压缩失败: {algorithm}, 错误: {str(e)}")
            return data, "none", 1.0
    
    def decompress(self, data: bytes, algorithm: str) -> bytes:
        """
        解压缩数据
        
        Args:
            data: 压缩数据
            algorithm: 压缩算法
            
        Returns:
            解压缩后的数据
        """
        if algorithm == "none":
            return data
        
        if algorithm not in self.ALGORITHMS:
            raise ValueError(f"不支持的压缩算法: {algorithm}")
        
        try:
            start_time = time.time()
            decompressed_data = self.ALGORITHMS[algorithm]["decompress"](data)
            decompression_time = time.time() - start_time
            
            logger.debug(
                f"解压缩完成: {algorithm}, 压缩大小: {len(data)}, "
                f"解压后大小: {len(decompressed_data)}, 耗时: {decompression_time:.3f}s"
            )
            
            return decompressed_data
            
        except Exception as e:
            logger.error(f"解压缩失败: {algorithm}, 错误: {str(e)}")
            raise


# ==================== 分块存储管理 ====================

class ChunkStorageManager:
    """
    分块存储管理器
    
    支持大文件的分块存储和管理
    """
    
    def __init__(self, chunk_size: int = 1024 * 1024, storage_path: str = None):
        """
        初始化分块存储管理器
        
        Args:
            chunk_size: 分块大小（默认1MB）
            storage_path: 存储路径
        """
        self.chunk_size = chunk_size
        settings = get_settings()
        self.storage_path = Path(storage_path or getattr(settings, 'STORAGE_PATH', 'storage'))
        self.storage_path.mkdir(parents=True, exist_ok=True)
        
        # 创建分块存储目录
        self.chunks_path = self.storage_path / "chunks"
        self.chunks_path.mkdir(exist_ok=True)
        
        self.compression_manager = CompressionManager()
        self.executor = ThreadPoolExecutor(max_workers=4)
    
    def _generate_chunk_id(self, file_hash: str, chunk_index: int) -> str:
        """
        生成分块ID
        
        Args:
            file_hash: 文件哈希
            chunk_index: 分块索引
            
        Returns:
            分块ID
        """
        return f"{file_hash}_{chunk_index:06d}"
    
    def _get_chunk_path(self, chunk_id: str) -> Path:
        """
        获取分块存储路径
        
        Args:
            chunk_id: 分块ID
            
        Returns:
            分块文件路径
        """
        # 使用前两个字符作为子目录，避免单个目录文件过多
        subdir = chunk_id[:2]
        chunk_dir = self.chunks_path / subdir
        chunk_dir.mkdir(exist_ok=True)
        return chunk_dir / f"{chunk_id}.chunk"
    
    async def store_file_chunks(self, file_data: bytes, file_hash: str) -> Dict[str, Any]:
        """
        存储文件分块
        
        Args:
            file_data: 文件数据
            file_hash: 文件哈希
            
        Returns:
            分块信息
        """
        try:
            file_size = len(file_data)
            chunk_count = (file_size + self.chunk_size - 1) // self.chunk_size
            
            chunk_info = {
                "file_hash": file_hash,
                "file_size": file_size,
                "chunk_size": self.chunk_size,
                "chunk_count": chunk_count,
                "chunks": [],
                "compression_stats": {
                    "total_compressed_size": 0,
                    "compression_ratio": 0.0,
                    "algorithms_used": {}
                }
            }
            
            # 并发处理分块
            tasks = []
            for i in range(chunk_count):
                start_pos = i * self.chunk_size
                end_pos = min(start_pos + self.chunk_size, file_size)
                chunk_data = file_data[start_pos:end_pos]
                
                task = self._store_single_chunk(chunk_data, file_hash, i)
                tasks.append(task)
            
            # 等待所有分块处理完成
            chunk_results = await asyncio.gather(*tasks)
            
            # 汇总分块信息
            total_compressed_size = 0
            algorithms_used = {}
            
            for chunk_result in chunk_results:
                chunk_info["chunks"].append(chunk_result)
                total_compressed_size += chunk_result["compressed_size"]
                
                algorithm = chunk_result["compression_algorithm"]
                algorithms_used[algorithm] = algorithms_used.get(algorithm, 0) + 1
            
            # 更新压缩统计
            chunk_info["compression_stats"].update({
                "total_compressed_size": total_compressed_size,
                "compression_ratio": total_compressed_size / file_size,
                "algorithms_used": algorithms_used
            })
            
            logger.info(
                f"文件分块存储完成: {file_hash}, 分块数: {chunk_count}, "
                f"压缩率: {chunk_info['compression_stats']['compression_ratio']:.2f}"
            )
            
            return chunk_info
            
        except Exception as e:
            logger.error(f"存储文件分块失败: {file_hash}, 错误: {str(e)}")
            raise
    
    async def _store_single_chunk(self, chunk_data: bytes, file_hash: str, chunk_index: int) -> Dict[str, Any]:
        """
        存储单个分块
        
        Args:
            chunk_data: 分块数据
            file_hash: 文件哈希
            chunk_index: 分块索引
            
        Returns:
            分块信息
        """
        chunk_id = self._generate_chunk_id(file_hash, chunk_index)
        chunk_path = self._get_chunk_path(chunk_id)
        
        # 计算分块哈希
        chunk_hash = hashlib.sha256(chunk_data).hexdigest()
        
        # 检查分块是否已存在（去重）
        if chunk_path.exists():
            logger.debug(f"分块已存在，跳过存储: {chunk_id}")
            stat = chunk_path.stat()
            return {
                "chunk_id": chunk_id,
                "chunk_index": chunk_index,
                "chunk_hash": chunk_hash,
                "original_size": len(chunk_data),
                "compressed_size": stat.st_size,
                "compression_algorithm": "unknown",
                "storage_path": str(chunk_path)
            }
        
        # 压缩分块
        compressed_data, algorithm, compression_ratio = self.compression_manager.compress(chunk_data)
        
        # 异步写入文件
        async with aiofiles.open(chunk_path, 'wb') as f:
            await f.write(compressed_data)
        
        chunk_info = {
            "chunk_id": chunk_id,
            "chunk_index": chunk_index,
            "chunk_hash": chunk_hash,
            "original_size": len(chunk_data),
            "compressed_size": len(compressed_data),
            "compression_algorithm": algorithm,
            "compression_ratio": compression_ratio,
            "storage_path": str(chunk_path)
        }
        
        logger.debug(f"分块存储完成: {chunk_id}, 压缩率: {compression_ratio:.2f}")
        
        return chunk_info
    
    async def retrieve_file_chunks(self, chunk_info: Dict[str, Any]) -> bytes:
        """
        检索文件分块并重组
        
        Args:
            chunk_info: 分块信息
            
        Returns:
            重组后的文件数据
        """
        try:
            chunks = chunk_info["chunks"]
            chunk_count = len(chunks)
            
            # 并发读取分块
            tasks = []
            for chunk in chunks:
                task = self._retrieve_single_chunk(chunk)
                tasks.append(task)
            
            # 等待所有分块读取完成
            chunk_data_list = await asyncio.gather(*tasks)
            
            # 按索引排序并重组
            sorted_chunks = sorted(zip(chunks, chunk_data_list), key=lambda x: x[0]["chunk_index"])
            file_data = b''.join([chunk_data for _, chunk_data in sorted_chunks])
            
            logger.debug(f"文件分块检索完成: 分块数: {chunk_count}, 文件大小: {len(file_data)}")
            
            return file_data
            
        except Exception as e:
            logger.error(f"检索文件分块失败: 错误: {str(e)}")
            raise
    
    async def _retrieve_single_chunk(self, chunk: Dict[str, Any]) -> bytes:
        """
        检索单个分块
        
        Args:
            chunk: 分块信息
            
        Returns:
            分块数据
        """
        chunk_path = Path(chunk["storage_path"])
        
        if not chunk_path.exists():
            raise FileNotFoundError(f"分块文件不存在: {chunk_path}")
        
        # 异步读取文件
        async with aiofiles.open(chunk_path, 'rb') as f:
            compressed_data = await f.read()
        
        # 解压缩
        algorithm = chunk["compression_algorithm"]
        chunk_data = self.compression_manager.decompress(compressed_data, algorithm)
        
        return chunk_data
    
    async def delete_file_chunks(self, chunk_info: Dict[str, Any]) -> bool:
        """
        删除文件分块
        
        Args:
            chunk_info: 分块信息
            
        Returns:
            是否删除成功
        """
        try:
            deleted_count = 0
            
            for chunk in chunk_info["chunks"]:
                chunk_path = Path(chunk["storage_path"])
                if chunk_path.exists():
                    chunk_path.unlink()
                    deleted_count += 1
            
            logger.info(f"文件分块删除完成: 删除数量: {deleted_count}")
            return True
            
        except Exception as e:
            logger.error(f"删除文件分块失败: 错误: {str(e)}")
            return False


# ==================== 数据库连接池优化 ====================

class OptimizedDatabaseManager:
    """
    优化的数据库管理器
    
    提供连接池优化、查询性能优化等功能
    """
    
    def __init__(self):
        """初始化数据库管理器"""
        self.engines = {}
        self.session_factories = {}
        self.connection_stats = {
            "total_connections": 0,
            "active_connections": 0,
            "pool_size": 0,
            "overflow_size": 0,
            "query_count": 0,
            "slow_query_count": 0
        }
        self._lock = threading.Lock()
    
    def create_optimized_engine(self, database_url: str, pool_name: str = "default") -> Engine:
        """
        创建优化的数据库引擎
        
        Args:
            database_url: 数据库URL
            pool_name: 连接池名称
            
        Returns:
            数据库引擎
        """
        with self._lock:
            if pool_name in self.engines:
                return self.engines[pool_name]
            
            # 连接池配置
            pool_config = {
                "poolclass": QueuePool,
                "pool_size": 20,  # 连接池大小
                "max_overflow": 30,  # 最大溢出连接数
                "pool_timeout": 30,  # 获取连接超时时间
                "pool_recycle": 3600,  # 连接回收时间（1小时）
                "pool_pre_ping": True,  # 连接前ping检查
                "echo": False,  # 是否打印SQL
                "echo_pool": False,  # 是否打印连接池信息
                "connect_args": {
                    "check_same_thread": False,  # SQLite特定配置
                    "timeout": 20  # 连接超时
                }
            }
            
            # 根据数据库类型调整配置
            if "postgresql" in database_url:
                pool_config["connect_args"] = {
                    "connect_timeout": 10,
                    "server_settings": {
                        "application_name": "a2a_backend",
                        "jit": "off"  # 关闭JIT以提高小查询性能
                    }
                }
            elif "mysql" in database_url:
                pool_config["connect_args"] = {
                    "connect_timeout": 10,
                    "charset": "utf8mb4"
                }
            elif "sqlite" in database_url:
                # SQLite使用StaticPool
                pool_config["poolclass"] = StaticPool
                pool_config["pool_size"] = 1
                pool_config["max_overflow"] = 0
            
            # 创建引擎
            engine = create_engine(database_url, **pool_config)
            
            # 注册事件监听器
            self._register_engine_events(engine)
            
            self.engines[pool_name] = engine
            
            # 创建会话工厂
            self.session_factories[pool_name] = sessionmaker(
                bind=engine,
                autocommit=False,
                autoflush=False,
                expire_on_commit=False
            )
            
            logger.info(f"数据库引擎创建完成: {pool_name}")
            
            return engine
    
    def _register_engine_events(self, engine: Engine):
        """
        注册引擎事件监听器
        
        Args:
            engine: 数据库引擎
        """
        from sqlalchemy import event
        
        @event.listens_for(engine, "connect")
        def on_connect(dbapi_connection, connection_record):
            """连接建立事件"""
            self.connection_stats["total_connections"] += 1
            self.connection_stats["active_connections"] += 1
            logger.debug("数据库连接建立")
        
        @event.listens_for(engine, "close")
        def on_close(dbapi_connection, connection_record):
            """连接关闭事件"""
            self.connection_stats["active_connections"] -= 1
            logger.debug("数据库连接关闭")
        
        @event.listens_for(engine, "before_cursor_execute")
        def before_cursor_execute(conn, cursor, statement, parameters, context, executemany):
            """查询执行前事件"""
            context._query_start_time = time.time()
        
        @event.listens_for(engine, "after_cursor_execute")
        def after_cursor_execute(conn, cursor, statement, parameters, context, executemany):
            """查询执行后事件"""
            total_time = time.time() - context._query_start_time
            self.connection_stats["query_count"] += 1
            
            # 记录慢查询
            if total_time > 1.0:  # 超过1秒的查询
                self.connection_stats["slow_query_count"] += 1
                logger.warning(f"慢查询检测: {total_time:.3f}s, SQL: {statement[:100]}...")
    
    @asynccontextmanager
    async def get_session(self, pool_name: str = "default") -> AsyncGenerator[Session, None]:
        """
        获取数据库会话（异步上下文管理器）
        
        Args:
            pool_name: 连接池名称
            
        Yields:
            数据库会话
        """
        if pool_name not in self.session_factories:
            raise ValueError(f"连接池不存在: {pool_name}")
        
        session = self.session_factories[pool_name]()
        try:
            yield session
        except Exception as e:
            session.rollback()
            logger.error(f"数据库会话异常: {str(e)}")
            raise
        finally:
            session.close()
    
    def get_connection_stats(self) -> Dict[str, Any]:
        """
        获取连接统计信息
        
        Returns:
            连接统计信息
        """
        stats = self.connection_stats.copy()
        
        # 添加连接池状态
        for pool_name, engine in self.engines.items():
            pool = engine.pool
            stats[f"{pool_name}_pool_size"] = pool.size()
            stats[f"{pool_name}_checked_in"] = pool.checkedin()
            stats[f"{pool_name}_checked_out"] = pool.checkedout()
            stats[f"{pool_name}_overflow"] = pool.overflow()
        
        return stats
    
    async def optimize_database(self, pool_name: str = "default"):
        """
        优化数据库性能
        
        Args:
            pool_name: 连接池名称
        """
        if pool_name not in self.engines:
            return
        
        engine = self.engines[pool_name]
        
        try:
            # 执行数据库特定的优化
            if "postgresql" in str(engine.url):
                await self._optimize_postgresql(engine)
            elif "mysql" in str(engine.url):
                await self._optimize_mysql(engine)
            elif "sqlite" in str(engine.url):
                await self._optimize_sqlite(engine)
            
            logger.info(f"数据库优化完成: {pool_name}")
            
        except Exception as e:
            logger.error(f"数据库优化失败: {pool_name}, 错误: {str(e)}")
    
    async def _optimize_postgresql(self, engine: Engine):
        """优化PostgreSQL数据库"""
        async with self.get_session() as session:
            # 更新表统计信息
            await session.execute(text("ANALYZE;"))
            
            # 清理无用数据
            await session.execute(text("VACUUM ANALYZE;"))
            
            session.commit()
    
    async def _optimize_mysql(self, engine: Engine):
        """优化MySQL数据库"""
        async with self.get_session() as session:
            # 优化表
            tables = ["artifacts", "artifact_versions", "artifact_shares", "system_configs", "user_configs"]
            for table in tables:
                await session.execute(text(f"OPTIMIZE TABLE {table};"))
            
            session.commit()
    
    async def _optimize_sqlite(self, engine: Engine):
        """优化SQLite数据库"""
        async with self.get_session() as session:
            # 分析查询计划
            await session.execute(text("ANALYZE;"))
            
            # 清理数据库
            await session.execute(text("VACUUM;"))
            
            session.commit()


# ==================== 存储清理管理 ====================

class StorageCleanupManager:
    """
    存储清理管理器
    
    提供存储空间管理和清理功能
    """
    
    def __init__(self, storage_path: str = None):
        """初始化存储清理管理器"""
        settings = get_settings()
        self.storage_path = Path(storage_path or getattr(settings, 'STORAGE_PATH', 'storage'))
        self.cleanup_rules = {
            "temp_files_retention_days": 7,  # 临时文件保留天数
            "archived_files_retention_days": 90,  # 归档文件保留天数
            "orphaned_chunks_retention_days": 30,  # 孤立分块保留天数
            "max_storage_usage_percent": 85,  # 最大存储使用率
            "cleanup_batch_size": 1000  # 清理批次大小
        }
    
    async def cleanup_storage(self) -> Dict[str, Any]:
        """
        执行存储清理
        
        Returns:
            清理结果统计
        """
        cleanup_stats = {
            "temp_files_deleted": 0,
            "archived_files_deleted": 0,
            "orphaned_chunks_deleted": 0,
            "space_freed_bytes": 0,
            "errors": []
        }
        
        try:
            # 清理临时文件
            temp_stats = await self._cleanup_temp_files()
            cleanup_stats.update(temp_stats)
            
            # 清理归档文件
            archived_stats = await self._cleanup_archived_files()
            cleanup_stats["archived_files_deleted"] = archived_stats["deleted_count"]
            cleanup_stats["space_freed_bytes"] += archived_stats["space_freed"]
            
            # 清理孤立分块
            orphaned_stats = await self._cleanup_orphaned_chunks()
            cleanup_stats["orphaned_chunks_deleted"] = orphaned_stats["deleted_count"]
            cleanup_stats["space_freed_bytes"] += orphaned_stats["space_freed"]
            
            logger.info(f"存储清理完成: {cleanup_stats}")
            
        except Exception as e:
            logger.error(f"存储清理失败: {str(e)}")
            cleanup_stats["errors"].append(str(e))
        
        return cleanup_stats
    
    async def _cleanup_temp_files(self) -> Dict[str, Any]:
        """清理临时文件"""
        temp_path = self.storage_path / "temp"
        if not temp_path.exists():
            return {"temp_files_deleted": 0, "space_freed_bytes": 0}
        
        cutoff_time = datetime.now() - timedelta(days=self.cleanup_rules["temp_files_retention_days"])
        deleted_count = 0
        space_freed = 0
        
        for file_path in temp_path.rglob("*"):
            if file_path.is_file():
                file_mtime = datetime.fromtimestamp(file_path.stat().st_mtime)
                if file_mtime < cutoff_time:
                    try:
                        file_size = file_path.stat().st_size
                        file_path.unlink()
                        deleted_count += 1
                        space_freed += file_size
                    except Exception as e:
                        logger.warning(f"删除临时文件失败: {file_path}, 错误: {str(e)}")
        
        return {
            "temp_files_deleted": deleted_count,
            "space_freed_bytes": space_freed
        }
    
    async def _cleanup_archived_files(self) -> Dict[str, Any]:
        """清理归档文件"""
        # TODO: 实现归档文件清理逻辑
        # 需要查询数据库中的归档文件记录
        return {"deleted_count": 0, "space_freed": 0}
    
    async def _cleanup_orphaned_chunks(self) -> Dict[str, Any]:
        """清理孤立分块"""
        chunks_path = self.storage_path / "chunks"
        if not chunks_path.exists():
            return {"deleted_count": 0, "space_freed": 0}
        
        # TODO: 实现孤立分块清理逻辑
        # 需要检查分块是否还被引用
        return {"deleted_count": 0, "space_freed": 0}
    
    def get_storage_usage(self) -> Dict[str, Any]:
        """
        获取存储使用情况
        
        Returns:
            存储使用统计
        """
        try:
            total_size = 0
            file_count = 0
            
            for file_path in self.storage_path.rglob("*"):
                if file_path.is_file():
                    total_size += file_path.stat().st_size
                    file_count += 1
            
            # 获取磁盘使用情况
            disk_usage = os.statvfs(self.storage_path)
            disk_total = disk_usage.f_frsize * disk_usage.f_blocks
            disk_free = disk_usage.f_frsize * disk_usage.f_available
            disk_used = disk_total - disk_free
            
            return {
                "storage_path": str(self.storage_path),
                "total_files": file_count,
                "total_size_bytes": total_size,
                "total_size_mb": total_size / (1024 * 1024),
                "disk_total_bytes": disk_total,
                "disk_used_bytes": disk_used,
                "disk_free_bytes": disk_free,
                "disk_usage_percent": (disk_used / disk_total) * 100
            }
            
        except Exception as e:
            logger.error(f"获取存储使用情况失败: {str(e)}")
            return {}


# ==================== 全局实例 ====================

# 全局实例
_compression_manager = None
_chunk_storage_manager = None
_database_manager = None
_storage_cleanup_manager = None


async def init_storage_managers():
    """初始化所有存储管理器"""
    global _compression_manager, _chunk_storage_manager, _database_manager, _storage_cleanup_manager
    
    _compression_manager = CompressionManager()
    _chunk_storage_manager = ChunkStorageManager()
    _database_manager = OptimizedDatabaseManager()
    _storage_cleanup_manager = StorageCleanupManager()
    
    # 初始化数据库连接池
    await _database_manager.init_pool()


async def cleanup_storage_managers():
    """清理所有存储管理器"""
    global _compression_manager, _chunk_storage_manager, _database_manager, _storage_cleanup_manager
    
    if _storage_cleanup_manager:
        await _storage_cleanup_manager.cleanup_expired_files()
    
    if _database_manager:
        await _database_manager.close_pool()
    
    _compression_manager = None
    _chunk_storage_manager = None
    _database_manager = None
    _storage_cleanup_manager = None


# ==================== 便捷函数 ====================

def get_compression_manager() -> CompressionManager:
    """获取压缩管理器实例"""
    global _compression_manager
    if _compression_manager is None:
        _compression_manager = CompressionManager()
    return _compression_manager


def get_chunk_storage_manager() -> ChunkStorageManager:
    """获取分块存储管理器实例"""
    global _chunk_storage_manager
    if _chunk_storage_manager is None:
        _chunk_storage_manager = ChunkStorageManager()
    return _chunk_storage_manager


def get_database_manager() -> OptimizedDatabaseManager:
    """获取优化数据库管理器实例"""
    global _database_manager
    if _database_manager is None:
        _database_manager = OptimizedDatabaseManager()
    return _database_manager


def get_storage_cleanup_manager() -> StorageCleanupManager:
    """获取存储清理管理器实例"""
    global _storage_cleanup_manager
    if _storage_cleanup_manager is None:
        _storage_cleanup_manager = StorageCleanupManager()
    return _storage_cleanup_manager