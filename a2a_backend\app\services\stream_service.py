# -*- coding: utf-8 -*-
"""
A2A多智能体系统流式输出服务

基于Google ADK实现流式输出和实时通信功能，不涉及额外的数据持久化
"""

import asyncio
import json
import time
import uuid
import gzip
import zlib
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Any, AsyncGenerator, Callable, Union
from enum import Enum
from dataclasses import dataclass, field
from collections import defaultdict, deque
from contextlib import asynccontextmanager

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete
from fastapi import HTTPException

from ..core.database import get_db
from ..models.user import User
from ..models.session import Session
from .auth_service import AuthService
from .llm_service import LLMService


class StreamStatus(str, Enum):
    """流式连接状态枚举"""
    CONNECTING = "connecting"
    CONNECTED = "connected"
    STREAMING = "streaming"
    PAUSED = "paused"
    DISCONNECTED = "disconnected"
    ERROR = "error"


class StreamType(str, Enum):
    """流式数据类型枚举"""
    TEXT = "text"
    JSON = "json"
    BINARY = "binary"
    EVENT = "event"
    HEARTBEAT = "heartbeat"
    ERROR = "error"


class CompressionType(str, Enum):
    """压缩类型枚举"""
    NONE = "none"
    GZIP = "gzip"
    DEFLATE = "deflate"
    BROTLI = "brotli"


@dataclass
class StreamConfig:
    """流式配置数据类"""
    user_id: int
    session_id: Optional[str] = None
    agent_id: Optional[str] = None
    buffer_size: int = 1024
    max_buffer_size: int = 10240
    compression: CompressionType = CompressionType.NONE
    heartbeat_interval: int = 30  # 秒
    timeout: int = 300  # 秒
    auto_reconnect: bool = True
    max_reconnect_attempts: int = 3
    reconnect_delay: float = 1.0
    enable_persistence: bool = True
    chunk_size: int = 1024
    rate_limit: int = 1000  # 每分钟消息数
    priority: int = 1
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class StreamChunk:
    """流式数据块"""
    chunk_id: str
    stream_id: str
    sequence: int
    data: Any
    chunk_type: StreamType
    timestamp: datetime
    size: int
    checksum: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "chunk_id": self.chunk_id,
            "stream_id": self.stream_id,
            "sequence": self.sequence,
            "data": self.data,
            "type": self.chunk_type.value,
            "timestamp": self.timestamp.isoformat(),
            "size": self.size,
            "checksum": self.checksum,
            "metadata": self.metadata
        }


@dataclass
class StreamConnection:
    """流式连接信息"""
    connection_id: str
    user_id: int
    session_id: Optional[str]
    agent_id: Optional[str]
    status: StreamStatus
    config: StreamConfig
    created_at: datetime
    last_activity: datetime
    total_chunks: int = 0
    total_bytes: int = 0
    error_count: int = 0
    reconnect_count: int = 0
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def update_activity(self):
        """更新活动时间"""
        self.last_activity = datetime.utcnow()
    
    def is_expired(self, timeout: int) -> bool:
        """检查连接是否过期"""
        return (datetime.utcnow() - self.last_activity).total_seconds() > timeout


class StreamBuffer:
    """流式数据缓冲区"""
    
    def __init__(self, max_size: int = 10240, chunk_size: int = 1024):
        self.max_size = max_size
        self.chunk_size = chunk_size
        self.buffer = deque()
        self.total_size = 0
        self.sequence = 0
        self._lock = asyncio.Lock()
    
    async def add_chunk(self, chunk: StreamChunk) -> bool:
        """添加数据块"""
        async with self._lock:
            # 检查缓冲区大小
            if self.total_size + chunk.size > self.max_size:
                # 移除最旧的数据块
                while self.buffer and self.total_size + chunk.size > self.max_size:
                    old_chunk = self.buffer.popleft()
                    self.total_size -= old_chunk.size
            
            # 添加新数据块
            chunk.sequence = self.sequence
            self.sequence += 1
            self.buffer.append(chunk)
            self.total_size += chunk.size
            return True
    
    async def get_chunks(self, count: Optional[int] = None) -> List[StreamChunk]:
        """获取数据块"""
        async with self._lock:
            if count is None:
                chunks = list(self.buffer)
                self.buffer.clear()
                self.total_size = 0
            else:
                chunks = []
                for _ in range(min(count, len(self.buffer))):
                    chunk = self.buffer.popleft()
                    chunks.append(chunk)
                    self.total_size -= chunk.size
            return chunks
    
    async def peek_chunks(self, count: int = 1) -> List[StreamChunk]:
        """查看数据块（不移除）"""
        async with self._lock:
            return list(self.buffer)[:count]
    
    def size(self) -> int:
        """获取缓冲区大小"""
        return len(self.buffer)
    
    def is_empty(self) -> bool:
        """检查缓冲区是否为空"""
        return len(self.buffer) == 0
    
    def is_full(self) -> bool:
        """检查缓冲区是否已满"""
        return self.total_size >= self.max_size


class StreamService:
    """
    流式输出服务
    
    基于Google ADK实现流式LLM连接、数据缓冲、格式化转换和连接管理
    支持用户权限验证、会话管理和性能监控，不涉及额外的数据持久化
    """
    
    def __init__(self):
        """初始化流式输出服务"""
        # 连接管理
        self.connections: Dict[str, StreamConnection] = {}
        self.user_connections: Dict[int, List[str]] = defaultdict(list)
        self.session_connections: Dict[str, List[str]] = defaultdict(list)
        
        # 缓冲区管理
        self.buffers: Dict[str, StreamBuffer] = {}
        
        # 回调函数
        self.data_callbacks: Dict[str, List[Callable]] = defaultdict(list)
        self.status_callbacks: Dict[str, List[Callable]] = defaultdict(list)
        
        # 配置管理
        self.default_config = StreamConfig(user_id=0)
        self.user_configs: Dict[int, StreamConfig] = {}
        
        # 故障转移配置
        self.failover_config = {
            'max_retries': 3,
            'retry_delay': 5,
            'circuit_breaker_threshold': 10,
            'circuit_breaker_timeout': 60
        }
        
        # 速率限制
        self.rate_limits = {
            'connections_per_user': 10,
            'messages_per_minute': 100,
            'data_per_minute_mb': 50
        }
        
        # 性能监控
        self.metrics = {
            "total_connections": 0,
            "active_connections": 0,
            "total_chunks": 0,
            "total_bytes": 0,
            "error_count": 0,
            "reconnect_count": 0
        }
        
        # 服务依赖
        self.llm_service = LLMService()
        self.auth_service = AuthService()
        
        # 后台任务
        self._cleanup_task = None
        self._heartbeat_task = None
        self._monitoring_task = None
    
    def _start_background_tasks(self):
        """启动后台任务"""
        self._cleanup_task = asyncio.create_task(self._cleanup_connections())
        self._heartbeat_task = asyncio.create_task(self._heartbeat_monitor())
    
    async def stop(self):
        """停止流式服务"""
        try:
            # 停止后台任务
            if self._cleanup_task:
                self._cleanup_task.cancel()
            if self._heartbeat_task:
                self._heartbeat_task.cancel()
            
            # 关闭所有连接
            await self._close_all_connections()
            
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"停止流式服务失败: {str(e)}")
    
    async def create_stream(
        self,
        session_id: str,
        user_id: int,
        stream_type: StreamType = StreamType.TEXT,
        config: Optional[StreamConfig] = None
    ) -> str:
        """
        创建流式连接
        
        Args:
            session_id: 会话ID
            user_id: 用户ID
            stream_type: 流式数据类型
            config: 流式配置
            
        Returns:
            连接ID
        """
        try:
            # 验证用户权限
            if not await self._check_user_permission(user_id, session_id):
                raise HTTPException(status_code=403, detail="用户无权限创建流式连接")
            
            # 检查连接数限制
            user_connection_count = len(self.user_connections.get(user_id, []))
            if user_connection_count >= self.rate_limits['connections_per_user']:
                raise HTTPException(status_code=429, detail="用户连接数超过限制")
            
            # 验证会话存在性
            if not await self._validate_session(session_id, user_id):
                raise HTTPException(status_code=404, detail="会话不存在或无权限访问")
            
            # 生成连接ID
            connection_id = f"stream_{uuid.uuid4().hex}"
            
            # 获取用户配置
            final_config = config or self.user_configs.get(user_id, self.default_config)
            
            # 创建连接对象
            connection = StreamConnection(
                connection_id=connection_id,
                user_id=user_id,
                session_id=session_id,
                agent_id=final_config.agent_id,
                status=StreamStatus.CONNECTED,
                config=final_config,
                created_at=datetime.utcnow(),
                last_activity=datetime.utcnow()
            )
            
            # 创建缓冲区
            buffer = StreamBuffer(
                max_size=final_config.max_buffer_size,
                chunk_size=final_config.chunk_size
            )
            
            # 存储连接和缓冲区
            self.connections[connection_id] = connection
            self.buffers[connection_id] = buffer
            
            # 更新索引
            self.user_connections[user_id].append(connection_id)
            self.session_connections[session_id].append(connection_id)
            
            # 更新指标
            self.metrics['total_connections'] += 1
            self.metrics['active_connections'] += 1
            
            return connection_id
            
        except Exception as e:
            self.metrics['error_count'] += 1
            raise HTTPException(status_code=500, detail=f"创建流式连接失败: {str(e)}")
    
    async def disconnect_stream(self, connection_id: str) -> bool:
        """
        断开流式连接
        
        Args:
            connection_id: 连接ID
        
        Returns:
            bool: 是否成功断开
        """
        try:
            connection = self.connections.get(connection_id)
            if not connection:
                return False
            
            # 更新连接状态
            await self._update_connection_status(connection_id, StreamStatus.DISCONNECTED)
            
            # 清理缓冲区
            if connection_id in self.buffers:
                del self.buffers[connection_id]
            
            # 清理回调函数
            if connection_id in self.data_callbacks:
                del self.data_callbacks[connection_id]
            if connection_id in self.status_callbacks:
                del self.status_callbacks[connection_id]
            
            # 更新索引
            user_id = connection.user_id
            if user_id in self.user_connections:
                if connection_id in self.user_connections[user_id]:
                    self.user_connections[user_id].remove(connection_id)
                if not self.user_connections[user_id]:
                    del self.user_connections[user_id]
            
            if connection.session_id and connection.session_id in self.session_connections:
                if connection_id in self.session_connections[connection.session_id]:
                    self.session_connections[connection.session_id].remove(connection_id)
                if not self.session_connections[connection.session_id]:
                    del self.session_connections[connection.session_id]
            
            # 删除连接
            del self.connections[connection_id]
            
            # 更新指标
            self.metrics["active_connections"] -= 1
            
            return True
            
        except Exception as e:
            return False
    
    async def send_chunk(
        self,
        connection_id: str,
        data: Any,
        chunk_type: StreamType = StreamType.TEXT,
        metadata: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        发送数据块
        
        Args:
            connection_id: 连接ID
            data: 数据内容
            chunk_type: 数据类型
            metadata: 元数据
        
        Returns:
            bool: 是否发送成功
        """
        try:
            connection = self.connections.get(connection_id)
            if not connection or connection.status not in [StreamStatus.CONNECTED, StreamStatus.STREAMING]:
                return False
            
            buffer = self.buffers.get(connection_id)
            if not buffer:
                return False
            
            # 创建数据块
            chunk = StreamChunk(
                chunk_id=str(uuid.uuid4()),
                stream_id=connection_id,
                sequence=0,  # 将在缓冲区中设置
                data=data,
                chunk_type=chunk_type,
                timestamp=datetime.utcnow(),
                size=len(str(data).encode('utf-8')),
                metadata=metadata or {}
            )
            
            # 添加到缓冲区
            success = await buffer.add_chunk(chunk)
            if success:
                # 更新连接统计
                connection.total_chunks += 1
                connection.total_bytes += chunk.size
                connection.update_activity()
                
                # 更新连接状态为流式传输
                connection.status = StreamStatus.STREAMING
                
                # 更新全局指标
                self.metrics["total_chunks"] += 1
                self.metrics["total_bytes"] += chunk.size
                
                # 触发数据回调
                await self._trigger_data_callbacks(connection_id, chunk)
                
                # 持久化数据（如果启用）
                if connection.config.enable_persistence:
                    await self._persist_chunk(chunk)
                
                return True
            
            return False
            
        except Exception as e:
            await self._handle_connection_error(connection_id, str(e))
            return False
    
    async def get_chunks(
        self,
        connection_id: str,
        count: Optional[int] = None
    ) -> List[StreamChunk]:
        """
        获取数据块
        
        Args:
            connection_id: 连接ID
            count: 获取数量
        
        Returns:
            List[StreamChunk]: 数据块列表
        """
        try:
            buffer = self.buffers.get(connection_id)
            if not buffer:
                return []
            
            chunks = await buffer.get_chunks(count)
            
            # 更新连接活动时间
            connection = self.connections.get(connection_id)
            if connection:
                connection.update_activity()
            
            return chunks
            
        except Exception as e:
            self.logger.error(f"获取数据块失败: {e}")
            return []
    
    async def stream_generator(
        self,
        connection_id: str,
        format_func: Optional[Callable[[StreamChunk], str]] = None
    ) -> AsyncGenerator[str, None]:
        """
        流式数据生成器
        
        Args:
            connection_id: 连接ID
            format_func: 数据格式化函数
        
        Yields:
            str: 格式化的数据
        """
        try:
            connection = self.connections.get(connection_id)
            if not connection:
                return
            
            buffer = self.buffers.get(connection_id)
            if not buffer:
                return
            
            # 默认格式化函数
            if format_func is None:
                format_func = lambda chunk: json.dumps(chunk.to_dict())
            
            while connection.status in [StreamStatus.CONNECTED, StreamStatus.STREAMING]:
                try:
                    # 获取数据块
                    chunks = await buffer.get_chunks()
                    
                    for chunk in chunks:
                        # 格式化数据
                        formatted_data = format_func(chunk)
                        yield formatted_data
                    
                    # 如果没有数据，等待一段时间
                    if not chunks:
                        await asyncio.sleep(0.1)
                    
                    # 更新连接活动时间
                    connection.update_activity()
                    
                except asyncio.CancelledError:
                    break
                except Exception as e:
                    self.logger.error(f"流式生成器错误: {e}")
                    await self._handle_connection_error(connection_id, str(e))
                    break
            
        except Exception as e:
            self.logger.error(f"流式生成器失败: {e}")
    
    async def register_data_callback(
        self,
        connection_id: str,
        callback: Callable[[StreamChunk], None]
    ):
        """
        注册数据回调函数
        
        Args:
            connection_id: 连接ID
            callback: 回调函数
        """
        self.data_callbacks[connection_id].append(callback)
    
    async def register_status_callback(
        self,
        connection_id: str,
        callback: Callable[[str, StreamStatus], None]
    ):
        """
        注册状态回调函数
        
        Args:
            connection_id: 连接ID
            callback: 回调函数
        """
        self.status_callbacks[connection_id].append(callback)
    
    async def get_connection_info(self, connection_id: str) -> Optional[StreamConnection]:
        """
        获取连接信息
        
        Args:
            connection_id: 连接ID
        
        Returns:
            Optional[StreamConnection]: 连接信息
        """
        return self.connections.get(connection_id)
    
    async def get_user_connections(self, user_id: int) -> List[StreamConnection]:
        """
        获取用户的所有连接
        
        Args:
            user_id: 用户ID
        
        Returns:
            List[StreamConnection]: 连接列表
        """
        connection_ids = self.user_connections.get(user_id, [])
        return [self.connections[cid] for cid in connection_ids if cid in self.connections]
    
    async def get_session_connections(self, session_id: str) -> List[StreamConnection]:
        """
        获取会话的所有连接
        
        Args:
            session_id: 会话ID
        
        Returns:
            List[StreamConnection]: 连接列表
        """
        connection_ids = self.session_connections.get(session_id, [])
        return [self.connections[cid] for cid in connection_ids if cid in self.connections]
    
    async def get_metrics(self) -> Dict[str, Any]:
        """
        获取性能指标
        
        Returns:
            Dict[str, Any]: 性能指标
        """
        return {
            **self.metrics,
            "connection_count_by_status": self._get_connection_count_by_status(),
            "buffer_usage": self._get_buffer_usage(),
            "timestamp": datetime.utcnow().isoformat()
        }
    
    # 私有方法
    
    async def _check_user_permission(
        self,
        user_id: int,
        session_id: str
    ) -> bool:
        """
        检查用户权限
        
        Args:
            user_id: 用户ID
            session_id: 会话ID
            
        Returns:
            bool: 是否有权限
        """
        try:
            # 使用认证服务验证权限
            return await self.auth_service.check_session_permission(user_id, session_id)
        except Exception:
            return False
    
    async def _validate_session(
        self,
        session_id: str,
        user_id: int
    ) -> bool:
        """
        验证会话存在性和权限
        
        Args:
            session_id: 会话ID
            user_id: 用户ID
            
        Returns:
            bool: 会话是否有效
        """
        try:
            async with get_db() as db:
                result = await db.execute(
                    select(Session).where(
                        Session.session_id == session_id,
                        Session.user_id == user_id,
                        Session.status == 'active'
                    )
                )
                session = result.scalar_one_or_none()
                return session is not None
        except Exception:
            return False
    
    async def _compress_data(
        self,
        data: Any,
        compression_type: CompressionType
    ) -> Any:
        """
        压缩数据
        
        Args:
            data: 原始数据
            compression_type: 压缩类型
            
        Returns:
            Any: 压缩后的数据
        """
        if compression_type == CompressionType.NONE:
            return data
        
        if isinstance(data, str):
            data = data.encode('utf-8')
        elif not isinstance(data, bytes):
            data = json.dumps(data).encode('utf-8')
        
        if compression_type == CompressionType.GZIP:
            return gzip.compress(data)
        elif compression_type == CompressionType.ZLIB:
            return zlib.compress(data)
        
        return data
    
    async def _cleanup_connections(self):
        """
        清理过期连接的后台任务
        """
        while True:
            try:
                current_time = datetime.now()
                expired_connections = []
                
                for connection_id, connection in self.connections.items():
                    # 检查连接是否超时
                    if (current_time - connection.last_activity).total_seconds() > connection.config.timeout:
                        expired_connections.append(connection_id)
                
                # 清理过期连接
                for connection_id in expired_connections:
                    await self.disconnect_stream(connection_id)
                
                await asyncio.sleep(60)  # 每分钟检查一次
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                await asyncio.sleep(60)
    
    async def _heartbeat_monitor(self):
        """
        心跳监控后台任务
        """
        while True:
            try:
                current_time = datetime.now()
                
                for connection in self.connections.values():
                    if connection.status == StreamStatus.CONNECTED:
                        # 发送心跳数据
                        await self.send_chunk(
                            connection.connection_id,
                            {"type": "heartbeat", "timestamp": current_time.isoformat()},
                            StreamType.JSON,
                            {"is_heartbeat": True}
                        )
                
                await asyncio.sleep(30)  # 每30秒发送心跳
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                await asyncio.sleep(30)
    
    async def _close_all_connections(self):
        """
        关闭所有连接
        """
        connection_ids = list(self.connections.keys())
        for connection_id in connection_ids:
            await self.disconnect_stream(connection_id)
    
    def _get_connection_count_by_status(self) -> Dict[str, int]:
        """
        获取按状态分组的连接数
        """
        status_counts = defaultdict(int)
        for connection in self.connections.values():
            status_counts[connection.status.value] += 1
        return dict(status_counts)
    
    def _get_buffer_usage(self) -> Dict[str, Any]:
        """
        获取缓冲区使用情况
        """
        total_buffer_size = 0
        max_buffer_size = 0
        
        for connection in self.connections.values():
            buffer_size = len(connection.buffer)
            total_buffer_size += buffer_size
            max_buffer_size = max(max_buffer_size, buffer_size)
        
        return {
            "total_buffer_size": total_buffer_size,
            "max_buffer_size": max_buffer_size,
            "avg_buffer_size": total_buffer_size / len(self.connections) if self.connections else 0
        }
    
    async def _old_check_stream_permission(
        self,
        user_id: int,
        config: StreamConfig,
        db: Session
    ) -> bool:
        """
        检查流式权限
        
        Args:
            user_id: 用户ID
            config: 流式配置
            db: 数据库会话
        
        Returns:
            bool: 是否有权限
        """
        try:
            # 检查基本流式权限
            if not await check_user_permission(user_id, "stream", "create", db):
                return False
            
            # 检查会话权限（如果指定了会话）
            if config.session_id:
                session = db.query(SessionModel).filter(
                    SessionModel.session_id == config.session_id
                ).first()
                if not session or session.user_id != user_id:
                    return False
            
            # 检查智能体权限（如果指定了智能体）
            if config.agent_id:
                agent = db.query(Agent).filter(
                    Agent.agent_id == config.agent_id
                ).first()
                if not agent:
                    return False
                
                # 检查智能体访问权限
                if not agent.is_public and agent.owner_id != user_id:
                    return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"检查流式权限失败: {e}")
            return False
    
    async def _get_user_max_connections(self, user_id: int, db: Session) -> int:
        """
        获取用户最大连接数
        
        Args:
            user_id: 用户ID
            db: 数据库会话
        
        Returns:
            int: 最大连接数
        """
        try:
            # 从用户配置获取
            user_config = await self.user_service.get_user_config(
                user_id, "stream_max_connections", db
            )
            if user_config:
                return int(user_config.get("value", 10))
            
            # 默认值
            return 10
            
        except Exception as e:
            self.logger.error(f"获取用户最大连接数失败: {e}")
            return 10
    
    async def _update_connection_status(
        self,
        connection_id: str,
        status: StreamStatus
    ):
        """
        更新连接状态
        
        Args:
            connection_id: 连接ID
            status: 新状态
        """
        try:
            connection = self.connections.get(connection_id)
            if not connection:
                return
            
            old_status = connection.status
            connection.status = status
            connection.update_activity()
            
            # 触发状态回调
            await self._trigger_status_callbacks(connection_id, status)
            
            self.logger.debug(f"连接状态更新: {connection_id} {old_status} -> {status}")
            
        except Exception as e:
            self.logger.error(f"更新连接状态失败: {e}")
    
    async def _trigger_data_callbacks(self, connection_id: str, chunk: StreamChunk):
        """
        触发数据回调
        
        Args:
            connection_id: 连接ID
            chunk: 数据块
        """
        try:
            callbacks = self.data_callbacks.get(connection_id, [])
            for callback in callbacks:
                try:
                    if asyncio.iscoroutinefunction(callback):
                        await callback(chunk)
                    else:
                        callback(chunk)
                except Exception as e:
                    self.logger.error(f"数据回调执行失败: {e}")
                    
        except Exception as e:
            self.logger.error(f"触发数据回调失败: {e}")
    
    async def _trigger_status_callbacks(
        self,
        connection_id: str,
        status: StreamStatus
    ):
        """
        触发状态回调
        
        Args:
            connection_id: 连接ID
            status: 状态
        """
        try:
            callbacks = self.status_callbacks.get(connection_id, [])
            for callback in callbacks:
                try:
                    if asyncio.iscoroutinefunction(callback):
                        await callback(connection_id, status)
                    else:
                        callback(connection_id, status)
                except Exception as e:
                    self.logger.error(f"状态回调执行失败: {e}")
                    
        except Exception as e:
            self.logger.error(f"触发状态回调失败: {e}")
    
    async def _handle_connection_error(self, connection_id: str, error: str):
        """
        处理连接错误
        
        Args:
            connection_id: 连接ID
            error: 错误信息
        """
        try:
            connection = self.connections.get(connection_id)
            if not connection:
                return
            
            connection.error_count += 1
            self.metrics["error_count"] += 1
            
            # 更新连接状态
            await self._update_connection_status(connection_id, StreamStatus.ERROR)
            
            # 如果启用自动重连
            if connection.config.auto_reconnect and connection.reconnect_count < connection.config.max_reconnect_attempts:
                await self._attempt_reconnect(connection_id)
            
            self.logger.error(f"连接错误: {connection_id} - {error}")
            
        except Exception as e:
            self.logger.error(f"处理连接错误失败: {e}")
    
    async def _attempt_reconnect(self, connection_id: str):
        """
        尝试重连
        
        Args:
            connection_id: 连接ID
        """
        try:
            connection = self.connections.get(connection_id)
            if not connection:
                return
            
            # 等待重连延迟
            await asyncio.sleep(connection.config.reconnect_delay)
            
            # 更新重连计数
            connection.reconnect_count += 1
            self.metrics["reconnect_count"] += 1
            
            # 更新连接状态
            await self._update_connection_status(connection_id, StreamStatus.CONNECTING)
            
            # 模拟重连成功
            await asyncio.sleep(0.1)
            await self._update_connection_status(connection_id, StreamStatus.CONNECTED)
            
            self.logger.info(f"重连成功: {connection_id} (第{connection.reconnect_count}次)")
            
        except Exception as e:
            self.logger.error(f"重连失败: {e}")
            await self._update_connection_status(connection_id, StreamStatus.ERROR)
    
    async def _persist_chunk(self, chunk: StreamChunk):
        """
        持久化数据块
        
        Args:
            chunk: 数据块
        """
        try:
            # 这里可以实现数据持久化逻辑
            # 例如存储到数据库或文件系统
            pass
            
        except Exception as e:
            self.logger.error(f"持久化数据块失败: {e}")
    
    def _get_connection_count_by_status(self) -> Dict[str, int]:
        """
        获取按状态分组的连接数
        
        Returns:
            Dict[str, int]: 状态连接数
        """
        counts = defaultdict(int)
        for connection in self.connections.values():
            counts[connection.status.value] += 1
        return dict(counts)
    
    def _get_buffer_usage(self) -> Dict[str, Any]:
        """
        获取缓冲区使用情况
        
        Returns:
            Dict[str, Any]: 缓冲区使用情况
        """
        total_buffers = len(self.buffers)
        total_chunks = sum(buffer.size() for buffer in self.buffers.values())
        total_bytes = sum(buffer.total_size for buffer in self.buffers.values())
        
        return {
            "total_buffers": total_buffers,
            "total_chunks": total_chunks,
            "total_bytes": total_bytes,
            "average_chunks_per_buffer": total_chunks / max(total_buffers, 1),
            "average_bytes_per_buffer": total_bytes / max(total_buffers, 1)
        }
    
    async def _cleanup_expired_connections(self):
        """
        清理过期连接（后台任务）
        """
        while True:
            try:
                current_time = datetime.utcnow()
                expired_connections = []
                
                for connection_id, connection in self.connections.items():
                    if connection.is_expired(connection.config.timeout):
                        expired_connections.append(connection_id)
                
                # 清理过期连接
                for connection_id in expired_connections:
                    await self.disconnect_stream(connection_id)
                    self.logger.info(f"清理过期连接: {connection_id}")
                
                # 等待下次清理
                await asyncio.sleep(60)  # 每分钟清理一次
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"清理过期连接失败: {e}")
                await asyncio.sleep(60)
    
    async def _send_heartbeats(self):
        """
        发送心跳（后台任务）
        """
        while True:
            try:
                current_time = datetime.utcnow()
                
                for connection_id, connection in self.connections.items():
                    if connection.status == StreamStatus.CONNECTED:
                        # 检查是否需要发送心跳
                        time_since_activity = (current_time - connection.last_activity).total_seconds()
                        if time_since_activity >= connection.config.heartbeat_interval:
                            # 发送心跳
                            await self.send_chunk(
                                connection_id,
                                {"type": "heartbeat", "timestamp": current_time.isoformat()},
                                StreamType.HEARTBEAT
                            )
                
                # 等待下次心跳
                await asyncio.sleep(30)  # 每30秒检查一次
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"发送心跳失败: {e}")
                await asyncio.sleep(30)
    
    async def _monitor_performance(self):
        """
        性能监控（后台任务）
        """
        while True:
            try:
                # 更新活跃连接数
                active_count = sum(
                    1 for conn in self.connections.values()
                    if conn.status in [StreamStatus.CONNECTED, StreamStatus.STREAMING]
                )
                self.metrics["active_connections"] = active_count
                
                # 记录性能指标
                metrics = await self.get_metrics()
                self.logger.debug(f"流式服务性能指标: {metrics}")
                
                # 等待下次监控
                await asyncio.sleep(300)  # 每5分钟监控一次
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"性能监控失败: {e}")
                await asyncio.sleep(300)


# 全局流式服务实例
stream_service = StreamService()