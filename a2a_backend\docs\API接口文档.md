# A2A多智能体系统 API接口文档

## 概述

A2A多智能体系统提供完整的RESTful API接口，支持用户认证、智能体管理、会话处理、任务执行、工作流管理等功能。

**基础URL**: `http://localhost:8000/api/v1`

**认证方式**: Bearer <PERSON> (JWT)

**内容类型**: `application/json`

## 目录

1. [认证接口](#认证接口)
2. [用户管理接口](#用户管理接口)
3. [智能体管理接口](#智能体管理接口)
4. [会话管理接口](#会话管理接口)
5. [消息管理接口](#消息管理接口)
6. [任务管理接口](#任务管理接口)
7. [工作流管理接口](#工作流管理接口)
8. [流式输出接口](#流式输出接口)
9. [WebSocket接口](#websocket接口)
10. [配置管理接口](#配置管理接口)
11. [工件管理接口](#工件管理接口)
12. [监控管理接口](#监控管理接口)

---

## 认证接口

### 1. 用户注册

**接口地址**: `POST /auth/register`

**功能描述**: 注册新用户账户

**请求参数**:
```json
{
  "username": "string",        // 用户名（3-50字符，只能包含字母、数字、下划线和连字符）
  "email": "string",          // 邮箱地址
  "password": "string",       // 密码（8-128字符，必须包含大小写字母、数字和特殊字符）
  "full_name": "string",      // 全名（可选）
  "phone": "string",          // 手机号（可选）
  "invite_code": "string"     // 邀请码（可选）
}
```

**响应格式**:
```json
{
  "success": true,
  "message": "注册成功，请查收邮箱验证邮件",
  "data": {
    "user_id": 123,
    "username": "testuser",
    "email": "<EMAIL>",
    "verification_required": true
  }
}
```

**错误响应**:
- `400 Bad Request`: 参数验证失败或用户名/邮箱已存在
- `429 Too Many Requests`: 注册频率限制（每小时最多5次）

### 2. 用户登录

**接口地址**: `POST /auth/login`

**功能描述**: 用户登录获取访问令牌

**请求参数**:
```json
{
  "username": "string",       // 用户名或邮箱
  "password": "string",       // 密码
  "remember_me": false,       // 是否记住登录状态
  "device_info": {}          // 设备信息（可选）
}
```

**响应格式**:
```json
{
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "token_type": "bearer",
  "expires_in": 1800,
  "user_info": {
    "user_id": 123,
    "username": "testuser",
    "email": "<EMAIL>",
    "full_name": "Test User",
    "role": "user",
    "avatar_url": null,
    "is_verified": true,
    "last_login_at": "2024-01-01T12:00:00Z"
  }
}
```

**错误响应**:
- `401 Unauthorized`: 用户名或密码错误
- `429 Too Many Requests`: 登录频率限制（每小时最多10次）

### 3. 刷新令牌

**接口地址**: `POST /auth/refresh`

**功能描述**: 使用刷新令牌获取新的访问令牌

**请求参数**:
```json
{
  "refresh_token": "string"   // 刷新令牌
}
```

**响应格式**:
```json
{
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "token_type": "bearer",
  "expires_in": 1800,
  "user_info": {
    "user_id": 123,
    "username": "testuser",
    "email": "<EMAIL>",
    "role": "user"
  }
}
```

### 4. 用户登出

**接口地址**: `POST /auth/logout`

**功能描述**: 用户登出，撤销当前令牌

**请求头**: `Authorization: Bearer <access_token>`

**响应格式**:
```json
{
  "success": true,
  "message": "登出成功"
}
```

### 5. 密码重置请求

**接口地址**: `POST /auth/password/reset`

**功能描述**: 请求密码重置，发送重置链接到邮箱

**请求参数**:
```json
{
  "email": "string"           // 邮箱地址
}
```

**响应格式**:
```json
{
  "success": true,
  "message": "如果邮箱存在，重置链接已发送到您的邮箱",
  "data": {
    "email": "<EMAIL>",
    "expires_in": 3600
  }
}
```

### 6. 密码重置确认

**接口地址**: `POST /auth/password/reset/confirm`

**功能描述**: 确认密码重置，设置新密码

**请求参数**:
```json
{
  "token": "string",          // 重置令牌
  "new_password": "string"    // 新密码（8-128字符，必须包含大小写字母、数字和特殊字符）
}
```

**响应格式**:
```json
{
  "success": true,
  "message": "密码重置成功，请使用新密码登录",
  "data": {
    "user_id": 123,
    "reset_at": "2024-01-01T12:00:00Z"
  }
}
```

### 7. 修改密码

**接口地址**: `POST /auth/password/change`

**功能描述**: 修改当前用户密码

**请求头**: `Authorization: Bearer <access_token>`

**请求参数**:
```json
{
  "current_password": "string",  // 当前密码
  "new_password": "string"       // 新密码（8-128字符，必须包含大小写字母、数字和特殊字符）
}
```

**响应格式**:
```json
{
  "success": true,
  "message": "密码修改成功",
  "data": {
    "user_id": 123,
    "changed_at": "2024-01-01T12:00:00Z"
  }
}
```

### 8. 验证令牌

**接口地址**: `GET /auth/verify`

**功能描述**: 验证访问令牌是否有效

**请求头**: `Authorization: Bearer <access_token>`

**响应格式**:
```json
{
  "success": true,
  "message": "令牌有效",
  "data": {
    "user_id": 123,
    "username": "testuser",
    "email": "<EMAIL>",
    "role": "user",
    "is_verified": true,
    "verified_at": "2024-01-01T12:00:00Z"
  }
}
```

### 9. 获取活跃会话

**接口地址**: `GET /auth/sessions`

**功能描述**: 获取用户的活跃会话列表

**请求头**: `Authorization: Bearer <access_token>`

**响应格式**:
```json
{
  "success": true,
  "message": "获取活跃会话成功",
  "data": {
    "user_id": 123,
    "sessions": [
      {
        "session_id": "abc123",
        "device_info": "Chrome on Windows",
        "ip_address": "*************",
        "created_at": "2024-01-01T12:00:00Z",
        "last_activity": "2024-01-01T13:00:00Z",
        "is_current": true
      }
    ],
    "total_sessions": 1
  }
}
```

### 10. 撤销会话

**接口地址**: `DELETE /auth/sessions/{session_id}`

**功能描述**: 撤销指定会话

**请求头**: `Authorization: Bearer <access_token>`

**路径参数**:
- `session_id`: 会话ID

**响应格式**:
```json
{
  "success": true,
  "message": "会话撤销成功",
  "data": {
    "session_id": "abc123",
    "revoked_at": "2024-01-01T12:00:00Z"
  }
}
```

---

## 用户管理接口

### 1. 获取当前用户信息

**接口地址**: `GET /users/me`

**功能描述**: 获取当前登录用户的详细信息

**请求头**: `Authorization: Bearer <access_token>`

**响应格式**:
```json
{
  "user_id": 123,
  "username": "testuser",
  "email": "<EMAIL>",
  "full_name": "Test User",
  "phone": "+1234567890",
  "avatar_url": "https://example.com/avatar.jpg",
  "role": "user",
  "is_verified": true,
  "is_locked": false,
  "timezone": "UTC",
  "language": "zh-CN",
  "preferences": {
    "theme": "light",
    "notifications": true
  },
  "created_at": "2024-01-01T12:00:00Z",
  "updated_at": "2024-01-01T12:00:00Z",
  "last_login_at": "2024-01-01T12:00:00Z"
}
```

### 2. 更新用户信息

**接口地址**: `PUT /users/me`

**功能描述**: 更新当前用户的个人信息

**请求头**: `Authorization: Bearer <access_token>`

**请求参数**:
```json
{
  "full_name": "string",      // 全名（可选）
  "phone": "string",          // 手机号（可选）
  "avatar_url": "string",     // 头像URL（可选）
  "timezone": "string",       // 时区（可选）
  "language": "string",       // 语言（可选）
  "preferences": {}           // 用户偏好设置（可选）
}
```

**响应格式**:
```json
{
  "success": true,
  "message": "用户信息更新成功",
  "data": {
    "user_id": 123,
    "updated_fields": ["full_name", "phone"],
    "updated_at": "2024-01-01T12:00:00Z"
  }
}
```

---

## 智能体管理接口

### 1. 创建智能体

**接口地址**: `POST /agents`

**功能描述**: 创建新的智能体

**请求头**: `Authorization: Bearer <access_token>`

**请求参数**:
```json
{
  "name": "string",           // 智能体名称（1-100字符）
  "description": "string",    // 智能体描述（可选，最多500字符）
  "agent_type": "string",     // 智能体类型：chat, task, workflow, tool, composite
  "config": {                 // 智能体配置
    "model": "gemini-pro",
    "temperature": 0.7,
    "max_tokens": 1000
  },
  "parent_id": 123,          // 父智能体ID（可选）
  "is_public": false,        // 是否公开
  "tags": ["tag1", "tag2"]   // 标签列表（可选）
}
```

**响应格式**:
```json
{
  "success": true,
  "message": "智能体创建成功",
  "data": {
    "agent_id": 456,
    "name": "My Agent",
    "agent_type": "chat",
    "status": "active",
    "created_at": "2024-01-01T12:00:00Z"
  }
}
```

### 2. 获取智能体列表

**接口地址**: `GET /agents`

**功能描述**: 获取用户的智能体列表

**请求头**: `Authorization: Bearer <access_token>`

**查询参数**:
- `page`: 页码（默认1）
- `size`: 每页数量（默认20）
- `agent_type`: 智能体类型过滤
- `status`: 状态过滤
- `search`: 搜索关键词
- `tags`: 标签过滤

**响应格式**:
```json
{
  "success": true,
  "message": "获取智能体列表成功",
  "data": {
    "agents": [
      {
        "agent_id": 456,
        "name": "My Agent",
        "description": "A helpful assistant",
        "agent_type": "chat",
        "status": "active",
        "is_public": false,
        "tags": ["assistant", "chat"],
        "created_at": "2024-01-01T12:00:00Z",
        "updated_at": "2024-01-01T12:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "size": 20,
      "total": 1,
      "pages": 1
    }
  }
}
```

### 3. 获取智能体详情

**接口地址**: `GET /agents/{agent_id}`

**功能描述**: 获取指定智能体的详细信息

**请求头**: `Authorization: Bearer <access_token>`

**路径参数**:
- `agent_id`: 智能体ID

**响应格式**:
```json
{
  "agent_id": 456,
  "name": "My Agent",
  "description": "A helpful assistant",
  "agent_type": "chat",
  "config": {
    "model": "gemini-pro",
    "temperature": 0.7,
    "max_tokens": 1000
  },
  "status": "active",
  "is_public": false,
  "tags": ["assistant", "chat"],
  "parent_id": null,
  "owner_id": 123,
  "created_at": "2024-01-01T12:00:00Z",
  "updated_at": "2024-01-01T12:00:00Z",
  "stats": {
    "total_executions": 100,
    "success_rate": 0.95,
    "avg_response_time": 1.5
  }
}
```

---

## 会话管理接口

### 1. 创建会话

**接口地址**: `POST /sessions`

**功能描述**: 创建新的会话

**请求头**: `Authorization: Bearer <access_token>`

**请求参数**:
```json
{
  "title": "string",          // 会话标题（1-200字符）
  "description": "string",    // 会话描述（可选，最多1000字符）
  "session_type": "string",   // 会话类型：chat, task, workflow, collaboration, debug
  "agent_ids": [456],        // 参与的智能体ID列表（可选）
  "participant_ids": [789],  // 参与的用户ID列表（可选）
  "config": {},              // 会话配置（可选）
  "is_private": true,        // 是否私有会话
  "tags": ["tag1"]           // 标签列表（可选）
}
```

**响应格式**:
```json
{
  "success": true,
  "message": "会话创建成功",
  "data": {
    "session_id": 789,
    "title": "My Session",
    "session_type": "chat",
    "status": "active",
    "created_at": "2024-01-01T12:00:00Z"
  }
}
```

### 2. 获取会话列表

**接口地址**: `GET /sessions`

**功能描述**: 获取用户的会话列表

**请求头**: `Authorization: Bearer <access_token>`

**查询参数**:
- `page`: 页码（默认1）
- `size`: 每页数量（默认20）
- `session_type`: 会话类型过滤
- `status`: 状态过滤
- `search`: 搜索关键词

**响应格式**:
```json
{
  "success": true,
  "message": "获取会话列表成功",
  "data": {
    "sessions": [
      {
        "session_id": 789,
        "title": "My Session",
        "description": "A chat session",
        "session_type": "chat",
        "status": "active",
        "is_private": true,
        "participant_count": 2,
        "message_count": 10,
        "created_at": "2024-01-01T12:00:00Z",
        "updated_at": "2024-01-01T13:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "size": 20,
      "total": 1,
      "pages": 1
    }
  }
}
```

---

*注：由于文档内容较长，这里只展示了部分接口。完整的API文档将包含所有模块的详细接口说明。*
