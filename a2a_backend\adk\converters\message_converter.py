#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A2A多智能体系统 消息转换器

ADK消息与数据库模型转换
"""

import logging
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
import json
import base64
import mimetypes

from google.ai.generativelanguage import GenerateContentRequest, GenerateContentResponse
from google.ai.generativelanguage import Content, Part, Blob
from google.protobuf.json_format import MessageToDict, ParseDict

from app.models.message import Message
from app.models.session import Session
from app.models.agent import Agent
from app.core.logging import get_logger

class MessageConverter:
    """
    消息转换器，ADK消息与数据库模型转换
    
    提供以下功能：
    1. ADK Content到数据库Message的转换
    2. 数据库Message到ADK Content的转换
    3. ADK Part到消息内容的转换
    4. 消息附件和媒体文件处理
    5. 消息格式验证和标准化
    6. 批量消息转换
    7. 消息历史记录管理
    8. 错误处理和日志记录
    """
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        """
        初始化消息转换器
        
        Args:
            logger: 日志记录器
        """
        self.logger = logger or get_logger("message_converter")
        
        # 支持的消息类型
        self.supported_message_types = {
            "text": "text/plain",
            "image": "image/*",
            "audio": "audio/*",
            "video": "video/*",
            "file": "application/*",
            "json": "application/json",
            "markdown": "text/markdown",
            "html": "text/html"
        }
        
        # 角色映射
        self.role_mapping = {
            "user": "user",
            "model": "assistant",
            "system": "system",
            "function": "function",
            "tool": "tool"
        }
        
        # 转换统计
        self.conversion_stats = {
            "total_conversions": 0,
            "successful_conversions": 0,
            "failed_conversions": 0,
            "message_types_processed": set(),
            "conversion_history": [],
            "last_conversion": None
        }
        
        self.logger.info("MessageConverter已初始化")
    
    def _format_timestamp(self, timestamp: Optional[datetime] = None) -> str:
        """
        格式化时间戳
        
        Args:
            timestamp: 时间戳
            
        Returns:
            str: 格式化的时间戳
        """
        if timestamp is None:
            timestamp = datetime.now()
        
        return timestamp.isoformat()
    
    def _detect_content_type(self, content: str) -> str:
        """
        检测内容类型
        
        Args:
            content: 内容
            
        Returns:
            str: 内容类型
        """
        try:
            # 尝试解析JSON
            json.loads(content)
            return "json"
        except:
            pass
        
        # 检查Markdown标记
        markdown_indicators = ["#", "*", "**", "`", "```", "[", "]("]
        if any(indicator in content for indicator in markdown_indicators):
            return "markdown"
        
        # 检查HTML标记
        html_indicators = ["<", ">", "</", "/>", "&lt;", "&gt;"]
        if any(indicator in content for indicator in html_indicators):
            return "html"
        
        # 默认为纯文本
        return "text"
    
    def _process_blob_data(self, blob_data: bytes, mime_type: str) -> Dict[str, Any]:
        """
        处理二进制数据
        
        Args:
            blob_data: 二进制数据
            mime_type: MIME类型
            
        Returns:
            Dict[str, Any]: 处理后的数据
        """
        try:
            # 编码为base64
            encoded_data = base64.b64encode(blob_data).decode('utf-8')
            
            # 获取文件扩展名
            extension = mimetypes.guess_extension(mime_type) or ""
            
            return {
                "data": encoded_data,
                "mime_type": mime_type,
                "extension": extension,
                "size": len(blob_data),
                "encoding": "base64"
            }
        except Exception as e:
            self.logger.error(f"处理二进制数据错误: {str(e)}")
            return {}
    
    def adk_content_to_message(
        self,
        content: Content,
        session_id: int,
        user_id: int,
        role: str = "user"
    ) -> Dict[str, Any]:
        """
        将ADK Content转换为数据库Message
        
        Args:
            content: ADK Content对象
            session_id: 会话ID
            user_id: 用户ID
            role: 角色
            
        Returns:
            Dict[str, Any]: 消息数据
        """
        try:
            message_data = {
                "session_id": session_id,
                "user_id": user_id,
                "role": self.role_mapping.get(role, role),
                "content": "",
                "message_type": "text",
                "metadata": {
                    "parts": [],
                    "original_role": role,
                    "conversion_timestamp": self._format_timestamp()
                }
            }
            
            # 处理Content的parts
            content_parts = []
            attachments = []
            
            for part in content.parts:
                part_data = {"type": "unknown"}
                
                # 处理文本部分
                if hasattr(part, 'text') and part.text:
                    part_data = {
                        "type": "text",
                        "content": part.text,
                        "content_type": self._detect_content_type(part.text)
                    }
                    content_parts.append(part.text)
                
                # 处理二进制数据部分
                elif hasattr(part, 'inline_data') and part.inline_data:
                    blob = part.inline_data
                    blob_info = self._process_blob_data(blob.data, blob.mime_type)
                    
                    part_data = {
                        "type": "blob",
                        "mime_type": blob.mime_type,
                        "data": blob_info.get("data"),
                        "size": blob_info.get("size"),
                        "encoding": blob_info.get("encoding")
                    }
                    
                    attachments.append({
                        "type": "inline_data",
                        "mime_type": blob.mime_type,
                        "size": blob_info.get("size"),
                        "data_preview": blob_info.get("data", "")[:100] + "..." if len(blob_info.get("data", "")) > 100 else blob_info.get("data", "")
                    })
                
                # 处理文件引用部分
                elif hasattr(part, 'file_data') and part.file_data:
                    file_data = part.file_data
                    part_data = {
                        "type": "file",
                        "mime_type": file_data.mime_type,
                        "file_uri": file_data.file_uri
                    }
                    
                    attachments.append({
                        "type": "file_data",
                        "mime_type": file_data.mime_type,
                        "file_uri": file_data.file_uri
                    })
                
                # 处理函数调用部分
                elif hasattr(part, 'function_call') and part.function_call:
                    function_call = part.function_call
                    part_data = {
                        "type": "function_call",
                        "name": function_call.name,
                        "args": MessageToDict(function_call.args) if function_call.args else {}
                    }
                
                # 处理函数响应部分
                elif hasattr(part, 'function_response') and part.function_response:
                    function_response = part.function_response
                    part_data = {
                        "type": "function_response",
                        "name": function_response.name,
                        "response": MessageToDict(function_response.response) if function_response.response else {}
                    }
                
                message_data["metadata"]["parts"].append(part_data)
            
            # 设置主要内容
            if content_parts:
                message_data["content"] = "\n".join(content_parts)
                message_data["message_type"] = self._detect_content_type(message_data["content"])
            elif attachments:
                message_data["content"] = f"[{len(attachments)} 个附件]"
                message_data["message_type"] = "attachment"
            
            # 添加附件信息
            if attachments:
                message_data["metadata"]["attachments"] = attachments
            
            return message_data
        except Exception as e:
            self.logger.error(f"转换ADK Content到消息错误: {str(e)}")
            raise e
    
    def message_to_adk_content(
        self,
        message: Message
    ) -> Content:
        """
        将数据库Message转换为ADK Content
        
        Args:
            message: 消息记录
            
        Returns:
            Content: ADK Content对象
        """
        try:
            parts = []
            
            # 处理主要文本内容
            if message.content and message.message_type in ["text", "markdown", "html", "json"]:
                text_part = Part(text=message.content)
                parts.append(text_part)
            
            # 处理元数据中的parts
            if message.metadata and "parts" in message.metadata:
                for part_data in message.metadata["parts"]:
                    part_type = part_data.get("type")
                    
                    if part_type == "text" and part_data.get("content"):
                        text_part = Part(text=part_data["content"])
                        parts.append(text_part)
                    
                    elif part_type == "blob" and part_data.get("data"):
                        try:
                            # 解码base64数据
                            blob_data = base64.b64decode(part_data["data"])
                            blob = Blob(
                                mime_type=part_data.get("mime_type", "application/octet-stream"),
                                data=blob_data
                            )
                            blob_part = Part(inline_data=blob)
                            parts.append(blob_part)
                        except Exception as e:
                            self.logger.error(f"处理blob数据错误: {str(e)}")
                    
                    elif part_type == "file" and part_data.get("file_uri"):
                        from google.ai.generativelanguage import FileData
                        file_data = FileData(
                            mime_type=part_data.get("mime_type", "application/octet-stream"),
                            file_uri=part_data["file_uri"]
                        )
                        file_part = Part(file_data=file_data)
                        parts.append(file_part)
                    
                    elif part_type == "function_call":
                        from google.ai.generativelanguage import FunctionCall
                        from google.protobuf.struct_pb2 import Struct
                        
                        args_struct = Struct()
                        if part_data.get("args"):
                            ParseDict(part_data["args"], args_struct)
                        
                        function_call = FunctionCall(
                            name=part_data.get("name", ""),
                            args=args_struct
                        )
                        function_part = Part(function_call=function_call)
                        parts.append(function_part)
                    
                    elif part_type == "function_response":
                        from google.ai.generativelanguage import FunctionResponse
                        from google.protobuf.struct_pb2 import Struct
                        
                        response_struct = Struct()
                        if part_data.get("response"):
                            ParseDict(part_data["response"], response_struct)
                        
                        function_response = FunctionResponse(
                            name=part_data.get("name", ""),
                            response=response_struct
                        )
                        response_part = Part(function_response=function_response)
                        parts.append(response_part)
            
            # 如果没有parts，创建一个默认的文本part
            if not parts and message.content:
                text_part = Part(text=message.content)
                parts.append(text_part)
            
            # 创建Content对象
            content = Content(parts=parts)
            
            return content
        except Exception as e:
            self.logger.error(f"转换消息到ADK Content错误: {str(e)}")
            raise e
    
    def conversation_to_adk_contents(
        self,
        conversation: Session,
        limit: Optional[int] = None
    ) -> List[Content]:
        """
        将对话转换为ADK Content列表
        
        Args:
            conversation: 会话记录
            limit: 消息数量限制
            
        Returns:
            List[Content]: ADK Content列表
        """
        try:
            contents = []
            
            # 获取消息列表
            messages = conversation.messages
            if limit:
                messages = messages[-limit:]  # 获取最近的消息
            
            for message in messages:
                try:
                    content = self.message_to_adk_content(message)
                    contents.append(content)
                except Exception as e:
                    self.logger.error(f"转换消息到Content错误: {str(e)}")
                    continue
            
            return contents
        except Exception as e:
            self.logger.error(f"转换对话到ADK Contents错误: {str(e)}")
            return []
    
    def adk_generate_request_to_messages(
        self,
        request: GenerateContentRequest,
        session_id: int,
        user_id: int
    ) -> List[Dict[str, Any]]:
        """
        将ADK生成请求转换为消息列表
        
        Args:
            request: ADK生成内容请求
            session_id: 会话ID
            user_id: 用户ID
            
        Returns:
            List[Dict[str, Any]]: 消息数据列表
        """
        try:
            messages = []
            
            # 处理请求中的contents
            for i, content in enumerate(request.contents):
                # 根据内容位置推断角色
                role = "user" if i % 2 == 0 else "assistant"
                
                message_data = self.adk_content_to_message(
                    content=content,
                    session_id=session_id,
                    user_id=user_id,
                    role=role
                )
                
                # 添加请求相关的元数据
                message_data["metadata"]["request_info"] = {
                    "model": request.model if hasattr(request, 'model') else None,
                    "generation_config": MessageToDict(request.generation_config) if hasattr(request, 'generation_config') and request.generation_config else None,
                    "safety_settings": [MessageToDict(setting) for setting in request.safety_settings] if hasattr(request, 'safety_settings') else [],
                    "tools": [MessageToDict(tool) for tool in request.tools] if hasattr(request, 'tools') else [],
                    "content_index": i
                }
                
                messages.append(message_data)
            
            return messages
        except Exception as e:
            self.logger.error(f"转换ADK生成请求到消息错误: {str(e)}")
            return []
    
    def adk_generate_response_to_message(
        self,
        response: GenerateContentResponse,
        session_id: int,
        user_id: int
    ) -> Dict[str, Any]:
        """
        将ADK生成响应转换为消息
        
        Args:
            response: ADK生成内容响应
            session_id: 会话ID
            user_id: 用户ID
            
        Returns:
            Dict[str, Any]: 消息数据
        """
        try:
            # 获取第一个候选响应
            if not response.candidates:
                raise ValueError("响应中没有候选内容")
            
            candidate = response.candidates[0]
            
            # 转换内容
            message_data = self.adk_content_to_message(
                content=candidate.content,
                session_id=session_id,
                user_id=user_id,
                role="assistant"
            )
            
            # 添加响应相关的元数据
            message_data["metadata"]["response_info"] = {
                "finish_reason": candidate.finish_reason if hasattr(candidate, 'finish_reason') else None,
                "safety_ratings": [MessageToDict(rating) for rating in candidate.safety_ratings] if hasattr(candidate, 'safety_ratings') else [],
                "citation_metadata": MessageToDict(candidate.citation_metadata) if hasattr(candidate, 'citation_metadata') and candidate.citation_metadata else None,
                "token_count": candidate.token_count if hasattr(candidate, 'token_count') else None
            }
            
            # 添加使用统计
            if hasattr(response, 'usage_metadata') and response.usage_metadata:
                message_data["metadata"]["usage_metadata"] = MessageToDict(response.usage_metadata)
            
            return message_data
        except Exception as e:
            self.logger.error(f"转换ADK生成响应到消息错误: {str(e)}")
            raise e
    
    def batch_convert_messages_to_contents(
        self,
        messages: List[Message]
    ) -> List[Content]:
        """
        批量转换消息为ADK Contents
        
        Args:
            messages: 消息列表
            
        Returns:
            List[Content]: ADK Content列表
        """
        try:
            contents = []
            
            for message in messages:
                try:
                    content = self.message_to_adk_content(message)
                    contents.append(content)
                    
                    # 更新统计
                    self.conversion_stats["successful_conversions"] += 1
                    self.conversion_stats["message_types_processed"].add(message.message_type)
                except Exception as e:
                    self.logger.error(f"批量转换消息错误: {str(e)}")
                    self.conversion_stats["failed_conversions"] += 1
            
            # 更新总体统计
            self.conversion_stats["total_conversions"] += len(messages)
            self.conversion_stats["last_conversion"] = self._format_timestamp()
            
            return contents
        except Exception as e:
            self.logger.error(f"批量转换消息到Contents错误: {str(e)}")
            return []
    
    def batch_convert_contents_to_messages(
        self,
        contents: List[Content],
        session_id: int,
        user_id: int,
        role_pattern: Optional[List[str]] = None
    ) -> List[Dict[str, Any]]:
        """
        批量转换ADK Contents为消息
        
        Args:
            contents: ADK Content列表
            session_id: 会话ID
            user_id: 用户ID
            role_pattern: 角色模式列表，如果提供则循环使用
            
        Returns:
            List[Dict[str, Any]]: 消息数据列表
        """
        try:
            messages = []
            default_roles = ["user", "assistant"]
            
            for i, content in enumerate(contents):
                try:
                    # 确定角色
                    if role_pattern:
                        role = role_pattern[i % len(role_pattern)]
                    else:
                        role = default_roles[i % len(default_roles)]
                    
                    message_data = self.adk_content_to_message(
                        content=content,
                        session_id=session_id,
                        user_id=user_id,
                        role=role
                    )
                    
                    messages.append(message_data)
                    
                    # 更新统计
                    self.conversion_stats["successful_conversions"] += 1
                except Exception as e:
                    self.logger.error(f"批量转换Content错误: {str(e)}")
                    self.conversion_stats["failed_conversions"] += 1
            
            # 更新总体统计
            self.conversion_stats["total_conversions"] += len(contents)
            self.conversion_stats["last_conversion"] = self._format_timestamp()
            
            return messages
        except Exception as e:
            self.logger.error(f"批量转换Contents到消息错误: {str(e)}")
            return []
    
    def validate_message_data(self, message_data: Dict[str, Any]) -> bool:
        """
        验证消息数据
        
        Args:
            message_data: 消息数据
            
        Returns:
            bool: 是否有效
        """
        try:
            # 检查必需字段
            required_fields = ["session_id", "user_id", "role", "content"]
            for field in required_fields:
                if field not in message_data:
                    self.logger.error(f"消息数据缺少必需字段: {field}")
                    return False
            
            # 检查角色有效性
            valid_roles = list(self.role_mapping.values())
            if message_data["role"] not in valid_roles:
                self.logger.error(f"无效的角色: {message_data['role']}")
                return False
            
            # 检查消息类型
            message_type = message_data.get("message_type", "text")
            if message_type not in self.supported_message_types:
                self.logger.warning(f"不支持的消息类型: {message_type}")
            
            return True
        except Exception as e:
            self.logger.error(f"验证消息数据错误: {str(e)}")
            return False
    
    def get_conversion_stats(self) -> Dict[str, Any]:
        """
        获取转换统计
        
        Returns:
            Dict[str, Any]: 转换统计信息
        """
        stats = self.conversion_stats.copy()
        stats["message_types_processed"] = list(stats["message_types_processed"])
        stats["success_rate"] = (
            stats["successful_conversions"] / stats["total_conversions"]
            if stats["total_conversions"] > 0 else 0
        )
        stats["supported_message_types"] = list(self.supported_message_types.keys())
        stats["supported_roles"] = list(self.role_mapping.keys())
        return stats
    
    def reset_stats(self) -> None:
        """
        重置转换统计
        """
        self.conversion_stats = {
            "total_conversions": 0,
            "successful_conversions": 0,
            "failed_conversions": 0,
            "message_types_processed": set(),
            "conversion_history": [],
            "last_conversion": None
        }
        
        self.logger.info("转换统计已重置")