#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A2A多智能体系统日志记录中间件

记录HTTP请求和响应的详细信息
"""

import time
import uuid
from typing import Optional

from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.middleware.base import RequestResponseEndpoint
from loguru import logger


class LoggingMiddleware(BaseHTTPMiddleware):
    """
    请求日志记录中间件
    
    记录所有HTTP请求的详细信息
    """
    
    def __init__(self, app, exclude_paths: Optional[list] = None):
        """
        初始化日志记录中间件
        
        Args:
            app: FastAPI应用实例
            exclude_paths: 排除记录的路径列表
        """
        super().__init__(app)
        self.exclude_paths = exclude_paths or ["/health", "/metrics", "/docs", "/redoc", "/openapi.json"]
    
    async def dispatch(self, request: Request, call_next: RequestResponseEndpoint) -> Response:
        """
        处理请求日志记录
        
        Args:
            request: HTTP请求
            call_next: 下一个中间件或路由处理器
            
        Returns:
            Response: HTTP响应
        """
        # 生成请求ID
        request_id = str(uuid.uuid4())
        request.state.request_id = request_id
        
        # 记录请求开始时间
        start_time = time.time()
        
        # 获取请求信息
        method = request.method
        url = str(request.url)
        path = request.url.path
        
        # 跳过排除的路径
        if path in self.exclude_paths:
            return await call_next(request)
        
        # 获取客户端信息
        client_ip = self._get_client_ip(request)
        user_agent = request.headers.get("user-agent", "")
        
        # 获取请求体（如果是POST/PUT/PATCH）
        request_body = None
        if method in ["POST", "PUT", "PATCH"] and request.headers.get("content-type", "").startswith("application/json"):
            try:
                body = await request.body()
                if body:
                    request_body = body.decode("utf-8")[:1000]  # 限制长度
            except Exception:
                request_body = "<无法读取请求体>"
        
        # 记录请求开始
        logger.info(
            f"请求开始: {method} {path}",
            extra={
                "request_id": request_id,
                "method": method,
                "url": url,
                "path": path,
                "client_ip": client_ip,
                "user_agent": user_agent,
                "request_body": request_body,
                "headers": dict(request.headers)
            }
        )
        
        # 处理请求
        try:
            response = await call_next(request)
            
            # 计算处理时间
            process_time = time.time() - start_time
            
            # 记录响应信息
            status_code = response.status_code
            
            # 记录请求完成
            log_level = "error" if status_code >= 400 else "info"
            getattr(logger, log_level)(
                f"请求完成: {method} {path} - {status_code} ({process_time:.3f}s)",
                extra={
                    "request_id": request_id,
                    "method": method,
                    "path": path,
                    "status_code": status_code,
                    "process_time": process_time,
                    "client_ip": client_ip
                }
            )
            
            # 添加响应头
            response.headers["X-Request-ID"] = request_id
            response.headers["X-Process-Time"] = str(process_time)
            
            return response
            
        except Exception as e:
            # 计算处理时间
            process_time = time.time() - start_time
            
            # 记录异常
            logger.error(
                f"请求异常: {method} {path} - {str(e)} ({process_time:.3f}s)",
                extra={
                    "request_id": request_id,
                    "method": method,
                    "path": path,
                    "error": str(e),
                    "process_time": process_time,
                    "client_ip": client_ip
                }
            )
            
            # 重新抛出异常
            raise
    
    def _get_client_ip(self, request: Request) -> str:
        """
        获取客户端IP地址
        
        Args:
            request: HTTP请求
            
        Returns:
            str: 客户端IP地址
        """
        # 检查代理头
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
        
        # 返回直接连接的IP
        return request.client.host if request.client else "unknown"