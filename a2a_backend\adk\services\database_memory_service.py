#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A2A多智能体系统 数据库内存服务

替代ADK内存存储，支持用户数据隔离
"""

import logging
import json
import pickle
import base64
from typing import Dict, List, Optional, Any, Union, Type
from datetime import datetime, timedelta
import uuid

from google.adk.memory.base_memory_service import BaseMemoryService
from google.adk.memory.memory_entry import MemoryEntry

from app.models.user import User
from app.models.memory import Memory as MemoryModel, MemoryEntry
from app.core.logging import get_logger
from app.auth.permissions import check_user_permission
from app.core.database import get_db

class DatabaseMemory:
    """
    数据库内存实现
    
    基于数据库存储的内存，支持持久化和用户隔离
    """
    
    def __init__(
        self,
        memory_id: str,
        user_id: int,
        owner_id: int,
        memory_model: MemoryModel,
        logger: Optional[logging.Logger] = None
    ):
        """
        初始化数据库内存
        
        Args:
            memory_id: 内存ID
            user_id: 用户ID
            owner_id: 拥有者ID
            memory_model: 内存模型
            logger: 日志记录器
        """
        super().__init__(memory_id)
        
        self.user_id = user_id
        self.owner_id = owner_id
        self.memory_model = memory_model
        self.logger = logger or get_logger(f"db_memory_{memory_id}")
        
        # 内存条目缓存
        self._entry_cache: Dict[str, MemoryEntry] = {}
        self._cache_dirty = False
        
        # 内存状态
        self._is_active = True
        self._last_access = datetime.now()
        
        self.logger.info(f"DatabaseMemory已初始化: {memory_id}")
    
    async def _load_entries(self) -> None:
        """
        从数据库加载内存条目
        """
        try:
            entries = await MemoryEntry.get_by_memory_id(self.memory_model.id)
            self._entry_cache = {entry.key: entry for entry in entries or []}
            self._cache_dirty = False
            
            self.logger.debug(f"已加载 {len(self._entry_cache)} 个内存条目")
        except Exception as e:
            self.logger.error(f"加载内存条目错误: {str(e)}")
            self._entry_cache = {}
    
    async def _save_entry(self, entry: MemoryEntry) -> None:
        """
        保存内存条目到数据库
        
        Args:
            entry: 内存条目
        """
        try:
            await entry.save()
            self._entry_cache[entry.key] = entry
            self._cache_dirty = True
            
            self.logger.debug(f"内存条目已保存: {entry.key}")
        except Exception as e:
            self.logger.error(f"保存内存条目错误: {str(e)}")
            raise e
    
    def _serialize_value(self, value: Any) -> str:
        """
        序列化值
        
        Args:
            value: 要序列化的值
            
        Returns:
            str: 序列化后的字符串
        """
        try:
            if isinstance(value, (str, int, float, bool)):
                return json.dumps(value)
            else:
                # 使用pickle序列化复杂对象
                pickled = pickle.dumps(value)
                encoded = base64.b64encode(pickled).decode('utf-8')
                return f"pickle:{encoded}"
        except Exception as e:
            self.logger.error(f"序列化值错误: {str(e)}")
            return json.dumps(str(value))
    
    def _deserialize_value(self, serialized: str) -> Any:
        """
        反序列化值
        
        Args:
            serialized: 序列化的字符串
            
        Returns:
            Any: 反序列化后的值
        """
        try:
            if serialized.startswith("pickle:"):
                # 反序列化pickle对象
                encoded = serialized[7:]  # 移除"pickle:"前缀
                pickled = base64.b64decode(encoded.encode('utf-8'))
                return pickle.loads(pickled)
            else:
                # JSON反序列化
                return json.loads(serialized)
        except Exception as e:
            self.logger.error(f"反序列化值错误: {str(e)}")
            return serialized
    
    async def store(
        self,
        key: str,
        value: Any,
        ttl: Optional[int] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> None:
        """
        存储键值对
        
        Args:
            key: 键
            value: 值
            ttl: 生存时间（秒）
            metadata: 元数据
        """
        try:
            # 更新最后访问时间
            self._last_access = datetime.now()
            
            # 序列化值
            serialized_value = self._serialize_value(value)
            
            # 计算过期时间
            expires_at = None
            if ttl:
                expires_at = datetime.now() + timedelta(seconds=ttl)
            
            # 检查是否已存在
            existing_entry = self._entry_cache.get(key)
            if existing_entry:
                # 更新现有条目
                existing_entry.value = serialized_value
                existing_entry.value_type = type(value).__name__
                existing_entry.expires_at = expires_at
                existing_entry.metadata = metadata or {}
                existing_entry.access_count += 1
                existing_entry.last_accessed = self._last_access
                
                await self._save_entry(existing_entry)
            else:
                # 创建新条目
                entry = MemoryEntry(
                    memory_id=self.memory_model.id,
                    user_id=self.user_id,
                    owner_id=self.owner_id,
                    key=key,
                    value=serialized_value,
                    value_type=type(value).__name__,
                    expires_at=expires_at,
                    metadata=metadata or {},
                    access_count=1,
                    last_accessed=self._last_access
                )
                
                await self._save_entry(entry)
            
            # 更新内存模型
            self.memory_model.entry_count = len(self._entry_cache)
            self.memory_model.last_access = self._last_access
            await self.memory_model.save()
            
            self.logger.info(f"键值对已存储: {key}")
        except Exception as e:
            self.logger.error(f"存储键值对错误: {str(e)}")
            raise e
    
    async def retrieve(self, key: str) -> Optional[Any]:
        """
        检索值
        
        Args:
            key: 键
            
        Returns:
            Optional[Any]: 值
        """
        try:
            # 更新最后访问时间
            self._last_access = datetime.now()
            
            # 如果缓存为空，从数据库加载
            if not self._entry_cache:
                await self._load_entries()
            
            # 获取条目
            entry = self._entry_cache.get(key)
            if not entry:
                return None
            
            # 检查是否过期
            if entry.expires_at and entry.expires_at < datetime.now():
                # 删除过期条目
                await self.delete(key)
                return None
            
            # 更新访问统计
            entry.access_count += 1
            entry.last_accessed = self._last_access
            await self._save_entry(entry)
            
            # 反序列化值
            value = self._deserialize_value(entry.value)
            
            self.logger.debug(f"键值对已检索: {key}")
            return value
        except Exception as e:
            self.logger.error(f"检索键值对错误: {str(e)}")
            return None
    
    async def delete(self, key: str) -> bool:
        """
        删除键值对
        
        Args:
            key: 键
            
        Returns:
            bool: 是否删除成功
        """
        try:
            # 如果缓存为空，从数据库加载
            if not self._entry_cache:
                await self._load_entries()
            
            # 获取条目
            entry = self._entry_cache.get(key)
            if not entry:
                return False
            
            # 从数据库删除
            await entry.delete()
            
            # 从缓存中移除
            del self._entry_cache[key]
            self._cache_dirty = True
            
            # 更新内存模型
            self.memory_model.entry_count = len(self._entry_cache)
            await self.memory_model.save()
            
            self.logger.info(f"键值对已删除: {key}")
            return True
        except Exception as e:
            self.logger.error(f"删除键值对错误: {str(e)}")
            return False
    
    async def exists(self, key: str) -> bool:
        """
        检查键是否存在
        
        Args:
            key: 键
            
        Returns:
            bool: 是否存在
        """
        try:
            # 如果缓存为空，从数据库加载
            if not self._entry_cache:
                await self._load_entries()
            
            entry = self._entry_cache.get(key)
            if not entry:
                return False
            
            # 检查是否过期
            if entry.expires_at and entry.expires_at < datetime.now():
                # 删除过期条目
                await self.delete(key)
                return False
            
            return True
        except Exception as e:
            self.logger.error(f"检查键存在错误: {str(e)}")
            return False
    
    async def list_keys(
        self,
        pattern: Optional[str] = None,
        limit: Optional[int] = None
    ) -> List[str]:
        """
        列出所有键
        
        Args:
            pattern: 键模式（简单通配符支持）
            limit: 数量限制
            
        Returns:
            List[str]: 键列表
        """
        try:
            # 如果缓存为空，从数据库加载
            if not self._entry_cache:
                await self._load_entries()
            
            keys = []
            for key, entry in self._entry_cache.items():
                # 检查是否过期
                if entry.expires_at and entry.expires_at < datetime.now():
                    continue
                
                # 应用模式过滤
                if pattern:
                    if '*' in pattern:
                        # 简单通配符支持
                        import fnmatch
                        if not fnmatch.fnmatch(key, pattern):
                            continue
                    else:
                        if pattern not in key:
                            continue
                
                keys.append(key)
            
            # 应用数量限制
            if limit:
                keys = keys[:limit]
            
            return keys
        except Exception as e:
            self.logger.error(f"列出键错误: {str(e)}")
            return []
    
    async def clear(self) -> None:
        """
        清除所有内存条目
        """
        try:
            # 删除数据库中的条目
            await MemoryEntry.delete_by_memory_id(self.memory_model.id)
            
            # 清空缓存
            self._entry_cache = {}
            self._cache_dirty = False
            
            # 更新内存模型
            self.memory_model.entry_count = 0
            await self.memory_model.save()
            
            self.logger.info("内存条目已清除")
        except Exception as e:
            self.logger.error(f"清除内存条目错误: {str(e)}")
            raise e
    
    async def get_stats(self) -> Dict[str, Any]:
        """
        获取内存统计
        
        Returns:
            Dict[str, Any]: 内存统计信息
        """
        try:
            # 如果缓存为空，从数据库加载
            if not self._entry_cache:
                await self._load_entries()
            
            total_entries = len(self._entry_cache)
            expired_entries = 0
            total_access_count = 0
            
            for entry in self._entry_cache.values():
                if entry.expires_at and entry.expires_at < datetime.now():
                    expired_entries += 1
                total_access_count += entry.access_count
            
            return {
                "memory_id": self.memory_id,
                "total_entries": total_entries,
                "active_entries": total_entries - expired_entries,
                "expired_entries": expired_entries,
                "total_access_count": total_access_count,
                "last_access": self._last_access.isoformat(),
                "is_active": self._is_active
            }
        except Exception as e:
            self.logger.error(f"获取内存统计错误: {str(e)}")
            return {}
    
    def is_active(self) -> bool:
        """
        检查内存是否活跃
        
        Returns:
            bool: 是否活跃
        """
        return self._is_active
    
    async def close(self) -> None:
        """
        关闭内存
        """
        try:
            self._is_active = False
            
            # 更新内存状态
            self.memory_model.status = "closed"
            await self.memory_model.save()
            
            self.logger.info("内存已关闭")
        except Exception as e:
            self.logger.error(f"关闭内存错误: {str(e)}")
            raise e

class DatabaseMemoryService(BaseMemoryService):
    """
    数据库内存服务，替代ADK内存存储，支持用户数据隔离
    
    提供以下功能：
    1. 基于数据库的内存存储
    2. 用户权限验证和数据隔离
    3. 内存生命周期管理
    4. 键值对持久化存储
    5. TTL支持和自动过期清理
    6. 内存统计和监控
    7. 批量操作支持
    """
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        """
        初始化数据库内存服务
        
        Args:
            logger: 日志记录器
        """
        super().__init__()
        
        self.logger = logger or get_logger("database_memory_service")
        
        # 内存缓存
        self._memory_cache: Dict[str, DatabaseMemory] = {}
        
        # 服务统计
        self.service_stats = {
            "total_memories_created": 0,
            "active_memories": 0,
            "total_entries": 0,
            "total_access_count": 0,
            "users_served": set(),
            "last_activity": None
        }
        
        self.logger.info("DatabaseMemoryService已初始化")
    
    async def _check_permission(
        self,
        user_id: int,
        owner_id: int,
        action: str = "create"
    ) -> bool:
        """
        检查用户权限
        
        Args:
            user_id: 用户ID
            owner_id: 拥有者ID
            action: 操作类型
            
        Returns:
            bool: 是否有权限
        """
        try:
            # 检查用户是否存在
            user = await User.get_by_id(user_id)
            if not user:
                self.logger.error(f"用户不存在: {user_id}")
                return False
            
            # 检查用户权限
            has_permission = await check_user_permission(
                user_id=user_id,
                owner_id=owner_id,
                resource_type="memory",
                action=action
            )
            
            if not has_permission:
                self.logger.error(f"用户 {user_id} 没有权限{action}内存")
            
            return has_permission
        except Exception as e:
            self.logger.error(f"权限检查错误: {str(e)}")
            return False
    
    async def create_memory(
        self,
        user_id: int,
        owner_id: int,
        memory_name: Optional[str] = None,
        memory_type: str = "general",
        metadata: Optional[Dict[str, Any]] = None
    ) -> Optional[DatabaseMemory]:
        """
        创建新内存
        
        Args:
            user_id: 用户ID
            owner_id: 拥有者ID
            memory_name: 内存名称
            memory_type: 内存类型
            metadata: 内存元数据
            
        Returns:
            Optional[DatabaseMemory]: 数据库内存实例
        """
        try:
            # 检查权限
            has_permission = await self._check_permission(user_id, owner_id, "create")
            if not has_permission:
                raise PermissionError(f"用户 {user_id} 没有权限创建内存")
            
            # 生成内存ID
            memory_id = str(uuid.uuid4())
            
            # 创建内存模型
            memory_model = MemoryModel(
                memory_id=memory_id,
                user_id=user_id,
                owner_id=owner_id,
                name=memory_name or f"Memory_{memory_id[:8]}",
                memory_type=memory_type,
                status="active",
                metadata=metadata or {},
                entry_count=0
            )
            
            await memory_model.save()
            
            # 创建内存实例
            memory = DatabaseMemory(
                memory_id=memory_id,
                user_id=user_id,
                owner_id=owner_id,
                memory_model=memory_model,
                logger=self.logger
            )
            
            # 缓存内存
            self._memory_cache[memory_id] = memory
            
            # 更新统计
            self.service_stats["total_memories_created"] += 1
            self.service_stats["active_memories"] += 1
            self.service_stats["users_served"].add(user_id)
            self.service_stats["last_activity"] = datetime.now().isoformat()
            
            self.logger.info(f"内存已创建: {memory_id} (用户: {user_id})")
            return memory
        except Exception as e:
            self.logger.error(f"创建内存错误: {str(e)}")
            raise e
    
    async def get_memory(
        self,
        memory_id: str,
        user_id: int
    ) -> Optional[DatabaseMemory]:
        """
        获取内存
        
        Args:
            memory_id: 内存ID
            user_id: 用户ID
            
        Returns:
            Optional[DatabaseMemory]: 数据库内存实例
        """
        try:
            # 检查缓存
            if memory_id in self._memory_cache:
                memory = self._memory_cache[memory_id]
                # 验证用户权限
                if memory.user_id == user_id or await self._check_permission(user_id, memory.owner_id, "read"):
                    return memory
                else:
                    self.logger.error(f"用户 {user_id} 没有权限访问内存 {memory_id}")
                    return None
            
            # 从数据库加载
            memory_model = await MemoryModel.get_by_memory_id(memory_id)
            if not memory_model:
                self.logger.error(f"内存不存在: {memory_id}")
                return None
            
            # 验证用户权限
            if memory_model.user_id != user_id and not await self._check_permission(user_id, memory_model.owner_id, "read"):
                self.logger.error(f"用户 {user_id} 没有权限访问内存 {memory_id}")
                return None
            
            # 创建内存实例
            memory = DatabaseMemory(
                memory_id=memory_id,
                user_id=memory_model.user_id,
                owner_id=memory_model.owner_id,
                memory_model=memory_model,
                logger=self.logger
            )
            
            # 缓存内存
            self._memory_cache[memory_id] = memory
            
            return memory
        except Exception as e:
            self.logger.error(f"获取内存错误: {str(e)}")
            return None
    
    async def list_user_memories(
        self,
        user_id: int,
        owner_id: int,
        memory_type: Optional[str] = None,
        limit: int = 50,
        offset: int = 0
    ) -> List[Dict[str, Any]]:
        """
        列出用户内存
        
        Args:
            user_id: 用户ID
            owner_id: 拥有者ID
            memory_type: 内存类型过滤
            limit: 数量限制
            offset: 偏移量
            
        Returns:
            List[Dict[str, Any]]: 内存列表
        """
        try:
            # 检查权限
            has_permission = await self._check_permission(user_id, owner_id, "list")
            if not has_permission:
                raise PermissionError(f"用户 {user_id} 没有权限列出内存")
            
            # 从数据库获取内存
            memories = await MemoryModel.get_by_user_id(
                user_id,
                memory_type=memory_type,
                limit=limit,
                offset=offset
            )
            
            memory_list = []
            for memory_model in memories:
                memory_info = {
                    "memory_id": memory_model.memory_id,
                    "name": memory_model.name,
                    "memory_type": memory_model.memory_type,
                    "status": memory_model.status,
                    "entry_count": memory_model.entry_count,
                    "created_at": memory_model.created_at.isoformat() if memory_model.created_at else None,
                    "last_access": memory_model.last_access.isoformat() if memory_model.last_access else None,
                    "metadata": memory_model.meta_data
                }
                memory_list.append(memory_info)
            
            return memory_list
        except Exception as e:
            self.logger.error(f"列出用户内存错误: {str(e)}")
            return []
    
    async def delete_memory(
        self,
        memory_id: str,
        user_id: int
    ) -> bool:
        """
        删除内存
        
        Args:
            memory_id: 内存ID
            user_id: 用户ID
            
        Returns:
            bool: 是否删除成功
        """
        try:
            # 获取内存
            memory = await self.get_memory(memory_id, user_id)
            if not memory:
                return False
            
            # 检查删除权限
            has_permission = await self._check_permission(user_id, memory.owner_id, "delete")
            if not has_permission:
                raise PermissionError(f"用户 {user_id} 没有权限删除内存 {memory_id}")
            
            # 关闭内存
            await memory.close()
            
            # 删除条目
            await MemoryEntry.delete_by_memory_id(memory.memory_model.id)
            
            # 删除内存模型
            await memory.memory_model.delete()
            
            # 从缓存中移除
            if memory_id in self._memory_cache:
                del self._memory_cache[memory_id]
            
            # 更新统计
            self.service_stats["active_memories"] = max(0, self.service_stats["active_memories"] - 1)
            
            self.logger.info(f"内存已删除: {memory_id}")
            return True
        except Exception as e:
            self.logger.error(f"删除内存错误: {str(e)}")
            return False
    
    async def cleanup_expired_entries(self) -> int:
        """
        清理过期条目
        
        Returns:
            int: 清理的条目数量
        """
        try:
            # 获取过期条目
            expired_entries = await MemoryEntry.get_expired_entries()
            
            cleaned_count = 0
            for entry in expired_entries:
                try:
                    await entry.delete()
                    cleaned_count += 1
                    
                    # 从缓存中移除
                    if entry.memory_id in self._memory_cache:
                        memory = self._memory_cache[entry.memory_id]
                        if entry.key in memory._entry_cache:
                            del memory._entry_cache[entry.key]
                            memory._cache_dirty = True
                except Exception as e:
                    self.logger.error(f"清理条目错误: {str(e)}")
            
            if cleaned_count > 0:
                self.logger.info(f"已清理 {cleaned_count} 个过期条目")
            
            return cleaned_count
        except Exception as e:
            self.logger.error(f"清理过期条目错误: {str(e)}")
            return 0
    
    async def batch_store(
        self,
        memory_id: str,
        user_id: int,
        items: List[Dict[str, Any]]
    ) -> Dict[str, bool]:
        """
        批量存储键值对
        
        Args:
            memory_id: 内存ID
            user_id: 用户ID
            items: 要存储的项目列表，每个项目包含key, value, ttl, metadata
            
        Returns:
            Dict[str, bool]: 每个键的存储结果
        """
        try:
            # 获取内存
            memory = await self.get_memory(memory_id, user_id)
            if not memory:
                return {item["key"]: False for item in items}
            
            results = {}
            for item in items:
                try:
                    await memory.store(
                        key=item["key"],
                        value=item["value"],
                        ttl=item.get("ttl"),
                        metadata=item.get("metadata")
                    )
                    results[item["key"]] = True
                except Exception as e:
                    self.logger.error(f"批量存储项目错误: {str(e)}")
                    results[item["key"]] = False
            
            return results
        except Exception as e:
            self.logger.error(f"批量存储错误: {str(e)}")
            return {item["key"]: False for item in items}
    
    async def batch_retrieve(
        self,
        memory_id: str,
        user_id: int,
        keys: List[str]
    ) -> Dict[str, Any]:
        """
        批量检索值
        
        Args:
            memory_id: 内存ID
            user_id: 用户ID
            keys: 键列表
            
        Returns:
            Dict[str, Any]: 键值对结果
        """
        try:
            # 获取内存
            memory = await self.get_memory(memory_id, user_id)
            if not memory:
                return {}
            
            results = {}
            for key in keys:
                try:
                    value = await memory.retrieve(key)
                    if value is not None:
                        results[key] = value
                except Exception as e:
                    self.logger.error(f"批量检索项目错误: {str(e)}")
            
            return results
        except Exception as e:
            self.logger.error(f"批量检索错误: {str(e)}")
            return {}
    
    def get_service_stats(self) -> Dict[str, Any]:
        """
        获取服务统计
        
        Returns:
            Dict[str, Any]: 服务统计信息
        """
        stats = self.service_stats.copy()
        stats["users_served"] = len(stats["users_served"])
        stats["cached_memories"] = len(self._memory_cache)
        return stats
    
    async def close(self) -> None:
        """
        关闭服务
        """
        try:
            # 关闭所有活跃内存
            for memory in self._memory_cache.values():
                await memory.close()
            
            # 清空缓存
            self._memory_cache.clear()
            
            self.logger.info("DatabaseMemoryService已关闭")
        except Exception as e:
            self.logger.error(f"关闭服务错误: {str(e)}")