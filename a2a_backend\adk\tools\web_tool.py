#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A2A多智能体系统 网络请求工具

支持用户配置和权限控制的网络请求工具，包含安全验证和监控
"""

import asyncio
import aiohttp
import logging
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime, timedelta
import json
import re
from urllib.parse import urlparse, urljoin, quote
import ssl
import certifi
from bs4 import BeautifulSoup
import xml.etree.ElementTree as ET

# Google ADK imports
from google.ai.generativelanguage import FunctionDeclaration, Schema, Type

from .base_tool import (
    BaseTool, ToolConfig, ToolResult, ToolError, ToolStatus,
    ToolExecutionContext, ToolPermission
)


class HttpMethod(Enum):
    """HTTP方法枚举"""
    GET = "GET"
    POST = "POST"
    PUT = "PUT"
    DELETE = "DELETE"
    PATCH = "PATCH"
    HEAD = "HEAD"
    OPTIONS = "OPTIONS"


class ResponseFormat(Enum):
    """响应格式枚举"""
    JSON = "json"
    TEXT = "text"
    HTML = "html"
    XML = "xml"
    BINARY = "binary"
    AUTO = "auto"


class SecurityLevel(Enum):
    """安全级别枚举"""
    STRICT = "strict"  # 严格模式，只允许HTTPS和白名单域名
    NORMAL = "normal"  # 正常模式，允许HTTP但有限制
    PERMISSIVE = "permissive"  # 宽松模式，允许大部分请求


@dataclass
class WebToolConfig(ToolConfig):
    """网络工具配置"""
    allowed_domains: List[str] = field(default_factory=list)
    blocked_domains: List[str] = field(default_factory=list)
    allowed_schemes: List[str] = field(default_factory=lambda: ["http", "https"])
    max_request_size: int = 10 * 1024 * 1024  # 10MB
    max_response_size: int = 50 * 1024 * 1024  # 50MB
    request_timeout: float = 30.0
    max_redirects: int = 5
    security_level: SecurityLevel = SecurityLevel.NORMAL
    enable_cookies: bool = True
    enable_javascript: bool = False
    user_agent: str = "A2A-WebTool/1.0"
    rate_limit_requests: int = 100
    rate_limit_window: int = 3600  # 1 hour
    ssl_verify: bool = True
    proxy_url: Optional[str] = None
    
    def __post_init__(self):
        """配置后处理"""
        # 严格模式下强制HTTPS
        if self.security_level == SecurityLevel.STRICT:
            self.allowed_schemes = ["https"]
            self.ssl_verify = True


@dataclass
class RequestConfig:
    """请求配置"""
    url: str
    method: HttpMethod = HttpMethod.GET
    headers: Dict[str, str] = field(default_factory=dict)
    params: Dict[str, Any] = field(default_factory=dict)
    data: Optional[Union[str, Dict[str, Any]]] = None
    json_data: Optional[Dict[str, Any]] = None
    files: Dict[str, Any] = field(default_factory=dict)
    auth: Optional[tuple] = None
    timeout: Optional[float] = None
    allow_redirects: bool = True
    response_format: ResponseFormat = ResponseFormat.AUTO
    extract_links: bool = False
    extract_images: bool = False
    extract_text: bool = False


@dataclass
class WebResponse:
    """网络响应"""
    status_code: int
    headers: Dict[str, str]
    content: Any
    content_type: str
    encoding: str
    url: str
    response_time: float
    size: int
    links: List[str] = field(default_factory=list)
    images: List[str] = field(default_factory=list)
    text_content: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)


class WebTool(BaseTool):
    """网络请求工具"""
    
    def __init__(self, config: WebToolConfig):
        """
        初始化网络工具
        
        Args:
            config: 网络工具配置
        """
        super().__init__(config)
        self._session: Optional[aiohttp.ClientSession] = None
        self._rate_limiter: Dict[str, List[datetime]] = {}
    
    def get_function_declaration(self) -> FunctionDeclaration:
        """
        获取工具的函数声明
        
        Returns:
            FunctionDeclaration: ADK函数声明
        """
        parameters_schema = Schema(
            type=Type.OBJECT,
            properties={
                "url": Schema(type=Type.STRING, description="请求URL"),
                "method": Schema(
                    type=Type.STRING,
                    description="HTTP方法 (GET, POST, PUT, DELETE, PATCH, HEAD, OPTIONS)"
                ),
                "headers": Schema(
                    type=Type.OBJECT,
                    description="请求头字典"
                ),
                "params": Schema(
                    type=Type.OBJECT,
                    description="URL参数字典"
                ),
                "data": Schema(
                    type=Type.STRING,
                    description="请求体数据（字符串格式）"
                ),
                "json_data": Schema(
                    type=Type.OBJECT,
                    description="JSON请求体数据"
                ),
                "auth": Schema(
                    type=Type.ARRAY,
                    items=Schema(type=Type.STRING),
                    description="认证信息 [username, password]"
                ),
                "timeout": Schema(type=Type.NUMBER, description="请求超时时间（秒）"),
                "allow_redirects": Schema(type=Type.BOOLEAN, description="是否允许重定向"),
                "response_format": Schema(
                    type=Type.STRING,
                    description="响应格式 (json, text, html, xml, binary, auto)"
                ),
                "extract_links": Schema(type=Type.BOOLEAN, description="是否提取链接"),
                "extract_images": Schema(type=Type.BOOLEAN, description="是否提取图片"),
                "extract_text": Schema(type=Type.BOOLEAN, description="是否提取文本内容")
            },
            required=["url"]
        )
        
        return FunctionDeclaration(
            name=self.name,
            description=self.description,
            parameters=parameters_schema
        )
    
    async def execute(
        self,
        context: ToolExecutionContext,
        **kwargs
    ) -> ToolResult:
        """
        执行网络请求
        
        Args:
            context: 执行上下文
            **kwargs: 额外参数
        
        Returns:
            ToolResult: 执行结果
        """
        async with self._execution_context(context):
            try:
                # 解析请求配置
                request_config = self._parse_request_config(context.parameters)
                
                # 验证请求权限
                permission_error = await self._validate_request_permissions(
                    request_config, context.user_id
                )
                if permission_error:
                    return ToolResult(
                        tool_name=self.name,
                        execution_id=context.execution_id,
                        status=ToolStatus.FAILED,
                        error=permission_error,
                        user_id=context.user_id
                    )
                
                # 检查速率限制
                rate_limit_error = await self._check_rate_limit(context.user_id)
                if rate_limit_error:
                    return ToolResult(
                        tool_name=self.name,
                        execution_id=context.execution_id,
                        status=ToolStatus.FAILED,
                        error=rate_limit_error,
                        user_id=context.user_id
                    )
                
                # 执行网络请求
                response = await self._execute_request(request_config, context.user_id)
                
                return ToolResult(
                    tool_name=self.name,
                    execution_id=context.execution_id,
                    status=ToolStatus.SUCCESS,
                    result=response.__dict__,
                    user_id=context.user_id,
                    metadata={
                        "url": request_config.url,
                        "method": request_config.method.value,
                        "status_code": response.status_code,
                        "response_time": response.response_time,
                        "content_size": response.size
                    }
                )
                
            except Exception as e:
                self.logger.error(f"网络请求失败: {e}")
                
                return ToolResult(
                    tool_name=self.name,
                    execution_id=context.execution_id,
                    status=ToolStatus.FAILED,
                    error=ToolError(
                        code="WEB_REQUEST_ERROR",
                        message=f"网络请求失败: {str(e)}"
                    ),
                    user_id=context.user_id
                )
    
    def _parse_request_config(self, parameters: Dict[str, Any]) -> RequestConfig:
        """
        解析请求配置
        
        Args:
            parameters: 原始参数
        
        Returns:
            RequestConfig: 解析后的请求配置
        """
        # 处理认证信息
        auth = None
        if "auth" in parameters and parameters["auth"]:
            auth_list = parameters["auth"]
            if len(auth_list) >= 2:
                auth = (auth_list[0], auth_list[1])
        
        return RequestConfig(
            url=parameters["url"],
            method=HttpMethod(parameters.get("method", "GET")),
            headers=parameters.get("headers", {}),
            params=parameters.get("params", {}),
            data=parameters.get("data"),
            json_data=parameters.get("json_data"),
            auth=auth,
            timeout=parameters.get("timeout"),
            allow_redirects=parameters.get("allow_redirects", True),
            response_format=ResponseFormat(parameters.get("response_format", "auto")),
            extract_links=parameters.get("extract_links", False),
            extract_images=parameters.get("extract_images", False),
            extract_text=parameters.get("extract_text", False)
        )
    
    async def _validate_request_permissions(
        self,
        request_config: RequestConfig,
        user_id: str
    ) -> Optional[ToolError]:
        """
        验证请求权限
        
        Args:
            request_config: 请求配置
            user_id: 用户ID
        
        Returns:
            Optional[ToolError]: 权限验证错误
        """
        try:
            # 解析URL
            parsed_url = urlparse(request_config.url)
            
            # 检查协议
            if parsed_url.scheme not in self.config.allowed_schemes:
                return ToolError(
                    code="INVALID_SCHEME",
                    message=f"不允许的协议: {parsed_url.scheme}"
                )
            
            # 检查域名白名单
            if self.config.allowed_domains:
                domain = parsed_url.netloc.lower()
                if not any(domain.endswith(allowed) for allowed in self.config.allowed_domains):
                    return ToolError(
                        code="DOMAIN_NOT_ALLOWED",
                        message=f"域名不在白名单中: {domain}"
                    )
            
            # 检查域名黑名单
            if self.config.blocked_domains:
                domain = parsed_url.netloc.lower()
                if any(domain.endswith(blocked) for blocked in self.config.blocked_domains):
                    return ToolError(
                        code="DOMAIN_BLOCKED",
                        message=f"域名被阻止: {domain}"
                    )
            
            # 检查本地地址（安全考虑）
            if self._is_local_address(parsed_url.netloc):
                if self.config.security_level == SecurityLevel.STRICT:
                    return ToolError(
                        code="LOCAL_ADDRESS_BLOCKED",
                        message="严格模式下不允许访问本地地址"
                    )
            
            # 检查请求大小
            request_size = self._estimate_request_size(request_config)
            if request_size > self.config.max_request_size:
                return ToolError(
                    code="REQUEST_TOO_LARGE",
                    message=f"请求过大: {request_size} > {self.config.max_request_size}"
                )
            
            return None
            
        except Exception as e:
            return ToolError(
                code="PERMISSION_VALIDATION_ERROR",
                message=f"权限验证异常: {str(e)}"
            )
    
    def _is_local_address(self, netloc: str) -> bool:
        """
        检查是否为本地地址
        
        Args:
            netloc: 网络位置
        
        Returns:
            bool: 是否为本地地址
        """
        local_patterns = [
            r'^localhost(:\d+)?$',
            r'^127\.\d+\.\d+\.\d+(:\d+)?$',
            r'^10\.\d+\.\d+\.\d+(:\d+)?$',
            r'^172\.(1[6-9]|2[0-9]|3[01])\.\d+\.\d+(:\d+)?$',
            r'^192\.168\.\d+\.\d+(:\d+)?$',
            r'^\[::1\](:\d+)?$',
            r'^\[::ffff:127\.\d+\.\d+\.\d+\](:\d+)?$'
        ]
        
        for pattern in local_patterns:
            if re.match(pattern, netloc.lower()):
                return True
        
        return False
    
    def _estimate_request_size(self, request_config: RequestConfig) -> int:
        """
        估算请求大小
        
        Args:
            request_config: 请求配置
        
        Returns:
            int: 估算的请求大小（字节）
        """
        size = len(request_config.url.encode('utf-8'))
        
        # 添加头部大小
        for key, value in request_config.headers.items():
            size += len(f"{key}: {value}\r\n".encode('utf-8'))
        
        # 添加数据大小
        if request_config.data:
            if isinstance(request_config.data, str):
                size += len(request_config.data.encode('utf-8'))
            else:
                size += len(str(request_config.data).encode('utf-8'))
        
        if request_config.json_data:
            size += len(json.dumps(request_config.json_data).encode('utf-8'))
        
        return size
    
    async def _check_rate_limit(self, user_id: str) -> Optional[ToolError]:
        """
        检查速率限制
        
        Args:
            user_id: 用户ID
        
        Returns:
            Optional[ToolError]: 速率限制错误
        """
        now = datetime.now()
        window_start = now - timedelta(seconds=self.config.rate_limit_window)
        
        # 清理过期记录
        if user_id in self._rate_limiter:
            self._rate_limiter[user_id] = [
                req_time for req_time in self._rate_limiter[user_id]
                if req_time > window_start
            ]
        else:
            self._rate_limiter[user_id] = []
        
        # 检查请求数量
        if len(self._rate_limiter[user_id]) >= self.config.rate_limit_requests:
            return ToolError(
                code="RATE_LIMIT_EXCEEDED",
                message=f"超过速率限制: {self.config.rate_limit_requests} 请求/{self.config.rate_limit_window} 秒"
            )
        
        # 记录当前请求
        self._rate_limiter[user_id].append(now)
        
        return None
    
    async def _execute_request(
        self,
        request_config: RequestConfig,
        user_id: str
    ) -> WebResponse:
        """
        执行网络请求
        
        Args:
            request_config: 请求配置
            user_id: 用户ID
        
        Returns:
            WebResponse: 响应结果
        """
        # 创建会话（如果不存在）
        if not self._session:
            await self._create_session()
        
        start_time = datetime.now()
        
        try:
            # 准备请求参数
            kwargs = {
                'method': request_config.method.value,
                'url': request_config.url,
                'headers': self._prepare_headers(request_config.headers),
                'params': request_config.params,
                'timeout': aiohttp.ClientTimeout(
                    total=request_config.timeout or self.config.request_timeout
                ),
                'allow_redirects': request_config.allow_redirects,
                'max_redirects': self.config.max_redirects
            }
            
            # 添加认证
            if request_config.auth:
                kwargs['auth'] = aiohttp.BasicAuth(
                    request_config.auth[0],
                    request_config.auth[1]
                )
            
            # 添加请求体
            if request_config.json_data:
                kwargs['json'] = request_config.json_data
            elif request_config.data:
                kwargs['data'] = request_config.data
            
            # 执行请求
            async with self._session.request(**kwargs) as response:
                # 检查响应大小
                content_length = response.headers.get('content-length')
                if content_length and int(content_length) > self.config.max_response_size:
                    raise ValueError(f"响应过大: {content_length} > {self.config.max_response_size}")
                
                # 读取响应内容
                content = await self._read_response_content(
                    response, request_config.response_format
                )
                
                # 计算响应时间
                response_time = (datetime.now() - start_time).total_seconds()
                
                # 创建响应对象
                web_response = WebResponse(
                    status_code=response.status,
                    headers=dict(response.headers),
                    content=content,
                    content_type=response.headers.get('content-type', ''),
                    encoding=response.charset or 'utf-8',
                    url=str(response.url),
                    response_time=response_time,
                    size=len(str(content).encode('utf-8'))
                )
                
                # 提取额外信息
                if request_config.extract_links or request_config.extract_images or request_config.extract_text:
                    await self._extract_content_info(web_response, request_config)
                
                return web_response
                
        except asyncio.TimeoutError:
            raise Exception("请求超时")
        except aiohttp.ClientError as e:
            raise Exception(f"网络请求错误: {str(e)}")
        except Exception as e:
            raise Exception(f"请求执行失败: {str(e)}")
    
    async def _create_session(self) -> None:
        """
        创建HTTP会话
        """
        # SSL配置
        ssl_context = None
        if self.config.ssl_verify:
            ssl_context = ssl.create_default_context(cafile=certifi.where())
        else:
            ssl_context = False
        
        # 连接器配置
        connector = aiohttp.TCPConnector(
            ssl=ssl_context,
            limit=100,
            limit_per_host=10
        )
        
        # 超时配置
        timeout = aiohttp.ClientTimeout(total=self.config.request_timeout)
        
        # 创建会话
        self._session = aiohttp.ClientSession(
            connector=connector,
            timeout=timeout,
            cookie_jar=aiohttp.CookieJar() if self.config.enable_cookies else None
        )
    
    def _prepare_headers(self, headers: Dict[str, str]) -> Dict[str, str]:
        """
        准备请求头
        
        Args:
            headers: 原始头部
        
        Returns:
            Dict[str, str]: 处理后的头部
        """
        prepared_headers = {
            'User-Agent': self.config.user_agent
        }
        
        # 添加用户自定义头部
        prepared_headers.update(headers)
        
        return prepared_headers
    
    async def _read_response_content(
        self,
        response: aiohttp.ClientResponse,
        response_format: ResponseFormat
    ) -> Any:
        """
        读取响应内容
        
        Args:
            response: HTTP响应
            response_format: 响应格式
        
        Returns:
            Any: 解析后的内容
        """
        content_type = response.headers.get('content-type', '').lower()
        
        # 自动检测格式
        if response_format == ResponseFormat.AUTO:
            if 'application/json' in content_type:
                response_format = ResponseFormat.JSON
            elif 'text/html' in content_type:
                response_format = ResponseFormat.HTML
            elif 'application/xml' in content_type or 'text/xml' in content_type:
                response_format = ResponseFormat.XML
            elif content_type.startswith('text/'):
                response_format = ResponseFormat.TEXT
            else:
                response_format = ResponseFormat.BINARY
        
        # 根据格式读取内容
        if response_format == ResponseFormat.JSON:
            try:
                return await response.json()
            except Exception:
                return await response.text()
        
        elif response_format == ResponseFormat.TEXT:
            return await response.text()
        
        elif response_format == ResponseFormat.HTML:
            return await response.text()
        
        elif response_format == ResponseFormat.XML:
            text = await response.text()
            try:
                root = ET.fromstring(text)
                return self._xml_to_dict(root)
            except Exception:
                return text
        
        elif response_format == ResponseFormat.BINARY:
            content = await response.read()
            # 返回base64编码的二进制数据
            import base64
            return base64.b64encode(content).decode('ascii')
        
        else:
            return await response.text()
    
    def _xml_to_dict(self, element) -> Dict[str, Any]:
        """
        将XML元素转换为字典
        
        Args:
            element: XML元素
        
        Returns:
            Dict[str, Any]: 字典表示
        """
        result = {}
        
        # 添加属性
        if element.attrib:
            result['@attributes'] = element.attrib
        
        # 添加文本内容
        if element.text and element.text.strip():
            if len(element) == 0:
                return element.text.strip()
            result['#text'] = element.text.strip()
        
        # 添加子元素
        for child in element:
            child_data = self._xml_to_dict(child)
            if child.tag in result:
                if not isinstance(result[child.tag], list):
                    result[child.tag] = [result[child.tag]]
                result[child.tag].append(child_data)
            else:
                result[child.tag] = child_data
        
        return result
    
    async def _extract_content_info(
        self,
        web_response: WebResponse,
        request_config: RequestConfig
    ) -> None:
        """
        提取内容信息
        
        Args:
            web_response: 网络响应
            request_config: 请求配置
        """
        if not isinstance(web_response.content, str):
            return
        
        try:
            # 解析HTML
            soup = BeautifulSoup(web_response.content, 'html.parser')
            
            # 提取链接
            if request_config.extract_links:
                links = []
                for link in soup.find_all('a', href=True):
                    href = link['href']
                    # 转换相对链接为绝对链接
                    absolute_url = urljoin(web_response.url, href)
                    links.append(absolute_url)
                web_response.links = list(set(links))  # 去重
            
            # 提取图片
            if request_config.extract_images:
                images = []
                for img in soup.find_all('img', src=True):
                    src = img['src']
                    # 转换相对链接为绝对链接
                    absolute_url = urljoin(web_response.url, src)
                    images.append(absolute_url)
                web_response.images = list(set(images))  # 去重
            
            # 提取文本内容
            if request_config.extract_text:
                # 移除脚本和样式
                for script in soup(["script", "style"]):
                    script.decompose()
                
                # 提取纯文本
                text = soup.get_text()
                # 清理空白字符
                lines = (line.strip() for line in text.splitlines())
                chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
                web_response.text_content = '\n'.join(chunk for chunk in chunks if chunk)
            
        except Exception as e:
            self.logger.warning(f"内容提取失败: {e}")
    
    async def cleanup(self) -> None:
        """
        清理资源
        """
        if self._session:
            await self._session.close()
            self._session = None
    
    async def _validate_specific_parameters(self, parameters: Dict[str, Any]) -> Optional[ToolError]:
        """
        验证网络工具参数
        
        Args:
            parameters: 参数字典
        
        Returns:
            Optional[ToolError]: 验证错误
        """
        try:
            # 检查必需参数
            if "url" not in parameters:
                return ToolError(
                    code="MISSING_REQUIRED_PARAMETER",
                    message="缺少必需参数: url"
                )
            
            # 验证URL格式
            try:
                parsed_url = urlparse(parameters["url"])
                if not parsed_url.scheme or not parsed_url.netloc:
                    return ToolError(
                        code="INVALID_URL",
                        message=f"无效的URL格式: {parameters['url']}"
                    )
            except Exception:
                return ToolError(
                    code="INVALID_URL",
                    message=f"URL解析失败: {parameters['url']}"
                )
            
            # 验证HTTP方法
            if "method" in parameters:
                try:
                    HttpMethod(parameters["method"])
                except ValueError:
                    return ToolError(
                        code="INVALID_METHOD",
                        message=f"无效的HTTP方法: {parameters['method']}"
                    )
            
            # 验证响应格式
            if "response_format" in parameters:
                try:
                    ResponseFormat(parameters["response_format"])
                except ValueError:
                    return ToolError(
                        code="INVALID_RESPONSE_FORMAT",
                        message=f"无效的响应格式: {parameters['response_format']}"
                    )
            
            # 验证超时时间
            if "timeout" in parameters:
                timeout = parameters["timeout"]
                if not isinstance(timeout, (int, float)) or timeout <= 0:
                    return ToolError(
                        code="INVALID_TIMEOUT",
                        message="超时时间必须是正数"
                    )
            
            return None
            
        except Exception as e:
            return ToolError(
                code="PARAMETER_VALIDATION_ERROR",
                message=f"参数验证异常: {str(e)}"
            )
    
    def __del__(self):
        """析构函数"""
        if self._session:
            self.logger.warning(f"网络工具 {self.name} 未正确清理会话")