#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A2A多智能体系统会话相关Pydantic模式

定义会话相关的请求和响应模式
"""

from typing import Dict, List, Optional, Any
from datetime import datetime
from pydantic import Field, validator
from enum import Enum

from .base import BaseSchema


class SessionType(str, Enum):
    """会话类型枚举"""
    CHAT = "chat"
    TASK = "task"
    WORKFLOW = "workflow"
    COLLABORATION = "collaboration"


class SessionMode(str, Enum):
    """会话模式枚举"""
    SINGLE = "single"
    MULTI = "multi"
    GROUP = "group"


class SessionStatus(str, Enum):
    """会话状态枚举"""
    ACTIVE = "active"
    PAUSED = "paused"
    COMPLETED = "completed"
    ARCHIVED = "archived"
    DELETED = "deleted"


class SharePermission(str, Enum):
    """分享权限枚举"""
    VIEW = "view"
    COMMENT = "comment"
    EDIT = "edit"


class SessionCreate(BaseSchema):
    """
    会话创建请求模式
    """
    
    title: Optional[str] = Field(
        default=None,
        max_length=200,
        description="会话标题"
    )
    
    agent_id: str = Field(
        description="智能体ID"
    )
    
    type: SessionType = Field(
        default=SessionType.CHAT,
        description="会话类型"
    )
    
    mode: SessionMode = Field(
        default=SessionMode.SINGLE,
        description="会话模式"
    )
    
    config: Optional[Dict[str, Any]] = Field(
        default_factory=dict,
        description="会话配置"
    )
    
    context: Optional[Dict[str, Any]] = Field(
        default_factory=dict,
        description="会话上下文"
    )
    
    tags: List[str] = Field(
        default_factory=list,
        description="标签列表"
    )
    
    initial_message: Optional[str] = Field(
        default=None,
        max_length=10000,
        description="初始消息"
    )


class SessionUpdate(BaseSchema):
    """
    会话更新请求模式
    """
    
    title: Optional[str] = Field(
        default=None,
        max_length=200,
        description="会话标题"
    )
    
    config: Optional[Dict[str, Any]] = Field(
        default=None,
        description="会话配置"
    )
    
    context: Optional[Dict[str, Any]] = Field(
        default=None,
        description="会话上下文"
    )
    
    tags: Optional[List[str]] = Field(
        default=None,
        description="标签列表"
    )
    
    is_pinned: Optional[bool] = Field(
        default=None,
        description="是否置顶"
    )
    
    status: Optional[SessionStatus] = Field(
        default=None,
        description="会话状态"
    )


class SessionResponse(BaseSchema):
    """
    会话响应模式
    """
    
    id: str = Field(
        description="会话ID"
    )
    
    title: Optional[str] = Field(
        description="会话标题"
    )
    
    user_id: str = Field(
        description="用户ID"
    )
    
    agent_id: str = Field(
        description="智能体ID"
    )
    
    type: SessionType = Field(
        description="会话类型"
    )
    
    mode: SessionMode = Field(
        description="会话模式"
    )
    
    status: SessionStatus = Field(
        description="会话状态"
    )
    
    config: Dict[str, Any] = Field(
        description="会话配置"
    )
    
    context: Dict[str, Any] = Field(
        description="会话上下文"
    )
    
    tags: List[str] = Field(
        description="标签列表"
    )
    
    is_pinned: bool = Field(
        description="是否置顶"
    )
    
    message_count: int = Field(
        description="消息数量"
    )
    
    total_tokens: int = Field(
        description="总令牌数"
    )
    
    total_cost: float = Field(
        description="总成本"
    )
    
    last_message_at: Optional[datetime] = Field(
        description="最后消息时间"
    )
    
    last_activity_at: datetime = Field(
        description="最后活动时间"
    )
    
    created_at: datetime = Field(
        description="创建时间"
    )
    
    updated_at: datetime = Field(
        description="更新时间"
    )


class SessionShareCreate(BaseSchema):
    """
    会话分享创建请求模式
    """
    
    permission: SharePermission = Field(
        default=SharePermission.VIEW,
        description="分享权限"
    )
    
    expires_at: Optional[datetime] = Field(
        default=None,
        description="过期时间"
    )
    
    max_views: Optional[int] = Field(
        default=None,
        ge=1,
        description="最大查看次数"
    )
    
    password: Optional[str] = Field(
        default=None,
        min_length=4,
        max_length=50,
        description="访问密码"
    )
    
    allowed_ips: List[str] = Field(
        default_factory=list,
        description="允许的IP地址列表"
    )
    
    description: Optional[str] = Field(
        default=None,
        max_length=500,
        description="分享描述"
    )


class SessionShareResponse(BaseSchema):
    """
    会话分享响应模式
    """
    
    id: str = Field(
        description="分享ID"
    )
    
    session_id: str = Field(
        description="会话ID"
    )
    
    share_token: str = Field(
        description="分享令牌"
    )
    
    share_url: str = Field(
        description="分享URL"
    )
    
    permission: SharePermission = Field(
        description="分享权限"
    )
    
    expires_at: Optional[datetime] = Field(
        description="过期时间"
    )
    
    max_views: Optional[int] = Field(
        description="最大查看次数"
    )
    
    view_count: int = Field(
        description="查看次数"
    )
    
    has_password: bool = Field(
        description="是否设置密码"
    )
    
    allowed_ips: List[str] = Field(
        description="允许的IP地址列表"
    )
    
    description: Optional[str] = Field(
        description="分享描述"
    )
    
    is_active: bool = Field(
        description="是否激活"
    )
    
    created_by: str = Field(
        description="创建者ID"
    )
    
    created_at: datetime = Field(
        description="创建时间"
    )
    
    updated_at: datetime = Field(
        description="更新时间"
    )


class SessionBookmarkCreate(BaseSchema):
    """
    会话书签创建请求模式
    """
    
    title: Optional[str] = Field(
        default=None,
        max_length=200,
        description="书签标题"
    )
    
    description: Optional[str] = Field(
        default=None,
        max_length=500,
        description="书签描述"
    )
    
    category: Optional[str] = Field(
        default=None,
        max_length=50,
        description="书签分类"
    )
    
    tags: List[str] = Field(
        default_factory=list,
        description="标签列表"
    )


class SessionBookmarkResponse(BaseSchema):
    """
    会话书签响应模式
    """
    
    id: str = Field(
        description="书签ID"
    )
    
    user_id: str = Field(
        description="用户ID"
    )
    
    session_id: str = Field(
        description="会话ID"
    )
    
    title: Optional[str] = Field(
        description="书签标题"
    )
    
    description: Optional[str] = Field(
        description="书签描述"
    )
    
    category: Optional[str] = Field(
        description="书签分类"
    )
    
    tags: List[str] = Field(
        description="标签列表"
    )
    
    created_at: datetime = Field(
        description="创建时间"
    )
    
    updated_at: datetime = Field(
        description="更新时间"
    )


class SessionStatsResponse(BaseSchema):
    """
    会话统计信息响应模式
    """
    
    total_messages: int = Field(
        description="总消息数"
    )
    
    total_tokens: int = Field(
        description="总令牌数"
    )
    
    total_cost: float = Field(
        description="总成本"
    )
    
    avg_response_time: float = Field(
        description="平均响应时间（秒）"
    )
    
    duration: int = Field(
        description="会话持续时间（秒）"
    )
    
    message_frequency: float = Field(
        description="消息频率（消息/分钟）"
    )
    
    last_activity_at: datetime = Field(
        description="最后活动时间"
    )


class SessionExportRequest(BaseSchema):
    """
    会话导出请求模式
    """
    
    format: str = Field(
        default="json",
        description="导出格式（json, markdown, pdf）"
    )
    
    include_metadata: bool = Field(
        default=True,
        description="是否包含元数据"
    )
    
    include_attachments: bool = Field(
        default=False,
        description="是否包含附件"
    )
    
    date_range: Optional[Dict[str, datetime]] = Field(
        default=None,
        description="日期范围过滤"
    )