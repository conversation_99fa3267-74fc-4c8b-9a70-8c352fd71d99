# A2A多智能体系统环境变量配置模板
# 复制此文件为 .env 并修改相应的配置值

# ================================
# 应用基础配置
# ================================
APP_NAME=A2A多智能体系统
DEBUG=true
HOST=0.0.0.0
PORT=8000

# ================================
# 数据库配置
# ================================
# MySQL配置示例
DATABASE_URL=mysql+aiomysql://root:password@localhost:3306/a2a_system

# PostgreSQL配置示例（可选）
# DATABASE_URL=postgresql+asyncpg://username:password@localhost:5432/a2a_system

# SQLite配置示例（仅用于开发测试）
# DATABASE_URL=sqlite+aiosqlite:///./a2a_system.db

# 数据库连接池配置
DATABASE_POOL_SIZE=20
DATABASE_MAX_OVERFLOW=30
DATABASE_POOL_TIMEOUT=30
DATABASE_POOL_RECYCLE=3600

# ================================
# JWT认证配置
# ================================
# 请在生产环境中使用强密钥！
JWT_SECRET_KEY=your-super-secret-jwt-key-change-in-production-please
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30
JWT_REFRESH_TOKEN_EXPIRE_DAYS=7

# ================================
# 密码加密配置
# ================================
PASSWORD_BCRYPT_ROUNDS=12

# ================================
# CORS跨域配置
# ================================
ALLOWED_ORIGINS=["http://localhost:3000","http://127.0.0.1:3000","http://localhost:8080"]

# ================================
# Google ADK配置
# ================================
# Google Cloud项目ID
GOOGLE_ADK_PROJECT_ID=your-google-project-id

# Google凭证文件路径
GOOGLE_ADK_CREDENTIALS_PATH=./credentials/google-credentials.json

# ================================
# LLM模型配置
# ================================
DEFAULT_LLM_PROVIDER=google
DEFAULT_LLM_MODEL=gemini-pro

# ================================
# 工具系统配置
# ================================
TOOLS_ENABLED=true
MCP_ENABLED=true

# ================================
# 日志配置
# ================================
LOG_LEVEL=INFO
LOG_TO_DATABASE=true
LOG_FILE_PATH=./logs/app.log

# ================================
# 监控配置
# ================================
METRICS_ENABLED=true
PROMETHEUS_ENABLED=false

# ================================
# 文件存储配置
# ================================
FILE_STORAGE_TYPE=database
FILE_STORAGE_PATH=./storage
MAX_FILE_SIZE=104857600

# ================================
# 会话配置
# ================================
SESSION_TIMEOUT_MINUTES=60
MAX_SESSIONS_PER_USER=10

# ================================
# 工作流配置
# ================================
WORKFLOW_MAX_STEPS=100
WORKFLOW_TIMEOUT_MINUTES=30

# ================================
# 任务配置
# ================================
MAX_CONCURRENT_TASKS_PER_USER=5
TASK_TIMEOUT_MINUTES=60
MAX_TASK_DURATION_MINUTES=120
MAX_TASK_RETRIES=3
MAX_MEMORY_USAGE_MB=1024

# ================================
# Redis缓存配置
# ================================
REDIS_URL=redis://localhost:6379/0
REDIS_PASSWORD=
REDIS_DB=0
REDIS_MAX_CONNECTIONS=20

# ================================
# 邮件配置（可选）
# ================================
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
SMTP_USE_TLS=true
FROM_EMAIL=<EMAIL>

# ================================
# 速率限制配置
# ================================
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS_PER_MINUTE=60
RATE_LIMIT_BURST_SIZE=10

# ================================
# 安全配置
# ================================
ENABLE_HTTPS=false
SSL_CERT_PATH=./certs/cert.pem
SSL_KEY_PATH=./certs/key.pem

# ================================
# 开发配置
# ================================
# 是否启用API文档
ENABLE_DOCS=true

# 是否启用调试工具
ENABLE_DEBUG_TOOLBAR=false

# 是否启用性能分析
ENABLE_PROFILING=false
