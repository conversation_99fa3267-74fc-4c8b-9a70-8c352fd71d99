#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A2A多智能体系统基础Pydantic模式

定义通用的请求和响应模式
"""

from typing import Any, Dict, Generic, List, Optional, TypeVar
from datetime import datetime
from pydantic import BaseModel, Field, ConfigDict

# 泛型类型变量
DataType = TypeVar('DataType')


class BaseSchema(BaseModel):
    """
    基础Pydantic模式
    
    所有其他模式的基类，提供通用配置
    """
    
    model_config = ConfigDict(
        # 允许从ORM对象创建
        from_attributes=True,
        # 使用枚举值而不是名称
        use_enum_values=True,
        # 验证赋值
        validate_assignment=True,
        # 允许额外字段
        extra='forbid',
        # 序列化时排除None值
        exclude_none=True
    )


class PaginationSchema(BaseSchema):
    """
    分页参数模式
    """
    
    page: int = Field(
        default=1,
        ge=1,
        description="页码，从1开始"
    )
    
    size: int = Field(
        default=20,
        ge=1,
        le=100,
        description="每页数量，最大100"
    )
    
    @property
    def offset(self) -> int:
        """计算偏移量"""
        return (self.page - 1) * self.size


class ResponseSchema(BaseSchema, Generic[DataType]):
    """
    通用API响应模式
    """
    
    success: bool = Field(
        default=True,
        description="请求是否成功"
    )
    
    message: str = Field(
        default="操作成功",
        description="响应消息"
    )
    
    data: Optional[DataType] = Field(
        default=None,
        description="响应数据"
    )
    
    timestamp: datetime = Field(
        default_factory=datetime.now,
        description="响应时间戳"
    )


class PaginatedResponseSchema(BaseSchema, Generic[DataType]):
    """
    分页响应模式
    """
    
    success: bool = Field(
        default=True,
        description="请求是否成功"
    )
    
    message: str = Field(
        default="操作成功",
        description="响应消息"
    )
    
    data: List[DataType] = Field(
        default_factory=list,
        description="响应数据列表"
    )
    
    pagination: 'PaginationInfo' = Field(
        description="分页信息"
    )
    
    timestamp: datetime = Field(
        default_factory=datetime.now,
        description="响应时间戳"
    )


class PaginationInfo(BaseSchema):
    """
    分页信息模式
    """
    
    page: int = Field(
        description="当前页码"
    )
    
    size: int = Field(
        description="每页数量"
    )
    
    total: int = Field(
        description="总记录数"
    )
    
    pages: int = Field(
        description="总页数"
    )
    
    has_prev: bool = Field(
        description="是否有上一页"
    )
    
    has_next: bool = Field(
        description="是否有下一页"
    )


class ErrorResponseSchema(BaseSchema):
    """
    错误响应模式
    """
    
    success: bool = Field(
        default=False,
        description="请求是否成功"
    )
    
    message: str = Field(
        description="错误消息"
    )
    
    error_code: Optional[str] = Field(
        default=None,
        description="错误代码"
    )
    
    details: Optional[Dict[str, Any]] = Field(
        default=None,
        description="错误详情"
    )
    
    timestamp: datetime = Field(
        default_factory=datetime.now,
        description="响应时间戳"
    )


class HealthCheckSchema(BaseSchema):
    """
    健康检查响应模式
    """
    
    status: str = Field(
        description="服务状态"
    )
    
    version: str = Field(
        description="服务版本"
    )
    
    timestamp: datetime = Field(
        default_factory=datetime.now,
        description="检查时间"
    )
    
    database: bool = Field(
        description="数据库连接状态"
    )
    
    redis: Optional[bool] = Field(
        default=None,
        description="Redis连接状态"
    )


class TokenSchema(BaseSchema):
    """
    令牌模式
    """
    
    access_token: str = Field(
        description="访问令牌"
    )
    
    token_type: str = Field(
        default="bearer",
        description="令牌类型"
    )
    
    expires_in: int = Field(
        description="过期时间（秒）"
    )
    
    refresh_token: Optional[str] = Field(
        default=None,
        description="刷新令牌"
    )


class RefreshTokenSchema(BaseSchema):
    """
    刷新令牌请求模式
    """
    
    refresh_token: str = Field(
        description="刷新令牌"
    )


# 更新前向引用
PaginatedResponseSchema.model_rebuild()