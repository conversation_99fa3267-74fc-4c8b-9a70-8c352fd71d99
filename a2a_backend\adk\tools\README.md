# A2A多智能体系统工具包

## 概述

A2A工具包是一个完整的多智能体系统工具生态系统，提供了从基础工具到高级管理功能的全套解决方案。该工具包支持MCP（Model Context Protocol）协议，具备企业级的安全控制、性能监控、插件扩展等功能。

## 核心特性

### 🛠️ 基础工具
- **BaseTool**: 所有工具的基础抽象类
- **WebTool**: HTTP/HTTPS网络请求工具
- **CalculationTool**: 数学计算和表达式求值工具
- 支持自定义工具扩展

### 📋 工具管理
- **ToolRegistry**: 工具注册和发现系统
- **ToolExecutor**: 多模式工具执行引擎
- **ToolAPI**: RESTful API和WebSocket接口
- 支持工具的动态加载和卸载

### 🔒 安全控制
- **SecurityManager**: 统一安全管理
- 多层次权限验证
- 速率限制和IP白名单
- 安全审计和威胁检测

### 💾 缓存系统
- **ToolCache**: 智能缓存管理
- 多种缓存策略（LRU、LFU、TTL等）
- 多种存储后端（内存、Redis、文件等）
- 自动序列化和压缩

### 📊 监控告警
- **ToolMonitor**: 实时性能监控
- 健康检查和指标收集
- 智能告警规则
- 可视化监控面板

### ⚙️ 配置管理
- **ConfigManager**: 统一配置管理
- 多格式支持（JSON、YAML等）
- 环境变量覆盖
- 配置热更新

### 🔌 插件系统
- **PluginManager**: 动态插件管理
- 插件依赖解析
- 热插拔支持
- 插件沙箱隔离

### 🌐 MCP协议支持
- **MCPClient/MCPServer**: MCP协议实现
- **MCPManager**: 统一MCP管理
- 支持工具、资源、提示词
- 多种传输协议（WebSocket、HTTP、STDIO）

## 快速开始

### 安装依赖

```bash
pip install fastapi uvicorn websockets aiohttp redis pydantic sqlalchemy
```

### 基本使用

```python
from adk.tools import (
    create_tool_ecosystem,
    WebTool,
    CalculationTool,
    ToolRegistry
)

# 创建工具生态系统
ecosystem = create_tool_ecosystem()

# 获取组件
registry = ecosystem['tool_registry']
executor = ecosystem['executor']
api = ecosystem['api']

# 注册工具
web_tool = WebTool()
calc_tool = CalculationTool()

registry.register_tool(web_tool)
registry.register_tool(calc_tool)

# 执行工具
result = await executor.execute_tool(
    tool_name="web_tool",
    method="GET",
    url="https://api.example.com/data"
)

print(f"执行结果: {result.data}")
```

### 启动API服务

```python
import uvicorn
from adk.tools import ToolAPI

# 创建API实例
api = ToolAPI()

# 启动服务
if __name__ == "__main__":
    uvicorn.run(
        api.app,
        host="0.0.0.0",
        port=8000,
        log_level="info"
    )
```

### 使用MCP协议

```python
from adk.tools import MCPManager, MCPServer, MCPClient

# 创建MCP管理器
manager = MCPManager()

# 启动MCP服务器
server_config = {
    'name': 'my-server',
    'version': '1.0.0',
    'transport': 'websocket',
    'host': 'localhost',
    'port': 8001
}

await manager.start_server('my-server', server_config)

# 连接MCP客户端
client_config = {
    'server_url': 'ws://localhost:8001',
    'transport': 'websocket'
}

await manager.start_client('my-client', client_config)

# 调用远程工具
result = await manager.call_tool(
    endpoint_name='my-client',
    tool_name='calculator',
    arguments={'expression': '2 + 2'}
)
```

## 架构设计

### 分层架构

```
┌─────────────────────────────────────────┐
│              API Layer                  │
│  (RESTful API, WebSocket, MCP)         │
├─────────────────────────────────────────┤
│            Service Layer                │
│  (ToolExecutor, SecurityManager)       │
├─────────────────────────────────────────┤
│            Core Layer                   │
│  (ToolRegistry, PluginManager)         │
├─────────────────────────────────────────┤
│            Tool Layer                   │
│  (BaseTool, WebTool, CalculationTool)  │
├─────────────────────────────────────────┤
│          Infrastructure Layer          │
│  (Cache, Monitor, Config, Security)    │
└─────────────────────────────────────────┘
```

### 核心组件关系

```mermaid
graph TB
    A[ToolAPI] --> B[ToolExecutor]
    B --> C[ToolRegistry]
    B --> D[SecurityManager]
    B --> E[ToolCache]
    B --> F[ToolMonitor]
    
    C --> G[BaseTool]
    G --> H[WebTool]
    G --> I[CalculationTool]
    
    J[PluginManager] --> C
    K[ConfigManager] --> L[All Components]
    
    M[MCPManager] --> N[MCPServer]
    M --> O[MCPClient]
    N --> C
    O --> C
```

## 配置示例

### 完整配置文件 (config.yaml)

```yaml
# 安全配置
security:
  default_level: MEDIUM
  enable_rate_limiting: true
  rate_limit_requests: 100
  rate_limit_window: 60
  allowed_ips: []
  blocked_ips: []
  require_authentication: true
  session_timeout: 3600

# 缓存配置
cache:
  strategy: LRU
  max_size: 1000
  ttl: 3600
  storage_backend: MEMORY
  enable_compression: true
  serialization_format: JSON

# 监控配置
monitor:
  level: INFO
  enable_metrics: true
  enable_health_checks: true
  health_check_interval: 30
  metrics_retention: 86400
  alert_rules:
    - name: "high_error_rate"
      condition: "error_rate > 0.1"
      level: WARNING
      actions: ["log", "notify"]

# 插件配置
plugin:
  plugin_dirs: ["./plugins"]
  auto_discovery: true
  enable_hot_reload: true
  sandbox_enabled: true
  max_plugins: 50

# API配置
api:
  host: "0.0.0.0"
  port: 8000
  enable_cors: true
  cors_origins: ["*"]
  enable_docs: true
  docs_url: "/docs"
  max_request_size: 10485760

# MCP配置
mcp:
  servers:
    - name: "tool-server"
      transport: "websocket"
      host: "localhost"
      port: 8001
      auto_start: true
  
  clients:
    - name: "external-client"
      server_url: "ws://external-server:8001"
      transport: "websocket"
      auto_connect: true
      retry_attempts: 3
      retry_delay: 5
```

## 工具开发指南

### 创建自定义工具

```python
from adk.tools import BaseTool, ToolResult, ToolConfig
from typing import Dict, Any

class MyCustomTool(BaseTool):
    """自定义工具示例"""
    
    def __init__(self):
        config = ToolConfig(
            name="my_custom_tool",
            description="这是一个自定义工具示例",
            version="1.0.0",
            category="custom",
            tags=["example", "custom"]
        )
        super().__init__(config)
    
    async def _execute(self, **kwargs) -> ToolResult:
        """执行工具逻辑"""
        try:
            # 实现你的工具逻辑
            input_data = kwargs.get('input', '')
            result_data = f"处理结果: {input_data}"
            
            return ToolResult(
                success=True,
                data=result_data,
                message="执行成功"
            )
        except Exception as e:
            return ToolResult(
                success=False,
                error=str(e),
                message="执行失败"
            )
    
    def validate_input(self, **kwargs) -> bool:
        """验证输入参数"""
        return 'input' in kwargs
    
    def get_schema(self) -> Dict[str, Any]:
        """获取工具模式定义"""
        return {
            "type": "object",
            "properties": {
                "input": {
                    "type": "string",
                    "description": "输入数据"
                }
            },
            "required": ["input"]
        }
```

### 创建插件

```python
from adk.tools import IPlugin, PluginMetadata

class MyPlugin(IPlugin):
    """自定义插件示例"""
    
    def get_metadata(self) -> PluginMetadata:
        return PluginMetadata(
            name="my_plugin",
            version="1.0.0",
            description="这是一个自定义插件",
            author="Your Name",
            dependencies=[]
        )
    
    async def initialize(self, config: dict) -> bool:
        """初始化插件"""
        self.config = config
        # 执行初始化逻辑
        return True
    
    async def start(self) -> bool:
        """启动插件"""
        # 执行启动逻辑
        return True
    
    async def stop(self) -> bool:
        """停止插件"""
        # 执行停止逻辑
        return True
    
    async def cleanup(self) -> bool:
        """清理插件"""
        # 执行清理逻辑
        return True
```

## API文档

### RESTful API端点

- `GET /tools` - 获取所有工具列表
- `GET /tools/{tool_name}` - 获取特定工具信息
- `POST /tools/{tool_name}/execute` - 执行工具
- `POST /tools/batch` - 批量执行工具
- `GET /executions/{execution_id}` - 获取执行状态
- `DELETE /executions/{execution_id}` - 取消执行
- `GET /health` - 健康检查
- `GET /metrics` - 获取监控指标

### WebSocket API

连接到 `/ws` 端点进行实时通信：

```javascript
const ws = new WebSocket('ws://localhost:8000/ws');

// 发送工具执行请求
ws.send(JSON.stringify({
    type: 'execute_tool',
    tool_name: 'web_tool',
    arguments: {
        method: 'GET',
        url: 'https://api.example.com/data'
    }
}));

// 接收执行结果
ws.onmessage = (event) => {
    const result = JSON.parse(event.data);
    console.log('执行结果:', result);
};
```

## 性能优化

### 缓存策略

1. **LRU缓存**: 适用于访问模式相对稳定的场景
2. **TTL缓存**: 适用于数据有时效性的场景
3. **自适应缓存**: 根据访问模式自动调整策略

### 并发控制

1. **异步执行**: 所有工具支持异步执行
2. **批量处理**: 支持批量工具执行
3. **连接池**: 数据库和网络连接复用
4. **限流控制**: 防止系统过载

### 监控指标

- 工具执行次数和耗时
- 成功率和错误率
- 内存和CPU使用率
- 缓存命中率
- 并发连接数

## 故障排除

### 常见问题

1. **工具注册失败**
   - 检查工具名称是否重复
   - 验证工具配置是否正确
   - 确认权限设置

2. **执行超时**
   - 调整超时配置
   - 检查网络连接
   - 优化工具逻辑

3. **缓存问题**
   - 检查缓存配置
   - 验证序列化格式
   - 清理过期缓存

4. **权限错误**
   - 检查用户权限配置
   - 验证认证信息
   - 查看安全日志

### 日志分析

```python
import logging

# 启用详细日志
logging.getLogger('adk.tools').setLevel(logging.DEBUG)

# 查看特定组件日志
logging.getLogger('adk.tools.executor').setLevel(logging.DEBUG)
logging.getLogger('adk.tools.security').setLevel(logging.DEBUG)
```

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

### 开发环境设置

```bash
# 克隆项目
git clone https://github.com/a2a-team/a2a-tools.git
cd a2a-tools

# 安装开发依赖
pip install -e .[dev]

# 运行测试
pytest tests/

# 代码格式化
black adk/
flake8 adk/
```

## 许可证

MIT License - 详见 LICENSE 文件

## 联系我们

- 邮箱: <EMAIL>
- 官网: https://a2a.ai
- 文档: https://docs.a2a.ai/tools
- GitHub: https://github.com/a2a-team/a2a-tools