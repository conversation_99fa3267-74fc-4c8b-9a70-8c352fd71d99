# -*- coding: utf-8 -*-
"""
A2A多智能体系统SSE流式接口

基于Google ADK的服务器发送事件(SSE)实时通信接口
"""

import asyncio
import json
import time
from datetime import datetime
from typing import Dict, List, Optional, Any, AsyncGenerator
from contextlib import asynccontextmanager

from fastapi import APIRouter, Depends, HTTPException, Request, Response, Query, Path
from fastapi.responses import StreamingResponse
from sqlalchemy.orm import Session
from pydantic import BaseModel, Field
from loguru import logger

from ...core.database import get_db
from ...auth.dependencies import get_current_user, verify_token
from ...models import User
from ...services.stream_service import (
    stream_service,
    StreamConfig,
    StreamType,
    StreamStatus,
    CompressionType,
    StreamChunk
)
from ...services.user_service import UserService
from ...services.session_service import SessionService
from ...services.agent_service import AgentService


router = APIRouter(prefix="/stream", tags=["流式输出"])


class SSEConnectionRequest(BaseModel):
    """SSE连接请求模型"""
    session_id: Optional[str] = Field(None, description="会话ID")
    agent_id: Optional[str] = Field(None, description="智能体ID")
    buffer_size: int = Field(1024, ge=256, le=10240, description="缓冲区大小")
    max_buffer_size: int = Field(10240, ge=1024, le=102400, description="最大缓冲区大小")
    compression: CompressionType = Field(CompressionType.NONE, description="压缩类型")
    heartbeat_interval: int = Field(30, ge=10, le=300, description="心跳间隔(秒)")
    timeout: int = Field(300, ge=60, le=3600, description="超时时间(秒)")
    auto_reconnect: bool = Field(True, description="自动重连")
    max_reconnect_attempts: int = Field(3, ge=1, le=10, description="最大重连次数")
    reconnect_delay: float = Field(1.0, ge=0.1, le=10.0, description="重连延迟(秒)")
    enable_persistence: bool = Field(True, description="启用持久化")
    chunk_size: int = Field(1024, ge=256, le=8192, description="数据块大小")
    rate_limit: int = Field(1000, ge=100, le=10000, description="速率限制(每分钟)")
    priority: int = Field(1, ge=1, le=10, description="优先级")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="元数据")


class SSEConnectionResponse(BaseModel):
    """SSE连接响应模型"""
    connection_id: str = Field(..., description="连接ID")
    status: StreamStatus = Field(..., description="连接状态")
    created_at: datetime = Field(..., description="创建时间")
    config: Dict[str, Any] = Field(..., description="连接配置")
    stream_url: str = Field(..., description="流式URL")


class SSEMessageRequest(BaseModel):
    """SSE消息请求模型"""
    data: Any = Field(..., description="消息数据")
    message_type: StreamType = Field(StreamType.TEXT, description="消息类型")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="元数据")


class SSEStatusResponse(BaseModel):
    """SSE状态响应模型"""
    connection_id: str = Field(..., description="连接ID")
    status: StreamStatus = Field(..., description="连接状态")
    user_id: int = Field(..., description="用户ID")
    session_id: Optional[str] = Field(None, description="会话ID")
    agent_id: Optional[str] = Field(None, description="智能体ID")
    created_at: datetime = Field(..., description="创建时间")
    last_activity: datetime = Field(..., description="最后活动时间")
    total_chunks: int = Field(..., description="总数据块数")
    total_bytes: int = Field(..., description="总字节数")
    error_count: int = Field(..., description="错误次数")
    reconnect_count: int = Field(..., description="重连次数")
    buffer_size: int = Field(..., description="缓冲区大小")
    buffer_usage: float = Field(..., description="缓冲区使用率")


class SSEMetricsResponse(BaseModel):
    """SSE指标响应模型"""
    total_connections: int = Field(..., description="总连接数")
    active_connections: int = Field(..., description="活跃连接数")
    total_chunks: int = Field(..., description="总数据块数")
    total_bytes: int = Field(..., description="总字节数")
    error_count: int = Field(..., description="错误次数")
    reconnect_count: int = Field(..., description="重连次数")
    connection_count_by_status: Dict[str, int] = Field(..., description="按状态分组的连接数")
    buffer_usage: Dict[str, Any] = Field(..., description="缓冲区使用情况")
    timestamp: datetime = Field(..., description="时间戳")


# SSE连接管理

@router.post("/connect", response_model=SSEConnectionResponse, summary="创建SSE连接")
async def create_sse_connection(
    request: SSEConnectionRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    创建SSE流式连接
    
    创建一个新的服务器发送事件连接，用于实时数据推送。
    支持用户认证、权限验证、会话管理和智能体绑定。
    """
    try:
        # 创建流式配置
        config = StreamConfig(
            user_id=current_user.user_id,
            session_id=request.session_id,
            agent_id=request.agent_id,
            buffer_size=request.buffer_size,
            max_buffer_size=request.max_buffer_size,
            compression=request.compression,
            heartbeat_interval=request.heartbeat_interval,
            timeout=request.timeout,
            auto_reconnect=request.auto_reconnect,
            max_reconnect_attempts=request.max_reconnect_attempts,
            reconnect_delay=request.reconnect_delay,
            enable_persistence=request.enable_persistence,
            chunk_size=request.chunk_size,
            rate_limit=request.rate_limit,
            priority=request.priority,
            metadata=request.metadata
        )
        
        # 创建流式连接
        connection_id = await stream_service.create_stream(
            user_id=current_user.user_id,
            config=config,
            db=db
        )
        
        # 获取连接信息
        connection = await stream_service.get_connection_info(connection_id)
        if not connection:
            raise HTTPException(status_code=500, detail="创建连接失败")
        
        # 构建流式URL
        stream_url = f"/api/v1/stream/{connection_id}/events"
        
        return SSEConnectionResponse(
            connection_id=connection_id,
            status=connection.status,
            created_at=connection.created_at,
            config={
                "buffer_size": config.buffer_size,
                "max_buffer_size": config.max_buffer_size,
                "compression": config.compression.value,
                "heartbeat_interval": config.heartbeat_interval,
                "timeout": config.timeout,
                "auto_reconnect": config.auto_reconnect,
                "enable_persistence": config.enable_persistence
            },
            stream_url=stream_url
        )
        
    except PermissionError as e:
        raise HTTPException(status_code=403, detail=str(e))
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"创建SSE连接失败: {e}")
        raise HTTPException(status_code=500, detail="创建SSE连接失败")


@router.get("/{connection_id}/events", summary="SSE事件流")
async def sse_event_stream(
    connection_id: str = Path(..., description="连接ID"),
    request: Request = None,
    current_user: User = Depends(get_current_user)
):
    """
    SSE事件流端点
    
    建立服务器发送事件连接，实时推送数据到客户端。
    支持自动重连、心跳检测、数据压缩和错误恢复。
    """
    try:
        # 验证连接权限
        connection = await stream_service.get_connection_info(connection_id)
        if not connection:
            raise HTTPException(status_code=404, detail="连接不存在")
        
        if connection.user_id != current_user.user_id:
            raise HTTPException(status_code=403, detail="无权访问此连接")
        
        # 检查连接状态
        if connection.status not in [StreamStatus.CONNECTED, StreamStatus.STREAMING]:
            raise HTTPException(status_code=400, detail=f"连接状态无效: {connection.status}")
        
        # 更新连接状态为流式传输
        await stream_service._update_connection_status(connection_id, StreamStatus.STREAMING)
        
        # 创建SSE响应
        async def event_generator():
            """SSE事件生成器"""
            try:
                # 发送连接建立事件
                yield f"event: connected\n"
                yield f"data: {{\"connection_id\": \"{connection_id}\", \"timestamp\": \"{datetime.utcnow().isoformat()}\", \"status\": \"connected\"}}\n\n"
                
                # 流式数据生成器
                async for data in stream_service.stream_generator(
                    connection_id,
                    format_func=_format_sse_data
                ):
                    # 检查客户端是否断开连接
                    if await request.is_disconnected():
                        logger.info(f"客户端断开连接: {connection_id}")
                        break
                    
                    yield data
                    
            except asyncio.CancelledError:
                logger.info(f"SSE连接被取消: {connection_id}")
            except Exception as e:
                logger.error(f"SSE事件生成器错误: {e}")
                # 发送错误事件
                error_data = {
                    "error": str(e),
                    "timestamp": datetime.utcnow().isoformat(),
                    "connection_id": connection_id
                }
                yield f"event: error\n"
                yield f"data: {json.dumps(error_data)}\n\n"
            finally:
                # 更新连接状态
                await stream_service._update_connection_status(connection_id, StreamStatus.DISCONNECTED)
                # 发送断开连接事件
                disconnect_data = {
                    "connection_id": connection_id,
                    "timestamp": datetime.utcnow().isoformat(),
                    "status": "disconnected"
                }
                yield f"event: disconnected\n"
                yield f"data: {json.dumps(disconnect_data)}\n\n"
        
        # 返回流式响应
        return StreamingResponse(
            event_generator(),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Headers": "Cache-Control",
                "X-Accel-Buffering": "no"  # 禁用nginx缓冲
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"SSE事件流失败: {e}")
        raise HTTPException(status_code=500, detail="SSE事件流失败")


@router.post("/{connection_id}/send", summary="发送SSE消息")
async def send_sse_message(
    connection_id: str = Path(..., description="连接ID"),
    request: SSEMessageRequest = None,
    current_user: User = Depends(get_current_user)
):
    """
    发送SSE消息
    
    向指定的SSE连接发送消息数据。
    支持多种数据类型、元数据和优先级控制。
    """
    try:
        # 验证连接权限
        connection = await stream_service.get_connection_info(connection_id)
        if not connection:
            raise HTTPException(status_code=404, detail="连接不存在")
        
        if connection.user_id != current_user.user_id:
            raise HTTPException(status_code=403, detail="无权访问此连接")
        
        # 检查连接状态
        if connection.status not in [StreamStatus.CONNECTED, StreamStatus.STREAMING]:
            raise HTTPException(status_code=400, detail=f"连接状态无效: {connection.status}")
        
        # 发送数据块
        success = await stream_service.send_chunk(
            connection_id=connection_id,
            data=request.data,
            chunk_type=request.message_type,
            metadata=request.metadata
        )
        
        if not success:
            raise HTTPException(status_code=500, detail="发送消息失败")
        
        return {
            "success": True,
            "connection_id": connection_id,
            "message_type": request.message_type.value,
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"发送SSE消息失败: {e}")
        raise HTTPException(status_code=500, detail="发送SSE消息失败")


@router.delete("/{connection_id}", summary="断开SSE连接")
async def disconnect_sse_connection(
    connection_id: str = Path(..., description="连接ID"),
    current_user: User = Depends(get_current_user)
):
    """
    断开SSE连接
    
    主动断开指定的SSE连接，清理相关资源。
    """
    try:
        # 验证连接权限
        connection = await stream_service.get_connection_info(connection_id)
        if not connection:
            raise HTTPException(status_code=404, detail="连接不存在")
        
        if connection.user_id != current_user.user_id:
            raise HTTPException(status_code=403, detail="无权访问此连接")
        
        # 断开连接
        success = await stream_service.disconnect_stream(connection_id)
        
        if not success:
            raise HTTPException(status_code=500, detail="断开连接失败")
        
        return {
            "success": True,
            "connection_id": connection_id,
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"断开SSE连接失败: {e}")
        raise HTTPException(status_code=500, detail="断开SSE连接失败")


# 连接状态和监控

@router.get("/{connection_id}/status", response_model=SSEStatusResponse, summary="获取SSE连接状态")
async def get_sse_connection_status(
    connection_id: str = Path(..., description="连接ID"),
    current_user: User = Depends(get_current_user)
):
    """
    获取SSE连接状态
    
    查询指定SSE连接的详细状态信息，包括统计数据和配置信息。
    """
    try:
        # 验证连接权限
        connection = await stream_service.get_connection_info(connection_id)
        if not connection:
            raise HTTPException(status_code=404, detail="连接不存在")
        
        if connection.user_id != current_user.user_id:
            raise HTTPException(status_code=403, detail="无权访问此连接")
        
        # 获取缓冲区信息
        buffer = stream_service.buffers.get(connection_id)
        buffer_size = buffer.size() if buffer else 0
        buffer_usage = (buffer.total_size / buffer.max_size) if buffer else 0.0
        
        return SSEStatusResponse(
            connection_id=connection.connection_id,
            status=connection.status,
            user_id=connection.user_id,
            session_id=connection.session_id,
            agent_id=connection.agent_id,
            created_at=connection.created_at,
            last_activity=connection.last_activity,
            total_chunks=connection.total_chunks,
            total_bytes=connection.total_bytes,
            error_count=connection.error_count,
            reconnect_count=connection.reconnect_count,
            buffer_size=buffer_size,
            buffer_usage=buffer_usage
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取SSE连接状态失败: {e}")
        raise HTTPException(status_code=500, detail="获取SSE连接状态失败")


@router.get("/connections", response_model=List[SSEStatusResponse], summary="获取用户SSE连接列表")
async def get_user_sse_connections(
    current_user: User = Depends(get_current_user),
    session_id: Optional[str] = Query(None, description="会话ID过滤"),
    agent_id: Optional[str] = Query(None, description="智能体ID过滤"),
    status: Optional[StreamStatus] = Query(None, description="状态过滤")
):
    """
    获取用户SSE连接列表
    
    查询当前用户的所有SSE连接，支持按会话、智能体和状态过滤。
    """
    try:
        # 获取用户连接
        connections = await stream_service.get_user_connections(current_user.user_id)
        
        # 应用过滤条件
        if session_id:
            connections = [conn for conn in connections if conn.session_id == session_id]
        
        if agent_id:
            connections = [conn for conn in connections if conn.agent_id == agent_id]
        
        if status:
            connections = [conn for conn in connections if conn.status == status]
        
        # 构建响应
        result = []
        for connection in connections:
            buffer = stream_service.buffers.get(connection.connection_id)
            buffer_size = buffer.size() if buffer else 0
            buffer_usage = (buffer.total_size / buffer.max_size) if buffer else 0.0
            
            result.append(SSEStatusResponse(
                connection_id=connection.connection_id,
                status=connection.status,
                user_id=connection.user_id,
                session_id=connection.session_id,
                agent_id=connection.agent_id,
                created_at=connection.created_at,
                last_activity=connection.last_activity,
                total_chunks=connection.total_chunks,
                total_bytes=connection.total_bytes,
                error_count=connection.error_count,
                reconnect_count=connection.reconnect_count,
                buffer_size=buffer_size,
                buffer_usage=buffer_usage
            ))
        
        return result
        
    except Exception as e:
        logger.error(f"获取用户SSE连接列表失败: {e}")
        raise HTTPException(status_code=500, detail="获取用户SSE连接列表失败")


@router.get("/metrics", response_model=SSEMetricsResponse, summary="获取SSE服务指标")
async def get_sse_metrics(
    current_user: User = Depends(get_current_user)
):
    """
    获取SSE服务指标
    
    查询SSE服务的性能指标和统计信息。
    需要管理员权限。
    """
    try:
        # 检查管理员权限
        if not current_user.is_admin:
            raise HTTPException(status_code=403, detail="需要管理员权限")
        
        # 获取指标
        metrics = await stream_service.get_metrics()
        
        return SSEMetricsResponse(
            total_connections=metrics["total_connections"],
            active_connections=metrics["active_connections"],
            total_chunks=metrics["total_chunks"],
            total_bytes=metrics["total_bytes"],
            error_count=metrics["error_count"],
            reconnect_count=metrics["reconnect_count"],
            connection_count_by_status=metrics["connection_count_by_status"],
            buffer_usage=metrics["buffer_usage"],
            timestamp=datetime.fromisoformat(metrics["timestamp"])
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取SSE服务指标失败: {e}")
        raise HTTPException(status_code=500, detail="获取SSE服务指标失败")


# 批量操作

@router.post("/broadcast", summary="广播SSE消息")
async def broadcast_sse_message(
    request: SSEMessageRequest,
    current_user: User = Depends(get_current_user),
    session_id: Optional[str] = Query(None, description="会话ID过滤"),
    agent_id: Optional[str] = Query(None, description="智能体ID过滤")
):
    """
    广播SSE消息
    
    向用户的所有SSE连接广播消息，支持按会话和智能体过滤。
    """
    try:
        # 获取用户连接
        connections = await stream_service.get_user_connections(current_user.user_id)
        
        # 应用过滤条件
        if session_id:
            connections = [conn for conn in connections if conn.session_id == session_id]
        
        if agent_id:
            connections = [conn for conn in connections if conn.agent_id == agent_id]
        
        # 过滤活跃连接
        active_connections = [
            conn for conn in connections
            if conn.status in [StreamStatus.CONNECTED, StreamStatus.STREAMING]
        ]
        
        # 广播消息
        success_count = 0
        for connection in active_connections:
            success = await stream_service.send_chunk(
                connection_id=connection.connection_id,
                data=request.data,
                chunk_type=request.message_type,
                metadata=request.metadata
            )
            if success:
                success_count += 1
        
        return {
            "success": True,
            "total_connections": len(active_connections),
            "success_count": success_count,
            "failed_count": len(active_connections) - success_count,
            "message_type": request.message_type.value,
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"广播SSE消息失败: {e}")
        raise HTTPException(status_code=500, detail="广播SSE消息失败")


@router.delete("/connections", summary="断开所有SSE连接")
async def disconnect_all_sse_connections(
    current_user: User = Depends(get_current_user),
    session_id: Optional[str] = Query(None, description="会话ID过滤"),
    agent_id: Optional[str] = Query(None, description="智能体ID过滤")
):
    """
    断开所有SSE连接
    
    断开用户的所有SSE连接，支持按会话和智能体过滤。
    """
    try:
        # 获取用户连接
        connections = await stream_service.get_user_connections(current_user.user_id)
        
        # 应用过滤条件
        if session_id:
            connections = [conn for conn in connections if conn.session_id == session_id]
        
        if agent_id:
            connections = [conn for conn in connections if conn.agent_id == agent_id]
        
        # 断开连接
        success_count = 0
        for connection in connections:
            success = await stream_service.disconnect_stream(connection.connection_id)
            if success:
                success_count += 1
        
        return {
            "success": True,
            "total_connections": len(connections),
            "success_count": success_count,
            "failed_count": len(connections) - success_count,
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"断开所有SSE连接失败: {e}")
        raise HTTPException(status_code=500, detail="断开所有SSE连接失败")


# 辅助函数

def _format_sse_data(chunk: StreamChunk) -> str:
    """
    格式化SSE数据
    
    Args:
        chunk: 数据块
    
    Returns:
        str: 格式化的SSE数据
    """
    try:
        # 构建SSE事件
        event_type = chunk.chunk_type.value
        data = chunk.to_dict()
        
        # 格式化为SSE格式
        sse_data = f"event: {event_type}\n"
        sse_data += f"id: {chunk.chunk_id}\n"
        sse_data += f"data: {json.dumps(data, ensure_ascii=False)}\n\n"
        
        return sse_data
        
    except Exception as e:
        logger.error(f"格式化SSE数据失败: {e}")
        # 返回错误事件
        error_data = {
            "error": str(e),
            "timestamp": datetime.utcnow().isoformat(),
            "chunk_id": chunk.chunk_id if chunk else "unknown"
        }
        return f"event: error\ndata: {json.dumps(error_data)}\n\n"


@asynccontextmanager
async def sse_connection_manager(connection_id: str):
    """
    SSE连接管理器上下文
    
    Args:
        connection_id: 连接ID
    """
    try:
        # 连接建立
        logger.info(f"SSE连接建立: {connection_id}")
        yield connection_id
    except Exception as e:
        logger.error(f"SSE连接错误: {e}")
        raise
    finally:
        # 连接清理
        try:
            await stream_service.disconnect_stream(connection_id)
            logger.info(f"SSE连接清理: {connection_id}")
        except Exception as e:
            logger.error(f"SSE连接清理失败: {e}")