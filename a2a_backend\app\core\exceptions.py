#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A2A多智能体系统异常定义

定义系统中使用的自定义异常类
"""

from typing import Optional, Dict, Any
from fastapi import HTTPException, status


class A2AException(HTTPException):
    """
    A2A系统基础异常类
    
    继承自FastAPI的HTTPException，提供统一的异常处理
    """
    
    def __init__(
        self,
        status_code: int = status.HTTP_500_INTERNAL_SERVER_ERROR,
        detail: str = "系统内部错误",
        error_code: Optional[str] = None,
        error_data: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None
    ):
        """
        初始化A2A异常
        
        Args:
            status_code: HTTP状态码
            detail: 错误详情
            error_code: 错误代码
            error_data: 错误数据
            headers: 响应头
        """
        super().__init__(status_code=status_code, detail=detail, headers=headers)
        self.error_code = error_code
        self.error_data = error_data or {}


class ValidationException(A2AException):
    """
    数据验证异常
    """
    
    def __init__(self, detail: str = "数据验证失败", error_data: Optional[Dict[str, Any]] = None):
        super().__init__(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=detail,
            error_code="VALIDATION_ERROR",
            error_data=error_data
        )


class AuthenticationException(A2AException):
    """
    认证异常
    """
    
    def __init__(self, detail: str = "认证失败", error_data: Optional[Dict[str, Any]] = None):
        super().__init__(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=detail,
            error_code="AUTHENTICATION_ERROR",
            error_data=error_data
        )


class AuthorizationException(A2AException):
    """
    授权异常
    """
    
    def __init__(self, detail: str = "权限不足", error_data: Optional[Dict[str, Any]] = None):
        super().__init__(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=detail,
            error_code="AUTHORIZATION_ERROR",
            error_data=error_data
        )


class ResourceNotFoundException(A2AException):
    """
    资源未找到异常
    """
    
    def __init__(self, detail: str = "资源未找到", error_data: Optional[Dict[str, Any]] = None):
        super().__init__(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=detail,
            error_code="RESOURCE_NOT_FOUND",
            error_data=error_data
        )


class BusinessLogicException(A2AException):
    """
    业务逻辑异常
    """
    
    def __init__(self, detail: str = "业务逻辑错误", error_data: Optional[Dict[str, Any]] = None):
        super().__init__(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=detail,
            error_code="BUSINESS_LOGIC_ERROR",
            error_data=error_data
        )


class DatabaseException(A2AException):
    """
    数据库异常
    """
    
    def __init__(self, detail: str = "数据库操作失败", error_data: Optional[Dict[str, Any]] = None):
        super().__init__(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=detail,
            error_code="DATABASE_ERROR",
            error_data=error_data
        )


class ExternalServiceException(A2AException):
    """
    外部服务异常
    """
    
    def __init__(self, detail: str = "外部服务调用失败", error_data: Optional[Dict[str, Any]] = None):
        super().__init__(
            status_code=status.HTTP_502_BAD_GATEWAY,
            detail=detail,
            error_code="EXTERNAL_SERVICE_ERROR",
            error_data=error_data
        )


class RateLimitException(A2AException):
    """
    速率限制异常
    """
    
    def __init__(self, detail: str = "请求频率过高", error_data: Optional[Dict[str, Any]] = None):
        super().__init__(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail=detail,
            error_code="RATE_LIMIT_ERROR",
            error_data=error_data
        )


class ConfigurationException(A2AException):
    """
    配置异常
    """
    
    def __init__(self, detail: str = "配置错误", error_data: Optional[Dict[str, Any]] = None):
        super().__init__(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=detail,
            error_code="CONFIGURATION_ERROR",
            error_data=error_data
        )