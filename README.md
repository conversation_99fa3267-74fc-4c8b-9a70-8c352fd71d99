# A2A多智能体系统

基于Google ADK和FastAPI的多智能体协作系统，支持智能体管理、工作流编排、任务执行和实时通信。

## 🚀 快速开始

### 环境要求

- **Python**: 3.12+
- **数据库**: MySQL 8.0+ 或 PostgreSQL 13+
- **缓存**: Redis 6.0+
- **操作系统**: Windows 11, macOS 10.15+, Ubuntu 20.04+

### 安装步骤

#### 1. 克隆项目
```bash
git clone <repository-url>
cd A2A
```

#### 2. 创建虚拟环境
```bash
# Windows (使用Conda)
conda create -n a2a_backend python=3.12
conda activate a2a_backend

# macOS/Linux
python -m venv a2a_backend
source a2a_backend/bin/activate
```

#### 3. 安装依赖
```bash
cd a2a_backend
pip install -r requirements.txt
```

#### 4. 配置环境变量
```bash
# 复制配置模板
cp .env.example .env

# 编辑配置文件
# 修改数据库连接、JWT密钥等配置
```

#### 5. 配置Google ADK
```bash
# 创建凭证目录
mkdir credentials

# 将Google Cloud凭证文件复制到credentials目录
cp /path/to/google-credentials.json ./credentials/

# 设置环境变量
export GOOGLE_APPLICATION_CREDENTIALS=./credentials/google-credentials.json
```

#### 6. 初始化数据库
```bash
# 创建数据库表结构和管理员用户
python scripts/init_database.py

# 或者单独创建管理员用户
python scripts/create_admin.py
```

#### 7. 启动应用
```bash
# 开发模式
python main.py

# 或使用启动脚本（包含健康检查）
python scripts/start.py

# 生产模式
python scripts/start.py --mode production
```

### 验证安装

访问以下URL验证系统是否正常运行：

- **健康检查**: http://localhost:8000/health
- **API文档**: http://localhost:8000/docs
- **ReDoc文档**: http://localhost:8000/redoc

## 📚 文档

- [API接口文档](docs/API接口文档.md) - 完整的API接口说明
- [系统初始化指南](docs/系统初始化指南.md) - 详细的安装和配置指南

## 🏗️ 系统架构

```
A2A多智能体系统
├── 认证模块 (JWT认证、权限管理)
├── 用户管理 (用户CRUD、角色权限)
├── 智能体管理 (智能体创建、配置、执行)
├── 会话管理 (多用户会话、消息处理)
├── 任务管理 (任务调度、执行监控)
├── 工作流管理 (流程编排、状态管理)
├── 流式输出 (实时响应、SSE)
├── WebSocket通信 (实时消息推送)
├── 配置管理 (系统配置、用户偏好)
├── 工件管理 (文件上传、存储)
└── 监控管理 (系统监控、性能统计)
```

## 🔧 主要功能

### 智能体管理
- 创建和配置多种类型的智能体
- 支持Google Gemini等LLM模型
- 智能体执行和性能监控

### 工作流编排
- 可视化工作流设计
- 智能体协作和任务分发
- 流程状态管理和监控

### 实时通信
- WebSocket实时消息推送
- 流式输出支持
- 多用户协作会话

### 权限管理
- 基于角色的访问控制
- 细粒度权限设置
- 用户活动审计

## 🛠️ 技术栈

- **后端框架**: FastAPI
- **数据库**: MySQL/PostgreSQL + SQLAlchemy
- **缓存**: Redis
- **认证**: JWT
- **LLM集成**: Google ADK (Gemini)
- **异步处理**: asyncio
- **API文档**: OpenAPI/Swagger
- **日志**: loguru
- **配置管理**: Pydantic Settings

## 📝 API示例

### 用户认证
```python
import requests

# 用户登录
response = requests.post("http://localhost:8000/api/v1/auth/login", json={
    "username": "admin",
    "password": "Admin123!@#"
})
token = response.json()["access_token"]

# 使用令牌访问API
headers = {"Authorization": f"Bearer {token}"}
```

### 创建智能体
```python
agent_data = {
    "name": "助手智能体",
    "description": "一个有用的AI助手",
    "agent_type": "chat",
    "config": {
        "model": "gemini-pro",
        "temperature": 0.7,
        "max_tokens": 1000
    }
}

response = requests.post(
    "http://localhost:8000/api/v1/agents",
    json=agent_data,
    headers=headers
)
```

### 执行智能体
```python
execute_data = {
    "input_data": {
        "message": "你好，请介绍一下自己"
    }
}

response = requests.post(
    f"http://localhost:8000/api/v1/agents/{agent_id}/execute",
    json=execute_data,
    headers=headers
)
```

## 🔒 安全配置

### 生产环境建议
1. 使用强密码和安全的JWT密钥
2. 启用HTTPS
3. 配置防火墙规则
4. 定期更新依赖包
5. 设置访问日志和监控

### 环境变量安全
```bash
# 生成安全的JWT密钥
python -c "import secrets; print(secrets.token_urlsafe(32))"

# 设置强密码策略
PASSWORD_BCRYPT_ROUNDS=12
```

## 🐛 故障排除

### 常见问题

1. **数据库连接失败**
   ```bash
   # 检查数据库服务状态
   sudo systemctl status mysql
   
   # 验证连接字符串
   python -c "from app.core.database import get_database_manager; print('连接测试')"
   ```

2. **Google ADK认证失败**
   ```bash
   # 检查凭证文件
   ls -la credentials/google-credentials.json
   
   # 验证环境变量
   echo $GOOGLE_APPLICATION_CREDENTIALS
   ```

3. **端口占用**
   ```bash
   # 查看端口占用
   netstat -tulpn | grep :8000
   
   # 更换端口
   uvicorn main:app --port 8001
   ```

## 📈 性能优化

- 使用连接池管理数据库连接
- 启用Redis缓存
- 配置负载均衡
- 优化数据库索引
- 使用异步处理

## 🤝 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 📄 许可证

本项目采用MIT许可证 - 查看[LICENSE](LICENSE)文件了解详情。

## 📞 支持

如有问题或建议，请：

1. 查看[文档](docs/)
2. 提交[Issue](issues)
3. 联系技术支持

---

**注意**: 这是一个开发版本，请在生产环境使用前进行充分测试。
