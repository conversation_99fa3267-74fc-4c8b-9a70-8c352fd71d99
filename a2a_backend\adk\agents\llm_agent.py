# Copyright 2024 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

from __future__ import annotations

import inspect
import logging
from typing import Any, AsyncGenerator, Awaitable, Callable, Literal, Optional, Union
from pydantic import BaseModel, Field, field_validator, model_validator
from typing_extensions import override, TypeAlias

from .base_agent import BaseAgent
from .invocation_context import InvocationContext
from ..events.event import Event

logger = logging.getLogger('adk.' + __name__)

# Type aliases for callbacks
_SingleBeforeModelCallback: TypeAlias = Callable[
    [Any, Any],
    Union[Awaitable[Optional[Any]], Optional[Any]],
]

BeforeModelCallback: TypeAlias = Union[
    _SingleBeforeModelCallback,
    list[_SingleBeforeModelCallback],
]

_SingleAfterModelCallback: TypeAlias = Callable[
    [Any, Any],
    Union[Awaitable[Optional[Any]], Optional[Any]],
]

AfterModelCallback: TypeAlias = Union[
    _SingleAfterModelCallback,
    list[_SingleAfterModelCallback],
]

_SingleBeforeToolCallback: TypeAlias = Callable[
    [Any, dict[str, Any], Any],
    Union[Awaitable[Optional[dict]], Optional[dict]],
]

BeforeToolCallback: TypeAlias = Union[
    _SingleBeforeToolCallback,
    list[_SingleBeforeToolCallback],
]

_SingleAfterToolCallback: TypeAlias = Callable[
    [Any, dict[str, Any], Any, dict],
    Union[Awaitable[Optional[dict]], Optional[dict]],
]

AfterToolCallback: TypeAlias = Union[
    _SingleAfterToolCallback,
    list[_SingleAfterToolCallback],
]

InstructionProvider: TypeAlias = Callable[
    [Any], Union[str, Awaitable[str]]
]

ToolUnion: TypeAlias = Union[Callable, Any]
ExamplesUnion = Union[list[Any], Any]


class LlmAgent(BaseAgent):
    """LLM-based Agent."""

    model: Union[str, Any] = ''
    """The model to use for the agent.

    When not set, the agent will inherit the model from its ancestor.
    """

    instruction: Union[str, InstructionProvider] = ''
    """Instructions for the LLM model, guiding the agent's behavior."""

    global_instruction: Union[str, InstructionProvider] = ''
    """Instructions for all the agents in the entire agent tree.

    ONLY the global_instruction in root agent will take effect.

    For example: use global_instruction to make all agents have a stable identity
    or personality.
    """

    tools: list[ToolUnion] = Field(default_factory=list)
    """Tools available to this agent."""

    generate_content_config: Optional[Any] = None
    """The additional content generation configurations.

    NOTE: not all fields are usable, e.g. tools must be configured via `tools`,
    thinking_config must be configured via `planner` in LlmAgent.

    For example: use this config to adjust model temperature, configure safety
    settings, etc.
    """

    # LLM-based agent transfer configs - Start
    disallow_transfer_to_parent: bool = False
    """Disallows LLM-controlled transferring to the parent agent.

    NOTE: Setting this as True also prevents this agent to continue reply to the
    end-user. This behavior prevents one-way transfer, in which end-user may be
    stuck with one agent that cannot transfer to other agents in the agent tree.
    """
    disallow_transfer_to_peers: bool = False
    """Disallows LLM-controlled transferring to the peer agents."""
    # LLM-based agent transfer configs - End

    include_contents: Literal['default', 'none'] = 'default'
    """Whether to include contents in the model request.

    When set to 'none', the model request will not include any contents, such as
    user messages, tool results, etc.
    """

    # Controlled input/output configurations - Start
    input_schema: Optional[type[BaseModel]] = None
    """The input schema when agent is used as a tool."""
    output_schema: Optional[type[BaseModel]] = None
    """The output schema when agent replies.

    NOTE: when this is set, agent can ONLY reply and CANNOT use any tools, such as
    function tools, RAGs, agent transfer, etc.
    """
    output_key: Optional[str] = None
    """The key in session state to store the output of the agent.

    Typically use cases:
    - Extracts agent reply for later use, such as in tools, callbacks, etc.
    - Connects agents to coordinate with each other.
    """
    # Controlled input/output configurations - End

    # Advance features - Start
    planner: Optional[Any] = None
    """Instructs the agent to make a plan and execute it step by step.

    NOTE: to use model's built-in thinking features, set the `thinking_config`
    field in `google.adk.planners.built_in_planner`.
    """

    code_executor: Optional[Any] = None
    """Allow agent to execute code blocks from model responses using the provided
    CodeExecutor.

    NOTE: to use model's built-in code executor, use the `BuiltInCodeExecutor`.
    """
    # Advance features - End

    # Callbacks - Start
    before_model_callback: Optional[BeforeModelCallback] = None
    """Callback or list of callbacks to be called before calling the LLM."""
    
    after_model_callback: Optional[AfterModelCallback] = None
    """Callback or list of callbacks to be called after calling the LLM."""
    
    before_tool_callback: Optional[BeforeToolCallback] = None
    """Callback or list of callbacks to be called before calling the tool."""
    
    after_tool_callback: Optional[AfterToolCallback] = None
    """Callback or list of callbacks to be called after calling the tool."""
    # Callbacks - End

    @override
    async def _run_async_impl(
        self, ctx: InvocationContext
    ) -> AsyncGenerator[Event, None]:
        """异步运行LLM代理的实现。
        
        Args:
            ctx: 调用上下文
            
        Yields:
            Event: 生成的事件
        """
        # 简化实现：创建一个基本的响应事件
        event = Event(
            invocation_id=ctx.invocation_id,
            author=self.name,
            content=f"LLM Agent {self.name} response",
            branch=ctx.branch
        )
        self._maybe_save_output_to_state(event)
        yield event

    @override
    async def _run_live_impl(
        self, ctx: InvocationContext
    ) -> AsyncGenerator[Event, None]:
        """实时运行LLM代理的实现。
        
        Args:
            ctx: 调用上下文
            
        Yields:
            Event: 生成的事件
        """
        async for event in self._run_async_impl(ctx):
            yield event
        if ctx.end_invocation:
            return

    @property
    def canonical_model(self) -> Any:
        """解析后的模型字段。
        
        Returns:
            解析后的模型实例
        """
        if self.model:
            return self.model
        else:
            # 从祖先代理中查找模型
            ancestor_agent = self.parent_agent
            while ancestor_agent is not None:
                if isinstance(ancestor_agent, LlmAgent) and ancestor_agent.model:
                    return ancestor_agent.model
                ancestor_agent = getattr(ancestor_agent, 'parent_agent', None)
            raise ValueError(f'No model found for {self.name}.')

    async def canonical_instruction(
        self, ctx: Any
    ) -> tuple[str, bool]:
        """解析指令字段。
        
        Args:
            ctx: 上下文
            
        Returns:
            tuple[str, bool]: (指令, 是否绕过状态注入)
        """
        if isinstance(self.instruction, str):
            return self.instruction, False
        else:
            instruction = self.instruction(ctx)
            if inspect.isawaitable(instruction):
                instruction = await instruction
            return instruction, True

    async def canonical_global_instruction(
        self, ctx: Any
    ) -> tuple[str, bool]:
        """解析全局指令字段。
        
        Args:
            ctx: 上下文
            
        Returns:
            tuple[str, bool]: (全局指令, 是否绕过状态注入)
        """
        if isinstance(self.global_instruction, str):
            return self.global_instruction, False
        else:
            global_instruction = self.global_instruction(ctx)
            if inspect.isawaitable(global_instruction):
                global_instruction = await global_instruction
            return global_instruction, True

    async def canonical_tools(
        self, ctx: Any = None
    ) -> list[Any]:
        """解析工具字段。
        
        Args:
            ctx: 上下文
            
        Returns:
            list[Any]: 解析后的工具列表
        """
        resolved_tools = []
        for tool_union in self.tools:
            if callable(tool_union):
                resolved_tools.append(tool_union)
            else:
                resolved_tools.append(tool_union)
        return resolved_tools

    @property
    def canonical_before_model_callbacks(
        self,
    ) -> list[_SingleBeforeModelCallback]:
        """解析模型前回调字段。
        
        Returns:
            list[_SingleBeforeModelCallback]: 回调列表
        """
        if not self.before_model_callback:
            return []
        if isinstance(self.before_model_callback, list):
            return self.before_model_callback
        return [self.before_model_callback]

    @property
    def canonical_after_model_callbacks(self) -> list[_SingleAfterModelCallback]:
        """解析模型后回调字段。
        
        Returns:
            list[_SingleAfterModelCallback]: 回调列表
        """
        if not self.after_model_callback:
            return []
        if isinstance(self.after_model_callback, list):
            return self.after_model_callback
        return [self.after_model_callback]

    @property
    def canonical_before_tool_callbacks(
        self,
    ) -> list[BeforeToolCallback]:
        """解析工具前回调字段。
        
        Returns:
            list[BeforeToolCallback]: 回调列表
        """
        if not self.before_tool_callback:
            return []
        if isinstance(self.before_tool_callback, list):
            return self.before_tool_callback
        return [self.before_tool_callback]

    @property
    def canonical_after_tool_callbacks(
        self,
    ) -> list[AfterToolCallback]:
        """解析工具后回调字段。
        
        Returns:
            list[AfterToolCallback]: 回调列表
        """
        if not self.after_tool_callback:
            return []
        if isinstance(self.after_tool_callback, list):
            return self.after_tool_callback
        return [self.after_tool_callback]

    def _maybe_save_output_to_state(self, event: Event):
        """如果需要，将模型输出保存到状态中。
        
        Args:
            event: 事件对象
        """
        if (
            self.output_key
            and event.is_final_response()
            and event.content
        ):
            result = str(event.content)
            if self.output_schema:
                try:
                    result = self.output_schema.model_validate_json(result).model_dump(
                        exclude_none=True
                    )
                except Exception:
                    # 如果解析失败，使用原始结果
                    pass
            # 简化实现：直接设置到事件数据中
            if not hasattr(event, 'data') or event.data is None:
                event.data = {}
            event.data[self.output_key] = result

    @model_validator(mode='after')
    def __model_validator_after(self) -> 'LlmAgent':
        """模型验证器。
        
        Returns:
            LlmAgent: 验证后的实例
        """
        self._check_output_schema()
        return self

    def _check_output_schema(self):
        """检查输出模式配置。"""
        if not self.output_schema:
            return

        if (
            not self.disallow_transfer_to_parent
            or not self.disallow_transfer_to_peers
        ):
            logger.warning(
                'Invalid config for agent %s: output_schema cannot co-exist with'
                ' agent transfer configurations. Setting'
                ' disallow_transfer_to_parent=True, disallow_transfer_to_peers=True',
                self.name,
            )
            self.disallow_transfer_to_parent = True
            self.disallow_transfer_to_peers = True

        if self.sub_agents:
            raise ValueError(
                f'Invalid config for agent {self.name}: if output_schema is set,'
                ' sub_agents must be empty to disable agent transfer.'
            )

        if self.tools:
            raise ValueError(
                f'Invalid config for agent {self.name}: if output_schema is set,'
                ' tools must be empty'
            )

    @field_validator('generate_content_config', mode='after')
    @classmethod
    def __validate_generate_content_config(
        cls, generate_content_config: Optional[Any]
    ) -> Any:
        """验证生成内容配置。
        
        Args:
            generate_content_config: 生成内容配置
            
        Returns:
            Any: 验证后的配置
        """
        if not generate_content_config:
            return {}
        return generate_content_config


# Type alias for backward compatibility
Agent: TypeAlias = LlmAgent