-- A2A多智能体系统完整数据库建表脚本
-- 生成时间: 2024年
-- 数据库类型: MySQL 8.0+
-- 修复版本: 解决外键约束错误(1215)

-- 设置字符集和存储引擎
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ============================================================================
-- 基础表结构
-- ============================================================================

-- 用户表
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    email VARCHAR(100) NOT NULL UNIQUE COMMENT '邮箱地址',
    password_hash VARCHAR(255) NOT NULL COMMENT '密码哈希',
    full_name VARCHAR(100) COMMENT '全名',
    avatar_url VARCHAR(500) COMMENT '头像URL',
    phone VARCHAR(20) COMMENT '手机号码',
    role VARCHAR(20) NOT NULL DEFAULT 'user' COMMENT '用户角色',
    is_verified BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否已验证邮箱',
    is_locked BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否被锁定',
    last_login_at TIMESTAMP NULL COMMENT '最后登录时间',
    last_login_ip VARCHAR(45) COMMENT '最后登录IP',
    login_count INT NOT NULL DEFAULT 0 COMMENT '登录次数',
    password_changed_at TIMESTAMP NULL COMMENT '密码修改时间',
    failed_login_attempts INT NOT NULL DEFAULT 0 COMMENT '失败登录尝试次数',
    locked_until TIMESTAMP NULL COMMENT '锁定到期时间',
    preferences TEXT COMMENT '用户偏好设置（JSON格式）',
    timezone VARCHAR(50) NOT NULL DEFAULT 'UTC' COMMENT '时区',
    language VARCHAR(10) NOT NULL DEFAULT 'zh-CN' COMMENT '语言',
    is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否激活',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted_at TIMESTAMP NULL COMMENT '删除时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 用户令牌表
CREATE TABLE user_tokens (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL COMMENT '用户ID',
    session_id VARCHAR(36) NOT NULL UNIQUE COMMENT '会话ID',
    access_token TEXT COMMENT '访问令牌',
    refresh_token TEXT NOT NULL COMMENT '刷新令牌',
    access_expires_at TIMESTAMP NULL COMMENT '访问令牌过期时间',
    refresh_expires_at TIMESTAMP NOT NULL COMMENT '刷新令牌过期时间',
    is_revoked BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否已撤销',
    revoked_at TIMESTAMP NULL COMMENT '撤销时间',
    device_info TEXT COMMENT '设备信息（JSON格式）',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent VARCHAR(500) COMMENT '用户代理',
    is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否激活',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted_at TIMESTAMP NULL COMMENT '删除时间',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 用户权限表
CREATE TABLE user_permissions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL COMMENT '用户ID',
    permission_name VARCHAR(100) NOT NULL COMMENT '权限名称',
    is_granted BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否授予',
    granted_at TIMESTAMP NULL COMMENT '授予时间',
    revoked_at TIMESTAMP NULL COMMENT '撤销时间',
    expires_at TIMESTAMP NULL COMMENT '过期时间',
    granted_by INT COMMENT '授予者ID',
    notes TEXT COMMENT '备注',
    is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否激活',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted_at TIMESTAMP NULL COMMENT '删除时间',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (granted_by) REFERENCES users(id) ON DELETE SET NULL,
    UNIQUE KEY idx_user_permissions_unique (user_id, permission_name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 用户活动日志表
CREATE TABLE user_activity_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL COMMENT '用户ID',
    action VARCHAR(100) NOT NULL COMMENT '操作动作',
    resource_type VARCHAR(50) COMMENT '资源类型',
    resource_id INT COMMENT '资源ID',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent VARCHAR(500) COMMENT '用户代理',
    details TEXT COMMENT '详细信息（JSON格式）',
    status VARCHAR(20) NOT NULL DEFAULT 'success' COMMENT '操作状态',
    error_message TEXT COMMENT '错误信息',
    timestamp TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '时间戳',
    is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否激活',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted_at TIMESTAMP NULL COMMENT '删除时间',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ============================================================================
-- 智能体相关表
-- ============================================================================

-- 智能体表
CREATE TABLE agents (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL COMMENT '创建者用户ID',
    owner_id INT NOT NULL COMMENT '拥有者用户ID',
    agent_id VARCHAR(36) NOT NULL UNIQUE COMMENT '智能体唯一标识',
    name VARCHAR(100) NOT NULL COMMENT '智能体名称',
    description TEXT COMMENT '智能体描述',
    agent_type VARCHAR(50) NOT NULL DEFAULT 'general' COMMENT '智能体类型',
    category VARCHAR(50) NOT NULL DEFAULT 'general' COMMENT '智能体分类',
    avatar_url VARCHAR(500) COMMENT '头像URL',
    config TEXT COMMENT '智能体配置（JSON格式）',
    system_prompt TEXT COMMENT '系统提示词',
    model_config TEXT COMMENT '模型配置（JSON格式）',
    capabilities TEXT COMMENT '能力列表（JSON格式）',
    status VARCHAR(20) NOT NULL DEFAULT 'active' COMMENT '智能体状态',
    is_public BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否公开',
    is_template BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否为模板',
    version VARCHAR(20) NOT NULL DEFAULT '1.0.0' COMMENT '版本号',
    usage_count INT NOT NULL DEFAULT 0 COMMENT '使用次数',
    success_rate DECIMAL(5,2) NOT NULL DEFAULT 0.00 COMMENT '成功率',
    avg_response_time DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '平均响应时间（毫秒）',
    last_used_at TIMESTAMP NULL COMMENT '最后使用时间',
    is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否激活',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted_at TIMESTAMP NULL COMMENT '删除时间',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (owner_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 智能体能力表
CREATE TABLE agent_capabilities (
    id INT AUTO_INCREMENT PRIMARY KEY,
    agent_id INT NOT NULL COMMENT '智能体ID',
    capability_name VARCHAR(100) NOT NULL COMMENT '能力名称',
    capability_type VARCHAR(50) NOT NULL COMMENT '能力类型',
    description TEXT COMMENT '能力描述',
    config TEXT COMMENT '能力配置（JSON格式）',
    is_enabled BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否启用',
    priority INT NOT NULL DEFAULT 0 COMMENT '优先级',
    is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否激活',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted_at TIMESTAMP NULL COMMENT '删除时间',
    FOREIGN KEY (agent_id) REFERENCES agents(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ============================================================================
-- 会话和消息相关表
-- ============================================================================

-- 会话表
CREATE TABLE sessions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL COMMENT '用户ID',
    owner_id INT NOT NULL COMMENT '拥有者用户ID',
    session_id VARCHAR(36) NOT NULL UNIQUE COMMENT '会话唯一标识',
    title VARCHAR(200) COMMENT '会话标题',
    session_type VARCHAR(50) NOT NULL DEFAULT 'chat' COMMENT '会话类型',
    status VARCHAR(20) NOT NULL DEFAULT 'active' COMMENT '会话状态',
    config TEXT COMMENT '会话配置（JSON格式）',
    context TEXT COMMENT '会话上下文（JSON格式）',
    metadata TEXT COMMENT '元数据（JSON格式）',
    message_count INT NOT NULL DEFAULT 0 COMMENT '消息数量',
    last_message_at TIMESTAMP NULL COMMENT '最后消息时间',
    expires_at TIMESTAMP NULL COMMENT '过期时间',
    is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否激活',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted_at TIMESTAMP NULL COMMENT '删除时间',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (owner_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 消息表
CREATE TABLE messages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    session_id INT NOT NULL COMMENT '会话ID',
    user_id INT NOT NULL COMMENT '用户ID',
    owner_id INT NOT NULL COMMENT '拥有者用户ID',
    message_id VARCHAR(36) NOT NULL UNIQUE COMMENT '消息唯一标识',
    parent_message_id INT COMMENT '父消息ID',
    agent_id INT COMMENT '智能体ID',
    role VARCHAR(20) NOT NULL COMMENT '角色',
    message_type VARCHAR(50) NOT NULL DEFAULT 'text' COMMENT '消息类型',
    content TEXT COMMENT '消息内容',
    content_type VARCHAR(50) NOT NULL DEFAULT 'text' COMMENT '内容类型',
    metadata TEXT COMMENT '元数据（JSON格式）',
    attachments TEXT COMMENT '附件信息（JSON格式）',
    status VARCHAR(20) NOT NULL DEFAULT 'sent' COMMENT '消息状态',
    is_edited BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否已编辑',
    edited_at TIMESTAMP NULL COMMENT '编辑时间',
    tokens_count INT NOT NULL DEFAULT 0 COMMENT 'token数量',
    processing_time DECIMAL(10,3) COMMENT '处理时间（秒）',
    is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否激活',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted_at TIMESTAMP NULL COMMENT '删除时间',
    FOREIGN KEY (session_id) REFERENCES sessions(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (owner_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (parent_message_id) REFERENCES messages(id) ON DELETE SET NULL,
    FOREIGN KEY (agent_id) REFERENCES agents(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ============================================================================
-- 任务管理相关表
-- ============================================================================

-- 任务表
CREATE TABLE tasks (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL COMMENT '创建者用户ID',
    owner_id INT NOT NULL COMMENT '拥有者用户ID',
    agent_id INT NOT NULL COMMENT '执行智能体ID',
    task_id VARCHAR(36) NOT NULL UNIQUE COMMENT '任务唯一标识',
    title VARCHAR(200) NOT NULL COMMENT '任务标题',
    description TEXT COMMENT '任务描述',
    task_type VARCHAR(50) NOT NULL DEFAULT 'general' COMMENT '任务类型',
    priority VARCHAR(20) NOT NULL DEFAULT 'medium' COMMENT '任务优先级',
    status VARCHAR(20) NOT NULL DEFAULT 'pending' COMMENT '任务状态',
    progress DECIMAL(5,2) NOT NULL DEFAULT 0.00 COMMENT '任务进度（0-100）',
    config TEXT COMMENT '任务配置（JSON格式）',
    input_data TEXT COMMENT '输入数据（JSON格式）',
    output_data TEXT COMMENT '输出数据（JSON格式）',
    execution_log TEXT COMMENT '执行日志（JSON格式）',
    error_message TEXT COMMENT '错误信息',
    scheduled_at TIMESTAMP NULL COMMENT '计划执行时间',
    started_at TIMESTAMP NULL COMMENT '开始执行时间',
    completed_at TIMESTAMP NULL COMMENT '完成时间',
    execution_time DECIMAL(10,3) COMMENT '执行时间（秒）',
    tokens_used INT NOT NULL DEFAULT 0 COMMENT '使用的token数',
    cost DECIMAL(10,4) NOT NULL DEFAULT 0.0000 COMMENT '执行费用',
    parent_task_id INT COMMENT '父任务ID',
    is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否激活',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted_at TIMESTAMP NULL COMMENT '删除时间',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (owner_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (agent_id) REFERENCES agents(id) ON DELETE CASCADE,
    FOREIGN KEY (parent_task_id) REFERENCES tasks(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 任务执行记录表
CREATE TABLE task_executions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    task_id INT NOT NULL COMMENT '任务ID',
    user_id INT NOT NULL COMMENT '创建者用户ID',
    owner_id INT NOT NULL COMMENT '拥有者用户ID',
    execution_id VARCHAR(36) NOT NULL UNIQUE COMMENT '执行唯一标识',
    status VARCHAR(20) NOT NULL DEFAULT 'running' COMMENT '执行状态',
    result TEXT COMMENT '执行结果（JSON格式）',
    error_details TEXT COMMENT '错误详情（JSON格式）',
    start_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '开始时间',
    end_time TIMESTAMP NULL COMMENT '结束时间',
    duration DECIMAL(10,3) COMMENT '执行时长（秒）',
    memory_usage DECIMAL(10,2) COMMENT '内存使用（MB）',
    cpu_usage DECIMAL(5,2) COMMENT 'CPU使用率（%）',
    tokens_consumed INT NOT NULL DEFAULT 0 COMMENT '消耗的token数',
    api_calls INT NOT NULL DEFAULT 0 COMMENT 'API调用次数',
    environment TEXT COMMENT '执行环境信息（JSON格式）',
    is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否激活',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted_at TIMESTAMP NULL COMMENT '删除时间',
    FOREIGN KEY (task_id) REFERENCES tasks(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (owner_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 任务结果表
CREATE TABLE task_results (
    id INT AUTO_INCREMENT PRIMARY KEY,
    task_id INT NOT NULL COMMENT '任务ID',
    result_type VARCHAR(50) NOT NULL COMMENT '结果类型',
    result_data TEXT COMMENT '结果数据（JSON格式）',
    status VARCHAR(20) NOT NULL DEFAULT 'success' COMMENT '结果状态',
    execution_time DECIMAL(10,3) COMMENT '执行时间（秒）',
    tokens_used INT NOT NULL DEFAULT 0 COMMENT '使用的token数',
    cost DECIMAL(10,4) NOT NULL DEFAULT 0.0000 COMMENT '执行费用',
    error_message TEXT COMMENT '错误信息',
    error_details TEXT COMMENT '错误详情（JSON格式）',
    is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否激活',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted_at TIMESTAMP NULL COMMENT '删除时间',
    FOREIGN KEY (task_id) REFERENCES tasks(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 任务依赖表
CREATE TABLE task_dependencies (
    id INT AUTO_INCREMENT PRIMARY KEY,
    task_id INT NOT NULL COMMENT '任务ID',
    depends_on_task_id INT NOT NULL COMMENT '依赖的任务ID',
    dependency_type VARCHAR(20) NOT NULL DEFAULT 'sequential' COMMENT '依赖类型',
    condition TEXT COMMENT '依赖条件（JSON格式）',
    is_satisfied BOOLEAN NOT NULL DEFAULT FALSE COMMENT '依赖是否已满足',
    satisfied_at TIMESTAMP NULL COMMENT '依赖满足时间',
    is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否激活',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted_at TIMESTAMP NULL COMMENT '删除时间',
    FOREIGN KEY (task_id) REFERENCES tasks(id) ON DELETE CASCADE,
    FOREIGN KEY (depends_on_task_id) REFERENCES tasks(id) ON DELETE CASCADE,
    UNIQUE KEY idx_task_dependencies_unique (task_id, depends_on_task_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 重新启用外键检查
SET FOREIGN_KEY_CHECKS = 1;

-- ============================================================================
-- 索引创建
-- ============================================================================

-- 用户表索引
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_users_is_active ON users(is_active);
CREATE INDEX idx_users_created_at ON users(created_at);

-- 用户令牌表索引
CREATE INDEX idx_user_tokens_user_id ON user_tokens(user_id);
CREATE INDEX idx_user_tokens_session_id ON user_tokens(session_id);
CREATE INDEX idx_user_tokens_access_expires_at ON user_tokens(access_expires_at);
CREATE INDEX idx_user_tokens_refresh_expires_at ON user_tokens(refresh_expires_at);

-- 智能体表索引
CREATE INDEX idx_agents_user_id ON agents(user_id);
CREATE INDEX idx_agents_owner_id ON agents(owner_id);
CREATE INDEX idx_agents_agent_id ON agents(agent_id);
CREATE INDEX idx_agents_agent_type ON agents(agent_type);
CREATE INDEX idx_agents_status ON agents(status);
CREATE INDEX idx_agents_is_public ON agents(is_public);

-- 会话表索引
CREATE INDEX idx_sessions_user_id ON sessions(user_id);
CREATE INDEX idx_sessions_owner_id ON sessions(owner_id);
CREATE INDEX idx_sessions_session_id ON sessions(session_id);
CREATE INDEX idx_sessions_status ON sessions(status);
CREATE INDEX idx_sessions_created_at ON sessions(created_at);

-- 消息表索引
CREATE INDEX idx_messages_session_id ON messages(session_id);
CREATE INDEX idx_messages_user_id ON messages(user_id);
CREATE INDEX idx_messages_agent_id ON messages(agent_id);
CREATE INDEX idx_messages_message_id ON messages(message_id);
CREATE INDEX idx_messages_parent_message_id ON messages(parent_message_id);
CREATE INDEX idx_messages_created_at ON messages(created_at);

-- 任务表索引
CREATE INDEX idx_tasks_user_id ON tasks(user_id);
CREATE INDEX idx_tasks_owner_id ON tasks(owner_id);
CREATE INDEX idx_tasks_agent_id ON tasks(agent_id);
CREATE INDEX idx_tasks_task_id ON tasks(task_id);
CREATE INDEX idx_tasks_status ON tasks(status);
CREATE INDEX idx_tasks_priority ON tasks(priority);
CREATE INDEX idx_tasks_created_at ON tasks(created_at);

-- 任务执行记录表索引
CREATE INDEX idx_task_executions_task_id ON task_executions(task_id);
CREATE INDEX idx_task_executions_user_id ON task_executions(user_id);
CREATE INDEX idx_task_executions_execution_id ON task_executions(execution_id);
CREATE INDEX idx_task_executions_status ON task_executions(status);
CREATE INDEX idx_task_executions_start_time ON task_executions(start_time);

-- ============================================================================
-- 说明文档
-- ============================================================================

/*
修复说明:
1. 将所有 SERIAL 改为 INT AUTO_INCREMENT (MySQL语法)
2. 将所有 INTEGER 改为 INT (MySQL标准)
3. 将所有 TIMESTAMP 字段改为 TIMESTAMP NULL (避免默认值问题)
4. 添加了 ENGINE=InnoDB 和字符集设置
5. 修复了外键约束的数据类型不匹配问题:
   - task_results.task_id: VARCHAR(36) -> INT
   - task_dependencies.task_id: VARCHAR(36) -> INT
   - task_dependencies.depends_on_task_id: VARCHAR(36) -> INT
   - memory_entries.memory_id: VARCHAR(36) -> INT
6. 添加了 SET FOREIGN_KEY_CHECKS 控制
7. 确保所有外键字段与引用字段的数据类型完全匹配
8. 添加了完整的索引定义

常见的1215错误原因及解决方案:
- 外键字段与引用字段的数据类型不匹配 ✓ 已修复
- 引用的表或字段不存在 ✓ 已确保表创建顺序正确
- 字符集不匹配 ✓ 统一使用utf8mb4
- 存储引擎不支持外键(如MyISAM) ✓ 使用InnoDB
- 外键字段没有索引 ✓ 自动创建索引

使用说明:
1. 确保MySQL版本为8.0+
2. 创建数据库: CREATE DATABASE a2a_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
3. 使用数据库: USE a2a_system;
4. 执行此脚本: SOURCE /path/to/a2a_database_schema_fixed.sql;
*/