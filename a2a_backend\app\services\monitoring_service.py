# -*- coding: utf-8 -*-
"""
A2A多智能体系统实时监控服务

基于Google ADK的实时监控和告警系统
"""

import asyncio
import json
import time
import uuid
import psutil
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable, Union, Set
from dataclasses import dataclass, field
from enum import Enum
import logging
from collections import defaultdict, deque
import weakref
from concurrent.futures import ThreadPoolExecutor

from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc, func
from cachetools import TTLCache
from loguru import logger
import pickle
import gzip

from ..models import User, Session as SessionModel, Message, Agent, Task, Workflow
from ..core.database import get_db
from ..auth.permissions import check_user_permission
from .user_service import UserService
from .event_service import EventService, EventType, EventPriority


class MetricType(str, Enum):
    """指标类型枚举"""
    COUNTER = "counter"
    GAUGE = "gauge"
    HISTOGRAM = "histogram"
    SUMMARY = "summary"
    TIMER = "timer"


class AlertLevel(str, Enum):
    """告警级别枚举"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"
    EMERGENCY = "emergency"


class AlertStatus(str, Enum):
    """告警状态枚举"""
    ACTIVE = "active"
    RESOLVED = "resolved"
    SUPPRESSED = "suppressed"
    ACKNOWLEDGED = "acknowledged"


class MonitoringScope(str, Enum):
    """监控范围枚举"""
    SYSTEM = "system"
    APPLICATION = "application"
    USER = "user"
    AGENT = "agent"
    SESSION = "session"
    WORKFLOW = "workflow"
    TASK = "task"
    STREAM = "stream"
    DATABASE = "database"
    NETWORK = "network"


@dataclass
class Metric:
    """指标数据类"""
    metric_id: str
    name: str
    metric_type: MetricType
    value: Union[int, float]
    timestamp: datetime = field(default_factory=datetime.utcnow)
    labels: Dict[str, str] = field(default_factory=dict)
    scope: MonitoringScope = MonitoringScope.APPLICATION
    unit: Optional[str] = None
    description: Optional[str] = None
    
    def __post_init__(self):
        if not self.metric_id:
            self.metric_id = str(uuid.uuid4())
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "metric_id": self.metric_id,
            "name": self.name,
            "metric_type": self.metric_type.value,
            "value": self.value,
            "timestamp": self.timestamp.isoformat(),
            "labels": self.labels,
            "scope": self.scope.value,
            "unit": self.unit,
            "description": self.description
        }


@dataclass
class Alert:
    """告警数据类"""
    alert_id: str
    name: str
    level: AlertLevel
    message: str
    timestamp: datetime = field(default_factory=datetime.utcnow)
    status: AlertStatus = AlertStatus.ACTIVE
    scope: MonitoringScope = MonitoringScope.APPLICATION
    source: Optional[str] = None
    target: Optional[str] = None
    user_id: Optional[int] = None
    session_id: Optional[str] = None
    agent_id: Optional[str] = None
    workflow_id: Optional[str] = None
    task_id: Optional[str] = None
    labels: Dict[str, str] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)
    resolved_at: Optional[datetime] = None
    acknowledged_at: Optional[datetime] = None
    acknowledged_by: Optional[str] = None
    
    def __post_init__(self):
        if not self.alert_id:
            self.alert_id = str(uuid.uuid4())
    
    def resolve(self, resolved_by: Optional[str] = None):
        """解决告警"""
        self.status = AlertStatus.RESOLVED
        self.resolved_at = datetime.utcnow()
        if resolved_by:
            self.metadata["resolved_by"] = resolved_by
    
    def acknowledge(self, acknowledged_by: str):
        """确认告警"""
        self.status = AlertStatus.ACKNOWLEDGED
        self.acknowledged_at = datetime.utcnow()
        self.acknowledged_by = acknowledged_by
    
    def suppress(self):
        """抑制告警"""
        self.status = AlertStatus.SUPPRESSED
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "alert_id": self.alert_id,
            "name": self.name,
            "level": self.level.value,
            "message": self.message,
            "timestamp": self.timestamp.isoformat(),
            "status": self.status.value,
            "scope": self.scope.value,
            "source": self.source,
            "target": self.target,
            "user_id": self.user_id,
            "session_id": self.session_id,
            "agent_id": self.agent_id,
            "workflow_id": self.workflow_id,
            "task_id": self.task_id,
            "labels": self.labels,
            "metadata": self.metadata,
            "resolved_at": self.resolved_at.isoformat() if self.resolved_at else None,
            "acknowledged_at": self.acknowledged_at.isoformat() if self.acknowledged_at else None,
            "acknowledged_by": self.acknowledged_by
        }


@dataclass
class AlertRule:
    """告警规则"""
    rule_id: str
    name: str
    metric_name: str
    condition: str  # 条件表达式，如 "> 80", "< 10", "== 0"
    threshold: Union[int, float]
    level: AlertLevel
    scope: MonitoringScope
    enabled: bool = True
    labels: Dict[str, str] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)
    cooldown_seconds: int = 300  # 冷却时间
    last_triggered: Optional[datetime] = None
    
    def __post_init__(self):
        if not self.rule_id:
            self.rule_id = str(uuid.uuid4())
    
    def should_trigger(self, metric_value: Union[int, float]) -> bool:
        """检查是否应该触发告警"""
        if not self.enabled:
            return False
        
        # 检查冷却时间
        if self.last_triggered:
            cooldown_end = self.last_triggered + timedelta(seconds=self.cooldown_seconds)
            if datetime.utcnow() < cooldown_end:
                return False
        
        # 评估条件
        try:
            if self.condition.startswith(">="):
                return metric_value >= self.threshold
            elif self.condition.startswith("<="):
                return metric_value <= self.threshold
            elif self.condition.startswith(">"):
                return metric_value > self.threshold
            elif self.condition.startswith("<"):
                return metric_value < self.threshold
            elif self.condition.startswith("=="):
                return metric_value == self.threshold
            elif self.condition.startswith("!="):
                return metric_value != self.threshold
            else:
                return False
        except Exception:
            return False
    
    def trigger(self) -> Alert:
        """触发告警"""
        self.last_triggered = datetime.utcnow()
        
        return Alert(
            alert_id=str(uuid.uuid4()),
            name=f"{self.name} - {self.metric_name}",
            level=self.level,
            message=f"指标 {self.metric_name} {self.condition} {self.threshold}",
            scope=self.scope,
            source=f"rule:{self.rule_id}",
            labels=self.labels.copy(),
            metadata={
                **self.metadata,
                "rule_id": self.rule_id,
                "metric_name": self.metric_name,
                "condition": self.condition,
                "threshold": self.threshold
            }
        )


class MetricCollector:
    """指标收集器"""
    
    def __init__(self, max_metrics: int = 100000):
        self.max_metrics = max_metrics
        self.metrics: Dict[str, Metric] = {}
        self.metric_history: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))
        self.metric_aggregates: Dict[str, Dict[str, float]] = defaultdict(dict)
        self._lock = asyncio.Lock()
    
    async def collect_metric(
        self,
        name: str,
        metric_type: MetricType,
        value: Union[int, float],
        labels: Optional[Dict[str, str]] = None,
        scope: MonitoringScope = MonitoringScope.APPLICATION,
        unit: Optional[str] = None,
        description: Optional[str] = None
    ) -> str:
        """收集指标"""
        async with self._lock:
            # 创建指标
            metric = Metric(
                metric_id=str(uuid.uuid4()),
                name=name,
                metric_type=metric_type,
                value=value,
                labels=labels or {},
                scope=scope,
                unit=unit,
                description=description
            )
            
            # 存储指标
            metric_key = self._get_metric_key(name, labels or {})
            
            # 检查存储限制
            if len(self.metrics) >= self.max_metrics:
                await self._cleanup_old_metrics()
            
            self.metrics[metric.metric_id] = metric
            
            # 更新历史记录
            self.metric_history[metric_key].append({
                "timestamp": metric.timestamp,
                "value": value
            })
            
            # 更新聚合数据
            await self._update_aggregates(metric_key, value)
            
            return metric.metric_id
    
    async def get_metric(self, metric_id: str) -> Optional[Metric]:
        """获取指标"""
        return self.metrics.get(metric_id)
    
    async def get_metrics(
        self,
        name_pattern: Optional[str] = None,
        scope: Optional[MonitoringScope] = None,
        labels: Optional[Dict[str, str]] = None,
        time_range: Optional[tuple] = None,
        limit: int = 1000
    ) -> List[Metric]:
        """获取指标列表"""
        async with self._lock:
            filtered_metrics = []
            
            for metric in self.metrics.values():
                # 名称过滤
                if name_pattern and name_pattern not in metric.name:
                    continue
                
                # 范围过滤
                if scope and metric.scope != scope:
                    continue
                
                # 标签过滤
                if labels:
                    if not all(metric.labels.get(k) == v for k, v in labels.items()):
                        continue
                
                # 时间范围过滤
                if time_range:
                    start_time, end_time = time_range
                    if metric.timestamp < start_time or metric.timestamp > end_time:
                        continue
                
                filtered_metrics.append(metric)
            
            # 按时间排序并限制数量
            filtered_metrics.sort(key=lambda x: x.timestamp, reverse=True)
            return filtered_metrics[:limit]
    
    async def get_metric_history(
        self,
        name: str,
        labels: Optional[Dict[str, str]] = None,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """获取指标历史"""
        metric_key = self._get_metric_key(name, labels or {})
        history = list(self.metric_history.get(metric_key, []))
        return history[-limit:]
    
    async def get_metric_aggregates(
        self,
        name: str,
        labels: Optional[Dict[str, str]] = None
    ) -> Dict[str, float]:
        """获取指标聚合数据"""
        metric_key = self._get_metric_key(name, labels or {})
        return self.metric_aggregates.get(metric_key, {})
    
    def _get_metric_key(self, name: str, labels: Dict[str, str]) -> str:
        """生成指标键"""
        label_str = ",".join(f"{k}={v}" for k, v in sorted(labels.items()))
        return f"{name}#{label_str}" if label_str else name
    
    async def _update_aggregates(self, metric_key: str, value: Union[int, float]):
        """更新聚合数据"""
        history = self.metric_history[metric_key]
        if not history:
            return
        
        values = [item["value"] for item in history]
        
        self.metric_aggregates[metric_key] = {
            "count": len(values),
            "sum": sum(values),
            "avg": sum(values) / len(values),
            "min": min(values),
            "max": max(values),
            "latest": values[-1] if values else 0
        }
    
    async def _cleanup_old_metrics(self):
        """清理旧指标"""
        # 删除最旧的10%指标
        cleanup_count = max(1, len(self.metrics) // 10)
        
        # 按时间排序，删除最旧的指标
        sorted_metrics = sorted(self.metrics.items(), key=lambda x: x[1].timestamp)
        
        for metric_id, _ in sorted_metrics[:cleanup_count]:
            del self.metrics[metric_id]


class SystemMonitor:
    """系统监控器"""
    
    def __init__(self, metric_collector: MetricCollector):
        self.metric_collector = metric_collector
        self.logger = logger
        self._monitoring_task = None
        self._interval = 30  # 监控间隔（秒）
    
    async def start(self):
        """启动系统监控"""
        self._monitoring_task = asyncio.create_task(self._monitor_system())
        self.logger.info("系统监控启动成功")
    
    async def stop(self):
        """停止系统监控"""
        if self._monitoring_task:
            self._monitoring_task.cancel()
        self.logger.info("系统监控停止成功")
    
    async def _monitor_system(self):
        """监控系统指标"""
        while True:
            try:
                # CPU使用率
                cpu_percent = psutil.cpu_percent(interval=1)
                await self.metric_collector.collect_metric(
                    "system_cpu_usage",
                    MetricType.GAUGE,
                    cpu_percent,
                    scope=MonitoringScope.SYSTEM,
                    unit="percent",
                    description="系统CPU使用率"
                )
                
                # 内存使用情况
                memory = psutil.virtual_memory()
                await self.metric_collector.collect_metric(
                    "system_memory_usage",
                    MetricType.GAUGE,
                    memory.percent,
                    scope=MonitoringScope.SYSTEM,
                    unit="percent",
                    description="系统内存使用率"
                )
                
                await self.metric_collector.collect_metric(
                    "system_memory_available",
                    MetricType.GAUGE,
                    memory.available / (1024 * 1024 * 1024),  # GB
                    scope=MonitoringScope.SYSTEM,
                    unit="GB",
                    description="系统可用内存"
                )
                
                # 磁盘使用情况
                disk = psutil.disk_usage('/')
                await self.metric_collector.collect_metric(
                    "system_disk_usage",
                    MetricType.GAUGE,
                    (disk.used / disk.total) * 100,
                    scope=MonitoringScope.SYSTEM,
                    unit="percent",
                    description="系统磁盘使用率"
                )
                
                # 网络IO
                net_io = psutil.net_io_counters()
                await self.metric_collector.collect_metric(
                    "system_network_bytes_sent",
                    MetricType.COUNTER,
                    net_io.bytes_sent,
                    scope=MonitoringScope.NETWORK,
                    unit="bytes",
                    description="网络发送字节数"
                )
                
                await self.metric_collector.collect_metric(
                    "system_network_bytes_recv",
                    MetricType.COUNTER,
                    net_io.bytes_recv,
                    scope=MonitoringScope.NETWORK,
                    unit="bytes",
                    description="网络接收字节数"
                )
                
                # 进程数量
                process_count = len(psutil.pids())
                await self.metric_collector.collect_metric(
                    "system_process_count",
                    MetricType.GAUGE,
                    process_count,
                    scope=MonitoringScope.SYSTEM,
                    unit="count",
                    description="系统进程数量"
                )
                
                # 负载平均值（仅Linux/Unix）
                try:
                    load_avg = psutil.getloadavg()
                    await self.metric_collector.collect_metric(
                        "system_load_avg_1m",
                        MetricType.GAUGE,
                        load_avg[0],
                        scope=MonitoringScope.SYSTEM,
                        description="系统1分钟负载平均值"
                    )
                except (AttributeError, OSError):
                    # Windows系统不支持getloadavg
                    pass
                
                await asyncio.sleep(self._interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"系统监控失败: {e}")
                await asyncio.sleep(self._interval)


class ApplicationMonitor:
    """应用监控器"""
    
    def __init__(self, metric_collector: MetricCollector):
        self.metric_collector = metric_collector
        self.logger = logger
        self._monitoring_task = None
        self._interval = 60  # 监控间隔（秒）
    
    async def start(self):
        """启动应用监控"""
        self._monitoring_task = asyncio.create_task(self._monitor_application())
        self.logger.info("应用监控启动成功")
    
    async def stop(self):
        """停止应用监控"""
        if self._monitoring_task:
            self._monitoring_task.cancel()
        self.logger.info("应用监控停止成功")
    
    async def _monitor_application(self):
        """监控应用指标"""
        while True:
            try:
                # 获取数据库会话
                db = next(get_db())
                
                try:
                    # 用户统计
                    total_users = db.query(User).count()
                    active_users = db.query(User).filter(
                        User.last_login_at > datetime.utcnow() - timedelta(days=30)
                    ).count()
                    
                    await self.metric_collector.collect_metric(
                        "app_total_users",
                        MetricType.GAUGE,
                        total_users,
                        scope=MonitoringScope.APPLICATION,
                        unit="count",
                        description="总用户数"
                    )
                    
                    await self.metric_collector.collect_metric(
                        "app_active_users",
                        MetricType.GAUGE,
                        active_users,
                        scope=MonitoringScope.APPLICATION,
                        unit="count",
                        description="活跃用户数（30天内）"
                    )
                    
                    # 智能体统计
                    total_agents = db.query(Agent).count()
                    active_agents = db.query(Agent).filter(
                        Agent.status == "active"
                    ).count()
                    
                    await self.metric_collector.collect_metric(
                        "app_total_agents",
                        MetricType.GAUGE,
                        total_agents,
                        scope=MonitoringScope.APPLICATION,
                        unit="count",
                        description="总智能体数"
                    )
                    
                    await self.metric_collector.collect_metric(
                        "app_active_agents",
                        MetricType.GAUGE,
                        active_agents,
                        scope=MonitoringScope.APPLICATION,
                        unit="count",
                        description="活跃智能体数"
                    )
                    
                    # 会话统计
                    total_sessions = db.query(SessionModel).count()
                    active_sessions = db.query(SessionModel).filter(
                        SessionModel.updated_at > datetime.utcnow() - timedelta(hours=24)
                    ).count()
                    
                    await self.metric_collector.collect_metric(
                        "app_total_sessions",
                        MetricType.GAUGE,
                        total_sessions,
                        scope=MonitoringScope.APPLICATION,
                        unit="count",
                        description="总会话数"
                    )
                    
                    await self.metric_collector.collect_metric(
                        "app_active_sessions",
                        MetricType.GAUGE,
                        active_sessions,
                        scope=MonitoringScope.APPLICATION,
                        unit="count",
                        description="活跃会话数（24小时内）"
                    )
                    
                    # 消息统计
                    total_messages = db.query(Message).count()
                    recent_messages = db.query(Message).filter(
                        Message.created_at > datetime.utcnow() - timedelta(hours=1)
                    ).count()
                    
                    await self.metric_collector.collect_metric(
                        "app_total_messages",
                        MetricType.GAUGE,
                        total_messages,
                        scope=MonitoringScope.APPLICATION,
                        unit="count",
                        description="总消息数"
                    )
                    
                    await self.metric_collector.collect_metric(
                        "app_recent_messages",
                        MetricType.GAUGE,
                        recent_messages,
                        scope=MonitoringScope.APPLICATION,
                        unit="count",
                        description="最近1小时消息数"
                    )
                    
                    # 任务统计
                    total_tasks = db.query(Task).count()
                    running_tasks = db.query(Task).filter(
                        Task.status == "running"
                    ).count()
                    
                    await self.metric_collector.collect_metric(
                        "app_total_tasks",
                        MetricType.GAUGE,
                        total_tasks,
                        scope=MonitoringScope.APPLICATION,
                        unit="count",
                        description="总任务数"
                    )
                    
                    await self.metric_collector.collect_metric(
                        "app_running_tasks",
                        MetricType.GAUGE,
                        running_tasks,
                        scope=MonitoringScope.APPLICATION,
                        unit="count",
                        description="运行中任务数"
                    )
                    
                    # 工作流统计
                    total_workflows = db.query(Workflow).count()
                    active_workflows = db.query(Workflow).filter(
                        Workflow.status == "active"
                    ).count()
                    
                    await self.metric_collector.collect_metric(
                        "app_total_workflows",
                        MetricType.GAUGE,
                        total_workflows,
                        scope=MonitoringScope.APPLICATION,
                        unit="count",
                        description="总工作流数"
                    )
                    
                    await self.metric_collector.collect_metric(
                        "app_active_workflows",
                        MetricType.GAUGE,
                        active_workflows,
                        scope=MonitoringScope.APPLICATION,
                        unit="count",
                        description="活跃工作流数"
                    )
                    
                finally:
                    db.close()
                
                await asyncio.sleep(self._interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"应用监控失败: {e}")
                await asyncio.sleep(self._interval)


class MonitoringService:
    """
    实时监控服务
    
    基于Google ADK的实时监控和告警系统
    """
    
    def __init__(self):
        """初始化监控服务"""
        self.logger = logger
        
        # 组件初始化
        self.metric_collector = MetricCollector(max_metrics=100000)
        self.system_monitor = SystemMonitor(self.metric_collector)
        self.application_monitor = ApplicationMonitor(self.metric_collector)
        
        # 告警管理
        self.alerts: Dict[str, Alert] = {}
        self.alert_rules: Dict[str, AlertRule] = {}
        self.alert_history: deque = deque(maxlen=10000)
        
        # 订阅者管理
        self.alert_subscribers: Dict[str, Set[Callable]] = defaultdict(set)
        self.metric_subscribers: Dict[str, Set[Callable]] = defaultdict(set)
        
        # 性能统计
        self.stats = {
            "total_metrics": 0,
            "total_alerts": 0,
            "active_alerts": 0,
            "resolved_alerts": 0,
            "total_rules": 0,
            "active_rules": 0
        }
        
        # 后台任务
        self._alert_processor_task = None
        self._stats_task = None
        
        # 服务依赖
        self.user_service = UserService()
        self.event_service = None  # 延迟初始化
        
        self.logger.info("监控服务初始化完成")
    
    async def start(self):
        """启动监控服务"""
        try:
            # 启动监控器
            await self.system_monitor.start()
            await self.application_monitor.start()
            
            # 启动后台任务
            self._alert_processor_task = asyncio.create_task(self._process_alerts())
            self._stats_task = asyncio.create_task(self._update_stats())
            
            # 初始化事件服务
            from .event_service import event_service
            self.event_service = event_service
            
            # 添加默认告警规则
            await self._setup_default_alert_rules()
            
            self.logger.info("监控服务启动成功")
            
        except Exception as e:
            self.logger.error(f"监控服务启动失败: {e}")
            raise
    
    async def stop(self):
        """停止监控服务"""
        try:
            # 停止监控器
            await self.system_monitor.stop()
            await self.application_monitor.stop()
            
            # 取消后台任务
            if self._alert_processor_task:
                self._alert_processor_task.cancel()
            if self._stats_task:
                self._stats_task.cancel()
            
            self.logger.info("监控服务停止成功")
            
        except Exception as e:
            self.logger.error(f"监控服务停止失败: {e}")
    
    async def collect_metric(
        self,
        name: str,
        metric_type: MetricType,
        value: Union[int, float],
        labels: Optional[Dict[str, str]] = None,
        scope: MonitoringScope = MonitoringScope.APPLICATION,
        unit: Optional[str] = None,
        description: Optional[str] = None
    ) -> str:
        """
        收集指标
        
        Args:
            name: 指标名称
            metric_type: 指标类型
            value: 指标值
            labels: 标签
            scope: 监控范围
            unit: 单位
            description: 描述
        
        Returns:
            str: 指标ID
        """
        try:
            metric_id = await self.metric_collector.collect_metric(
                name, metric_type, value, labels, scope, unit, description
            )
            
            # 更新统计
            self.stats["total_metrics"] += 1
            
            # 检查告警规则
            await self._check_alert_rules(name, value, labels or {})
            
            # 通知订阅者
            await self._notify_metric_subscribers(name, value, labels or {})
            
            return metric_id
            
        except Exception as e:
            self.logger.error(f"收集指标失败: {e}")
            raise
    
    async def create_alert(
        self,
        name: str,
        level: AlertLevel,
        message: str,
        scope: MonitoringScope = MonitoringScope.APPLICATION,
        source: Optional[str] = None,
        target: Optional[str] = None,
        user_id: Optional[int] = None,
        session_id: Optional[str] = None,
        agent_id: Optional[str] = None,
        workflow_id: Optional[str] = None,
        task_id: Optional[str] = None,
        labels: Optional[Dict[str, str]] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        创建告警
        
        Args:
            name: 告警名称
            level: 告警级别
            message: 告警消息
            scope: 监控范围
            source: 来源
            target: 目标
            user_id: 用户ID
            session_id: 会话ID
            agent_id: 智能体ID
            workflow_id: 工作流ID
            task_id: 任务ID
            labels: 标签
            metadata: 元数据
        
        Returns:
            str: 告警ID
        """
        try:
            alert = Alert(
                alert_id=str(uuid.uuid4()),
                name=name,
                level=level,
                message=message,
                scope=scope,
                source=source,
                target=target,
                user_id=user_id,
                session_id=session_id,
                agent_id=agent_id,
                workflow_id=workflow_id,
                task_id=task_id,
                labels=labels or {},
                metadata=metadata or {}
            )
            
            # 存储告警
            self.alerts[alert.alert_id] = alert
            self.alert_history.append(alert)
            
            # 更新统计
            self.stats["total_alerts"] += 1
            self.stats["active_alerts"] += 1
            
            # 发布事件
            if self.event_service:
                await self.event_service.publish_event(
                    EventType.NOTIFICATION,
                    "monitoring_service",
                    alert.to_dict(),
                    priority=self._get_event_priority(level),
                    tags=["alert", level.value, scope.value],
                    metadata={"alert_id": alert.alert_id}
                )
            
            # 通知订阅者
            await self._notify_alert_subscribers(alert)
            
            self.logger.warning(f"创建告警: {alert.name} ({level.value}) - {message}")
            return alert.alert_id
            
        except Exception as e:
            self.logger.error(f"创建告警失败: {e}")
            raise
    
    async def resolve_alert(self, alert_id: str, resolved_by: Optional[str] = None) -> bool:
        """
        解决告警
        
        Args:
            alert_id: 告警ID
            resolved_by: 解决者
        
        Returns:
            bool: 是否成功
        """
        try:
            alert = self.alerts.get(alert_id)
            if not alert or alert.status == AlertStatus.RESOLVED:
                return False
            
            alert.resolve(resolved_by)
            
            # 更新统计
            self.stats["active_alerts"] -= 1
            self.stats["resolved_alerts"] += 1
            
            # 发布事件
            if self.event_service:
                await self.event_service.publish_event(
                    EventType.NOTIFICATION,
                    "monitoring_service",
                    {"action": "resolve", "alert": alert.to_dict()},
                    tags=["alert", "resolved"],
                    metadata={"alert_id": alert_id}
                )
            
            self.logger.info(f"告警已解决: {alert.name} (ID: {alert_id})")
            return True
            
        except Exception as e:
            self.logger.error(f"解决告警失败: {e}")
            return False
    
    async def acknowledge_alert(self, alert_id: str, acknowledged_by: str) -> bool:
        """
        确认告警
        
        Args:
            alert_id: 告警ID
            acknowledged_by: 确认者
        
        Returns:
            bool: 是否成功
        """
        try:
            alert = self.alerts.get(alert_id)
            if not alert or alert.status != AlertStatus.ACTIVE:
                return False
            
            alert.acknowledge(acknowledged_by)
            
            # 发布事件
            if self.event_service:
                await self.event_service.publish_event(
                    EventType.NOTIFICATION,
                    "monitoring_service",
                    {"action": "acknowledge", "alert": alert.to_dict()},
                    tags=["alert", "acknowledged"],
                    metadata={"alert_id": alert_id}
                )
            
            self.logger.info(f"告警已确认: {alert.name} (ID: {alert_id}) by {acknowledged_by}")
            return True
            
        except Exception as e:
            self.logger.error(f"确认告警失败: {e}")
            return False
    
    async def create_alert_rule(
        self,
        name: str,
        metric_name: str,
        condition: str,
        threshold: Union[int, float],
        level: AlertLevel,
        scope: MonitoringScope,
        enabled: bool = True,
        labels: Optional[Dict[str, str]] = None,
        metadata: Optional[Dict[str, Any]] = None,
        cooldown_seconds: int = 300
    ) -> str:
        """
        创建告警规则
        
        Args:
            name: 规则名称
            metric_name: 指标名称
            condition: 条件
            threshold: 阈值
            level: 告警级别
            scope: 监控范围
            enabled: 是否启用
            labels: 标签
            metadata: 元数据
            cooldown_seconds: 冷却时间
        
        Returns:
            str: 规则ID
        """
        try:
            rule = AlertRule(
                rule_id=str(uuid.uuid4()),
                name=name,
                metric_name=metric_name,
                condition=condition,
                threshold=threshold,
                level=level,
                scope=scope,
                enabled=enabled,
                labels=labels or {},
                metadata=metadata or {},
                cooldown_seconds=cooldown_seconds
            )
            
            self.alert_rules[rule.rule_id] = rule
            
            # 更新统计
            self.stats["total_rules"] += 1
            if enabled:
                self.stats["active_rules"] += 1
            
            self.logger.info(f"创建告警规则: {name} ({metric_name} {condition} {threshold})")
            return rule.rule_id
            
        except Exception as e:
            self.logger.error(f"创建告警规则失败: {e}")
            raise
    
    async def get_metrics(
        self,
        name_pattern: Optional[str] = None,
        scope: Optional[MonitoringScope] = None,
        labels: Optional[Dict[str, str]] = None,
        time_range: Optional[tuple] = None,
        limit: int = 1000
    ) -> List[Metric]:
        """
        获取指标列表
        
        Args:
            name_pattern: 名称模式
            scope: 监控范围
            labels: 标签
            time_range: 时间范围
            limit: 限制数量
        
        Returns:
            List[Metric]: 指标列表
        """
        return await self.metric_collector.get_metrics(
            name_pattern, scope, labels, time_range, limit
        )
    
    async def get_alerts(
        self,
        status: Optional[AlertStatus] = None,
        level: Optional[AlertLevel] = None,
        scope: Optional[MonitoringScope] = None,
        time_range: Optional[tuple] = None,
        limit: int = 1000
    ) -> List[Alert]:
        """
        获取告警列表
        
        Args:
            status: 告警状态
            level: 告警级别
            scope: 监控范围
            time_range: 时间范围
            limit: 限制数量
        
        Returns:
            List[Alert]: 告警列表
        """
        try:
            filtered_alerts = []
            
            for alert in self.alerts.values():
                # 状态过滤
                if status and alert.status != status:
                    continue
                
                # 级别过滤
                if level and alert.level != level:
                    continue
                
                # 范围过滤
                if scope and alert.scope != scope:
                    continue
                
                # 时间范围过滤
                if time_range:
                    start_time, end_time = time_range
                    if alert.timestamp < start_time or alert.timestamp > end_time:
                        continue
                
                filtered_alerts.append(alert)
            
            # 按时间排序并限制数量
            filtered_alerts.sort(key=lambda x: x.timestamp, reverse=True)
            return filtered_alerts[:limit]
            
        except Exception as e:
            self.logger.error(f"获取告警列表失败: {e}")
            return []
    
    async def get_monitoring_stats(self) -> Dict[str, Any]:
        """
        获取监控统计
        
        Returns:
            Dict[str, Any]: 监控统计
        """
        try:
            # 获取指标统计
            metric_stats = {
                "total_metrics": len(self.metric_collector.metrics),
                "metric_types": {},
                "metric_scopes": {}
            }
            
            for metric in self.metric_collector.metrics.values():
                metric_type = metric.metric_type.value
                metric_scope = metric.scope.value
                
                metric_stats["metric_types"][metric_type] = metric_stats["metric_types"].get(metric_type, 0) + 1
                metric_stats["metric_scopes"][metric_scope] = metric_stats["metric_scopes"].get(metric_scope, 0) + 1
            
            # 获取告警统计
            alert_stats = {
                "total_alerts": len(self.alerts),
                "alert_levels": {},
                "alert_statuses": {},
                "alert_scopes": {}
            }
            
            for alert in self.alerts.values():
                alert_level = alert.level.value
                alert_status = alert.status.value
                alert_scope = alert.scope.value
                
                alert_stats["alert_levels"][alert_level] = alert_stats["alert_levels"].get(alert_level, 0) + 1
                alert_stats["alert_statuses"][alert_status] = alert_stats["alert_statuses"].get(alert_status, 0) + 1
                alert_stats["alert_scopes"][alert_scope] = alert_stats["alert_scopes"].get(alert_scope, 0) + 1
            
            return {
                **self.stats,
                "metric_stats": metric_stats,
                "alert_stats": alert_stats,
                "rule_stats": {
                    "total_rules": len(self.alert_rules),
                    "enabled_rules": sum(1 for rule in self.alert_rules.values() if rule.enabled)
                },
                "timestamp": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"获取监控统计失败: {e}")
            return {}
    
    async def subscribe_to_alerts(
        self,
        subscriber_id: str,
        callback: Callable[[Alert], None],
        level_filter: Optional[Set[AlertLevel]] = None
    ):
        """
        订阅告警
        
        Args:
            subscriber_id: 订阅者ID
            callback: 回调函数
            level_filter: 级别过滤器
        """
        filter_key = "all" if not level_filter else "|".join(sorted(l.value for l in level_filter))
        self.alert_subscribers[f"{subscriber_id}:{filter_key}"].add(callback)
        self.logger.info(f"订阅告警: {subscriber_id} (过滤器: {filter_key})")
    
    async def subscribe_to_metrics(
        self,
        subscriber_id: str,
        metric_name: str,
        callback: Callable[[str, Union[int, float], Dict[str, str]], None]
    ):
        """
        订阅指标
        
        Args:
            subscriber_id: 订阅者ID
            metric_name: 指标名称
            callback: 回调函数
        """
        self.metric_subscribers[f"{subscriber_id}:{metric_name}"].add(callback)
        self.logger.info(f"订阅指标: {subscriber_id} -> {metric_name}")
    
    # 私有方法
    
    async def _setup_default_alert_rules(self):
        """设置默认告警规则"""
        default_rules = [
            {
                "name": "高CPU使用率",
                "metric_name": "system_cpu_usage",
                "condition": ">=",
                "threshold": 80,
                "level": AlertLevel.WARNING,
                "scope": MonitoringScope.SYSTEM
            },
            {
                "name": "极高CPU使用率",
                "metric_name": "system_cpu_usage",
                "condition": ">=",
                "threshold": 95,
                "level": AlertLevel.CRITICAL,
                "scope": MonitoringScope.SYSTEM
            },
            {
                "name": "高内存使用率",
                "metric_name": "system_memory_usage",
                "condition": ">=",
                "threshold": 85,
                "level": AlertLevel.WARNING,
                "scope": MonitoringScope.SYSTEM
            },
            {
                "name": "极高内存使用率",
                "metric_name": "system_memory_usage",
                "condition": ">=",
                "threshold": 95,
                "level": AlertLevel.CRITICAL,
                "scope": MonitoringScope.SYSTEM
            },
            {
                "name": "高磁盘使用率",
                "metric_name": "system_disk_usage",
                "condition": ">=",
                "threshold": 90,
                "level": AlertLevel.WARNING,
                "scope": MonitoringScope.SYSTEM
            },
            {
                "name": "极高磁盘使用率",
                "metric_name": "system_disk_usage",
                "condition": ">=",
                "threshold": 98,
                "level": AlertLevel.CRITICAL,
                "scope": MonitoringScope.SYSTEM
            }
        ]
        
        for rule_config in default_rules:
            await self.create_alert_rule(**rule_config)
    
    async def _check_alert_rules(self, metric_name: str, value: Union[int, float], labels: Dict[str, str]):
        """检查告警规则"""
        for rule in self.alert_rules.values():
            if rule.metric_name == metric_name and rule.should_trigger(value):
                alert = rule.trigger()
                
                # 添加标签信息
                alert.labels.update(labels)
                
                # 存储告警
                self.alerts[alert.alert_id] = alert
                self.alert_history.append(alert)
                
                # 更新统计
                self.stats["total_alerts"] += 1
                self.stats["active_alerts"] += 1
                
                # 发布事件
                if self.event_service:
                    await self.event_service.publish_event(
                        EventType.NOTIFICATION,
                        "monitoring_service",
                        alert.to_dict(),
                        priority=self._get_event_priority(alert.level),
                        tags=["alert", "rule_triggered", alert.level.value],
                        metadata={"alert_id": alert.alert_id, "rule_id": rule.rule_id}
                    )
                
                # 通知订阅者
                await self._notify_alert_subscribers(alert)
                
                self.logger.warning(f"规则触发告警: {rule.name} - {metric_name}={value}")
    
    async def _notify_alert_subscribers(self, alert: Alert):
        """通知告警订阅者"""
        try:
            # 通知所有订阅者
            for key, callbacks in self.alert_subscribers.items():
                subscriber_id, filter_key = key.split(":", 1)
                
                # 检查级别过滤
                if filter_key != "all":
                    allowed_levels = set(AlertLevel(level) for level in filter_key.split("|"))
                    if alert.level not in allowed_levels:
                        continue
                
                # 执行回调
                for callback in callbacks:
                    try:
                        if asyncio.iscoroutinefunction(callback):
                            await callback(alert)
                        else:
                            callback(alert)
                    except Exception as e:
                        self.logger.error(f"告警订阅者回调失败: {subscriber_id} - {e}")
                        
        except Exception as e:
            self.logger.error(f"通知告警订阅者失败: {e}")
    
    async def _notify_metric_subscribers(self, metric_name: str, value: Union[int, float], labels: Dict[str, str]):
        """通知指标订阅者"""
        try:
            # 查找匹配的订阅
            for key, callbacks in self.metric_subscribers.items():
                subscriber_id, subscribed_metric = key.split(":", 1)
                
                if subscribed_metric == metric_name or subscribed_metric == "*":
                    # 执行回调
                    for callback in callbacks:
                        try:
                            if asyncio.iscoroutinefunction(callback):
                                await callback(metric_name, value, labels)
                            else:
                                callback(metric_name, value, labels)
                        except Exception as e:
                            self.logger.error(f"指标订阅者回调失败: {subscriber_id} - {e}")
                            
        except Exception as e:
            self.logger.error(f"通知指标订阅者失败: {e}")
    
    async def _process_alerts(self):
        """处理告警（后台任务）"""
        while True:
            try:
                # 检查是否有需要自动解决的告警
                current_time = datetime.utcnow()
                
                for alert in list(self.alerts.values()):
                    if alert.status == AlertStatus.ACTIVE:
                        # 检查是否超过自动解决时间（如果配置了）
                        auto_resolve_hours = alert.metadata.get("auto_resolve_hours")
                        if auto_resolve_hours:
                            auto_resolve_time = alert.timestamp + timedelta(hours=auto_resolve_hours)
                            if current_time >= auto_resolve_time:
                                await self.resolve_alert(alert.alert_id, "system_auto")
                
                await asyncio.sleep(300)  # 每5分钟检查一次
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"处理告警失败: {e}")
                await asyncio.sleep(300)
    
    async def _update_stats(self):
        """更新统计（后台任务）"""
        while True:
            try:
                # 更新活跃告警数量
                active_alerts = sum(1 for alert in self.alerts.values() if alert.status == AlertStatus.ACTIVE)
                resolved_alerts = sum(1 for alert in self.alerts.values() if alert.status == AlertStatus.RESOLVED)
                
                self.stats["active_alerts"] = active_alerts
                self.stats["resolved_alerts"] = resolved_alerts
                
                # 更新活跃规则数量
                active_rules = sum(1 for rule in self.alert_rules.values() if rule.enabled)
                self.stats["active_rules"] = active_rules
                
                await asyncio.sleep(60)  # 每分钟更新一次
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"更新统计失败: {e}")
                await asyncio.sleep(60)
    
    def _get_event_priority(self, alert_level: AlertLevel) -> EventPriority:
        """获取事件优先级"""
        mapping = {
            AlertLevel.INFO: EventPriority.LOW,
            AlertLevel.WARNING: EventPriority.NORMAL,
            AlertLevel.ERROR: EventPriority.HIGH,
            AlertLevel.CRITICAL: EventPriority.CRITICAL,
            AlertLevel.EMERGENCY: EventPriority.EMERGENCY
        }
        return mapping.get(alert_level, EventPriority.NORMAL)


# 全局监控服务实例
monitoring_service = MonitoringService()