#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A2A多智能体系统 工具执行引擎

提供统一的工具执行接口，支持异步执行、结果缓存、权限控制等功能
"""

import asyncio
import logging
import json
import hashlib
import time
from typing import Dict, List, Any, Optional, Callable, Union, Set, Type
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor, Future
import threading
from contextlib import asynccontextmanager

# Google ADK imports
from google.ai.generativelanguage import FunctionDeclaration, Schema, Type as SchemaType

from .base_tool import (
    BaseTool, ToolConfig, ToolResult, ToolError, ToolStatus,
    ToolExecutionContext, ToolPermission
)
from .tool_registry import ToolRegistry, ToolFilter, get_global_registry


class ExecutionMode(Enum):
    """执行模式枚举"""
    SYNC = "sync"  # 同步执行
    ASYNC = "async"  # 异步执行
    BACKGROUND = "background"  # 后台执行
    BATCH = "batch"  # 批量执行


class ExecutionPriority(Enum):
    """执行优先级枚举"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    URGENT = 4


class ExecutionState(Enum):
    """执行状态枚举"""
    PENDING = "pending"  # 等待执行
    RUNNING = "running"  # 正在执行
    COMPLETED = "completed"  # 执行完成
    FAILED = "failed"  # 执行失败
    CANCELLED = "cancelled"  # 已取消
    TIMEOUT = "timeout"  # 执行超时


@dataclass
class ExecutionRequest:
    """执行请求"""
    # 基础信息
    request_id: str
    tool_name: str
    arguments: Dict[str, Any]
    
    # 执行配置
    mode: ExecutionMode = ExecutionMode.SYNC
    priority: ExecutionPriority = ExecutionPriority.NORMAL
    timeout: Optional[int] = None
    retry_attempts: int = 0
    retry_delay: float = 1.0
    
    # 上下文信息
    user_id: Optional[str] = None
    session_id: Optional[str] = None
    context: Optional[ToolExecutionContext] = None
    
    # 缓存配置
    enable_cache: bool = True
    cache_ttl: Optional[int] = None
    cache_key: Optional[str] = None
    
    # 回调函数
    on_success: Optional[Callable[[ToolResult], None]] = None
    on_error: Optional[Callable[[Exception], None]] = None
    on_progress: Optional[Callable[[Dict[str, Any]], None]] = None
    
    # 元数据
    metadata: Dict[str, Any] = field(default_factory=dict)
    created_at: datetime = field(default_factory=datetime.now)


@dataclass
class ExecutionResult:
    """执行结果"""
    request_id: str
    tool_name: str
    state: ExecutionState
    
    # 结果数据
    result: Optional[ToolResult] = None
    error: Optional[Exception] = None
    
    # 执行信息
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    duration: Optional[float] = None
    
    # 重试信息
    attempt_count: int = 0
    retry_errors: List[str] = field(default_factory=list)
    
    # 缓存信息
    from_cache: bool = False
    cache_hit: bool = False
    
    # 统计信息
    stats: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ExecutorConfig:
    """执行器配置"""
    # 基础配置
    name: str = "A2A-Tool-Executor"
    max_workers: int = 10
    max_queue_size: int = 1000
    
    # 超时配置
    default_timeout: int = 30
    max_timeout: int = 300
    
    # 重试配置
    default_retry_attempts: int = 3
    max_retry_attempts: int = 10
    default_retry_delay: float = 1.0
    
    # 缓存配置
    enable_cache: bool = True
    cache_size: int = 1000
    default_cache_ttl: int = 3600
    
    # 限流配置
    enable_rate_limiting: bool = True
    rate_limit_per_user: int = 100  # 每分钟
    rate_limit_per_tool: int = 1000  # 每分钟
    
    # 监控配置
    enable_metrics: bool = True
    metrics_interval: int = 60
    
    # 日志配置
    log_level: str = "INFO"
    log_executions: bool = True
    log_results: bool = False
    
    # 安全配置
    enable_sandbox: bool = True
    sandbox_timeout: int = 60
    
    # 工具注册器
    tool_registry: Optional[ToolRegistry] = None


class ExecutorError(Exception):
    """执行器异常"""
    pass


class ToolExecutor:
    """工具执行引擎
    
    提供统一的工具执行接口，支持异步执行、结果缓存、权限控制等功能
    """
    
    def __init__(self, config: ExecutorConfig):
        """
        初始化工具执行器
        
        Args:
            config: 执行器配置
        """
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
        self.logger.setLevel(getattr(logging, config.log_level.upper()))
        
        # 工具注册器
        self.registry = config.tool_registry or get_global_registry()
        
        # 线程池
        self.thread_pool = ThreadPoolExecutor(
            max_workers=config.max_workers,
            thread_name_prefix="ToolExecutor"
        )
        
        # 执行队列
        self.execution_queue: asyncio.Queue = asyncio.Queue(maxsize=config.max_queue_size)
        self.background_tasks: Dict[str, asyncio.Task] = {}
        
        # 执行状态
        self.executions: Dict[str, ExecutionResult] = {}
        self.execution_lock = threading.RLock()
        
        # 缓存
        self.cache: Dict[str, Any] = {}
        self.cache_timestamps: Dict[str, datetime] = {}
        self.cache_lock = threading.RLock()
        
        # 限流
        self.rate_limits: Dict[str, List[datetime]] = {}
        self.rate_limit_lock = threading.RLock()
        
        # 统计信息
        self.stats = {
            'total_executions': 0,
            'successful_executions': 0,
            'failed_executions': 0,
            'cached_executions': 0,
            'timeout_executions': 0,
            'cancelled_executions': 0,
            'total_duration': 0.0,
            'average_duration': 0.0,
            'queue_size': 0,
            'active_executions': 0,
            'cache_hit_rate': 0.0,
            'error_rate': 0.0
        }
        
        # 任务管理
        self._worker_task: Optional[asyncio.Task] = None
        self._metrics_task: Optional[asyncio.Task] = None
        self._cleanup_task: Optional[asyncio.Task] = None
        self._running = False
    
    async def start(self) -> None:
        """
        启动执行器
        """
        if self._running:
            return
        
        self._running = True
        
        # 启动工作任务
        self._worker_task = asyncio.create_task(self._worker_loop())
        
        # 启动指标收集任务
        if self.config.enable_metrics:
            self._metrics_task = asyncio.create_task(self._metrics_loop())
        
        # 启动清理任务
        self._cleanup_task = asyncio.create_task(self._cleanup_loop())
        
        self.logger.info("工具执行器已启动")
    
    async def stop(self) -> None:
        """
        停止执行器
        """
        if not self._running:
            return
        
        self._running = False
        
        # 停止工作任务
        if self._worker_task:
            self._worker_task.cancel()
            try:
                await self._worker_task
            except asyncio.CancelledError:
                pass
        
        # 停止指标任务
        if self._metrics_task:
            self._metrics_task.cancel()
            try:
                await self._metrics_task
            except asyncio.CancelledError:
                pass
        
        # 停止清理任务
        if self._cleanup_task:
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
        
        # 取消所有后台任务
        for task in self.background_tasks.values():
            task.cancel()
        
        if self.background_tasks:
            await asyncio.gather(
                *self.background_tasks.values(),
                return_exceptions=True
            )
        self.background_tasks.clear()
        
        # 关闭线程池
        self.thread_pool.shutdown(wait=True)
        
        self.logger.info("工具执行器已停止")
    
    async def execute(
        self,
        tool_name: str,
        arguments: Dict[str, Any],
        **kwargs
    ) -> ExecutionResult:
        """
        执行工具
        
        Args:
            tool_name: 工具名称
            arguments: 工具参数
            **kwargs: 其他执行参数
        
        Returns:
            ExecutionResult: 执行结果
        """
        # 创建执行请求
        request = ExecutionRequest(
            request_id=self._generate_request_id(tool_name, arguments),
            tool_name=tool_name,
            arguments=arguments,
            **kwargs
        )
        
        return await self.execute_request(request)
    
    async def execute_request(self, request: ExecutionRequest) -> ExecutionResult:
        """
        执行请求
        
        Args:
            request: 执行请求
        
        Returns:
            ExecutionResult: 执行结果
        """
        # 验证请求
        await self._validate_request(request)
        
        # 检查缓存
        if request.enable_cache:
            cached_result = self._get_cached_result(request)
            if cached_result:
                return cached_result
        
        # 检查限流
        if self.config.enable_rate_limiting:
            await self._check_rate_limit(request)
        
        # 根据执行模式处理
        if request.mode == ExecutionMode.SYNC:
            return await self._execute_sync(request)
        elif request.mode == ExecutionMode.ASYNC:
            return await self._execute_async(request)
        elif request.mode == ExecutionMode.BACKGROUND:
            return await self._execute_background(request)
        elif request.mode == ExecutionMode.BATCH:
            return await self._execute_batch(request)
        else:
            raise ExecutorError(f"不支持的执行模式: {request.mode}")
    
    async def execute_batch(
        self,
        requests: List[ExecutionRequest]
    ) -> List[ExecutionResult]:
        """
        批量执行工具
        
        Args:
            requests: 执行请求列表
        
        Returns:
            List[ExecutionResult]: 执行结果列表
        """
        tasks = []
        for request in requests:
            request.mode = ExecutionMode.ASYNC
            task = asyncio.create_task(self.execute_request(request))
            tasks.append(task)
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理异常
        final_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                final_results.append(ExecutionResult(
                    request_id=requests[i].request_id,
                    tool_name=requests[i].tool_name,
                    state=ExecutionState.FAILED,
                    error=result
                ))
            else:
                final_results.append(result)
        
        return final_results
    
    def get_execution_status(self, request_id: str) -> Optional[ExecutionResult]:
        """
        获取执行状态
        
        Args:
            request_id: 请求ID
        
        Returns:
            Optional[ExecutionResult]: 执行结果
        """
        with self.execution_lock:
            return self.executions.get(request_id)
    
    async def cancel_execution(self, request_id: str) -> bool:
        """
        取消执行
        
        Args:
            request_id: 请求ID
        
        Returns:
            bool: 是否成功取消
        """
        # 取消后台任务
        if request_id in self.background_tasks:
            task = self.background_tasks[request_id]
            task.cancel()
            del self.background_tasks[request_id]
            
            # 更新状态
            with self.execution_lock:
                if request_id in self.executions:
                    result = self.executions[request_id]
                    result.state = ExecutionState.CANCELLED
                    result.completed_at = datetime.now()
                    if result.started_at:
                        result.duration = (result.completed_at - result.started_at).total_seconds()
            
            self.stats['cancelled_executions'] += 1
            return True
        
        return False
    
    def clear_cache(self, pattern: Optional[str] = None) -> int:
        """
        清理缓存
        
        Args:
            pattern: 缓存键模式（可选）
        
        Returns:
            int: 清理的缓存项数量
        """
        with self.cache_lock:
            if pattern is None:
                count = len(self.cache)
                self.cache.clear()
                self.cache_timestamps.clear()
                return count
            else:
                keys_to_remove = [
                    key for key in self.cache.keys()
                    if pattern in key
                ]
                for key in keys_to_remove:
                    del self.cache[key]
                    if key in self.cache_timestamps:
                        del self.cache_timestamps[key]
                return len(keys_to_remove)
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        with self.execution_lock:
            # 更新队列大小
            self.stats['queue_size'] = self.execution_queue.qsize()
            
            # 更新活跃执行数
            active_count = sum(
                1 for result in self.executions.values()
                if result.state == ExecutionState.RUNNING
            )
            self.stats['active_executions'] = active_count
            
            # 计算平均执行时间
            if self.stats['successful_executions'] > 0:
                self.stats['average_duration'] = (
                    self.stats['total_duration'] / self.stats['successful_executions']
                )
            
            # 计算缓存命中率
            total_executions = self.stats['total_executions']
            if total_executions > 0:
                self.stats['cache_hit_rate'] = (
                    self.stats['cached_executions'] / total_executions
                )
                self.stats['error_rate'] = (
                    self.stats['failed_executions'] / total_executions
                )
            
            return self.stats.copy()
    
    def list_active_executions(self) -> List[ExecutionResult]:
        """
        列出活跃的执行
        
        Returns:
            List[ExecutionResult]: 活跃执行列表
        """
        with self.execution_lock:
            return [
                result for result in self.executions.values()
                if result.state == ExecutionState.RUNNING
            ]
    
    async def _validate_request(self, request: ExecutionRequest) -> None:
        """
        验证执行请求
        
        Args:
            request: 执行请求
        """
        # 检查工具是否存在
        tool = self.registry.get_tool(request.tool_name)
        if not tool:
            raise ExecutorError(f"工具不存在: {request.tool_name}")
        
        # 检查用户权限
        if request.user_id:
            has_permission = self.registry.check_user_permission(
                request.user_id, request.tool_name
            )
            if not has_permission:
                raise ExecutorError(f"用户无权限使用工具: {request.tool_name}")
        
        # 验证参数
        try:
            tool.validate_arguments(request.arguments)
        except Exception as e:
            raise ExecutorError(f"参数验证失败: {str(e)}")
        
        # 检查超时设置
        if request.timeout:
            if request.timeout > self.config.max_timeout:
                request.timeout = self.config.max_timeout
        else:
            request.timeout = self.config.default_timeout
        
        # 检查重试设置
        if request.retry_attempts > self.config.max_retry_attempts:
            request.retry_attempts = self.config.max_retry_attempts
    
    def _get_cached_result(self, request: ExecutionRequest) -> Optional[ExecutionResult]:
        """
        获取缓存结果
        
        Args:
            request: 执行请求
        
        Returns:
            Optional[ExecutionResult]: 缓存的执行结果
        """
        if not self.config.enable_cache:
            return None
        
        cache_key = request.cache_key or self._generate_cache_key(request)
        
        with self.cache_lock:
            if cache_key not in self.cache:
                return None
            
            # 检查缓存是否过期
            cache_time = self.cache_timestamps.get(cache_key)
            if cache_time:
                ttl = request.cache_ttl or self.config.default_cache_ttl
                if (datetime.now() - cache_time).total_seconds() > ttl:
                    del self.cache[cache_key]
                    del self.cache_timestamps[cache_key]
                    return None
            
            # 返回缓存结果
            cached_data = self.cache[cache_key]
            result = ExecutionResult(
                request_id=request.request_id,
                tool_name=request.tool_name,
                state=ExecutionState.COMPLETED,
                result=cached_data,
                from_cache=True,
                cache_hit=True,
                completed_at=datetime.now(),
                duration=0.0
            )
            
            self.stats['cached_executions'] += 1
            return result
    
    def _cache_result(self, request: ExecutionRequest, result: ToolResult) -> None:
        """
        缓存执行结果
        
        Args:
            request: 执行请求
            result: 工具执行结果
        """
        if not self.config.enable_cache or not request.enable_cache:
            return
        
        cache_key = request.cache_key or self._generate_cache_key(request)
        
        with self.cache_lock:
            # 检查缓存大小
            if len(self.cache) >= self.config.cache_size:
                # 删除最旧的缓存项
                oldest_key = min(
                    self.cache_timestamps.keys(),
                    key=lambda k: self.cache_timestamps[k]
                )
                del self.cache[oldest_key]
                del self.cache_timestamps[oldest_key]
            
            # 添加到缓存
            self.cache[cache_key] = result
            self.cache_timestamps[cache_key] = datetime.now()
    
    async def _check_rate_limit(self, request: ExecutionRequest) -> None:
        """
        检查限流
        
        Args:
            request: 执行请求
        """
        now = datetime.now()
        minute_ago = now - timedelta(minutes=1)
        
        with self.rate_limit_lock:
            # 检查用户限流
            if request.user_id:
                user_key = f"user:{request.user_id}"
                if user_key not in self.rate_limits:
                    self.rate_limits[user_key] = []
                
                # 清理过期记录
                self.rate_limits[user_key] = [
                    ts for ts in self.rate_limits[user_key]
                    if ts > minute_ago
                ]
                
                # 检查限制
                if len(self.rate_limits[user_key]) >= self.config.rate_limit_per_user:
                    raise ExecutorError(f"用户请求频率超限: {request.user_id}")
                
                self.rate_limits[user_key].append(now)
            
            # 检查工具限流
            tool_key = f"tool:{request.tool_name}"
            if tool_key not in self.rate_limits:
                self.rate_limits[tool_key] = []
            
            # 清理过期记录
            self.rate_limits[tool_key] = [
                ts for ts in self.rate_limits[tool_key]
                if ts > minute_ago
            ]
            
            # 检查限制
            if len(self.rate_limits[tool_key]) >= self.config.rate_limit_per_tool:
                raise ExecutorError(f"工具请求频率超限: {request.tool_name}")
            
            self.rate_limits[tool_key].append(now)
    
    async def _execute_sync(self, request: ExecutionRequest) -> ExecutionResult:
        """
        同步执行
        
        Args:
            request: 执行请求
        
        Returns:
            ExecutionResult: 执行结果
        """
        return await self._execute_tool(request)
    
    async def _execute_async(self, request: ExecutionRequest) -> ExecutionResult:
        """
        异步执行
        
        Args:
            request: 执行请求
        
        Returns:
            ExecutionResult: 执行结果
        """
        # 添加到队列
        await self.execution_queue.put(request)
        
        # 创建结果对象
        result = ExecutionResult(
            request_id=request.request_id,
            tool_name=request.tool_name,
            state=ExecutionState.PENDING
        )
        
        with self.execution_lock:
            self.executions[request.request_id] = result
        
        return result
    
    async def _execute_background(self, request: ExecutionRequest) -> ExecutionResult:
        """
        后台执行
        
        Args:
            request: 执行请求
        
        Returns:
            ExecutionResult: 执行结果
        """
        # 创建后台任务
        task = asyncio.create_task(self._execute_tool(request))
        self.background_tasks[request.request_id] = task
        
        # 创建结果对象
        result = ExecutionResult(
            request_id=request.request_id,
            tool_name=request.tool_name,
            state=ExecutionState.RUNNING,
            started_at=datetime.now()
        )
        
        with self.execution_lock:
            self.executions[request.request_id] = result
        
        return result
    
    async def _execute_batch(self, request: ExecutionRequest) -> ExecutionResult:
        """
        批量执行（单个请求的批量模式处理）
        
        Args:
            request: 执行请求
        
        Returns:
            ExecutionResult: 执行结果
        """
        # 批量模式下，单个请求按异步处理
        return await self._execute_async(request)
    
    async def _execute_tool(self, request: ExecutionRequest) -> ExecutionResult:
        """
        执行工具
        
        Args:
            request: 执行请求
        
        Returns:
            ExecutionResult: 执行结果
        """
        result = ExecutionResult(
            request_id=request.request_id,
            tool_name=request.tool_name,
            state=ExecutionState.RUNNING,
            started_at=datetime.now()
        )
        
        with self.execution_lock:
            self.executions[request.request_id] = result
        
        try:
            # 获取工具
            tool = self.registry.get_tool(request.tool_name)
            if not tool:
                raise ExecutorError(f"工具不存在: {request.tool_name}")
            
            # 执行工具（带重试）
            last_error = None
            for attempt in range(request.retry_attempts + 1):
                try:
                    result.attempt_count = attempt + 1
                    
                    # 设置执行上下文
                    context = request.context or ToolExecutionContext(
                        user_id=request.user_id,
                        session_id=request.session_id,
                        request_id=request.request_id
                    )
                    
                    # 执行工具
                    if asyncio.iscoroutinefunction(tool.execute):
                        tool_result = await asyncio.wait_for(
                            tool.execute(request.arguments, context),
                            timeout=request.timeout
                        )
                    else:
                        # 在线程池中执行同步工具
                        loop = asyncio.get_event_loop()
                        tool_result = await asyncio.wait_for(
                            loop.run_in_executor(
                                self.thread_pool,
                                lambda: tool.execute(request.arguments, context)
                            ),
                            timeout=request.timeout
                        )
                    
                    # 执行成功
                    result.result = tool_result
                    result.state = ExecutionState.COMPLETED
                    result.completed_at = datetime.now()
                    result.duration = (result.completed_at - result.started_at).total_seconds()
                    
                    # 缓存结果
                    if tool_result.success:
                        self._cache_result(request, tool_result)
                    
                    # 更新统计
                    self.stats['total_executions'] += 1
                    if tool_result.success:
                        self.stats['successful_executions'] += 1
                    else:
                        self.stats['failed_executions'] += 1
                    self.stats['total_duration'] += result.duration
                    
                    # 调用成功回调
                    if request.on_success:
                        try:
                            request.on_success(tool_result)
                        except Exception as e:
                            self.logger.error(f"成功回调异常: {e}")
                    
                    break
                    
                except asyncio.TimeoutError:
                    last_error = ExecutorError(f"工具执行超时: {request.timeout}秒")
                    result.state = ExecutionState.TIMEOUT
                    self.stats['timeout_executions'] += 1
                    break
                    
                except Exception as e:
                    last_error = e
                    result.retry_errors.append(str(e))
                    
                    if attempt < request.retry_attempts:
                        self.logger.warning(
                            f"工具执行失败，将重试 {request.tool_name} "
                            f"(尝试 {attempt + 1}/{request.retry_attempts + 1}): {e}"
                        )
                        await asyncio.sleep(request.retry_delay)
                    else:
                        result.state = ExecutionState.FAILED
                        self.stats['failed_executions'] += 1
                        break
            
            # 处理最终错误
            if result.state in [ExecutionState.FAILED, ExecutionState.TIMEOUT]:
                result.error = last_error
                result.completed_at = datetime.now()
                result.duration = (result.completed_at - result.started_at).total_seconds()
                
                # 调用错误回调
                if request.on_error:
                    try:
                        request.on_error(last_error)
                    except Exception as e:
                        self.logger.error(f"错误回调异常: {e}")
            
            # 记录日志
            if self.config.log_executions:
                self.logger.info(
                    f"工具执行完成: {request.tool_name} "
                    f"状态={result.state.value} "
                    f"耗时={result.duration:.3f}s "
                    f"尝试={result.attempt_count}"
                )
            
            return result
            
        except Exception as e:
            # 未预期的异常
            result.error = e
            result.state = ExecutionState.FAILED
            result.completed_at = datetime.now()
            result.duration = (result.completed_at - result.started_at).total_seconds()
            
            self.stats['total_executions'] += 1
            self.stats['failed_executions'] += 1
            
            self.logger.error(f"工具执行异常 {request.tool_name}: {e}")
            return result
    
    async def _worker_loop(self) -> None:
        """
        工作循环
        """
        try:
            while self._running:
                try:
                    # 从队列获取请求
                    request = await asyncio.wait_for(
                        self.execution_queue.get(),
                        timeout=1.0
                    )
                    
                    # 执行工具
                    result = await self._execute_tool(request)
                    
                    # 标记任务完成
                    self.execution_queue.task_done()
                    
                except asyncio.TimeoutError:
                    continue
                except Exception as e:
                    self.logger.error(f"工作循环异常: {e}")
                    
        except asyncio.CancelledError:
            pass
    
    async def _metrics_loop(self) -> None:
        """
        指标收集循环
        """
        try:
            while self._running:
                await asyncio.sleep(self.config.metrics_interval)
                
                # 收集指标
                stats = self.get_stats()
                
                # 记录指标日志
                self.logger.info(
                    f"执行器指标: "
                    f"总执行={stats['total_executions']} "
                    f"成功={stats['successful_executions']} "
                    f"失败={stats['failed_executions']} "
                    f"缓存命中率={stats['cache_hit_rate']:.2%} "
                    f"错误率={stats['error_rate']:.2%} "
                    f"平均耗时={stats['average_duration']:.3f}s "
                    f"队列大小={stats['queue_size']} "
                    f"活跃执行={stats['active_executions']}"
                )
                
        except asyncio.CancelledError:
            pass
    
    async def _cleanup_loop(self) -> None:
        """
        清理循环
        """
        try:
            while self._running:
                await asyncio.sleep(300)  # 每5分钟清理一次
                
                now = datetime.now()
                
                # 清理过期的执行记录
                with self.execution_lock:
                    expired_keys = [
                        key for key, result in self.executions.items()
                        if result.completed_at and 
                           (now - result.completed_at).total_seconds() > 3600  # 1小时
                    ]
                    for key in expired_keys:
                        del self.executions[key]
                
                # 清理过期的缓存
                with self.cache_lock:
                    expired_keys = [
                        key for key, timestamp in self.cache_timestamps.items()
                        if (now - timestamp).total_seconds() > self.config.default_cache_ttl
                    ]
                    for key in expired_keys:
                        if key in self.cache:
                            del self.cache[key]
                        del self.cache_timestamps[key]
                
                # 清理过期的限流记录
                with self.rate_limit_lock:
                    minute_ago = now - timedelta(minutes=1)
                    for key in list(self.rate_limits.keys()):
                        self.rate_limits[key] = [
                            ts for ts in self.rate_limits[key]
                            if ts > minute_ago
                        ]
                        if not self.rate_limits[key]:
                            del self.rate_limits[key]
                
                # 清理完成的后台任务
                completed_tasks = [
                    request_id for request_id, task in self.background_tasks.items()
                    if task.done()
                ]
                for request_id in completed_tasks:
                    del self.background_tasks[request_id]
                
        except asyncio.CancelledError:
            pass
    
    def _generate_request_id(self, tool_name: str, arguments: Dict[str, Any]) -> str:
        """
        生成请求ID
        
        Args:
            tool_name: 工具名称
            arguments: 工具参数
        
        Returns:
            str: 请求ID
        """
        content = f"{tool_name}:{json.dumps(arguments, sort_keys=True)}:{time.time()}"
        return hashlib.md5(content.encode()).hexdigest()
    
    def _generate_cache_key(self, request: ExecutionRequest) -> str:
        """
        生成缓存键
        
        Args:
            request: 执行请求
        
        Returns:
            str: 缓存键
        """
        content = f"{request.tool_name}:{json.dumps(request.arguments, sort_keys=True)}"
        return hashlib.md5(content.encode()).hexdigest()
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.start()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.stop()


# 全局执行器实例
_global_executor: Optional[ToolExecutor] = None


def get_global_executor() -> Optional[ToolExecutor]:
    """
    获取全局工具执行器
    
    Returns:
        Optional[ToolExecutor]: 全局执行器实例
    """
    return _global_executor


def set_global_executor(executor: ToolExecutor) -> None:
    """
    设置全局工具执行器
    
    Args:
        executor: 执行器实例
    """
    global _global_executor
    _global_executor = executor