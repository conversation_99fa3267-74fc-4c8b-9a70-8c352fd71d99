#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A2A多智能体系统JWT处理模块

负责JWT令牌的生成、验证和管理
"""

import jwt
import uuid
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, Union
from loguru import logger
from sqlalchemy import text

from app.core.config import get_settings
from app.core.database import get_database_manager


class JWTHandler:
    """
    JWT处理器
    
    负责JWT令牌的生成、验证、刷新和撤销
    """
    
    def __init__(self):
        self.settings = get_settings()
        self.algorithm = "HS256"
    
    async def generate_tokens(self, user_id: int, session_id: Optional[str] = None) -> Dict[str, Any]:
        """
        生成访问令牌和刷新令牌
        
        Args:
            user_id: 用户ID
            session_id: 会话ID（可选）
            
        Returns:
            Dict[str, Any]: 包含访问令牌和刷新令牌的字典
        """
        try:
            # 生成会话ID（如果未提供）
            if not session_id:
                session_id = str(uuid.uuid4())
            
            # 获取当前时间
            now = datetime.utcnow()
            
            # 生成访问令牌
            access_payload = {
                "user_id": user_id,
                "session_id": session_id,
                "token_type": "access",
                "iat": now,
                "exp": now + timedelta(minutes=self.settings.jwt_access_token_expire_minutes),
                "jti": str(uuid.uuid4())  # JWT ID
            }
            
            access_token = jwt.encode(
                access_payload,
                self.settings.jwt_secret_key,
                algorithm=self.algorithm
            )
            
            # 生成刷新令牌
            refresh_payload = {
                "user_id": user_id,
                "session_id": session_id,
                "token_type": "refresh",
                "iat": now,
                "exp": now + timedelta(days=self.settings.jwt_refresh_token_expire_days),
                "jti": str(uuid.uuid4())
            }
            
            refresh_token = jwt.encode(
                refresh_payload,
                self.settings.jwt_secret_key,
                algorithm=self.algorithm
            )
            
            # 保存令牌到数据库
            await self._save_token_to_database(
                user_id=user_id,
                session_id=session_id,
                access_token=access_token,
                refresh_token=refresh_token,
                access_expires_at=access_payload["exp"],
                refresh_expires_at=refresh_payload["exp"]
            )
            
            logger.info(f"为用户 {user_id} 生成JWT令牌", extra={"user_id": user_id, "session_id": session_id})
            
            return {
                "access_token": access_token,
                "refresh_token": refresh_token,
                "token_type": "bearer",
                "expires_in": self.settings.jwt_access_token_expire_minutes * 60,
                "session_id": session_id
            }
        
        except Exception as e:
            logger.error(f"生成JWT令牌失败: {str(e)}", extra={"user_id": user_id})
            raise ValueError(f"生成令牌失败: {str(e)}")
    
    async def verify_token(self, token: str, token_type: str = "access") -> Dict[str, Any]:
        """
        验证JWT令牌
        
        Args:
            token: JWT令牌
            token_type: 令牌类型（access或refresh）
            
        Returns:
            Dict[str, Any]: 令牌载荷
            
        Raises:
            ValueError: 令牌无效时抛出异常
        """
        try:
            # 解码JWT令牌
            payload = jwt.decode(
                token,
                self.settings.jwt_secret_key,
                algorithms=[self.algorithm]
            )
            
            # 验证令牌类型
            if payload.get("token_type") != token_type:
                raise ValueError(f"令牌类型不匹配，期望: {token_type}, 实际: {payload.get('token_type')}")
            
            # 验证令牌是否在数据库中存在且有效
            is_valid = await self._verify_token_in_database(token, payload)
            if not is_valid:
                raise ValueError("令牌已被撤销或不存在")
            
            return payload
        
        except jwt.ExpiredSignatureError:
            raise ValueError("令牌已过期")
        except jwt.InvalidTokenError as e:
            raise ValueError(f"无效的令牌: {str(e)}")
        except Exception as e:
            logger.error(f"验证JWT令牌失败: {str(e)}")
            raise ValueError(f"令牌验证失败: {str(e)}")
    
    async def refresh_token(self, refresh_token: str) -> Dict[str, Any]:
        """
        使用刷新令牌生成新的访问令牌
        
        Args:
            refresh_token: 刷新令牌
            
        Returns:
            Dict[str, Any]: 新的令牌信息
        """
        try:
            # 验证刷新令牌
            payload = await self.verify_token(refresh_token, "refresh")
            user_id = payload["user_id"]
            session_id = payload["session_id"]
            
            # 撤销旧的访问令牌
            await self._revoke_access_tokens(user_id, session_id)
            
            # 生成新的访问令牌
            now = datetime.utcnow()
            access_payload = {
                "user_id": user_id,
                "session_id": session_id,
                "token_type": "access",
                "iat": now,
                "exp": now + timedelta(minutes=self.settings.jwt_access_token_expire_minutes),
                "jti": str(uuid.uuid4())
            }
            
            access_token = jwt.encode(
                access_payload,
                self.settings.jwt_secret_key,
                algorithm=self.algorithm
            )
            
            # 更新数据库中的访问令牌
            await self._update_access_token_in_database(
                session_id=session_id,
                access_token=access_token,
                expires_at=access_payload["exp"]
            )
            
            logger.info(f"为用户 {user_id} 刷新访问令牌", extra={"user_id": user_id, "session_id": session_id})
            
            return {
                "access_token": access_token,
                "token_type": "bearer",
                "expires_in": self.settings.jwt_access_token_expire_minutes * 60
            }
        
        except Exception as e:
            logger.error(f"刷新令牌失败: {str(e)}")
            raise ValueError(f"刷新令牌失败: {str(e)}")
    
    async def revoke_token(self, token: str) -> bool:
        """
        撤销令牌
        
        Args:
            token: 要撤销的令牌
            
        Returns:
            bool: 撤销是否成功
        """
        try:
            # 解码令牌获取信息
            payload = jwt.decode(
                token,
                self.settings.jwt_secret_key,
                algorithms=[self.algorithm],
                options={"verify_exp": False}  # 允许过期的令牌
            )
            
            session_id = payload.get("session_id")
            if not session_id:
                return False
            
            # 从数据库中撤销令牌
            await self._revoke_tokens_in_database(session_id)
            
            logger.info(f"撤销会话 {session_id} 的令牌")
            return True
        
        except Exception as e:
            logger.error(f"撤销令牌失败: {str(e)}")
            return False
    
    async def revoke_user_tokens(self, user_id: int) -> bool:
        """
        撤销用户的所有令牌
        
        Args:
            user_id: 用户ID
            
        Returns:
            bool: 撤销是否成功
        """
        try:
            db_manager = get_database_manager()
            engine = db_manager.get_engine()
            
            sql = """
                UPDATE user_tokens
                SET is_revoked = TRUE, revoked_at = :revoked_at
                WHERE user_id = :user_id AND is_revoked = FALSE
            """
            
            async with engine.begin() as conn:
                result = await conn.execute(text(sql), {
                    "user_id": user_id,
                    "revoked_at": datetime.now()
                })
            
            logger.info(f"撤销用户 {user_id} 的所有令牌")
            return result.rowcount > 0
        
        except Exception as e:
            logger.error(f"撤销用户令牌失败: {str(e)}")
            return False
    
    async def _save_token_to_database(
        self,
        user_id: int,
        session_id: str,
        access_token: str,
        refresh_token: str,
        access_expires_at: datetime,
        refresh_expires_at: datetime
    ) -> None:
        """
        保存令牌到数据库
        
        Args:
            user_id: 用户ID
            session_id: 会话ID
            access_token: 访问令牌
            refresh_token: 刷新令牌
            access_expires_at: 访问令牌过期时间
            refresh_expires_at: 刷新令牌过期时间
        """
        try:
            db_manager = get_database_manager()
            engine = db_manager.get_engine()
            
            sql = """
                INSERT INTO user_tokens (
                    user_id, session_id, access_token, refresh_token,
                    access_expires_at, refresh_expires_at, created_at
                ) VALUES (
                    :user_id, :session_id, :access_token, :refresh_token,
                    :access_expires_at, :refresh_expires_at, :created_at
                )
            """
            
            async with engine.begin() as conn:
                await conn.execute(text(sql), {
                    "user_id": user_id,
                    "session_id": session_id,
                    "access_token": access_token,
                    "refresh_token": refresh_token,
                    "access_expires_at": access_expires_at,
                    "refresh_expires_at": refresh_expires_at,
                    "created_at": datetime.now()
                })
        
        except Exception as e:
            logger.error(f"保存令牌到数据库失败: {str(e)}")
            raise
    
    async def _verify_token_in_database(self, token: str, payload: Dict[str, Any]) -> bool:
        """
        在数据库中验证令牌
        
        Args:
            token: JWT令牌
            payload: 令牌载荷
            
        Returns:
            bool: 令牌是否有效
        """
        try:
            db_manager = get_database_manager()
            engine = db_manager.get_engine()
            
            session_id = payload.get("session_id")
            token_type = payload.get("token_type")
            
            if token_type == "access":
                column = "access_token"
                expires_column = "access_expires_at"
            else:
                column = "refresh_token"
                expires_column = "refresh_expires_at"
            
            sql = f"""
                SELECT id FROM user_tokens
                WHERE session_id = :session_id
                AND {column} = :token
                AND {expires_column} > :now
                AND is_revoked = FALSE
            """
            
            async with engine.begin() as conn:
                result = await conn.execute(text(sql), {
                    "session_id": session_id,
                    "token": token,
                    "now": datetime.now()
                })
                
                return result.fetchone() is not None
        
        except Exception as e:
            logger.error(f"数据库验证令牌失败: {str(e)}")
            return False
    
    async def _revoke_access_tokens(self, user_id: int, session_id: str) -> None:
        """
        撤销指定会话的访问令牌
        
        Args:
            user_id: 用户ID
            session_id: 会话ID
        """
        try:
            db_manager = get_database_manager()
            engine = db_manager.get_engine()
            
            sql = """
                UPDATE user_tokens
                SET access_token = NULL, access_expires_at = :now
                WHERE user_id = :user_id AND session_id = :session_id
            """
            
            async with engine.begin() as conn:
                await conn.execute(text(sql), {
                    "user_id": user_id,
                    "session_id": session_id,
                    "now": datetime.now()
                })
        
        except Exception as e:
            logger.error(f"撤销访问令牌失败: {str(e)}")
            raise
    
    async def _update_access_token_in_database(
        self,
        session_id: str,
        access_token: str,
        expires_at: datetime
    ) -> None:
        """
        更新数据库中的访问令牌
        
        Args:
            session_id: 会话ID
            access_token: 新的访问令牌
            expires_at: 过期时间
        """
        try:
            db_manager = get_database_manager()
            engine = db_manager.get_engine()
            
            sql = """
                UPDATE user_tokens
                SET access_token = :access_token, access_expires_at = :expires_at
                WHERE session_id = :session_id
            """
            
            async with engine.begin() as conn:
                await conn.execute(text(sql), {
                    "session_id": session_id,
                    "access_token": access_token,
                    "expires_at": expires_at
                })
        
        except Exception as e:
            logger.error(f"更新访问令牌失败: {str(e)}")
            raise
    
    async def _revoke_tokens_in_database(self, session_id: str) -> None:
        """
        在数据库中撤销令牌
        
        Args:
            session_id: 会话ID
        """
        try:
            db_manager = get_database_manager()
            engine = db_manager.get_engine()
            
            sql = """
                UPDATE user_tokens
                SET is_revoked = TRUE, revoked_at = :revoked_at
                WHERE session_id = :session_id AND is_revoked = FALSE
            """
            
            async with engine.begin() as conn:
                await conn.execute(text(sql), {
                    "session_id": session_id,
                    "revoked_at": datetime.now()
                })
        
        except Exception as e:
            logger.error(f"数据库撤销令牌失败: {str(e)}")
            raise
    
    async def cleanup_expired_tokens(self) -> int:
        """
        清理过期的令牌
        
        Returns:
            int: 清理的令牌数量
        """
        try:
            db_manager = get_database_manager()
            engine = db_manager.get_engine()
            
            sql = """
                DELETE FROM user_tokens
                WHERE (
                    access_expires_at < :now AND refresh_expires_at < :now
                ) OR is_revoked = TRUE
            """
            
            async with engine.begin() as conn:
                result = await conn.execute(text(sql), {"now": datetime.now()})
            
            count = result.rowcount
            logger.info(f"清理了 {count} 个过期令牌")
            return count
        
        except Exception as e:
            logger.error(f"清理过期令牌失败: {str(e)}")
            return 0


# 全局JWT处理器实例
_jwt_handler: Optional[JWTHandler] = None


def get_jwt_handler() -> JWTHandler:
    """
    获取JWT处理器实例（单例模式）
    
    Returns:
        JWTHandler: JWT处理器实例
    """
    global _jwt_handler
    if _jwt_handler is None:
        _jwt_handler = JWTHandler()
    return _jwt_handler