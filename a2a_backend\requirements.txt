# A2A多智能体系统后端依赖

# 核心框架
fastapi>=0.104.0
uvicorn[standard]>=0.24.0
pydantic>=2.5.0
sqlalchemy>=2.0.0
alembic>=1.13.0

# 数据库
psycopg2-binary>=2.9.0  # PostgreSQL
aiosqlite>=0.19.0       # SQLite异步支持

# 认证和安全
python-jose[cryptography]>=3.3.0
passlib[bcrypt]>=1.7.4
python-multipart>=0.0.6
loguru>=0.7.0           # 日志记录
websockets>=12.0        # WebSocket支持

# Google ADK和LLM集成
google-adk>=1.4.2      # Google Agent Development Kit
google-generativeai>=0.3.0
google-cloud-aiplatform>=1.38.0
openai>=1.3.0
anthropic>=0.7.0
litellm>=1.17.0
dashscope>=1.14.0       # 阿里云千问
zhipuai>=2.0.0          # 智谱AI

# 异步和并发
aiohttp>=3.9.0
aiofiles>=23.2.0
anyio>=4.0.0

# 数据处理
pandas>=2.1.0
numpy>=1.24.0
pillow>=10.0.0          # 图像处理

# 文本处理和解析
markdown>=3.5.0
beautifulsoup4>=4.12.0
lxml>=4.9.0
pyyaml>=6.0.1
jinja2>=3.1.0

# 监控和日志
prometheus-client>=0.19.0
structlog>=23.2.0

# 缓存
redis>=5.0.0
aioredis>=2.0.0

# 工具和实用程序
requests>=2.31.0
httpx>=0.25.0
tenacity>=8.2.0         # 重试机制
click>=8.1.0            # CLI工具
python-dotenv>=1.0.0    # 环境变量

# 开发工具（可选）
pytest>=7.4.0
pytest-asyncio>=0.21.0
black>=23.0.0
flake8>=6.0.0
mypy>=1.7.0

# 性能监控
psutil>=5.9.0
memory-profiler>=0.61.0

# 类型检查
types-requests>=2.31.0
types-PyYAML>=6.0.0