#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A2A多智能体系统 智能体工厂

根据配置和用户权限创建智能体实例
"""

import logging
from typing import Dict, List, Optional, Any, Type, Union
from datetime import datetime
import json
import uuid

from app.models.user import User
from app.models.agent import Agent as AgentModel
from app.models.workflow import Workflow
from app.core.logging import get_logger
from app.auth.permissions import check_user_permission

from .agent_registry import agent_registry
from .custom_llm_agent import CustomLLMAgent
from .workflow_agent import WorkflowAgent
from .tool_agent import ToolAgent

class AgentFactory:
    """
    智能体工厂，根据配置和用户权限创建智能体实例
    
    提供以下功能：
    1. 智能体实例创建
    2. 配置验证和处理
    3. 权限检查和验证
    4. 实例缓存管理
    5. 批量创建支持
    6. 创建历史记录
    """
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        """
        初始化智能体工厂
        
        Args:
            logger: 日志记录器
        """
        self.logger = logger or get_logger("agent_factory")
        
        # 创建历史记录
        self.creation_history: List[Dict[str, Any]] = []
        
        # 创建统计
        self.creation_stats = {
            "total_created": 0,
            "successful_creations": 0,
            "failed_creations": 0,
            "types_created": {},
            "users_served": set(),
            "last_creation": None
        }
        
        self.logger.info("AgentFactory已初始化")
    
    async def _check_user_permission(
        self,
        user_id: int,
        owner_id: int,
        action: str = "create",
        resource_type: str = "agent",
        resource_id: Optional[str] = None
    ) -> bool:
        """
        检查用户权限
        
        Args:
            user_id: 用户ID
            owner_id: 拥有者ID
            action: 操作类型
            resource_type: 资源类型
            resource_id: 资源ID
            
        Returns:
            bool: 是否有权限
        """
        try:
            # 检查用户是否存在
            user = await User.get_by_id(user_id)
            if not user:
                self.logger.error(f"用户不存在: {user_id}")
                return False
            
            # 检查用户权限
            has_permission = await check_user_permission(
                user_id=user_id,
                owner_id=owner_id,
                resource_type=resource_type,
                action=action,
                resource_id=resource_id
            )
            
            if not has_permission:
                self.logger.error(f"用户 {user_id} 没有权限{action} {resource_type}")
            
            return has_permission
        except Exception as e:
            self.logger.error(f"权限检查错误: {str(e)}")
            return False
    
    def _validate_agent_config(
        self,
        agent_type: str,
        config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        验证智能体配置
        
        Args:
            agent_type: 智能体类型
            config: 配置信息
            
        Returns:
            Dict[str, Any]: 验证结果
        """
        try:
            # 使用注册器验证配置
            validation_result = agent_registry.validate_config(agent_type, config)
            
            # 添加额外的验证逻辑
            if agent_type == "custom_llm":
                validation_result = self._validate_llm_config(config, validation_result)
            elif agent_type == "workflow":
                validation_result = self._validate_workflow_config(config, validation_result)
            elif agent_type == "tool":
                validation_result = self._validate_tool_config(config, validation_result)
            
            return validation_result
        except Exception as e:
            self.logger.error(f"配置验证错误: {str(e)}")
            return {
                "valid": False,
                "errors": [f"配置验证错误: {str(e)}"]
            }
    
    def _validate_llm_config(
        self,
        config: Dict[str, Any],
        base_result: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        验证LLM智能体配置
        
        Args:
            config: 配置信息
            base_result: 基础验证结果
            
        Returns:
            Dict[str, Any]: 验证结果
        """
        errors = base_result.get("errors", [])
        
        # 验证LLM提供商
        llm_provider = config.get("llm_provider")
        if llm_provider:
            supported_providers = ["google", "openai", "anthropic", "azure", "local"]
            if llm_provider not in supported_providers:
                errors.append(f"不支持的LLM提供商: {llm_provider}")
        
        # 验证模型名称
        model_name = config.get("model_name")
        if model_name and llm_provider:
            if not self._is_valid_model_for_provider(llm_provider, model_name):
                errors.append(f"模型 {model_name} 不适用于提供商 {llm_provider}")
        
        # 验证数值参数
        numeric_params = {
            "temperature": (0.0, 2.0),
            "max_tokens": (1, 100000),
            "top_p": (0.0, 1.0),
            "frequency_penalty": (-2.0, 2.0),
            "presence_penalty": (-2.0, 2.0)
        }
        
        for param, (min_val, max_val) in numeric_params.items():
            if param in config:
                value = config[param]
                if not isinstance(value, (int, float)) or not (min_val <= value <= max_val):
                    errors.append(f"参数 {param} 值无效，应在 {min_val} 到 {max_val} 之间")
        
        return {
            "valid": len(errors) == 0,
            "errors": errors
        }
    
    def _validate_workflow_config(
        self,
        config: Dict[str, Any],
        base_result: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        验证工作流智能体配置
        
        Args:
            config: 配置信息
            base_result: 基础验证结果
            
        Returns:
            Dict[str, Any]: 验证结果
        """
        errors = base_result.get("errors", [])
        
        # 验证工作流ID
        workflow_id = config.get("workflow_id")
        if workflow_id and not isinstance(workflow_id, int):
            errors.append("workflow_id 必须是整数")
        
        # 验证数值参数
        numeric_params = {
            "max_parallel_nodes": (1, 100),
            "timeout": (1, 3600),
            "retry_count": (0, 10)
        }
        
        for param, (min_val, max_val) in numeric_params.items():
            if param in config:
                value = config[param]
                if not isinstance(value, int) or not (min_val <= value <= max_val):
                    errors.append(f"参数 {param} 值无效，应在 {min_val} 到 {max_val} 之间")
        
        return {
            "valid": len(errors) == 0,
            "errors": errors
        }
    
    def _validate_tool_config(
        self,
        config: Dict[str, Any],
        base_result: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        验证工具智能体配置
        
        Args:
            config: 配置信息
            base_result: 基础验证结果
            
        Returns:
            Dict[str, Any]: 验证结果
        """
        errors = base_result.get("errors", [])
        
        # 验证数值参数
        numeric_params = {
            "max_tool_calls": (1, 100),
            "tool_timeout": (1, 300)
        }
        
        for param, (min_val, max_val) in numeric_params.items():
            if param in config:
                value = config[param]
                if not isinstance(value, int) or not (min_val <= value <= max_val):
                    errors.append(f"参数 {param} 值无效，应在 {min_val} 到 {max_val} 之间")
        
        # 验证允许的工具列表
        allowed_tools = config.get("allowed_tools")
        if allowed_tools and not isinstance(allowed_tools, list):
            errors.append("allowed_tools 必须是列表")
        
        return {
            "valid": len(errors) == 0,
            "errors": errors
        }
    
    def _is_valid_model_for_provider(
        self,
        provider: str,
        model_name: str
    ) -> bool:
        """
        检查模型是否适用于指定的提供商
        
        Args:
            provider: LLM提供商
            model_name: 模型名称
            
        Returns:
            bool: 是否有效
        """
        # 定义各提供商支持的模型
        provider_models = {
            "google": ["gemini-pro", "gemini-pro-vision", "gemini-1.5-pro", "gemini-1.5-flash"],
            "openai": ["gpt-3.5-turbo", "gpt-4", "gpt-4-turbo", "gpt-4o", "gpt-4o-mini"],
            "anthropic": ["claude-3-haiku", "claude-3-sonnet", "claude-3-opus", "claude-3.5-sonnet"],
            "azure": ["gpt-35-turbo", "gpt-4", "gpt-4-32k"],
            "local": []  # 本地模型不限制
        }
        
        if provider == "local":
            return True  # 本地模型不限制
        
        return model_name in provider_models.get(provider, [])
    
    def _record_creation(
        self,
        user_id: int,
        agent_type: str,
        agent_id: Optional[int] = None,
        success: bool = True,
        error: Optional[str] = None
    ) -> None:
        """
        记录创建历史
        
        Args:
            user_id: 用户ID
            agent_type: 智能体类型
            agent_id: 智能体ID
            success: 是否成功
            error: 错误信息
        """
        record = {
            "timestamp": datetime.now().isoformat(),
            "user_id": user_id,
            "agent_type": agent_type,
            "agent_id": agent_id,
            "success": success,
            "error": error
        }
        
        self.creation_history.append(record)
        
        # 限制历史记录数量
        if len(self.creation_history) > 1000:
            self.creation_history = self.creation_history[-1000:]
        
        # 更新统计
        self.creation_stats["total_created"] += 1
        if success:
            self.creation_stats["successful_creations"] += 1
        else:
            self.creation_stats["failed_creations"] += 1
        
        if agent_type not in self.creation_stats["types_created"]:
            self.creation_stats["types_created"][agent_type] = 0
        self.creation_stats["types_created"][agent_type] += 1
        
        self.creation_stats["users_served"].add(user_id)
        self.creation_stats["last_creation"] = datetime.now().isoformat()
    
    async def create_agent(
        self,
        agent_type: str,
        user_id: int,
        owner_id: int,
        agent_model: AgentModel,
        config: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> Optional[Any]:
        """
        创建智能体实例
        
        Args:
            agent_type: 智能体类型
            user_id: 用户ID
            owner_id: 拥有者ID
            agent_model: 智能体模型
            config: 配置信息
            **kwargs: 其他参数
            
        Returns:
            Optional[Any]: 智能体实例
        """
        try:
            self.logger.info(f"开始创建智能体: {agent_type} (用户: {user_id})")
            
            # 检查用户权限
            has_permission = await self._check_user_permission(
                user_id=user_id,
                owner_id=owner_id,
                action="create",
                resource_type="agent"
            )
            if not has_permission:
                error_msg = f"用户 {user_id} 没有权限创建智能体"
                self._record_creation(user_id, agent_type, success=False, error=error_msg)
                raise PermissionError(error_msg)
            
            # 检查智能体类型权限
            has_type_permission = await self._check_user_permission(
                user_id=user_id,
                owner_id=owner_id,
                action="use",
                resource_type="agent_type",
                resource_id=agent_type
            )
            if not has_type_permission:
                error_msg = f"用户 {user_id} 没有权限使用 {agent_type} 类型的智能体"
                self._record_creation(user_id, agent_type, success=False, error=error_msg)
                raise PermissionError(error_msg)
            
            # 验证配置
            config = config or {}
            validation_result = self._validate_agent_config(agent_type, config)
            if not validation_result["valid"]:
                error_msg = f"配置验证失败: {validation_result['errors']}"
                self._record_creation(user_id, agent_type, success=False, error=error_msg)
                raise ValueError(error_msg)
            
            # 检查是否有缓存的实例
            cached_instance = agent_registry.get_cached_instance(
                agent_type, agent_model.id, user_id
            )
            if cached_instance:
                self.logger.info(f"使用缓存的智能体实例: {agent_type} (ID: {agent_model.id})")
                self._record_creation(user_id, agent_type, agent_model.id, success=True)
                return cached_instance
            
            # 使用注册器创建实例
            instance = await agent_registry.create_agent_instance(
                type_name=agent_type,
                user_id=user_id,
                owner_id=owner_id,
                agent_model=agent_model,
                config=config,
                **kwargs
            )
            
            if instance:
                self.logger.info(f"智能体创建成功: {agent_type} (ID: {agent_model.id})")
                self._record_creation(user_id, agent_type, agent_model.id, success=True)
            else:
                error_msg = "智能体创建失败"
                self._record_creation(user_id, agent_type, success=False, error=error_msg)
                raise RuntimeError(error_msg)
            
            return instance
        except Exception as e:
            error_msg = str(e)
            self.logger.error(f"创建智能体失败: {error_msg}")
            self._record_creation(user_id, agent_type, success=False, error=error_msg)
            raise e
    
    async def create_custom_llm_agent(
        self,
        user_id: int,
        owner_id: int,
        agent_model: AgentModel,
        llm_provider: str,
        model_name: str,
        api_key: Optional[str] = None,
        **kwargs
    ) -> Optional[CustomLLMAgent]:
        """
        创建自定义LLM智能体
        
        Args:
            user_id: 用户ID
            owner_id: 拥有者ID
            agent_model: 智能体模型
            llm_provider: LLM提供商
            model_name: 模型名称
            api_key: API密钥
            **kwargs: 其他参数
            
        Returns:
            Optional[CustomLLMAgent]: CustomLLMAgent实例
        """
        config = {
            "llm_provider": llm_provider,
            "model_name": model_name
        }
        
        if api_key:
            config["api_key"] = api_key
        
        # 添加其他配置参数
        for key, value in kwargs.items():
            if key in ["base_url", "temperature", "max_tokens", "top_p", "frequency_penalty", "presence_penalty"]:
                config[key] = value
        
        return await self.create_agent(
            agent_type="custom_llm",
            user_id=user_id,
            owner_id=owner_id,
            agent_model=agent_model,
            config=config
        )
    
    async def create_workflow_agent(
        self,
        user_id: int,
        owner_id: int,
        workflow_model: Workflow,
        **kwargs
    ) -> Optional[WorkflowAgent]:
        """
        创建工作流智能体
        
        Args:
            user_id: 用户ID
            owner_id: 拥有者ID
            workflow_model: 工作流模型
            **kwargs: 其他参数
            
        Returns:
            Optional[WorkflowAgent]: WorkflowAgent实例
        """
        config = {
            "workflow_id": workflow_model.id
        }
        
        # 添加其他配置参数
        for key, value in kwargs.items():
            if key in ["max_parallel_nodes", "timeout", "retry_count"]:
                config[key] = value
        
        # 创建临时的AgentModel用于工作流智能体
        agent_model = AgentModel(
            name=workflow_model.name,
            description=workflow_model.description,
            type="workflow",
            user_id=user_id,
            owner_id=owner_id
        )
        
        return await self.create_agent(
            agent_type="workflow",
            user_id=user_id,
            owner_id=owner_id,
            agent_model=agent_model,
            config=config
        )
    
    async def create_tool_agent(
        self,
        user_id: int,
        owner_id: int,
        agent_model: AgentModel,
        **kwargs
    ) -> Optional[ToolAgent]:
        """
        创建工具智能体
        
        Args:
            user_id: 用户ID
            owner_id: 拥有者ID
            agent_model: 智能体模型
            **kwargs: 其他参数
            
        Returns:
            Optional[ToolAgent]: ToolAgent实例
        """
        config = {}
        
        # 添加配置参数
        for key, value in kwargs.items():
            if key in ["max_tool_calls", "tool_timeout", "allowed_tools"]:
                config[key] = value
        
        return await self.create_agent(
            agent_type="tool",
            user_id=user_id,
            owner_id=owner_id,
            agent_model=agent_model,
            config=config
        )
    
    async def batch_create_agents(
        self,
        agent_specs: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """
        批量创建智能体
        
        Args:
            agent_specs: 智能体规格列表
            
        Returns:
            List[Dict[str, Any]]: 创建结果列表
        """
        results = []
        
        for spec in agent_specs:
            try:
                agent_type = spec.get("agent_type")
                user_id = spec.get("user_id")
                owner_id = spec.get("owner_id")
                agent_model = spec.get("agent_model")
                config = spec.get("config", {})
                
                instance = await self.create_agent(
                    agent_type=agent_type,
                    user_id=user_id,
                    owner_id=owner_id,
                    agent_model=agent_model,
                    config=config
                )
                
                results.append({
                    "success": True,
                    "agent_type": agent_type,
                    "agent_id": agent_model.id if agent_model else None,
                    "instance": instance
                })
            except Exception as e:
                results.append({
                    "success": False,
                    "agent_type": spec.get("agent_type"),
                    "error": str(e)
                })
        
        return results
    
    def get_creation_history(
        self,
        user_id: Optional[int] = None,
        agent_type: Optional[str] = None,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """
        获取创建历史
        
        Args:
            user_id: 用户ID过滤
            agent_type: 智能体类型过滤
            limit: 返回数量限制
            
        Returns:
            List[Dict[str, Any]]: 创建历史记录
        """
        history = self.creation_history.copy()
        
        # 应用过滤器
        if user_id is not None:
            history = [record for record in history if record["user_id"] == user_id]
        
        if agent_type is not None:
            history = [record for record in history if record["agent_type"] == agent_type]
        
        # 按时间倒序排列并限制数量
        history.sort(key=lambda x: x["timestamp"], reverse=True)
        return history[:limit]
    
    def get_creation_stats(self) -> Dict[str, Any]:
        """
        获取创建统计
        
        Returns:
            Dict[str, Any]: 创建统计信息
        """
        stats = self.creation_stats.copy()
        stats["users_served"] = len(stats["users_served"])
        return stats
    
    def clear_cache_for_user(self, user_id: int) -> None:
        """
        清除用户的缓存
        
        Args:
            user_id: 用户ID
        """
        agent_registry.clear_user_cache(user_id)
        self.logger.info(f"用户 {user_id} 的缓存已清除")

# 全局工厂实例
agent_factory = AgentFactory()