#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A2A多智能体系统启动脚本

功能：
1. 检查系统环境
2. 验证配置
3. 启动应用服务
"""

import asyncio
import sys
import os
import subprocess
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from loguru import logger
from app.core.config import get_settings
from app.core.database import get_database_manager


def check_python_version():
    """
    检查Python版本
    
    Returns:
        bool: 是否满足要求
    """
    logger.info("检查Python版本...")
    
    version = sys.version_info
    if version.major != 3 or version.minor < 12:
        logger.error(f"Python版本不满足要求，当前版本: {version.major}.{version.minor}")
        logger.error("请使用Python 3.12或更高版本")
        return False
    
    logger.success(f"Python版本检查通过: {version.major}.{version.minor}.{version.micro}")
    return True


def check_environment_variables():
    """
    检查环境变量
    
    Returns:
        bool: 是否配置正确
    """
    logger.info("检查环境变量配置...")
    
    try:
        settings = get_settings()
        
        # 检查必需的配置
        required_configs = [
            ("DATABASE_URL", settings.database_url),
            ("JWT_SECRET_KEY", settings.jwt_secret_key),
        ]
        
        for config_name, config_value in required_configs:
            if not config_value or config_value == "your-super-secret-jwt-key-change-in-production":
                logger.error(f"配置项 {config_name} 未正确设置")
                return False
        
        logger.success("环境变量配置检查通过")
        return True
        
    except Exception as e:
        logger.error(f"检查环境变量时发生错误: {str(e)}")
        return False


async def check_database_connection():
    """
    检查数据库连接
    
    Returns:
        bool: 是否连接成功
    """
    logger.info("检查数据库连接...")
    
    try:
        db_manager = get_database_manager()
        
        # 测试连接
        async with db_manager.get_session() as session:
            await session.execute("SELECT 1")
        
        logger.success("数据库连接检查通过")
        return True
        
    except Exception as e:
        logger.error(f"数据库连接失败: {str(e)}")
        logger.error("请检查数据库配置和服务状态")
        return False


def check_redis_connection():
    """
    检查Redis连接
    
    Returns:
        bool: 是否连接成功
    """
    logger.info("检查Redis连接...")
    
    try:
        import redis
        settings = get_settings()
        
        # 解析Redis URL
        if hasattr(settings, 'redis_url'):
            r = redis.from_url(settings.redis_url)
        else:
            r = redis.Redis(host='localhost', port=6379, db=0)
        
        # 测试连接
        r.ping()
        
        logger.success("Redis连接检查通过")
        return True
        
    except ImportError:
        logger.warning("Redis库未安装，跳过Redis连接检查")
        return True
    except Exception as e:
        logger.warning(f"Redis连接失败: {str(e)}")
        logger.warning("Redis连接失败不会阻止应用启动，但可能影响缓存功能")
        return True


def check_google_credentials():
    """
    检查Google凭证
    
    Returns:
        bool: 是否配置正确
    """
    logger.info("检查Google ADK凭证...")
    
    try:
        settings = get_settings()
        
        # 检查凭证文件
        if hasattr(settings, 'google_adk_credentials_path') and settings.google_adk_credentials_path:
            cred_path = Path(settings.google_adk_credentials_path)
            if not cred_path.exists():
                logger.warning(f"Google凭证文件不存在: {cred_path}")
                logger.warning("Google ADK功能可能无法正常使用")
                return True
        
        # 检查环境变量
        if not os.getenv('GOOGLE_APPLICATION_CREDENTIALS'):
            logger.warning("GOOGLE_APPLICATION_CREDENTIALS环境变量未设置")
            logger.warning("Google ADK功能可能无法正常使用")
            return True
        
        logger.success("Google ADK凭证检查通过")
        return True
        
    except Exception as e:
        logger.warning(f"检查Google凭证时发生错误: {str(e)}")
        return True


def check_required_directories():
    """
    检查并创建必需的目录
    
    Returns:
        bool: 是否成功
    """
    logger.info("检查必需的目录...")
    
    try:
        required_dirs = [
            "logs",
            "storage",
            "credentials"
        ]
        
        for dir_name in required_dirs:
            dir_path = project_root / dir_name
            if not dir_path.exists():
                dir_path.mkdir(parents=True, exist_ok=True)
                logger.info(f"创建目录: {dir_path}")
        
        logger.success("目录检查完成")
        return True
        
    except Exception as e:
        logger.error(f"创建目录时发生错误: {str(e)}")
        return False


async def run_health_checks():
    """
    运行所有健康检查
    
    Returns:
        bool: 是否全部通过
    """
    logger.info("=" * 50)
    logger.info("开始系统健康检查")
    logger.info("=" * 50)
    
    checks = [
        ("Python版本", check_python_version),
        ("环境变量", check_environment_variables),
        ("必需目录", check_required_directories),
        ("数据库连接", check_database_connection),
        ("Redis连接", check_redis_connection),
        ("Google凭证", check_google_credentials),
    ]
    
    all_passed = True
    
    for check_name, check_func in checks:
        try:
            if asyncio.iscoroutinefunction(check_func):
                result = await check_func()
            else:
                result = check_func()
            
            if not result:
                all_passed = False
                logger.error(f"❌ {check_name} 检查失败")
            else:
                logger.success(f"✅ {check_name} 检查通过")
                
        except Exception as e:
            logger.error(f"❌ {check_name} 检查时发生错误: {str(e)}")
            all_passed = False
    
    return all_passed


def start_application(mode="development"):
    """
    启动应用
    
    Args:
        mode: 启动模式 (development/production)
    """
    logger.info(f"以 {mode} 模式启动应用...")
    
    try:
        if mode == "development":
            # 开发模式
            cmd = [
                sys.executable, "main.py"
            ]
        else:
            # 生产模式
            cmd = [
                "uvicorn", "main:app",
                "--host", "0.0.0.0",
                "--port", "8000",
                "--workers", "4"
            ]
        
        logger.info(f"执行命令: {' '.join(cmd)}")
        subprocess.run(cmd, cwd=project_root)
        
    except KeyboardInterrupt:
        logger.info("用户中断了应用")
    except Exception as e:
        logger.error(f"启动应用时发生错误: {str(e)}")


async def main():
    """
    主函数
    """
    import argparse
    
    parser = argparse.ArgumentParser(description="A2A多智能体系统启动脚本")
    parser.add_argument(
        "--mode", 
        choices=["development", "production"], 
        default="development",
        help="启动模式"
    )
    parser.add_argument(
        "--skip-checks", 
        action="store_true",
        help="跳过健康检查"
    )
    
    args = parser.parse_args()
    
    logger.info("A2A多智能体系统启动脚本")
    logger.info(f"启动模式: {args.mode}")
    
    # 运行健康检查
    if not args.skip_checks:
        if not await run_health_checks():
            logger.error("健康检查失败，请修复问题后重试")
            logger.info("如需跳过检查，请使用 --skip-checks 参数")
            return False
        
        logger.success("=" * 50)
        logger.success("所有健康检查通过，准备启动应用")
        logger.success("=" * 50)
    else:
        logger.warning("跳过健康检查")
    
    # 启动应用
    start_application(args.mode)
    
    return True


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("用户中断了启动过程")
    except Exception as e:
        logger.error(f"启动过程中发生错误: {str(e)}")
        sys.exit(1)
