#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A2A多智能体系统 提示词模板管理

提供提示词模板的创建、管理和渲染功能
"""

import re
import json
import asyncio
import logging
from typing import Dict, List, Any, Optional, Union, Callable
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime
from pathlib import Path
from jinja2 import Template, Environment, BaseLoader, TemplateNotFound
import yaml


class TemplateType(Enum):
    """模板类型枚举"""
    SYSTEM = "system"
    USER = "user"
    ASSISTANT = "assistant"
    FUNCTION = "function"
    CHAT = "chat"
    COMPLETION = "completion"


class TemplateFormat(Enum):
    """模板格式枚举"""
    JINJA2 = "jinja2"
    F_STRING = "f_string"
    SIMPLE = "simple"


@dataclass
class TemplateVariable:
    """模板变量定义"""
    name: str
    type: str
    description: str
    required: bool = True
    default: Any = None
    validation: Optional[str] = None  # 正则表达式验证
    examples: List[str] = field(default_factory=list)


@dataclass
class PromptTemplate:
    """提示词模板"""
    name: str
    description: str
    template: str
    template_type: TemplateType
    template_format: TemplateFormat = TemplateFormat.JINJA2
    variables: Dict[str, TemplateVariable] = field(default_factory=dict)
    tags: List[str] = field(default_factory=list)
    category: str = "general"
    version: str = "1.0.0"
    author: str = ""
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    usage_count: int = 0
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "name": self.name,
            "description": self.description,
            "template": self.template,
            "template_type": self.template_type.value,
            "template_format": self.template_format.value,
            "variables": {
                k: {
                    "name": v.name,
                    "type": v.type,
                    "description": v.description,
                    "required": v.required,
                    "default": v.default,
                    "validation": v.validation,
                    "examples": v.examples
                } for k, v in self.variables.items()
            },
            "tags": self.tags,
            "category": self.category,
            "version": self.version,
            "author": self.author,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
            "usage_count": self.usage_count,
            "metadata": self.metadata
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'PromptTemplate':
        """从字典创建模板"""
        variables = {}
        for k, v in data.get("variables", {}).items():
            variables[k] = TemplateVariable(
                name=v["name"],
                type=v["type"],
                description=v["description"],
                required=v.get("required", True),
                default=v.get("default"),
                validation=v.get("validation"),
                examples=v.get("examples", [])
            )
        
        return cls(
            name=data["name"],
            description=data["description"],
            template=data["template"],
            template_type=TemplateType(data["template_type"]),
            template_format=TemplateFormat(data.get("template_format", "jinja2")),
            variables=variables,
            tags=data.get("tags", []),
            category=data.get("category", "general"),
            version=data.get("version", "1.0.0"),
            author=data.get("author", ""),
            created_at=datetime.fromisoformat(data.get("created_at", datetime.now().isoformat())),
            updated_at=datetime.fromisoformat(data.get("updated_at", datetime.now().isoformat())),
            usage_count=data.get("usage_count", 0),
            metadata=data.get("metadata", {})
        )


@dataclass
class RenderResult:
    """渲染结果"""
    template_name: str
    rendered_content: str
    variables_used: Dict[str, Any]
    render_time: float
    success: bool
    error: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)


class CustomTemplateLoader(BaseLoader):
    """自定义模板加载器"""
    
    def __init__(self, templates: Dict[str, str]):
        self.templates = templates
    
    def get_source(self, environment, template):
        if template not in self.templates:
            raise TemplateNotFound(template)
        
        source = self.templates[template]
        return source, None, lambda: True


class PromptTemplateManager:
    """提示词模板管理器"""
    
    def __init__(self, template_dir: Optional[str] = None):
        """
        初始化提示词模板管理器
        
        Args:
            template_dir: 模板目录路径
        """
        self.logger = logging.getLogger(__name__)
        self.template_dir = Path(template_dir) if template_dir else None
        
        # 模板存储
        self.templates: Dict[str, PromptTemplate] = {}
        
        # 模板分类
        self.categories: Dict[str, List[str]] = {}
        
        # Jinja2环境
        self.jinja_env = Environment(
            loader=CustomTemplateLoader({}),
            trim_blocks=True,
            lstrip_blocks=True
        )
        
        # 渲染历史
        self.render_history: List[RenderResult] = []
        
        # 最大历史记录数
        self._max_history = 1000
        
        # 内置模板
        self._load_builtin_templates()
        
        # 从文件加载模板
        if self.template_dir and self.template_dir.exists():
            asyncio.create_task(self._load_templates_from_dir())
        
        self.logger.info("提示词模板管理器初始化完成")
    
    def register_template(
        self,
        name: str,
        description: str,
        template: str,
        template_type: TemplateType,
        template_format: TemplateFormat = TemplateFormat.JINJA2,
        variables: Optional[Dict[str, TemplateVariable]] = None,
        **kwargs
    ) -> PromptTemplate:
        """
        注册模板
        
        Args:
            name: 模板名称
            description: 模板描述
            template: 模板内容
            template_type: 模板类型
            template_format: 模板格式
            variables: 模板变量
            **kwargs: 其他参数
        
        Returns:
            PromptTemplate: 注册的模板
        """
        try:
            # 创建模板对象
            prompt_template = PromptTemplate(
                name=name,
                description=description,
                template=template,
                template_type=template_type,
                template_format=template_format,
                variables=variables or {},
                **kwargs
            )
            
            # 验证模板
            self._validate_template(prompt_template)
            
            # 注册模板
            self.templates[name] = prompt_template
            
            # 更新分类
            category = prompt_template.category
            if category not in self.categories:
                self.categories[category] = []
            if name not in self.categories[category]:
                self.categories[category].append(name)
            
            # 更新Jinja2加载器
            if template_format == TemplateFormat.JINJA2:
                self._update_jinja_loader()
            
            self.logger.info(f"注册模板: {name}, 类型: {template_type.value}")
            
            return prompt_template
            
        except Exception as e:
            self.logger.error(f"注册模板失败: {e}")
            raise
    
    async def render_template(
        self,
        template_name: str,
        variables: Dict[str, Any],
        validate_variables: bool = True
    ) -> RenderResult:
        """
        渲染模板
        
        Args:
            template_name: 模板名称
            variables: 模板变量
            validate_variables: 是否验证变量
        
        Returns:
            RenderResult: 渲染结果
        """
        start_time = datetime.now()
        
        try:
            # 检查模板是否存在
            if template_name not in self.templates:
                raise ValueError(f"未找到模板: {template_name}")
            
            template = self.templates[template_name]
            
            # 验证变量
            if validate_variables:
                validated_variables = self._validate_variables(template, variables)
            else:
                validated_variables = variables
            
            # 渲染模板
            if template.template_format == TemplateFormat.JINJA2:
                rendered_content = await self._render_jinja2(
                    template, validated_variables
                )
            elif template.template_format == TemplateFormat.F_STRING:
                rendered_content = await self._render_f_string(
                    template, validated_variables
                )
            elif template.template_format == TemplateFormat.SIMPLE:
                rendered_content = await self._render_simple(
                    template, validated_variables
                )
            else:
                raise ValueError(f"不支持的模板格式: {template.template_format}")
            
            # 计算渲染时间
            render_time = (datetime.now() - start_time).total_seconds()
            
            # 更新使用计数
            template.usage_count += 1
            template.updated_at = datetime.now()
            
            # 创建渲染结果
            result = RenderResult(
                template_name=template_name,
                rendered_content=rendered_content,
                variables_used=validated_variables,
                render_time=render_time,
                success=True
            )
            
            # 记录渲染历史
            self._add_to_history(result)
            
            self.logger.info(
                f"模板渲染成功: {template_name}, 耗时: {render_time:.3f}s"
            )
            
            return result
            
        except Exception as e:
            render_time = (datetime.now() - start_time).total_seconds()
            
            # 创建错误结果
            result = RenderResult(
                template_name=template_name,
                rendered_content="",
                variables_used=variables,
                render_time=render_time,
                success=False,
                error=str(e)
            )
            
            # 记录渲染历史
            self._add_to_history(result)
            
            self.logger.error(
                f"模板渲染失败: {template_name}, 错误: {e}, 耗时: {render_time:.3f}s"
            )
            
            return result
    
    async def render_template_chain(
        self,
        template_names: List[str],
        variables: Dict[str, Any],
        separator: str = "\n\n"
    ) -> RenderResult:
        """
        渲染模板链
        
        Args:
            template_names: 模板名称列表
            variables: 模板变量
            separator: 分隔符
        
        Returns:
            RenderResult: 渲染结果
        """
        try:
            rendered_parts = []
            all_variables = {}
            total_render_time = 0.0
            
            for template_name in template_names:
                result = await self.render_template(template_name, variables)
                
                if not result.success:
                    return result  # 返回第一个失败的结果
                
                rendered_parts.append(result.rendered_content)
                all_variables.update(result.variables_used)
                total_render_time += result.render_time
            
            # 合并渲染结果
            combined_content = separator.join(rendered_parts)
            
            return RenderResult(
                template_name="|".join(template_names),
                rendered_content=combined_content,
                variables_used=all_variables,
                render_time=total_render_time,
                success=True,
                metadata={"chain_length": len(template_names)}
            )
            
        except Exception as e:
            self.logger.error(f"渲染模板链失败: {e}")
            return RenderResult(
                template_name="|".join(template_names),
                rendered_content="",
                variables_used=variables,
                render_time=0.0,
                success=False,
                error=str(e)
            )
    
    def get_template(self, name: str) -> Optional[PromptTemplate]:
        """
        获取模板
        
        Args:
            name: 模板名称
        
        Returns:
            Optional[PromptTemplate]: 模板对象
        """
        return self.templates.get(name)
    
    def list_templates(
        self,
        category: Optional[str] = None,
        template_type: Optional[TemplateType] = None,
        tags: Optional[List[str]] = None
    ) -> List[PromptTemplate]:
        """
        列出模板
        
        Args:
            category: 分类过滤
            template_type: 类型过滤
            tags: 标签过滤
        
        Returns:
            List[PromptTemplate]: 模板列表
        """
        try:
            templates = list(self.templates.values())
            
            # 分类过滤
            if category:
                templates = [t for t in templates if t.category == category]
            
            # 类型过滤
            if template_type:
                templates = [t for t in templates if t.template_type == template_type]
            
            # 标签过滤
            if tags:
                templates = [
                    t for t in templates
                    if any(tag in t.tags for tag in tags)
                ]
            
            # 按使用次数排序
            templates.sort(key=lambda x: x.usage_count, reverse=True)
            
            return templates
            
        except Exception as e:
            self.logger.error(f"列出模板失败: {e}")
            return []
    
    def search_templates(self, query: str) -> List[PromptTemplate]:
        """
        搜索模板
        
        Args:
            query: 搜索查询
        
        Returns:
            List[PromptTemplate]: 匹配的模板列表
        """
        try:
            query_lower = query.lower()
            matched_templates = []
            
            for template in self.templates.values():
                # 搜索名称、描述、标签
                if (
                    query_lower in template.name.lower() or
                    query_lower in template.description.lower() or
                    any(query_lower in tag.lower() for tag in template.tags)
                ):
                    matched_templates.append(template)
            
            # 按相关性排序（简单实现）
            def relevance_score(template):
                score = 0
                if query_lower in template.name.lower():
                    score += 10
                if query_lower in template.description.lower():
                    score += 5
                for tag in template.tags:
                    if query_lower in tag.lower():
                        score += 3
                return score
            
            matched_templates.sort(key=relevance_score, reverse=True)
            
            return matched_templates
            
        except Exception as e:
            self.logger.error(f"搜索模板失败: {e}")
            return []
    
    def delete_template(self, name: str) -> bool:
        """
        删除模板
        
        Args:
            name: 模板名称
        
        Returns:
            bool: 是否删除成功
        """
        try:
            if name not in self.templates:
                return False
            
            template = self.templates[name]
            
            # 从分类中移除
            if template.category in self.categories:
                if name in self.categories[template.category]:
                    self.categories[template.category].remove(name)
                
                # 如果分类为空，删除分类
                if not self.categories[template.category]:
                    del self.categories[template.category]
            
            # 删除模板
            del self.templates[name]
            
            # 更新Jinja2加载器
            self._update_jinja_loader()
            
            self.logger.info(f"删除模板: {name}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"删除模板失败: {e}")
            return False
    
    async def export_templates(
        self,
        file_path: str,
        format_type: str = "json",
        category: Optional[str] = None
    ) -> bool:
        """
        导出模板
        
        Args:
            file_path: 文件路径
            format_type: 导出格式（json, yaml）
            category: 分类过滤
        
        Returns:
            bool: 是否导出成功
        """
        try:
            # 获取要导出的模板
            templates_to_export = self.list_templates(category=category)
            
            # 转换为字典格式
            templates_data = {
                "templates": [t.to_dict() for t in templates_to_export],
                "exported_at": datetime.now().isoformat(),
                "total_count": len(templates_to_export)
            }
            
            # 写入文件
            file_path_obj = Path(file_path)
            file_path_obj.parent.mkdir(parents=True, exist_ok=True)
            
            if format_type == "json":
                with open(file_path_obj, 'w', encoding='utf-8') as f:
                    json.dump(templates_data, f, ensure_ascii=False, indent=2)
            elif format_type == "yaml":
                with open(file_path_obj, 'w', encoding='utf-8') as f:
                    yaml.dump(templates_data, f, allow_unicode=True, indent=2)
            else:
                raise ValueError(f"不支持的导出格式: {format_type}")
            
            self.logger.info(
                f"导出模板成功: {len(templates_to_export)} 个模板到 {file_path}"
            )
            
            return True
            
        except Exception as e:
            self.logger.error(f"导出模板失败: {e}")
            return False
    
    async def import_templates(
        self,
        file_path: str,
        overwrite: bool = False
    ) -> int:
        """
        导入模板
        
        Args:
            file_path: 文件路径
            overwrite: 是否覆盖已存在的模板
        
        Returns:
            int: 导入的模板数量
        """
        try:
            file_path_obj = Path(file_path)
            
            if not file_path_obj.exists():
                raise FileNotFoundError(f"文件不存在: {file_path}")
            
            # 读取文件
            with open(file_path_obj, 'r', encoding='utf-8') as f:
                if file_path_obj.suffix.lower() == '.json':
                    data = json.load(f)
                elif file_path_obj.suffix.lower() in ['.yml', '.yaml']:
                    data = yaml.safe_load(f)
                else:
                    raise ValueError(f"不支持的文件格式: {file_path_obj.suffix}")
            
            # 导入模板
            imported_count = 0
            templates_data = data.get("templates", [])
            
            for template_data in templates_data:
                template_name = template_data["name"]
                
                # 检查是否已存在
                if template_name in self.templates and not overwrite:
                    self.logger.warning(f"模板已存在，跳过: {template_name}")
                    continue
                
                # 创建模板对象
                template = PromptTemplate.from_dict(template_data)
                
                # 注册模板
                self.templates[template_name] = template
                
                # 更新分类
                category = template.category
                if category not in self.categories:
                    self.categories[category] = []
                if template_name not in self.categories[category]:
                    self.categories[category].append(template_name)
                
                imported_count += 1
            
            # 更新Jinja2加载器
            self._update_jinja_loader()
            
            self.logger.info(f"导入模板成功: {imported_count} 个模板从 {file_path}")
            
            return imported_count
            
        except Exception as e:
            self.logger.error(f"导入模板失败: {e}")
            return 0
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            total_templates = len(self.templates)
            total_renders = len(self.render_history)
            successful_renders = sum(1 for r in self.render_history if r.success)
            
            # 按类型统计
            type_stats = {}
            for template in self.templates.values():
                template_type = template.template_type.value
                if template_type not in type_stats:
                    type_stats[template_type] = 0
                type_stats[template_type] += 1
            
            # 按分类统计
            category_stats = {k: len(v) for k, v in self.categories.items()}
            
            # 使用统计
            most_used = sorted(
                self.templates.values(),
                key=lambda x: x.usage_count,
                reverse=True
            )[:5]
            
            return {
                "total_templates": total_templates,
                "total_renders": total_renders,
                "successful_renders": successful_renders,
                "success_rate": successful_renders / total_renders if total_renders > 0 else 0,
                "type_stats": type_stats,
                "category_stats": category_stats,
                "most_used_templates": [
                    {"name": t.name, "usage_count": t.usage_count}
                    for t in most_used
                ]
            }
            
        except Exception as e:
            self.logger.error(f"获取统计信息失败: {e}")
            return {}
    
    def _validate_template(self, template: PromptTemplate) -> None:
        """
        验证模板
        
        Args:
            template: 模板对象
        
        Raises:
            ValueError: 模板验证失败
        """
        try:
            # 验证模板内容
            if not template.template.strip():
                raise ValueError("模板内容不能为空")
            
            # 验证Jinja2模板语法
            if template.template_format == TemplateFormat.JINJA2:
                try:
                    Template(template.template)
                except Exception as e:
                    raise ValueError(f"Jinja2模板语法错误: {e}")
            
            # 验证变量定义
            for var_name, var_def in template.variables.items():
                if var_def.validation:
                    try:
                        re.compile(var_def.validation)
                    except re.error as e:
                        raise ValueError(f"变量 {var_name} 的验证正则表达式无效: {e}")
            
        except Exception as e:
            self.logger.error(f"模板验证失败: {e}")
            raise
    
    def _validate_variables(
        self,
        template: PromptTemplate,
        variables: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        验证模板变量
        
        Args:
            template: 模板对象
            variables: 变量值
        
        Returns:
            Dict[str, Any]: 验证后的变量
        
        Raises:
            ValueError: 变量验证失败
        """
        validated_vars = {}
        
        # 检查必需变量
        for var_name, var_def in template.variables.items():
            if var_def.required and var_name not in variables:
                if var_def.default is not None:
                    validated_vars[var_name] = var_def.default
                else:
                    raise ValueError(f"缺少必需变量: {var_name}")
            elif var_name in variables:
                value = variables[var_name]
                
                # 正则验证
                if var_def.validation:
                    if not re.match(var_def.validation, str(value)):
                        raise ValueError(
                            f"变量 {var_name} 的值不符合验证规则: {var_def.validation}"
                        )
                
                validated_vars[var_name] = value
        
        # 添加额外变量
        for var_name, value in variables.items():
            if var_name not in validated_vars:
                validated_vars[var_name] = value
        
        return validated_vars
    
    async def _render_jinja2(
        self,
        template: PromptTemplate,
        variables: Dict[str, Any]
    ) -> str:
        """
        渲染Jinja2模板
        
        Args:
            template: 模板对象
            variables: 变量值
        
        Returns:
            str: 渲染结果
        """
        try:
            jinja_template = Template(template.template)
            return jinja_template.render(**variables)
            
        except Exception as e:
            raise ValueError(f"Jinja2模板渲染失败: {e}")
    
    async def _render_f_string(
        self,
        template: PromptTemplate,
        variables: Dict[str, Any]
    ) -> str:
        """
        渲染F-string模板
        
        Args:
            template: 模板对象
            variables: 变量值
        
        Returns:
            str: 渲染结果
        """
        try:
            return template.template.format(**variables)
            
        except Exception as e:
            raise ValueError(f"F-string模板渲染失败: {e}")
    
    async def _render_simple(
        self,
        template: PromptTemplate,
        variables: Dict[str, Any]
    ) -> str:
        """
        渲染简单模板（使用{variable}格式）
        
        Args:
            template: 模板对象
            variables: 变量值
        
        Returns:
            str: 渲染结果
        """
        try:
            result = template.template
            
            for var_name, value in variables.items():
                placeholder = f"{{{var_name}}}"
                result = result.replace(placeholder, str(value))
            
            return result
            
        except Exception as e:
            raise ValueError(f"简单模板渲染失败: {e}")
    
    def _update_jinja_loader(self) -> None:
        """
        更新Jinja2加载器
        """
        try:
            jinja_templates = {
                name: template.template
                for name, template in self.templates.items()
                if template.template_format == TemplateFormat.JINJA2
            }
            
            self.jinja_env.loader = CustomTemplateLoader(jinja_templates)
            
        except Exception as e:
            self.logger.error(f"更新Jinja2加载器失败: {e}")
    
    def _add_to_history(self, result: RenderResult) -> None:
        """
        添加到渲染历史
        
        Args:
            result: 渲染结果
        """
        self.render_history.append(result)
        
        # 限制历史记录数量
        if len(self.render_history) > self._max_history:
            self.render_history = self.render_history[-self._max_history:]
    
    def _load_builtin_templates(self) -> None:
        """
        加载内置模板
        """
        try:
            # 系统提示词模板
            self.register_template(
                name="system_assistant",
                description="通用AI助手系统提示词",
                template="你是一个有用的AI助手。请根据用户的问题提供准确、有帮助的回答。",
                template_type=TemplateType.SYSTEM,
                category="builtin",
                tags=["system", "assistant"]
            )
            
            # 用户问题模板
            self.register_template(
                name="user_question",
                description="用户问题模板",
                template="用户问题：{question}",
                template_type=TemplateType.USER,
                variables={
                    "question": TemplateVariable(
                        name="question",
                        type="string",
                        description="用户的问题",
                        required=True
                    )
                },
                category="builtin",
                tags=["user", "question"]
            )
            
            # 代码分析模板
            self.register_template(
                name="code_analysis",
                description="代码分析提示词模板",
                template="""请分析以下{language}代码：

```{language}
{code}
```

请从以下方面进行分析：
1. 代码功能和逻辑
2. 潜在问题和改进建议
3. 代码质量评估

{additional_requirements}""",
                template_type=TemplateType.USER,
                variables={
                    "language": TemplateVariable(
                        name="language",
                        type="string",
                        description="编程语言",
                        required=True,
                        examples=["Python", "JavaScript", "Java"]
                    ),
                    "code": TemplateVariable(
                        name="code",
                        type="string",
                        description="要分析的代码",
                        required=True
                    ),
                    "additional_requirements": TemplateVariable(
                        name="additional_requirements",
                        type="string",
                        description="额外要求",
                        required=False,
                        default=""
                    )
                },
                category="builtin",
                tags=["code", "analysis"]
            )
            
            self.logger.info("内置模板加载完成")
            
        except Exception as e:
            self.logger.error(f"加载内置模板失败: {e}")
    
    async def _load_templates_from_dir(self) -> None:
        """
        从目录加载模板
        """
        try:
            if not self.template_dir or not self.template_dir.exists():
                return
            
            loaded_count = 0
            
            for file_path in self.template_dir.rglob("*.json"):
                try:
                    imported = await self.import_templates(str(file_path))
                    loaded_count += imported
                except Exception as e:
                    self.logger.error(f"加载模板文件失败 {file_path}: {e}")
            
            for file_path in self.template_dir.rglob("*.yml"):
                try:
                    imported = await self.import_templates(str(file_path))
                    loaded_count += imported
                except Exception as e:
                    self.logger.error(f"加载模板文件失败 {file_path}: {e}")
            
            for file_path in self.template_dir.rglob("*.yaml"):
                try:
                    imported = await self.import_templates(str(file_path))
                    loaded_count += imported
                except Exception as e:
                    self.logger.error(f"加载模板文件失败 {file_path}: {e}")
            
            self.logger.info(f"从目录加载模板完成: {loaded_count} 个模板")
            
        except Exception as e:
            self.logger.error(f"从目录加载模板失败: {e}")