#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A2A多智能体系统Pydantic模式模块

包含所有API请求和响应的数据模式定义
"""

__version__ = "1.0.0"
__author__ = "A2A Team"
__description__ = "A2A多智能体系统Pydantic模式"

# 导入基础模式
from .base import BaseSchema, PaginationSchema, ResponseSchema

# 导入用户相关模式
from .user import (
    UserCreate,
    UserUpdate,
    UserResponse,
    UserLogin,
    UserTokenResponse,
    UserPermissionResponse
)

# 导入智能体相关模式
from .agent import (
    AgentCreate,
    AgentUpdate,
    AgentResponse,
    AgentTemplateResponse
)

# 导入会话相关模式
from .session import (
    SessionCreate,
    SessionUpdate,
    SessionResponse,
    SessionShareCreate,
    SessionShareResponse
)

# 导入消息相关模式
from .message import (
    MessageCreate,
    MessageUpdate,
    MessageResponse,
    MessageReactionCreate,
    MessageReactionResponse
)

# 导入任务相关模式
from .task import (
    TaskCreate,
    TaskUpdate,
    TaskResponse,
    TaskExecutionResponse
)

# 导入工作流相关模式
from .workflow import (
    WorkflowCreate,
    WorkflowUpdate,
    WorkflowResponse,
    WorkflowExecutionResponse
)

# 导入工具相关模式
from .tool import (
    ToolType, ToolStatus, ExecutionStatus, SecurityLevel,
    ToolCreate, ToolUpdate, ToolResponse,
    ToolExecutionCreate, ToolExecutionResponse,
    ToolCategoryResponse, AgentToolResponse, ToolStatsResponse,
    ToolSearchRequest, ToolValidationRequest
)

# 导入工件相关模式
from .artifact import (
    ArtifactType, ArtifactStatus, SharePermission, StorageType,
    ArtifactCreate, ArtifactUpdate, ArtifactResponse,
    ArtifactShareCreate, ArtifactShareResponse,
    ArtifactVersionResponse, ArtifactStatsResponse,
    ArtifactSearchRequest, ArtifactAccessRequest, ArtifactBatchRequest
)

# 导入配置相关模式
from .config import (
    ConfigType, ConfigCategory, ConfigStatus, TemplateType,
    SystemConfigCreate, SystemConfigUpdate, SystemConfigResponse,
    UserConfigCreate, UserConfigUpdate, UserConfigResponse,
    AgentConfigCreate, AgentConfigUpdate, AgentConfigResponse,
    ConfigTemplateCreate, ConfigTemplateUpdate, ConfigTemplateResponse,
    ConfigSearchRequest, ConfigBatchRequest, ConfigValidationRequest,
    ConfigBackupRequest, ConfigRestoreRequest
)

# 导出所有模式
__all__ = [
    # 基础模式
    "BaseSchema",
    "PaginationSchema",
    "ResponseSchema",
    "PaginatedResponseSchema",
    "PaginationInfo",
    "ErrorResponseSchema",
    "HealthCheckSchema",
    "TokenSchema",
    "RefreshTokenSchema",
    
    # 用户相关
    "UserStatus",
    "UserRole",
    "UserCreate",
    "UserUpdate",
    "UserPasswordUpdate",
    "UserLogin",
    "UserResponse",
    "UserTokenResponse",
    "UserPermissionResponse",
    "UserActivityLogResponse",
    "UserStatsResponse",
    
    # 智能体相关
    "AgentType",
    "AgentStatus",
    "AgentVisibility",
    "AgentCreate",
    "AgentUpdate",
    "AgentResponse",
    "AgentTemplateResponse",
    "AgentCollaborationResponse",
    "AgentStatsResponse",
    "AgentCloneRequest",
    
    # 会话相关
    "SessionType",
    "SessionMode",
    "SessionStatus",
    "SharePermission",
    "SessionCreate",
    "SessionUpdate",
    "SessionResponse",
    "SessionShareCreate",
    "SessionShareResponse",
    "SessionBookmarkCreate",
    "SessionBookmarkResponse",
    "SessionStatsResponse",
    "SessionExportRequest",
    
    # 消息相关
    "MessageType",
    "MessageRole",
    "MessageStatus",
    "ReactionType",
    "MessageCreate",
    "MessageUpdate",
    "MessageResponse",
    "MessageReactionCreate",
    "MessageReactionResponse",
    "MessageEditResponse",
    "MessageTemplateResponse",
    "MessageStreamResponse",
    "MessageSearchRequest",
    "MessageRatingRequest",
    "MessageBatchRequest",
    
    # 任务相关
    "TaskType",
    "TaskPriority",
    "TaskStatus",
    "TaskCreate",
    "TaskUpdate",
    "TaskResponse",
    "TaskExecutionResponse",
    "TaskTemplateResponse",
    "TaskStatsResponse",
    "TaskBatchRequest",
    "TaskActionRequest",
    
    # 工作流相关
    "WorkflowType",
    "WorkflowStatus",
    "ExecutionStatus",
    "StepType",
    "TriggerType",
    "WorkflowCreate",
    "WorkflowUpdate",
    "WorkflowResponse",
    "WorkflowExecutionCreate",
    "WorkflowExecutionResponse",
    "WorkflowStepResponse",
    "WorkflowStepExecutionResponse",
    "WorkflowStatsResponse",
    "WorkflowActionRequest",
    
    # 工具相关
    "ToolType",
    "ToolStatus",
    "SecurityLevel",
    "ToolCreate",
    "ToolUpdate",
    "ToolResponse",
    "ToolExecutionCreate",
    "ToolExecutionResponse",
    "ToolCategoryResponse",
    "AgentToolResponse",
    "ToolStatsResponse",
    "ToolSearchRequest",
    "ToolValidationRequest",
    
    # 工件相关
    "ArtifactType",
    "ArtifactStatus",
    "StorageType",
    "ArtifactCreate",
    "ArtifactUpdate",
    "ArtifactResponse",
    "ArtifactShareCreate",
    "ArtifactShareResponse",
    "ArtifactVersionResponse",
    "ArtifactStatsResponse",
    "ArtifactSearchRequest",
    "ArtifactAccessRequest",
    "ArtifactBatchRequest",
    
    # 配置相关
    "ConfigType",
    "ConfigCategory",
    "ConfigStatus",
    "TemplateType",
    "SystemConfigCreate",
    "SystemConfigUpdate",
    "SystemConfigResponse",
    "UserConfigCreate",
    "UserConfigUpdate",
    "UserConfigResponse",
    "AgentConfigCreate",
    "AgentConfigUpdate",
    "AgentConfigResponse",
    "ConfigTemplateCreate",
    "ConfigTemplateUpdate",
    "ConfigTemplateResponse",
    "ConfigSearchRequest",
    "ConfigBatchRequest",
    "ConfigValidationRequest",
    "ConfigBackupRequest",
    "ConfigRestoreRequest",
]