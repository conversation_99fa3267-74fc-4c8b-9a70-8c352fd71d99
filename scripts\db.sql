-- =====================================================
-- A2A多智能体系统数据库创建脚本
--
-- 功能：创建完整的数据库表结构
-- 数据库：MySQL 5.7+
-- 编码：UTF8MB4
-- 作者：A2A开发团队
-- 创建时间：2024-01-01
-- 版本：1.0.0
-- =====================================================

-- 设置字符集和排序规则（MySQL 5.7兼容）
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;
SET sql_mode = 'STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_AUTO_CREATE_USER,NO_ENGINE_SUBSTITUTION';

-- 创建数据库（如果不存在）
-- CREATE DATABASE IF NOT EXISTS a2a_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
-- USE a2a_system;

-- =====================================================
-- 1. 用户相关表
-- =====================================================

-- 用户表
CREATE TABLE IF NOT EXISTS `users` (
    `id` INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `username` VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    `email` VARCHAR(100) NOT NULL UNIQUE COMMENT '邮箱地址',
    `password_hash` VARCHAR(255) NOT NULL COMMENT '密码哈希',
    `full_name` VARCHAR(100) DEFAULT NULL COMMENT '全名',
    `avatar_url` VARCHAR(500) DEFAULT NULL COMMENT '头像URL',
    `phone` VARCHAR(20) DEFAULT NULL COMMENT '手机号码',
    `role` VARCHAR(20) NOT NULL DEFAULT 'user' COMMENT '用户角色',
    `is_verified` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否已验证邮箱',
    `is_locked` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否被锁定',
    `locked_until` DATETIME DEFAULT NULL COMMENT '锁定到期时间',
    `last_login_at` DATETIME DEFAULT NULL COMMENT '最后登录时间',
    `last_login_ip` VARCHAR(45) DEFAULT NULL COMMENT '最后登录IP',
    `login_count` INT NOT NULL DEFAULT 0 COMMENT '登录次数',
    `timezone` VARCHAR(50) DEFAULT 'UTC' COMMENT '时区',
    `language` VARCHAR(10) DEFAULT 'zh-CN' COMMENT '语言',
    `preferences` TEXT DEFAULT NULL COMMENT '用户偏好设置（JSON格式）',
    `registration_ip` VARCHAR(45) DEFAULT NULL COMMENT '注册IP',
    `invite_code` VARCHAR(50) DEFAULT NULL COMMENT '邀请码',
    `invited_by` INT DEFAULT NULL COMMENT '邀请人用户ID',
    `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at` DATETIME DEFAULT NULL COMMENT '删除时间',
    `is_active` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '是否激活',
    
    INDEX `idx_username` (`username`),
    INDEX `idx_email` (`email`),
    INDEX `idx_role` (`role`),
    INDEX `idx_created_at` (`created_at`),
    INDEX `idx_last_login_at` (`last_login_at`),
    INDEX `idx_invited_by` (`invited_by`),
    FOREIGN KEY (`invited_by`) REFERENCES `users`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 用户令牌表
CREATE TABLE IF NOT EXISTS `user_tokens` (
    `id` INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `user_id` INT NOT NULL COMMENT '用户ID',
    `token_type` VARCHAR(20) NOT NULL COMMENT '令牌类型',
    `token_hash` VARCHAR(255) NOT NULL COMMENT '令牌哈希',
    `expires_at` DATETIME NOT NULL COMMENT '过期时间',
    `is_revoked` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否已撤销',
    `device_info` TEXT DEFAULT NULL COMMENT '设备信息（JSON格式）',
    `ip_address` VARCHAR(45) DEFAULT NULL COMMENT 'IP地址',
    `user_agent` TEXT DEFAULT NULL COMMENT '用户代理',
    `last_used_at` DATETIME DEFAULT NULL COMMENT '最后使用时间',
    `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_token_type` (`token_type`),
    INDEX `idx_expires_at` (`expires_at`),
    INDEX `idx_is_revoked` (`is_revoked`),
    FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户令牌表';

-- 用户权限表
CREATE TABLE IF NOT EXISTS `user_permissions` (
    `id` INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `user_id` INT NOT NULL COMMENT '用户ID',
    `resource_type` VARCHAR(50) NOT NULL COMMENT '资源类型',
    `resource_id` VARCHAR(100) DEFAULT NULL COMMENT '资源ID',
    `permission` VARCHAR(50) NOT NULL COMMENT '权限名称',
    `granted_by` INT DEFAULT NULL COMMENT '授权人用户ID',
    `granted_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '授权时间',
    `expires_at` DATETIME DEFAULT NULL COMMENT '过期时间',
    `is_active` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '是否激活',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    UNIQUE KEY `uk_user_resource_permission` (`user_id`, `resource_type`, `resource_id`, `permission`),
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_resource_type` (`resource_type`),
    INDEX `idx_permission` (`permission`),
    INDEX `idx_granted_by` (`granted_by`),
    FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`granted_by`) REFERENCES `users`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户权限表';

-- 用户活动日志表
CREATE TABLE IF NOT EXISTS `user_activity_logs` (
    `id` INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `user_id` INT NOT NULL COMMENT '用户ID',
    `action_type` VARCHAR(50) NOT NULL COMMENT '操作类型',
    `action_name` VARCHAR(100) NOT NULL COMMENT '操作名称',
    `resource_type` VARCHAR(50) DEFAULT NULL COMMENT '资源类型',
    `resource_id` VARCHAR(100) DEFAULT NULL COMMENT '资源ID',
    `details` TEXT DEFAULT NULL COMMENT '操作详情（JSON格式）',
    `ip_address` VARCHAR(45) DEFAULT NULL COMMENT 'IP地址',
    `user_agent` TEXT DEFAULT NULL COMMENT '用户代理',
    `status` VARCHAR(20) NOT NULL DEFAULT 'success' COMMENT '操作状态',
    `error_message` TEXT DEFAULT NULL COMMENT '错误信息',
    `duration_ms` INT DEFAULT NULL COMMENT '操作耗时（毫秒）',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_action_type` (`action_type`),
    INDEX `idx_resource_type` (`resource_type`),
    INDEX `idx_status` (`status`),
    INDEX `idx_created_at` (`created_at`),
    FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户活动日志表';

-- =====================================================
-- 2. 智能体相关表
-- =====================================================

-- 智能体表
CREATE TABLE IF NOT EXISTS `agents` (
    `id` INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `user_id` INT NOT NULL COMMENT '创建者用户ID',
    `owner_id` INT NOT NULL COMMENT '拥有者用户ID',
    `name` VARCHAR(100) NOT NULL COMMENT '智能体名称',
    `description` TEXT DEFAULT NULL COMMENT '智能体描述',
    `avatar_url` VARCHAR(500) DEFAULT NULL COMMENT '头像URL',
    `agent_type` VARCHAR(50) NOT NULL DEFAULT 'general' COMMENT '智能体类型',
    `version` VARCHAR(20) NOT NULL DEFAULT '1.0.0' COMMENT '版本号',
    `config` TEXT DEFAULT NULL COMMENT '智能体配置（JSON格式）',
    `system_prompt` TEXT DEFAULT NULL COMMENT '系统提示词',
    `model_name` VARCHAR(100) NOT NULL DEFAULT 'gemini-pro' COMMENT '使用的模型名称',
    `model_config` TEXT DEFAULT NULL COMMENT '模型配置（JSON格式）',
    `temperature` FLOAT DEFAULT 0.7 COMMENT '温度参数',
    `max_tokens` INT DEFAULT 1000 COMMENT '最大令牌数',
    `top_p` FLOAT DEFAULT 1.0 COMMENT 'Top-p参数',
    `frequency_penalty` FLOAT DEFAULT 0.0 COMMENT '频率惩罚',
    `presence_penalty` FLOAT DEFAULT 0.0 COMMENT '存在惩罚',
    `status` VARCHAR(20) NOT NULL DEFAULT 'active' COMMENT '状态',
    `is_public` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否公开',
    `is_template` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否为模板',
    `parent_id` INT DEFAULT NULL COMMENT '父智能体ID',
    `tags` TEXT DEFAULT NULL COMMENT '标签（JSON格式）',
    `capabilities` TEXT DEFAULT NULL COMMENT '能力列表（JSON格式）',
    `tools` TEXT DEFAULT NULL COMMENT '工具列表（JSON格式）',
    `memory_config` TEXT DEFAULT NULL COMMENT '记忆配置（JSON格式）',
    `execution_count` INT NOT NULL DEFAULT 0 COMMENT '执行次数',
    `success_count` INT NOT NULL DEFAULT 0 COMMENT '成功次数',
    `error_count` INT NOT NULL DEFAULT 0 COMMENT '错误次数',
    `avg_response_time` FLOAT DEFAULT NULL COMMENT '平均响应时间（秒）',
    `last_executed_at` DATETIME DEFAULT NULL COMMENT '最后执行时间',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at` DATETIME DEFAULT NULL COMMENT '删除时间',
    `is_active` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '是否激活',
    
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_owner_id` (`owner_id`),
    INDEX `idx_agent_type` (`agent_type`),
    INDEX `idx_status` (`status`),
    INDEX `idx_is_public` (`is_public`),
    INDEX `idx_parent_id` (`parent_id`),
    INDEX `idx_created_at` (`created_at`),
    INDEX `idx_last_executed_at` (`last_executed_at`),
    FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`owner_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`parent_id`) REFERENCES `agents`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='智能体表';

-- 智能体执行记录表
CREATE TABLE IF NOT EXISTS `agent_executions` (
    `id` INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `agent_id` INT NOT NULL COMMENT '智能体ID',
    `user_id` INT NOT NULL COMMENT '执行用户ID',
    `session_id` INT DEFAULT NULL COMMENT '会话ID',
    `execution_id` VARCHAR(100) NOT NULL UNIQUE COMMENT '执行ID',
    `input_data` TEXT DEFAULT NULL COMMENT '输入数据（JSON格式）',
    `output_data` TEXT DEFAULT NULL COMMENT '输出数据（JSON格式）',
    `context` TEXT DEFAULT NULL COMMENT '执行上下文（JSON格式）',
    `status` VARCHAR(20) NOT NULL DEFAULT 'pending' COMMENT '执行状态',
    `error_message` TEXT DEFAULT NULL COMMENT '错误信息',
    `error_code` VARCHAR(50) DEFAULT NULL COMMENT '错误代码',
    `started_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '开始时间',
    `completed_at` DATETIME DEFAULT NULL COMMENT '完成时间',
    `duration_ms` INT DEFAULT NULL COMMENT '执行耗时（毫秒）',
    `token_usage` TEXT DEFAULT NULL COMMENT '令牌使用情况（JSON格式）',
    `cost` DECIMAL(10,6) DEFAULT NULL COMMENT '执行成本',
    `metadata` TEXT DEFAULT NULL COMMENT '元数据（JSON格式）',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX `idx_agent_id` (`agent_id`),
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_session_id` (`session_id`),
    INDEX `idx_execution_id` (`execution_id`),
    INDEX `idx_status` (`status`),
    INDEX `idx_started_at` (`started_at`),
    INDEX `idx_completed_at` (`completed_at`),
    FOREIGN KEY (`agent_id`) REFERENCES `agents`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='智能体执行记录表';

-- =====================================================
-- 3. 会话相关表
-- =====================================================

-- 会话表
CREATE TABLE IF NOT EXISTS `sessions` (
    `id` INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `user_id` INT NOT NULL COMMENT '创建者用户ID',
    `title` VARCHAR(200) NOT NULL COMMENT '会话标题',
    `description` TEXT DEFAULT NULL COMMENT '会话描述',
    `session_type` VARCHAR(50) NOT NULL DEFAULT 'chat' COMMENT '会话类型',
    `status` VARCHAR(20) NOT NULL DEFAULT 'active' COMMENT '会话状态',
    `config` TEXT DEFAULT NULL COMMENT '会话配置（JSON格式）',
    `context` TEXT DEFAULT NULL COMMENT '会话上下文（JSON格式）',
    `is_private` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '是否私有',
    `tags` TEXT DEFAULT NULL COMMENT '标签（JSON格式）',
    `participant_count` INT NOT NULL DEFAULT 1 COMMENT '参与者数量',
    `message_count` INT NOT NULL DEFAULT 0 COMMENT '消息数量',
    `last_message_at` DATETIME DEFAULT NULL COMMENT '最后消息时间',
    `expires_at` DATETIME DEFAULT NULL COMMENT '过期时间',
    `archived_at` DATETIME DEFAULT NULL COMMENT '归档时间',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at` DATETIME DEFAULT NULL COMMENT '删除时间',
    `is_active` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '是否激活',
    
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_session_type` (`session_type`),
    INDEX `idx_status` (`status`),
    INDEX `idx_is_private` (`is_private`),
    INDEX `idx_created_at` (`created_at`),
    INDEX `idx_last_message_at` (`last_message_at`),
    FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='会话表';

-- 会话参与者表
CREATE TABLE IF NOT EXISTS `session_participants` (
    `id` INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `session_id` INT NOT NULL COMMENT '会话ID',
    `user_id` INT DEFAULT NULL COMMENT '用户ID',
    `agent_id` INT DEFAULT NULL COMMENT '智能体ID',
    `participant_type` VARCHAR(20) NOT NULL COMMENT '参与者类型',
    `role` VARCHAR(20) NOT NULL DEFAULT 'participant' COMMENT '角色',
    `permissions` TEXT DEFAULT NULL COMMENT '权限（JSON格式）',
    `joined_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '加入时间',
    `left_at` DATETIME DEFAULT NULL COMMENT '离开时间',
    `is_active` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '是否激活',
    `last_seen_at` DATETIME DEFAULT NULL COMMENT '最后在线时间',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    UNIQUE KEY `uk_session_user` (`session_id`, `user_id`),
    UNIQUE KEY `uk_session_agent` (`session_id`, `agent_id`),
    INDEX `idx_session_id` (`session_id`),
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_agent_id` (`agent_id`),
    INDEX `idx_participant_type` (`participant_type`),
    INDEX `idx_role` (`role`),
    FOREIGN KEY (`session_id`) REFERENCES `sessions`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`agent_id`) REFERENCES `agents`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='会话参与者表';

-- =====================================================
-- 4. 消息相关表
-- =====================================================

-- 消息表
CREATE TABLE IF NOT EXISTS `messages` (
    `id` INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `session_id` INT NOT NULL COMMENT '会话ID',
    `sender_id` INT DEFAULT NULL COMMENT '发送者用户ID',
    `agent_id` INT DEFAULT NULL COMMENT '发送者智能体ID',
    `sender_type` VARCHAR(20) NOT NULL COMMENT '发送者类型',
    `message_type` VARCHAR(20) NOT NULL DEFAULT 'text' COMMENT '消息类型',
    `content` TEXT NOT NULL COMMENT '消息内容',
    `content_format` VARCHAR(20) DEFAULT 'text' COMMENT '内容格式',
    `metadata` TEXT DEFAULT NULL COMMENT '元数据（JSON格式）',
    `reply_to_id` INT DEFAULT NULL COMMENT '回复的消息ID',
    `thread_id` VARCHAR(100) DEFAULT NULL COMMENT '线程ID',
    `sequence_number` INT NOT NULL COMMENT '序列号',
    `is_edited` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否已编辑',
    `edited_at` DATETIME DEFAULT NULL COMMENT '编辑时间',
    `is_deleted` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否已删除',
    `deleted_at` DATETIME DEFAULT NULL COMMENT '删除时间',
    `attachments` TEXT DEFAULT NULL COMMENT '附件（JSON格式）',
    `reactions` TEXT DEFAULT NULL COMMENT '反应（JSON格式）',
    `read_by` TEXT DEFAULT NULL COMMENT '已读用户（JSON格式）',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX `idx_session_id` (`session_id`),
    INDEX `idx_sender_id` (`sender_id`),
    INDEX `idx_agent_id` (`agent_id`),
    INDEX `idx_sender_type` (`sender_type`),
    INDEX `idx_message_type` (`message_type`),
    INDEX `idx_reply_to_id` (`reply_to_id`),
    INDEX `idx_thread_id` (`thread_id`),
    INDEX `idx_sequence_number` (`sequence_number`),
    INDEX `idx_created_at` (`created_at`),
    FOREIGN KEY (`session_id`) REFERENCES `sessions`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`sender_id`) REFERENCES `users`(`id`) ON DELETE SET NULL,
    FOREIGN KEY (`agent_id`) REFERENCES `agents`(`id`) ON DELETE SET NULL,
    FOREIGN KEY (`reply_to_id`) REFERENCES `messages`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='消息表';

-- 消息附件表
CREATE TABLE IF NOT EXISTS `message_attachments` (
    `id` INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `message_id` INT NOT NULL COMMENT '消息ID',
    `file_name` VARCHAR(255) NOT NULL COMMENT '文件名',
    `file_type` VARCHAR(50) NOT NULL COMMENT '文件类型',
    `file_size` BIGINT NOT NULL COMMENT '文件大小（字节）',
    `file_url` VARCHAR(500) NOT NULL COMMENT '文件URL',
    `file_path` VARCHAR(500) DEFAULT NULL COMMENT '文件路径',
    `mime_type` VARCHAR(100) DEFAULT NULL COMMENT 'MIME类型',
    `thumbnail_url` VARCHAR(500) DEFAULT NULL COMMENT '缩略图URL',
    `metadata` TEXT DEFAULT NULL COMMENT '元数据（JSON格式）',
    `upload_status` VARCHAR(20) NOT NULL DEFAULT 'pending' COMMENT '上传状态',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX `idx_message_id` (`message_id`),
    INDEX `idx_file_type` (`file_type`),
    INDEX `idx_upload_status` (`upload_status`),
    FOREIGN KEY (`message_id`) REFERENCES `messages`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='消息附件表';

-- =====================================================
-- 5. 任务相关表
-- =====================================================

-- 任务表
CREATE TABLE IF NOT EXISTS `tasks` (
    `id` INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `user_id` INT NOT NULL COMMENT '创建者用户ID',
    `agent_id` INT DEFAULT NULL COMMENT '执行智能体ID',
    `workflow_id` INT DEFAULT NULL COMMENT '关联工作流ID',
    `session_id` INT DEFAULT NULL COMMENT '关联会话ID',
    `parent_task_id` INT DEFAULT NULL COMMENT '父任务ID',
    `title` VARCHAR(200) NOT NULL COMMENT '任务标题',
    `description` TEXT DEFAULT NULL COMMENT '任务描述',
    `task_type` VARCHAR(50) NOT NULL DEFAULT 'general' COMMENT '任务类型',
    `priority` VARCHAR(20) NOT NULL DEFAULT 'medium' COMMENT '优先级',
    `status` VARCHAR(20) NOT NULL DEFAULT 'pending' COMMENT '任务状态',
    `config` TEXT DEFAULT NULL COMMENT '任务配置（JSON格式）',
    `input_data` TEXT DEFAULT NULL COMMENT '输入数据（JSON格式）',
    `output_data` TEXT DEFAULT NULL COMMENT '输出数据（JSON格式）',
    `context` TEXT DEFAULT NULL COMMENT '执行上下文（JSON格式）',
    `error_message` TEXT DEFAULT NULL COMMENT '错误信息',
    `error_code` VARCHAR(50) DEFAULT NULL COMMENT '错误代码',
    `progress` INT NOT NULL DEFAULT 0 COMMENT '进度百分比',
    `scheduled_at` DATETIME DEFAULT NULL COMMENT '计划执行时间',
    `started_at` DATETIME DEFAULT NULL COMMENT '开始时间',
    `completed_at` DATETIME DEFAULT NULL COMMENT '完成时间',
    `timeout_at` DATETIME DEFAULT NULL COMMENT '超时时间',
    `duration_ms` INT DEFAULT NULL COMMENT '执行耗时（毫秒）',
    `retry_count` INT NOT NULL DEFAULT 0 COMMENT '重试次数',
    `max_retries` INT NOT NULL DEFAULT 3 COMMENT '最大重试次数',
    `tags` TEXT DEFAULT NULL COMMENT '标签（JSON格式）',
    `metadata` TEXT DEFAULT NULL COMMENT '元数据（JSON格式）',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at` DATETIME DEFAULT NULL COMMENT '删除时间',
    `is_active` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '是否激活',

    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_agent_id` (`agent_id`),
    INDEX `idx_workflow_id` (`workflow_id`),
    INDEX `idx_session_id` (`session_id`),
    INDEX `idx_parent_task_id` (`parent_task_id`),
    INDEX `idx_task_type` (`task_type`),
    INDEX `idx_priority` (`priority`),
    INDEX `idx_status` (`status`),
    INDEX `idx_scheduled_at` (`scheduled_at`),
    INDEX `idx_created_at` (`created_at`),
    FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`agent_id`) REFERENCES `agents`(`id`) ON DELETE SET NULL,
    FOREIGN KEY (`session_id`) REFERENCES `sessions`(`id`) ON DELETE SET NULL,
    FOREIGN KEY (`parent_task_id`) REFERENCES `tasks`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='任务表';

-- 任务执行日志表
CREATE TABLE IF NOT EXISTS `task_execution_logs` (
    `id` INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `task_id` INT NOT NULL COMMENT '任务ID',
    `step_name` VARCHAR(100) DEFAULT NULL COMMENT '步骤名称',
    `step_order` INT DEFAULT NULL COMMENT '步骤顺序',
    `log_level` VARCHAR(20) NOT NULL DEFAULT 'info' COMMENT '日志级别',
    `message` TEXT NOT NULL COMMENT '日志消息',
    `details` TEXT DEFAULT NULL COMMENT '详细信息（JSON格式）',
    `timestamp` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '时间戳',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',

    INDEX `idx_task_id` (`task_id`),
    INDEX `idx_step_order` (`step_order`),
    INDEX `idx_log_level` (`log_level`),
    INDEX `idx_timestamp` (`timestamp`),
    FOREIGN KEY (`task_id`) REFERENCES `tasks`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='任务执行日志表';

-- =====================================================
-- 6. 工作流相关表
-- =====================================================

-- 工作流表
CREATE TABLE IF NOT EXISTS `workflows` (
    `id` INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `user_id` INT NOT NULL COMMENT '创建者用户ID',
    `name` VARCHAR(100) NOT NULL COMMENT '工作流名称',
    `description` TEXT DEFAULT NULL COMMENT '工作流描述',
    `version` VARCHAR(20) NOT NULL DEFAULT '1.0.0' COMMENT '版本号',
    `definition` TEXT NOT NULL COMMENT '工作流定义（JSON格式）',
    `config` TEXT DEFAULT NULL COMMENT '工作流配置（JSON格式）',
    `status` VARCHAR(20) NOT NULL DEFAULT 'draft' COMMENT '工作流状态',
    `is_public` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否公开',
    `is_template` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否为模板',
    `category` VARCHAR(50) DEFAULT NULL COMMENT '分类',
    `tags` TEXT DEFAULT NULL COMMENT '标签（JSON格式）',
    `execution_count` INT NOT NULL DEFAULT 0 COMMENT '执行次数',
    `success_count` INT NOT NULL DEFAULT 0 COMMENT '成功次数',
    `error_count` INT NOT NULL DEFAULT 0 COMMENT '错误次数',
    `avg_duration_ms` INT DEFAULT NULL COMMENT '平均执行时间（毫秒）',
    `last_executed_at` DATETIME DEFAULT NULL COMMENT '最后执行时间',
    `published_at` DATETIME DEFAULT NULL COMMENT '发布时间',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at` DATETIME DEFAULT NULL COMMENT '删除时间',
    `is_active` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '是否激活',

    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_status` (`status`),
    INDEX `idx_is_public` (`is_public`),
    INDEX `idx_category` (`category`),
    INDEX `idx_created_at` (`created_at`),
    INDEX `idx_last_executed_at` (`last_executed_at`),
    FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='工作流表';

-- 工作流执行记录表
CREATE TABLE IF NOT EXISTS `workflow_executions` (
    `id` INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `workflow_id` INT NOT NULL COMMENT '工作流ID',
    `user_id` INT NOT NULL COMMENT '执行用户ID',
    `execution_id` VARCHAR(100) NOT NULL UNIQUE COMMENT '执行ID',
    `input_data` TEXT DEFAULT NULL COMMENT '输入数据（JSON格式）',
    `output_data` TEXT DEFAULT NULL COMMENT '输出数据（JSON格式）',
    `context` TEXT DEFAULT NULL COMMENT '执行上下文（JSON格式）',
    `status` VARCHAR(20) NOT NULL DEFAULT 'pending' COMMENT '执行状态',
    `current_step` VARCHAR(100) DEFAULT NULL COMMENT '当前步骤',
    `completed_steps` TEXT DEFAULT NULL COMMENT '已完成步骤（JSON格式）',
    `error_message` TEXT DEFAULT NULL COMMENT '错误信息',
    `error_code` VARCHAR(50) DEFAULT NULL COMMENT '错误代码',
    `started_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '开始时间',
    `completed_at` DATETIME DEFAULT NULL COMMENT '完成时间',
    `duration_ms` INT DEFAULT NULL COMMENT '执行耗时（毫秒）',
    `metadata` TEXT DEFAULT NULL COMMENT '元数据（JSON格式）',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    INDEX `idx_workflow_id` (`workflow_id`),
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_execution_id` (`execution_id`),
    INDEX `idx_status` (`status`),
    INDEX `idx_started_at` (`started_at`),
    INDEX `idx_completed_at` (`completed_at`),
    FOREIGN KEY (`workflow_id`) REFERENCES `workflows`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='工作流执行记录表';

-- =====================================================
-- 7. 工具相关表
-- =====================================================

-- 工具表
CREATE TABLE IF NOT EXISTS `tools` (
    `id` INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `user_id` INT DEFAULT NULL COMMENT '创建者用户ID',
    `name` VARCHAR(100) NOT NULL COMMENT '工具名称',
    `display_name` VARCHAR(100) NOT NULL COMMENT '显示名称',
    `description` TEXT DEFAULT NULL COMMENT '工具描述',
    `tool_type` VARCHAR(50) NOT NULL COMMENT '工具类型',
    `category` VARCHAR(50) DEFAULT NULL COMMENT '工具分类',
    `version` VARCHAR(20) NOT NULL DEFAULT '1.0.0' COMMENT '版本号',
    `config` TEXT DEFAULT NULL COMMENT '工具配置（JSON格式）',
    `schema` TEXT DEFAULT NULL COMMENT '工具模式（JSON格式）',
    `implementation` TEXT DEFAULT NULL COMMENT '实现代码',
    `is_builtin` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否内置工具',
    `is_public` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否公开',
    `is_enabled` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
    `usage_count` INT NOT NULL DEFAULT 0 COMMENT '使用次数',
    `success_count` INT NOT NULL DEFAULT 0 COMMENT '成功次数',
    `error_count` INT NOT NULL DEFAULT 0 COMMENT '错误次数',
    `avg_execution_time_ms` INT DEFAULT NULL COMMENT '平均执行时间（毫秒）',
    `last_used_at` DATETIME DEFAULT NULL COMMENT '最后使用时间',
    `tags` TEXT DEFAULT NULL COMMENT '标签（JSON格式）',
    `metadata` TEXT DEFAULT NULL COMMENT '元数据（JSON格式）',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at` DATETIME DEFAULT NULL COMMENT '删除时间',
    `is_active` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '是否激活',

    UNIQUE KEY `uk_name` (`name`),
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_tool_type` (`tool_type`),
    INDEX `idx_category` (`category`),
    INDEX `idx_is_builtin` (`is_builtin`),
    INDEX `idx_is_public` (`is_public`),
    INDEX `idx_is_enabled` (`is_enabled`),
    INDEX `idx_created_at` (`created_at`),
    FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='工具表';

-- 工具执行记录表
CREATE TABLE IF NOT EXISTS `tool_executions` (
    `id` INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `tool_id` INT NOT NULL COMMENT '工具ID',
    `user_id` INT DEFAULT NULL COMMENT '执行用户ID',
    `agent_id` INT DEFAULT NULL COMMENT '执行智能体ID',
    `session_id` INT DEFAULT NULL COMMENT '会话ID',
    `task_id` INT DEFAULT NULL COMMENT '任务ID',
    `execution_id` VARCHAR(100) NOT NULL COMMENT '执行ID',
    `input_data` TEXT DEFAULT NULL COMMENT '输入数据（JSON格式）',
    `output_data` TEXT DEFAULT NULL COMMENT '输出数据（JSON格式）',
    `status` VARCHAR(20) NOT NULL DEFAULT 'pending' COMMENT '执行状态',
    `error_message` TEXT DEFAULT NULL COMMENT '错误信息',
    `error_code` VARCHAR(50) DEFAULT NULL COMMENT '错误代码',
    `started_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '开始时间',
    `completed_at` DATETIME DEFAULT NULL COMMENT '完成时间',
    `duration_ms` INT DEFAULT NULL COMMENT '执行耗时（毫秒）',
    `metadata` TEXT DEFAULT NULL COMMENT '元数据（JSON格式）',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',

    INDEX `idx_tool_id` (`tool_id`),
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_agent_id` (`agent_id`),
    INDEX `idx_session_id` (`session_id`),
    INDEX `idx_task_id` (`task_id`),
    INDEX `idx_execution_id` (`execution_id`),
    INDEX `idx_status` (`status`),
    INDEX `idx_started_at` (`started_at`),
    FOREIGN KEY (`tool_id`) REFERENCES `tools`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE SET NULL,
    FOREIGN KEY (`agent_id`) REFERENCES `agents`(`id`) ON DELETE SET NULL,
    FOREIGN KEY (`session_id`) REFERENCES `sessions`(`id`) ON DELETE SET NULL,
    FOREIGN KEY (`task_id`) REFERENCES `tasks`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='工具执行记录表';

-- =====================================================
-- 8. 配置相关表
-- =====================================================

-- 系统配置表
CREATE TABLE IF NOT EXISTS `system_configs` (
    `id` INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `config_key` VARCHAR(100) NOT NULL UNIQUE COMMENT '配置键',
    `config_value` TEXT DEFAULT NULL COMMENT '配置值',
    `config_type` VARCHAR(20) NOT NULL DEFAULT 'string' COMMENT '配置类型',
    `description` TEXT DEFAULT NULL COMMENT '配置描述',
    `category` VARCHAR(50) DEFAULT NULL COMMENT '配置分类',
    `is_public` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否公开',
    `is_editable` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '是否可编辑',
    `validation_rule` TEXT DEFAULT NULL COMMENT '验证规则（JSON格式）',
    `default_value` TEXT DEFAULT NULL COMMENT '默认值',
    `created_by` INT DEFAULT NULL COMMENT '创建者用户ID',
    `updated_by` INT DEFAULT NULL COMMENT '更新者用户ID',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    INDEX `idx_config_key` (`config_key`),
    INDEX `idx_category` (`category`),
    INDEX `idx_is_public` (`is_public`),
    INDEX `idx_created_by` (`created_by`),
    FOREIGN KEY (`created_by`) REFERENCES `users`(`id`) ON DELETE SET NULL,
    FOREIGN KEY (`updated_by`) REFERENCES `users`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表';

-- 用户配置表
CREATE TABLE IF NOT EXISTS `user_configs` (
    `id` INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `user_id` INT NOT NULL COMMENT '用户ID',
    `config_key` VARCHAR(100) NOT NULL COMMENT '配置键',
    `config_value` TEXT DEFAULT NULL COMMENT '配置值',
    `config_type` VARCHAR(20) NOT NULL DEFAULT 'string' COMMENT '配置类型',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    UNIQUE KEY `uk_user_config` (`user_id`, `config_key`),
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_config_key` (`config_key`),
    FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户配置表';

-- =====================================================
-- 9. 文件和工件相关表
-- =====================================================

-- 文件表
CREATE TABLE IF NOT EXISTS `files` (
    `id` INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `user_id` INT NOT NULL COMMENT '上传者用户ID',
    `file_name` VARCHAR(255) NOT NULL COMMENT '文件名',
    `original_name` VARCHAR(255) NOT NULL COMMENT '原始文件名',
    `file_type` VARCHAR(50) NOT NULL COMMENT '文件类型',
    `file_size` BIGINT NOT NULL COMMENT '文件大小（字节）',
    `file_path` VARCHAR(500) NOT NULL COMMENT '文件路径',
    `file_url` VARCHAR(500) DEFAULT NULL COMMENT '文件URL',
    `mime_type` VARCHAR(100) DEFAULT NULL COMMENT 'MIME类型',
    `file_hash` VARCHAR(64) DEFAULT NULL COMMENT '文件哈希',
    `thumbnail_path` VARCHAR(500) DEFAULT NULL COMMENT '缩略图路径',
    `thumbnail_url` VARCHAR(500) DEFAULT NULL COMMENT '缩略图URL',
    `storage_type` VARCHAR(20) NOT NULL DEFAULT 'local' COMMENT '存储类型',
    `storage_config` TEXT DEFAULT NULL COMMENT '存储配置（JSON格式）',
    `upload_status` VARCHAR(20) NOT NULL DEFAULT 'pending' COMMENT '上传状态',
    `is_public` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否公开',
    `access_count` INT NOT NULL DEFAULT 0 COMMENT '访问次数',
    `last_accessed_at` DATETIME DEFAULT NULL COMMENT '最后访问时间',
    `expires_at` DATETIME DEFAULT NULL COMMENT '过期时间',
    `metadata` TEXT DEFAULT NULL COMMENT '元数据（JSON格式）',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at` DATETIME DEFAULT NULL COMMENT '删除时间',
    `is_active` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '是否激活',

    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_file_type` (`file_type`),
    INDEX `idx_file_hash` (`file_hash`),
    INDEX `idx_upload_status` (`upload_status`),
    INDEX `idx_is_public` (`is_public`),
    INDEX `idx_created_at` (`created_at`),
    FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文件表';

-- 工件表
CREATE TABLE IF NOT EXISTS `artifacts` (
    `id` INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `user_id` INT NOT NULL COMMENT '创建者用户ID',
    `file_id` INT DEFAULT NULL COMMENT '关联文件ID',
    `name` VARCHAR(100) NOT NULL COMMENT '工件名称',
    `description` TEXT DEFAULT NULL COMMENT '工件描述',
    `artifact_type` VARCHAR(50) NOT NULL COMMENT '工件类型',
    `version` VARCHAR(20) NOT NULL DEFAULT '1.0.0' COMMENT '版本号',
    `content` TEXT DEFAULT NULL COMMENT '工件内容',
    `content_type` VARCHAR(50) DEFAULT NULL COMMENT '内容类型',
    `size` BIGINT DEFAULT NULL COMMENT '大小（字节）',
    `checksum` VARCHAR(64) DEFAULT NULL COMMENT '校验和',
    `is_public` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否公开',
    `download_count` INT NOT NULL DEFAULT 0 COMMENT '下载次数',
    `last_downloaded_at` DATETIME DEFAULT NULL COMMENT '最后下载时间',
    `tags` TEXT DEFAULT NULL COMMENT '标签（JSON格式）',
    `metadata` TEXT DEFAULT NULL COMMENT '元数据（JSON格式）',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at` DATETIME DEFAULT NULL COMMENT '删除时间',
    `is_active` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '是否激活',

    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_file_id` (`file_id`),
    INDEX `idx_artifact_type` (`artifact_type`),
    INDEX `idx_is_public` (`is_public`),
    INDEX `idx_created_at` (`created_at`),
    FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`file_id`) REFERENCES `files`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='工件表';

-- =====================================================
-- 10. 监控和日志相关表
-- =====================================================

-- 系统日志表
CREATE TABLE IF NOT EXISTS `system_logs` (
    `id` INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `level` VARCHAR(20) NOT NULL COMMENT '日志级别',
    `logger_name` VARCHAR(100) DEFAULT NULL COMMENT '记录器名称',
    `message` TEXT NOT NULL COMMENT '日志消息',
    `module` VARCHAR(100) DEFAULT NULL COMMENT '模块名称',
    `function_name` VARCHAR(100) DEFAULT NULL COMMENT '函数名称',
    `line_number` INT DEFAULT NULL COMMENT '行号',
    `user_id` INT DEFAULT NULL COMMENT '用户ID',
    `session_id` VARCHAR(100) DEFAULT NULL COMMENT '会话ID',
    `request_id` VARCHAR(100) DEFAULT NULL COMMENT '请求ID',
    `ip_address` VARCHAR(45) DEFAULT NULL COMMENT 'IP地址',
    `user_agent` TEXT DEFAULT NULL COMMENT '用户代理',
    `extra_data` TEXT DEFAULT NULL COMMENT '额外数据（JSON格式）',
    `stack_trace` TEXT DEFAULT NULL COMMENT '堆栈跟踪',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',

    INDEX `idx_level` (`level`),
    INDEX `idx_logger_name` (`logger_name`),
    INDEX `idx_module` (`module`),
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_session_id` (`session_id`),
    INDEX `idx_request_id` (`request_id`),
    INDEX `idx_created_at` (`created_at`),
    FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统日志表';

-- 性能指标表
CREATE TABLE IF NOT EXISTS `performance_metrics` (
    `id` INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `metric_name` VARCHAR(100) NOT NULL COMMENT '指标名称',
    `metric_type` VARCHAR(20) NOT NULL COMMENT '指标类型',
    `metric_value` DECIMAL(15,6) NOT NULL COMMENT '指标值',
    `metric_unit` VARCHAR(20) DEFAULT NULL COMMENT '指标单位',
    `tags` TEXT DEFAULT NULL COMMENT '标签（JSON格式）',
    `timestamp` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '时间戳',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',

    INDEX `idx_metric_name` (`metric_name`),
    INDEX `idx_metric_type` (`metric_type`),
    INDEX `idx_timestamp` (`timestamp`),
    INDEX `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='性能指标表';

-- API调用统计表
CREATE TABLE IF NOT EXISTS `api_call_stats` (
    `id` INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `user_id` INT DEFAULT NULL COMMENT '用户ID',
    `endpoint` VARCHAR(200) NOT NULL COMMENT 'API端点',
    `method` VARCHAR(10) NOT NULL COMMENT 'HTTP方法',
    `status_code` INT NOT NULL COMMENT '状态码',
    `response_time_ms` INT NOT NULL COMMENT '响应时间（毫秒）',
    `request_size` INT DEFAULT NULL COMMENT '请求大小（字节）',
    `response_size` INT DEFAULT NULL COMMENT '响应大小（字节）',
    `ip_address` VARCHAR(45) DEFAULT NULL COMMENT 'IP地址',
    `user_agent` TEXT DEFAULT NULL COMMENT '用户代理',
    `error_message` TEXT DEFAULT NULL COMMENT '错误信息',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',

    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_endpoint` (`endpoint`),
    INDEX `idx_method` (`method`),
    INDEX `idx_status_code` (`status_code`),
    INDEX `idx_created_at` (`created_at`),
    FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='API调用统计表';

-- =====================================================
-- 11. 通知相关表
-- =====================================================

-- 通知表
CREATE TABLE IF NOT EXISTS `notifications` (
    `id` INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `user_id` INT NOT NULL COMMENT '接收者用户ID',
    `sender_id` INT DEFAULT NULL COMMENT '发送者用户ID',
    `notification_type` VARCHAR(50) NOT NULL COMMENT '通知类型',
    `title` VARCHAR(200) NOT NULL COMMENT '通知标题',
    `content` TEXT NOT NULL COMMENT '通知内容',
    `data` TEXT DEFAULT NULL COMMENT '通知数据（JSON格式）',
    `priority` VARCHAR(20) NOT NULL DEFAULT 'normal' COMMENT '优先级',
    `is_read` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否已读',
    `read_at` DATETIME DEFAULT NULL COMMENT '阅读时间',
    `is_deleted` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否已删除',
    `deleted_at` DATETIME DEFAULT NULL COMMENT '删除时间',
    `expires_at` DATETIME DEFAULT NULL COMMENT '过期时间',
    `action_url` VARCHAR(500) DEFAULT NULL COMMENT '操作链接',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_sender_id` (`sender_id`),
    INDEX `idx_notification_type` (`notification_type`),
    INDEX `idx_priority` (`priority`),
    INDEX `idx_is_read` (`is_read`),
    INDEX `idx_created_at` (`created_at`),
    FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`sender_id`) REFERENCES `users`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='通知表';

-- =====================================================
-- 12. 标签和分类相关表
-- =====================================================

-- 标签表
CREATE TABLE IF NOT EXISTS `tags` (
    `id` INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `name` VARCHAR(50) NOT NULL UNIQUE COMMENT '标签名称',
    `display_name` VARCHAR(100) DEFAULT NULL COMMENT '显示名称',
    `description` TEXT DEFAULT NULL COMMENT '标签描述',
    `color` VARCHAR(7) DEFAULT NULL COMMENT '标签颜色',
    `category` VARCHAR(50) DEFAULT NULL COMMENT '标签分类',
    `usage_count` INT NOT NULL DEFAULT 0 COMMENT '使用次数',
    `is_system` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否系统标签',
    `created_by` INT DEFAULT NULL COMMENT '创建者用户ID',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    INDEX `idx_name` (`name`),
    INDEX `idx_category` (`category`),
    INDEX `idx_usage_count` (`usage_count`),
    INDEX `idx_is_system` (`is_system`),
    INDEX `idx_created_by` (`created_by`),
    FOREIGN KEY (`created_by`) REFERENCES `users`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='标签表';

-- 资源标签关联表
CREATE TABLE IF NOT EXISTS `resource_tags` (
    `id` INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `tag_id` INT NOT NULL COMMENT '标签ID',
    `resource_type` VARCHAR(50) NOT NULL COMMENT '资源类型',
    `resource_id` INT NOT NULL COMMENT '资源ID',
    `created_by` INT DEFAULT NULL COMMENT '创建者用户ID',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',

    UNIQUE KEY `uk_tag_resource` (`tag_id`, `resource_type`, `resource_id`),
    INDEX `idx_tag_id` (`tag_id`),
    INDEX `idx_resource_type` (`resource_type`),
    INDEX `idx_resource_id` (`resource_id`),
    INDEX `idx_created_by` (`created_by`),
    FOREIGN KEY (`tag_id`) REFERENCES `tags`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`created_by`) REFERENCES `users`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='资源标签关联表';

-- =====================================================
-- 13. 初始化数据
-- =====================================================

-- 插入系统配置
INSERT INTO `system_configs` (`config_key`, `config_value`, `config_type`, `description`, `category`, `is_public`, `is_editable`) VALUES
('system.name', 'A2A多智能体系统', 'string', '系统名称', 'general', TRUE, TRUE),
('system.version', '1.0.0', 'string', '系统版本', 'general', TRUE, FALSE),
('system.description', '基于Google ADK的多智能体协作系统', 'string', '系统描述', 'general', TRUE, TRUE),
('system.timezone', 'UTC', 'string', '系统默认时区', 'general', TRUE, TRUE),
('system.language', 'zh-CN', 'string', '系统默认语言', 'general', TRUE, TRUE),
('auth.jwt_expire_minutes', '30', 'integer', 'JWT访问令牌过期时间（分钟）', 'auth', FALSE, TRUE),
('auth.refresh_expire_days', '7', 'integer', '刷新令牌过期时间（天）', 'auth', FALSE, TRUE),
('auth.max_login_attempts', '5', 'integer', '最大登录尝试次数', 'auth', FALSE, TRUE),
('auth.lockout_duration_minutes', '30', 'integer', '账户锁定时长（分钟）', 'auth', FALSE, TRUE),
('user.default_role', 'user', 'string', '用户默认角色', 'user', FALSE, TRUE),
('user.max_sessions', '10', 'integer', '用户最大会话数', 'user', FALSE, TRUE),
('agent.max_per_user', '50', 'integer', '每用户最大智能体数量', 'agent', FALSE, TRUE),
('agent.default_model', 'gemini-pro', 'string', '默认AI模型', 'agent', FALSE, TRUE),
('agent.max_tokens', '4000', 'integer', '默认最大令牌数', 'agent', FALSE, TRUE),
('session.timeout_minutes', '60', 'integer', '会话超时时间（分钟）', 'session', FALSE, TRUE),
('session.max_per_user', '20', 'integer', '每用户最大会话数', 'session', FALSE, TRUE),
('task.max_concurrent', '5', 'integer', '最大并发任务数', 'task', FALSE, TRUE),
('task.timeout_minutes', '60', 'integer', '任务超时时间（分钟）', 'task', FALSE, TRUE),
('task.max_retries', '3', 'integer', '任务最大重试次数', 'task', FALSE, TRUE),
('file.max_size_mb', '100', 'integer', '文件最大大小（MB）', 'file', FALSE, TRUE),
('file.allowed_types', 'jpg,jpeg,png,gif,pdf,txt,doc,docx,xls,xlsx,ppt,pptx', 'string', '允许的文件类型', 'file', FALSE, TRUE),
('notification.enabled', 'true', 'boolean', '是否启用通知', 'notification', FALSE, TRUE),
('monitoring.enabled', 'true', 'boolean', '是否启用监控', 'monitoring', FALSE, TRUE),
('api.rate_limit_per_minute', '60', 'integer', 'API每分钟请求限制', 'api', FALSE, TRUE);

-- 插入系统标签
INSERT INTO `tags` (`name`, `display_name`, `description`, `color`, `category`, `is_system`) VALUES
('general', '通用', '通用标签', '#6B7280', 'system', TRUE),
('chat', '聊天', '聊天相关', '#3B82F6', 'agent', TRUE),
('task', '任务', '任务相关', '#10B981', 'agent', TRUE),
('workflow', '工作流', '工作流相关', '#8B5CF6', 'agent', TRUE),
('tool', '工具', '工具相关', '#F59E0B', 'agent', TRUE),
('assistant', '助手', '助手类型', '#06B6D4', 'agent', TRUE),
('automation', '自动化', '自动化相关', '#84CC16', 'workflow', TRUE),
('analysis', '分析', '分析相关', '#EF4444', 'task', TRUE),
('processing', '处理', '数据处理', '#F97316', 'task', TRUE),
('integration', '集成', '系统集成', '#EC4899', 'system', TRUE),
('public', '公开', '公开资源', '#14B8A6', 'visibility', TRUE),
('private', '私有', '私有资源', '#6366F1', 'visibility', TRUE),
('template', '模板', '模板资源', '#A855F7', 'type', TRUE),
('example', '示例', '示例资源', '#22C55E', 'type', TRUE),
('production', '生产', '生产环境', '#DC2626', 'environment', TRUE),
('development', '开发', '开发环境', '#2563EB', 'environment', TRUE),
('testing', '测试', '测试环境', '#7C3AED', 'environment', TRUE);

-- =====================================================
-- 14. 创建视图
-- =====================================================

-- 用户统计视图
CREATE VIEW `v_user_stats` AS
SELECT
    u.id,
    u.username,
    u.email,
    u.role,
    u.is_verified,
    u.is_locked,
    u.created_at,
    u.last_login_at,
    COALESCE(agent_count.count, 0) AS agent_count,
    COALESCE(session_count.count, 0) AS session_count,
    COALESCE(task_count.count, 0) AS task_count
FROM users u
LEFT JOIN (
    SELECT user_id, COUNT(*) as count
    FROM agents
    WHERE deleted_at IS NULL
    GROUP BY user_id
) agent_count ON u.id = agent_count.user_id
LEFT JOIN (
    SELECT user_id, COUNT(*) as count
    FROM sessions
    WHERE deleted_at IS NULL
    GROUP BY user_id
) session_count ON u.id = session_count.user_id
LEFT JOIN (
    SELECT user_id, COUNT(*) as count
    FROM tasks
    WHERE deleted_at IS NULL
    GROUP BY user_id
) task_count ON u.id = task_count.user_id
WHERE u.deleted_at IS NULL;

-- 智能体统计视图
CREATE VIEW `v_agent_stats` AS
SELECT
    a.id,
    a.name,
    a.agent_type,
    a.status,
    a.is_public,
    a.execution_count,
    a.success_count,
    a.error_count,
    a.avg_response_time,
    a.last_executed_at,
    a.created_at,
    u.username as owner_username
FROM agents a
JOIN users u ON a.owner_id = u.id
WHERE a.deleted_at IS NULL;

-- 会话统计视图
CREATE VIEW `v_session_stats` AS
SELECT
    s.id,
    s.title,
    s.session_type,
    s.status,
    s.participant_count,
    s.message_count,
    s.last_message_at,
    s.created_at,
    u.username as creator_username
FROM sessions s
JOIN users u ON s.user_id = u.id
WHERE s.deleted_at IS NULL;

-- =====================================================
-- 15. 创建存储过程
-- =====================================================

DELIMITER //

-- 清理过期令牌的存储过程
CREATE PROCEDURE `sp_cleanup_expired_tokens`()
BEGIN
    DELETE FROM user_tokens
    WHERE expires_at < NOW() OR is_revoked = TRUE;

    SELECT ROW_COUNT() as deleted_count;
END //

-- 更新用户统计信息的存储过程
CREATE PROCEDURE `sp_update_user_stats`(IN user_id_param INT)
BEGIN
    DECLARE agent_cnt INT DEFAULT 0;
    DECLARE session_cnt INT DEFAULT 0;
    DECLARE task_cnt INT DEFAULT 0;

    SELECT COUNT(*) INTO agent_cnt
    FROM agents
    WHERE user_id = user_id_param AND deleted_at IS NULL;

    SELECT COUNT(*) INTO session_cnt
    FROM sessions
    WHERE user_id = user_id_param AND deleted_at IS NULL;

    SELECT COUNT(*) INTO task_cnt
    FROM tasks
    WHERE user_id = user_id_param AND deleted_at IS NULL;

    -- 这里可以将统计信息存储到用户表或单独的统计表中
    SELECT agent_cnt, session_cnt, task_cnt;
END //

-- 获取系统健康状态的存储过程
CREATE PROCEDURE `sp_get_system_health`()
BEGIN
    SELECT
        'users' as table_name,
        COUNT(*) as total_count,
        COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR) THEN 1 END) as last_24h_count
    FROM users WHERE deleted_at IS NULL

    UNION ALL

    SELECT
        'agents' as table_name,
        COUNT(*) as total_count,
        COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR) THEN 1 END) as last_24h_count
    FROM agents WHERE deleted_at IS NULL

    UNION ALL

    SELECT
        'sessions' as table_name,
        COUNT(*) as total_count,
        COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR) THEN 1 END) as last_24h_count
    FROM sessions WHERE deleted_at IS NULL

    UNION ALL

    SELECT
        'tasks' as table_name,
        COUNT(*) as total_count,
        COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR) THEN 1 END) as last_24h_count
    FROM tasks WHERE deleted_at IS NULL;
END //

DELIMITER ;

-- =====================================================
-- 16. 设置外键约束
-- =====================================================

SET FOREIGN_KEY_CHECKS = 1;

-- =====================================================
-- 脚本执行完成
-- =====================================================

-- 显示创建的表
SHOW TABLES;

-- =====================================================
-- 17. 管理员用户初始化
-- =====================================================

-- 创建默认管理员用户
-- 密码：Admin123!@# (BCrypt哈希值)
INSERT INTO `users` (
    `username`,
    `email`,
    `password_hash`,
    `full_name`,
    `role`,
    `is_verified`,
    `is_active`,
    `timezone`,
    `language`
) VALUES (
    'admin',
    '<EMAIL>',
    '$2b$12$LQv3c1yqBwEHxPuNYjHNTO.eMQZOyCbquzHbnGJRBjOxMxEwVzgGS',  -- Admin123!@#
    '系统管理员',
    'super_admin',
    1,
    1,
    'UTC',
    'zh-CN'
);

-- 创建测试用户
INSERT INTO `users` (
    `username`,
    `email`,
    `password_hash`,
    `full_name`,
    `role`,
    `is_verified`,
    `is_active`,
    `timezone`,
    `language`
) VALUES (
    'testuser',
    '<EMAIL>',
    '$2b$12$LQv3c1yqBwEHxPuNYjHNTO.eMQZOyCbquzHbnGJRBjOxMxEwVzgGS',  -- Admin123!@#
    '测试用户',
    'user',
    1,
    1,
    'UTC',
    'zh-CN'
);

-- =====================================================
-- 18. 创建示例智能体
-- =====================================================

-- 获取管理员用户ID
SET @admin_user_id = (SELECT id FROM users WHERE username = 'admin' LIMIT 1);

-- 创建示例聊天智能体
INSERT INTO `agents` (
    `user_id`,
    `owner_id`,
    `name`,
    `description`,
    `agent_type`,
    `model_name`,
    `system_prompt`,
    `config`,
    `is_public`,
    `status`
) VALUES (
    @admin_user_id,
    @admin_user_id,
    '通用助手',
    '一个通用的AI助手，可以回答各种问题并协助完成任务',
    'chat',
    'gemini-pro',
    '你是一个有用、准确、友善的AI助手。请用中文回答用户的问题，提供有价值的帮助。',
    '{"temperature": 0.7, "max_tokens": 2000, "top_p": 1.0}',
    1,
    'active'
);

-- 创建示例任务智能体
INSERT INTO `agents` (
    `user_id`,
    `owner_id`,
    `name`,
    `description`,
    `agent_type`,
    `model_name`,
    `system_prompt`,
    `config`,
    `is_public`,
    `status`
) VALUES (
    @admin_user_id,
    @admin_user_id,
    '任务执行器',
    '专门用于执行各种自动化任务的智能体',
    'task',
    'gemini-pro',
    '你是一个专业的任务执行助手。请按照用户的指示，高效、准确地完成各种任务。',
    '{"temperature": 0.3, "max_tokens": 1500, "top_p": 0.9}',
    1,
    'active'
);

-- =====================================================
-- 19. 创建示例工具
-- =====================================================

-- 创建文本处理工具
INSERT INTO `tools` (
    `user_id`,
    `name`,
    `display_name`,
    `description`,
    `tool_type`,
    `category`,
    `config`,
    `schema`,
    `is_builtin`,
    `is_public`,
    `is_enabled`
) VALUES (
    @admin_user_id,
    'text_processor',
    '文本处理器',
    '用于处理和分析文本内容的工具',
    'text_processing',
    'utility',
    '{"max_length": 10000, "supported_formats": ["txt", "md", "json"]}',
    '{"type": "object", "properties": {"text": {"type": "string", "description": "要处理的文本"}}}',
    1,
    1,
    1
);

-- 创建数据分析工具
INSERT INTO `tools` (
    `user_id`,
    `name`,
    `display_name`,
    `description`,
    `tool_type`,
    `category`,
    `config`,
    `schema`,
    `is_builtin`,
    `is_public`,
    `is_enabled`
) VALUES (
    @admin_user_id,
    'data_analyzer',
    '数据分析器',
    '用于分析和可视化数据的工具',
    'data_analysis',
    'analytics',
    '{"max_rows": 10000, "supported_formats": ["csv", "json", "xlsx"]}',
    '{"type": "object", "properties": {"data": {"type": "string", "description": "要分析的数据"}}}',
    1,
    1,
    1
);

-- =====================================================
-- 20. 最终检查和清理
-- =====================================================

-- 重新启用外键检查
SET FOREIGN_KEY_CHECKS = 1;

-- 显示创建的表
SHOW TABLES;

-- 显示用户统计
SELECT
    COUNT(*) as total_users,
    COUNT(CASE WHEN role = 'super_admin' THEN 1 END) as admin_users,
    COUNT(CASE WHEN role = 'user' THEN 1 END) as regular_users
FROM users;

-- 显示智能体统计
SELECT
    COUNT(*) as total_agents,
    COUNT(CASE WHEN is_public = 1 THEN 1 END) as public_agents,
    COUNT(CASE WHEN status = 'active' THEN 1 END) as active_agents
FROM agents;

-- 显示工具统计
SELECT
    COUNT(*) as total_tools,
    COUNT(CASE WHEN is_builtin = 1 THEN 1 END) as builtin_tools,
    COUNT(CASE WHEN is_public = 1 THEN 1 END) as public_tools
FROM tools;

-- 显示数据库信息
SELECT
    'A2A多智能体系统数据库创建完成' as message,
    NOW() as created_at,
    DATABASE() as database_name,
    VERSION() as mysql_version,
    'MySQL 5.7+兼容' as compatibility;

-- =====================================================
-- 使用说明
-- =====================================================
/*
数据库创建完成后，您可以使用以下账户登录：

管理员账户：
- 用户名：admin
- 密码：Admin123!@#
- 邮箱：<EMAIL>
- 角色：super_admin

测试账户：
- 用户名：testuser
- 密码：Admin123!@#
- 邮箱：<EMAIL>
- 角色：user

注意事项：
1. 请在生产环境中立即修改默认密码
2. 建议删除或禁用测试账户
3. 定期备份数据库
4. 监控系统性能和安全性

表结构说明：
- 用户相关：users, user_tokens, user_permissions, user_activity_logs, user_configs
- 智能体相关：agents, agent_executions
- 会话相关：sessions, session_participants, messages, message_attachments
- 任务相关：tasks, task_execution_logs
- 工作流相关：workflows, workflow_executions
- 工具相关：tools, tool_executions
- 配置相关：system_configs
- 文件相关：files, artifacts
- 监控相关：system_logs, performance_metrics, api_call_stats
- 通知相关：notifications
- 标签相关：tags, resource_tags

视图：
- v_user_stats：用户统计视图
- v_agent_stats：智能体统计视图
- v_session_stats：会话统计视图

存储过程：
- sp_cleanup_expired_tokens：清理过期令牌
- sp_update_user_stats：更新用户统计
- sp_get_system_health：获取系统健康状态
*/
