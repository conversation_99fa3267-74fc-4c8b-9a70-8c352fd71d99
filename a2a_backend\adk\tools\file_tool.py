#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A2A多智能体系统 文件操作工具

包含用户文件权限验证的文件操作工具，支持安全的文件读写和管理
"""

import os
import asyncio
import aiofiles
import logging
import hashlib
import mimetypes
from typing import Dict, List, Any, Optional, Union, BinaryIO
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime
from pathlib import Path
import json
import base64
from urllib.parse import quote, unquote

# Google ADK imports
from google.ai.generativelanguage import FunctionDeclaration, Schema, Type

from .base_tool import (
    BaseTool, ToolConfig, ToolResult, ToolError, ToolStatus,
    ToolExecutionContext, ToolPermission
)


class FileOperation(Enum):
    """文件操作类型枚举"""
    READ = "read"
    WRITE = "write"
    APPEND = "append"
    DELETE = "delete"
    COPY = "copy"
    MOVE = "move"
    LIST = "list"
    MKDIR = "mkdir"
    RMDIR = "rmdir"
    STAT = "stat"
    EXISTS = "exists"
    SEARCH = "search"


class FileAccessMode(Enum):
    """文件访问模式枚举"""
    SANDBOX = "sandbox"  # 沙箱模式，只能访问用户目录
    RESTRICTED = "restricted"  # 受限模式，只能访问指定目录
    FULL = "full"  # 完全访问模式（管理员）


@dataclass
class FileToolConfig(ToolConfig):
    """文件工具配置"""
    base_directory: str = "/tmp/a2a_files"
    allowed_extensions: List[str] = field(default_factory=lambda: [
        '.txt', '.json', '.csv', '.xml', '.yaml', '.yml', '.md', '.log'
    ])
    blocked_extensions: List[str] = field(default_factory=lambda: [
        '.exe', '.bat', '.sh', '.ps1', '.dll', '.so'
    ])
    max_file_size: int = 10 * 1024 * 1024  # 10MB
    max_files_per_operation: int = 100
    access_mode: FileAccessMode = FileAccessMode.SANDBOX
    allowed_directories: List[str] = field(default_factory=list)
    enable_binary_files: bool = False
    enable_compression: bool = True
    auto_backup: bool = True
    
    def __post_init__(self):
        """配置后处理"""
        # 确保基础目录存在
        os.makedirs(self.base_directory, exist_ok=True)
        
        # 验证允许的目录
        if self.access_mode == FileAccessMode.RESTRICTED and not self.allowed_directories:
            raise ValueError("受限模式需要指定允许的目录")


@dataclass
class FileInfo:
    """文件信息"""
    path: str
    name: str
    size: int
    is_directory: bool
    created_at: datetime
    modified_at: datetime
    permissions: str
    mime_type: Optional[str] = None
    checksum: Optional[str] = None
    owner: Optional[str] = None


class FileTool(BaseTool):
    """文件操作工具"""
    
    def __init__(self, config: FileToolConfig):
        """
        初始化文件工具
        
        Args:
            config: 文件工具配置
        """
        super().__init__(config)
        self._user_directories: Dict[str, str] = {}
    
    def get_function_declaration(self) -> FunctionDeclaration:
        """
        获取工具的函数声明
        
        Returns:
            FunctionDeclaration: ADK函数声明
        """
        parameters_schema = Schema(
            type=Type.OBJECT,
            properties={
                "operation": Schema(
                    type=Type.STRING,
                    description="文件操作类型 (read, write, append, delete, copy, move, list, mkdir, rmdir, stat, exists, search)"
                ),
                "path": Schema(type=Type.STRING, description="文件或目录路径"),
                "content": Schema(type=Type.STRING, description="文件内容（用于写入操作）"),
                "destination": Schema(type=Type.STRING, description="目标路径（用于复制/移动操作）"),
                "encoding": Schema(type=Type.STRING, description="文件编码（默认utf-8）"),
                "recursive": Schema(type=Type.BOOLEAN, description="是否递归操作"),
                "pattern": Schema(type=Type.STRING, description="搜索模式（用于搜索操作）"),
                "max_depth": Schema(type=Type.INTEGER, description="最大搜索深度"),
                "include_hidden": Schema(type=Type.BOOLEAN, description="是否包含隐藏文件"),
                "binary_mode": Schema(type=Type.BOOLEAN, description="是否使用二进制模式"),
                "create_backup": Schema(type=Type.BOOLEAN, description="是否创建备份")
            },
            required=["operation", "path"]
        )
        
        return FunctionDeclaration(
            name=self.name,
            description=self.description,
            parameters=parameters_schema
        )
    
    async def execute(
        self,
        context: ToolExecutionContext,
        **kwargs
    ) -> ToolResult:
        """
        执行文件操作
        
        Args:
            context: 执行上下文
            **kwargs: 额外参数
        
        Returns:
            ToolResult: 执行结果
        """
        async with self._execution_context(context):
            try:
                operation = FileOperation(context.parameters["operation"])
                file_path = context.parameters["path"]
                
                # 验证文件路径权限
                permission_error = await self._validate_file_permissions(
                    file_path, operation, context.user_id
                )
                if permission_error:
                    return ToolResult(
                        tool_name=self.name,
                        execution_id=context.execution_id,
                        status=ToolStatus.FAILED,
                        error=permission_error,
                        user_id=context.user_id
                    )
                
                # 获取用户安全路径
                safe_path = self._get_safe_path(file_path, context.user_id)
                
                # 执行文件操作
                result = await self._execute_file_operation(
                    operation, safe_path, context.parameters, context.user_id
                )
                
                return ToolResult(
                    tool_name=self.name,
                    execution_id=context.execution_id,
                    status=ToolStatus.SUCCESS,
                    result=result,
                    user_id=context.user_id,
                    metadata={
                        "operation": operation.value,
                        "path": file_path,
                        "safe_path": safe_path
                    }
                )
                
            except Exception as e:
                self.logger.error(f"文件操作失败: {e}")
                
                return ToolResult(
                    tool_name=self.name,
                    execution_id=context.execution_id,
                    status=ToolStatus.FAILED,
                    error=ToolError(
                        code="FILE_OPERATION_ERROR",
                        message=f"文件操作失败: {str(e)}"
                    ),
                    user_id=context.user_id
                )
    
    async def _validate_file_permissions(
        self,
        file_path: str,
        operation: FileOperation,
        user_id: str
    ) -> Optional[ToolError]:
        """
        验证文件操作权限
        
        Args:
            file_path: 文件路径
            operation: 操作类型
            user_id: 用户ID
        
        Returns:
            Optional[ToolError]: 权限验证错误
        """
        try:
            # 检查文件扩展名
            file_ext = Path(file_path).suffix.lower()
            
            if file_ext in self.config.blocked_extensions:
                return ToolError(
                    code="BLOCKED_FILE_EXTENSION",
                    message=f"不允许操作的文件类型: {file_ext}"
                )
            
            if (self.config.allowed_extensions and 
                file_ext and 
                file_ext not in self.config.allowed_extensions):
                return ToolError(
                    code="DISALLOWED_FILE_EXTENSION",
                    message=f"不允许的文件类型: {file_ext}"
                )
            
            # 检查路径安全性
            if self._is_path_traversal_attempt(file_path):
                return ToolError(
                    code="PATH_TRAVERSAL_ATTEMPT",
                    message="检测到路径遍历攻击尝试"
                )
            
            # 检查访问模式权限
            if not self._check_access_mode_permission(file_path, user_id):
                return ToolError(
                    code="ACCESS_MODE_VIOLATION",
                    message="违反文件访问模式限制"
                )
            
            return None
            
        except Exception as e:
            return ToolError(
                code="PERMISSION_VALIDATION_ERROR",
                message=f"权限验证异常: {str(e)}"
            )
    
    def _is_path_traversal_attempt(self, file_path: str) -> bool:
        """
        检查是否为路径遍历攻击
        
        Args:
            file_path: 文件路径
        
        Returns:
            bool: 是否为路径遍历攻击
        """
        # 检查危险的路径模式
        dangerous_patterns = ['../', '..\\', '../', '..\\', '~/', '~\\']
        
        for pattern in dangerous_patterns:
            if pattern in file_path:
                return True
        
        # 检查绝对路径（在沙箱模式下不允许）
        if self.config.access_mode == FileAccessMode.SANDBOX and os.path.isabs(file_path):
            return True
        
        return False
    
    def _check_access_mode_permission(self, file_path: str, user_id: str) -> bool:
        """
        检查访问模式权限
        
        Args:
            file_path: 文件路径
            user_id: 用户ID
        
        Returns:
            bool: 是否有权限
        """
        if self.config.access_mode == FileAccessMode.FULL:
            return True
        
        safe_path = self._get_safe_path(file_path, user_id)
        
        if self.config.access_mode == FileAccessMode.SANDBOX:
            user_dir = self._get_user_directory(user_id)
            return safe_path.startswith(user_dir)
        
        elif self.config.access_mode == FileAccessMode.RESTRICTED:
            for allowed_dir in self.config.allowed_directories:
                if safe_path.startswith(allowed_dir):
                    return True
            return False
        
        return False
    
    def _get_safe_path(self, file_path: str, user_id: str) -> str:
        """
        获取安全的文件路径
        
        Args:
            file_path: 原始文件路径
            user_id: 用户ID
        
        Returns:
            str: 安全路径
        """
        if self.config.access_mode == FileAccessMode.FULL and os.path.isabs(file_path):
            return os.path.normpath(file_path)
        
        # 获取用户目录
        user_dir = self._get_user_directory(user_id)
        
        # 组合路径
        if os.path.isabs(file_path):
            # 绝对路径转换为相对路径
            file_path = file_path.lstrip(os.sep)
        
        safe_path = os.path.join(user_dir, file_path)
        return os.path.normpath(safe_path)
    
    def _get_user_directory(self, user_id: str) -> str:
        """
        获取用户目录
        
        Args:
            user_id: 用户ID
        
        Returns:
            str: 用户目录路径
        """
        if user_id not in self._user_directories:
            # 创建用户专用目录
            user_hash = hashlib.md5(user_id.encode()).hexdigest()[:8]
            user_dir = os.path.join(self.config.base_directory, f"user_{user_hash}")
            os.makedirs(user_dir, exist_ok=True)
            self._user_directories[user_id] = user_dir
        
        return self._user_directories[user_id]
    
    async def _execute_file_operation(
        self,
        operation: FileOperation,
        file_path: str,
        parameters: Dict[str, Any],
        user_id: str
    ) -> Dict[str, Any]:
        """
        执行文件操作
        
        Args:
            operation: 操作类型
            file_path: 文件路径
            parameters: 操作参数
            user_id: 用户ID
        
        Returns:
            Dict[str, Any]: 操作结果
        """
        if operation == FileOperation.READ:
            return await self._read_file(file_path, parameters)
        elif operation == FileOperation.WRITE:
            return await self._write_file(file_path, parameters, user_id)
        elif operation == FileOperation.APPEND:
            return await self._append_file(file_path, parameters, user_id)
        elif operation == FileOperation.DELETE:
            return await self._delete_file(file_path, parameters)
        elif operation == FileOperation.COPY:
            return await self._copy_file(file_path, parameters, user_id)
        elif operation == FileOperation.MOVE:
            return await self._move_file(file_path, parameters, user_id)
        elif operation == FileOperation.LIST:
            return await self._list_directory(file_path, parameters)
        elif operation == FileOperation.MKDIR:
            return await self._create_directory(file_path, parameters)
        elif operation == FileOperation.RMDIR:
            return await self._remove_directory(file_path, parameters)
        elif operation == FileOperation.STAT:
            return await self._get_file_stat(file_path)
        elif operation == FileOperation.EXISTS:
            return await self._check_file_exists(file_path)
        elif operation == FileOperation.SEARCH:
            return await self._search_files(file_path, parameters)
        else:
            raise ValueError(f"不支持的操作: {operation}")
    
    async def _read_file(
        self,
        file_path: str,
        parameters: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        读取文件
        
        Args:
            file_path: 文件路径
            parameters: 操作参数
        
        Returns:
            Dict[str, Any]: 读取结果
        """
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"文件不存在: {file_path}")
        
        if os.path.isdir(file_path):
            raise IsADirectoryError(f"路径是目录，不是文件: {file_path}")
        
        # 检查文件大小
        file_size = os.path.getsize(file_path)
        if file_size > self.config.max_file_size:
            raise ValueError(f"文件过大: {file_size} bytes > {self.config.max_file_size} bytes")
        
        encoding = parameters.get("encoding", "utf-8")
        binary_mode = parameters.get("binary_mode", False)
        
        try:
            if binary_mode or not self._is_text_file(file_path):
                # 二进制模式
                if not self.config.enable_binary_files:
                    raise ValueError("二进制文件读取被禁用")
                
                async with aiofiles.open(file_path, 'rb') as f:
                    content = await f.read()
                    content_b64 = base64.b64encode(content).decode('ascii')
                
                return {
                    "content": content_b64,
                    "encoding": "base64",
                    "size": file_size,
                    "binary": True
                }
            else:
                # 文本模式
                async with aiofiles.open(file_path, 'r', encoding=encoding) as f:
                    content = await f.read()
                
                return {
                    "content": content,
                    "encoding": encoding,
                    "size": file_size,
                    "binary": False
                }
                
        except UnicodeDecodeError as e:
            raise ValueError(f"文件编码错误: {e}")
    
    async def _write_file(
        self,
        file_path: str,
        parameters: Dict[str, Any],
        user_id: str
    ) -> Dict[str, Any]:
        """
        写入文件
        
        Args:
            file_path: 文件路径
            parameters: 操作参数
            user_id: 用户ID
        
        Returns:
            Dict[str, Any]: 写入结果
        """
        content = parameters.get("content", "")
        encoding = parameters.get("encoding", "utf-8")
        binary_mode = parameters.get("binary_mode", False)
        create_backup = parameters.get("create_backup", self.config.auto_backup)
        
        # 创建目录
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        
        # 创建备份
        if create_backup and os.path.exists(file_path):
            await self._create_backup(file_path)
        
        try:
            if binary_mode:
                # 二进制模式
                if not self.config.enable_binary_files:
                    raise ValueError("二进制文件写入被禁用")
                
                # 解码base64内容
                try:
                    content_bytes = base64.b64decode(content)
                except Exception as e:
                    raise ValueError(f"base64解码失败: {e}")
                
                async with aiofiles.open(file_path, 'wb') as f:
                    await f.write(content_bytes)
                
                file_size = len(content_bytes)
            else:
                # 文本模式
                async with aiofiles.open(file_path, 'w', encoding=encoding) as f:
                    await f.write(content)
                
                file_size = len(content.encode(encoding))
            
            return {
                "path": file_path,
                "size": file_size,
                "encoding": encoding,
                "binary": binary_mode
            }
            
        except Exception as e:
            raise ValueError(f"文件写入失败: {e}")
    
    async def _append_file(
        self,
        file_path: str,
        parameters: Dict[str, Any],
        user_id: str
    ) -> Dict[str, Any]:
        """
        追加文件内容
        
        Args:
            file_path: 文件路径
            parameters: 操作参数
            user_id: 用户ID
        
        Returns:
            Dict[str, Any]: 追加结果
        """
        content = parameters.get("content", "")
        encoding = parameters.get("encoding", "utf-8")
        
        # 创建目录
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        
        try:
            async with aiofiles.open(file_path, 'a', encoding=encoding) as f:
                await f.write(content)
            
            file_size = os.path.getsize(file_path)
            
            return {
                "path": file_path,
                "appended_size": len(content.encode(encoding)),
                "total_size": file_size,
                "encoding": encoding
            }
            
        except Exception as e:
            raise ValueError(f"文件追加失败: {e}")
    
    async def _delete_file(
        self,
        file_path: str,
        parameters: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        删除文件
        
        Args:
            file_path: 文件路径
            parameters: 操作参数
        
        Returns:
            Dict[str, Any]: 删除结果
        """
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"文件不存在: {file_path}")
        
        if os.path.isdir(file_path):
            raise IsADirectoryError(f"路径是目录，请使用rmdir操作: {file_path}")
        
        file_size = os.path.getsize(file_path)
        
        try:
            os.remove(file_path)
            
            return {
                "path": file_path,
                "deleted_size": file_size
            }
            
        except Exception as e:
            raise ValueError(f"文件删除失败: {e}")
    
    async def _copy_file(
        self,
        source_path: str,
        parameters: Dict[str, Any],
        user_id: str
    ) -> Dict[str, Any]:
        """
        复制文件
        
        Args:
            source_path: 源文件路径
            parameters: 操作参数
            user_id: 用户ID
        
        Returns:
            Dict[str, Any]: 复制结果
        """
        destination = parameters.get("destination")
        if not destination:
            raise ValueError("复制操作需要指定目标路径")
        
        dest_path = self._get_safe_path(destination, user_id)
        
        if not os.path.exists(source_path):
            raise FileNotFoundError(f"源文件不存在: {source_path}")
        
        if os.path.isdir(source_path):
            raise IsADirectoryError(f"源路径是目录: {source_path}")
        
        # 创建目标目录
        os.makedirs(os.path.dirname(dest_path), exist_ok=True)
        
        try:
            # 异步复制文件
            async with aiofiles.open(source_path, 'rb') as src:
                async with aiofiles.open(dest_path, 'wb') as dst:
                    while True:
                        chunk = await src.read(8192)  # 8KB chunks
                        if not chunk:
                            break
                        await dst.write(chunk)
            
            file_size = os.path.getsize(dest_path)
            
            return {
                "source": source_path,
                "destination": dest_path,
                "size": file_size
            }
            
        except Exception as e:
            raise ValueError(f"文件复制失败: {e}")
    
    async def _move_file(
        self,
        source_path: str,
        parameters: Dict[str, Any],
        user_id: str
    ) -> Dict[str, Any]:
        """
        移动文件
        
        Args:
            source_path: 源文件路径
            parameters: 操作参数
            user_id: 用户ID
        
        Returns:
            Dict[str, Any]: 移动结果
        """
        destination = parameters.get("destination")
        if not destination:
            raise ValueError("移动操作需要指定目标路径")
        
        dest_path = self._get_safe_path(destination, user_id)
        
        if not os.path.exists(source_path):
            raise FileNotFoundError(f"源文件不存在: {source_path}")
        
        # 创建目标目录
        os.makedirs(os.path.dirname(dest_path), exist_ok=True)
        
        try:
            os.rename(source_path, dest_path)
            
            file_size = os.path.getsize(dest_path)
            
            return {
                "source": source_path,
                "destination": dest_path,
                "size": file_size
            }
            
        except Exception as e:
            raise ValueError(f"文件移动失败: {e}")
    
    async def _list_directory(
        self,
        dir_path: str,
        parameters: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        列出目录内容
        
        Args:
            dir_path: 目录路径
            parameters: 操作参数
        
        Returns:
            Dict[str, Any]: 目录内容
        """
        if not os.path.exists(dir_path):
            raise FileNotFoundError(f"目录不存在: {dir_path}")
        
        if not os.path.isdir(dir_path):
            raise NotADirectoryError(f"路径不是目录: {dir_path}")
        
        recursive = parameters.get("recursive", False)
        include_hidden = parameters.get("include_hidden", False)
        max_depth = parameters.get("max_depth", 10)
        
        files = []
        directories = []
        
        try:
            if recursive:
                for root, dirs, filenames in os.walk(dir_path):
                    # 检查深度
                    depth = root[len(dir_path):].count(os.sep)
                    if depth >= max_depth:
                        dirs[:] = []  # 不再深入
                        continue
                    
                    # 过滤隐藏文件/目录
                    if not include_hidden:
                        dirs[:] = [d for d in dirs if not d.startswith('.')]
                        filenames = [f for f in filenames if not f.startswith('.')]
                    
                    # 添加目录
                    for dirname in dirs:
                        dir_full_path = os.path.join(root, dirname)
                        directories.append(await self._get_file_info(dir_full_path))
                    
                    # 添加文件
                    for filename in filenames:
                        file_full_path = os.path.join(root, filename)
                        files.append(await self._get_file_info(file_full_path))
            else:
                # 非递归列表
                for item in os.listdir(dir_path):
                    if not include_hidden and item.startswith('.'):
                        continue
                    
                    item_path = os.path.join(dir_path, item)
                    file_info = await self._get_file_info(item_path)
                    
                    if file_info.is_directory:
                        directories.append(file_info)
                    else:
                        files.append(file_info)
            
            return {
                "path": dir_path,
                "files": [f.__dict__ for f in files],
                "directories": [d.__dict__ for d in directories],
                "total_files": len(files),
                "total_directories": len(directories)
            }
            
        except Exception as e:
            raise ValueError(f"目录列表失败: {e}")
    
    async def _create_directory(
        self,
        dir_path: str,
        parameters: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        创建目录
        
        Args:
            dir_path: 目录路径
            parameters: 操作参数
        
        Returns:
            Dict[str, Any]: 创建结果
        """
        recursive = parameters.get("recursive", True)
        
        try:
            if recursive:
                os.makedirs(dir_path, exist_ok=True)
            else:
                os.mkdir(dir_path)
            
            return {
                "path": dir_path,
                "created": True
            }
            
        except FileExistsError:
            return {
                "path": dir_path,
                "created": False,
                "message": "目录已存在"
            }
        except Exception as e:
            raise ValueError(f"目录创建失败: {e}")
    
    async def _remove_directory(
        self,
        dir_path: str,
        parameters: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        删除目录
        
        Args:
            dir_path: 目录路径
            parameters: 操作参数
        
        Returns:
            Dict[str, Any]: 删除结果
        """
        if not os.path.exists(dir_path):
            raise FileNotFoundError(f"目录不存在: {dir_path}")
        
        if not os.path.isdir(dir_path):
            raise NotADirectoryError(f"路径不是目录: {dir_path}")
        
        recursive = parameters.get("recursive", False)
        
        try:
            if recursive:
                import shutil
                shutil.rmtree(dir_path)
            else:
                os.rmdir(dir_path)
            
            return {
                "path": dir_path,
                "deleted": True
            }
            
        except OSError as e:
            if e.errno == 39:  # Directory not empty
                raise ValueError("目录不为空，请使用recursive=true")
            else:
                raise ValueError(f"目录删除失败: {e}")
    
    async def _get_file_stat(self, file_path: str) -> Dict[str, Any]:
        """
        获取文件状态信息
        
        Args:
            file_path: 文件路径
        
        Returns:
            Dict[str, Any]: 文件状态
        """
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"文件不存在: {file_path}")
        
        file_info = await self._get_file_info(file_path)
        return file_info.__dict__
    
    async def _check_file_exists(self, file_path: str) -> Dict[str, Any]:
        """
        检查文件是否存在
        
        Args:
            file_path: 文件路径
        
        Returns:
            Dict[str, Any]: 存在性检查结果
        """
        exists = os.path.exists(file_path)
        is_file = os.path.isfile(file_path) if exists else False
        is_directory = os.path.isdir(file_path) if exists else False
        
        return {
            "path": file_path,
            "exists": exists,
            "is_file": is_file,
            "is_directory": is_directory
        }
    
    async def _search_files(
        self,
        search_path: str,
        parameters: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        搜索文件
        
        Args:
            search_path: 搜索路径
            parameters: 搜索参数
        
        Returns:
            Dict[str, Any]: 搜索结果
        """
        pattern = parameters.get("pattern", "*")
        max_depth = parameters.get("max_depth", 10)
        include_hidden = parameters.get("include_hidden", False)
        
        if not os.path.exists(search_path):
            raise FileNotFoundError(f"搜索路径不存在: {search_path}")
        
        import fnmatch
        
        matches = []
        
        try:
            for root, dirs, files in os.walk(search_path):
                # 检查深度
                depth = root[len(search_path):].count(os.sep)
                if depth >= max_depth:
                    dirs[:] = []  # 不再深入
                    continue
                
                # 过滤隐藏文件/目录
                if not include_hidden:
                    dirs[:] = [d for d in dirs if not d.startswith('.')]
                    files = [f for f in files if not f.startswith('.')]
                
                # 搜索匹配的文件
                for filename in files:
                    if fnmatch.fnmatch(filename, pattern):
                        file_path = os.path.join(root, filename)
                        file_info = await self._get_file_info(file_path)
                        matches.append(file_info.__dict__)
                
                # 限制结果数量
                if len(matches) >= self.config.max_files_per_operation:
                    break
            
            return {
                "search_path": search_path,
                "pattern": pattern,
                "matches": matches,
                "total_matches": len(matches),
                "truncated": len(matches) >= self.config.max_files_per_operation
            }
            
        except Exception as e:
            raise ValueError(f"文件搜索失败: {e}")
    
    async def _get_file_info(self, file_path: str) -> FileInfo:
        """
        获取文件信息
        
        Args:
            file_path: 文件路径
        
        Returns:
            FileInfo: 文件信息
        """
        stat = os.stat(file_path)
        
        # 获取MIME类型
        mime_type = None
        if os.path.isfile(file_path):
            mime_type, _ = mimetypes.guess_type(file_path)
        
        # 计算文件校验和（仅对小文件）
        checksum = None
        if os.path.isfile(file_path) and stat.st_size < 1024 * 1024:  # 1MB
            try:
                async with aiofiles.open(file_path, 'rb') as f:
                    content = await f.read()
                    checksum = hashlib.md5(content).hexdigest()
            except Exception:
                pass  # 忽略校验和计算错误
        
        return FileInfo(
            path=file_path,
            name=os.path.basename(file_path),
            size=stat.st_size,
            is_directory=os.path.isdir(file_path),
            created_at=datetime.fromtimestamp(stat.st_ctime),
            modified_at=datetime.fromtimestamp(stat.st_mtime),
            permissions=oct(stat.st_mode)[-3:],
            mime_type=mime_type,
            checksum=checksum
        )
    
    def _is_text_file(self, file_path: str) -> bool:
        """
        判断是否为文本文件
        
        Args:
            file_path: 文件路径
        
        Returns:
            bool: 是否为文本文件
        """
        mime_type, _ = mimetypes.guess_type(file_path)
        
        if mime_type:
            return mime_type.startswith('text/') or mime_type in [
                'application/json',
                'application/xml',
                'application/yaml',
                'application/x-yaml'
            ]
        
        # 根据扩展名判断
        text_extensions = {
            '.txt', '.md', '.json', '.xml', '.yaml', '.yml',
            '.csv', '.log', '.ini', '.cfg', '.conf'
        }
        
        return Path(file_path).suffix.lower() in text_extensions
    
    async def _create_backup(self, file_path: str) -> str:
        """
        创建文件备份
        
        Args:
            file_path: 文件路径
        
        Returns:
            str: 备份文件路径
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_path = f"{file_path}.backup_{timestamp}"
        
        async with aiofiles.open(file_path, 'rb') as src:
            async with aiofiles.open(backup_path, 'wb') as dst:
                while True:
                    chunk = await src.read(8192)
                    if not chunk:
                        break
                    await dst.write(chunk)
        
        return backup_path
    
    async def _validate_specific_parameters(self, parameters: Dict[str, Any]) -> Optional[ToolError]:
        """
        验证文件工具参数
        
        Args:
            parameters: 参数字典
        
        Returns:
            Optional[ToolError]: 验证错误
        """
        try:
            # 检查必需参数
            if "operation" not in parameters:
                return ToolError(
                    code="MISSING_REQUIRED_PARAMETER",
                    message="缺少必需参数: operation"
                )
            
            if "path" not in parameters:
                return ToolError(
                    code="MISSING_REQUIRED_PARAMETER",
                    message="缺少必需参数: path"
                )
            
            # 验证操作类型
            try:
                operation = FileOperation(parameters["operation"])
            except ValueError:
                return ToolError(
                    code="INVALID_OPERATION",
                    message=f"无效的操作类型: {parameters['operation']}"
                )
            
            # 验证特定操作的参数
            if operation in [FileOperation.WRITE, FileOperation.APPEND]:
                if "content" not in parameters:
                    return ToolError(
                        code="MISSING_CONTENT",
                        message=f"{operation.value} 操作需要提供content参数"
                    )
            
            if operation in [FileOperation.COPY, FileOperation.MOVE]:
                if "destination" not in parameters:
                    return ToolError(
                        code="MISSING_DESTINATION",
                        message=f"{operation.value} 操作需要提供destination参数"
                    )
            
            return None
            
        except Exception as e:
            return ToolError(
                code="PARAMETER_VALIDATION_ERROR",
                message=f"参数验证异常: {str(e)}"
            )