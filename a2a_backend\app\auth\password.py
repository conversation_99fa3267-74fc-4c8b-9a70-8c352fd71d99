#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A2A多智能体系统密码处理模块

使用bcrypt进行密码哈希和验证
"""

import bcrypt
import secrets
import string
from typing import Optional
from loguru import logger

from app.core.config import get_settings


class PasswordHandler:
    """
    密码处理器
    
    负责密码的哈希、验证和生成
    """
    
    def __init__(self):
        self.settings = get_settings()
        self.rounds = self.settings.password_bcrypt_rounds
    
    def hash_password(self, password: str) -> str:
        """
        对密码进行哈希处理
        
        Args:
            password: 明文密码
            
        Returns:
            str: 哈希后的密码
            
        Raises:
            ValueError: 密码为空或格式错误时抛出异常
        """
        if not password:
            raise ValueError("密码不能为空")
        
        if len(password) < self.settings.password_min_length:
            raise ValueError(f"密码长度不能少于 {self.settings.password_min_length} 位")
        
        if len(password) > self.settings.password_max_length:
            raise ValueError(f"密码长度不能超过 {self.settings.password_max_length} 位")
        
        try:
            # 将密码编码为字节
            password_bytes = password.encode('utf-8')
            
            # 生成盐值并进行哈希
            salt = bcrypt.gensalt(rounds=self.rounds)
            hashed = bcrypt.hashpw(password_bytes, salt)
            
            # 返回哈希后的密码（字符串格式）
            return hashed.decode('utf-8')
        
        except Exception as e:
            logger.error(f"密码哈希失败: {str(e)}")
            raise ValueError(f"密码哈希失败: {str(e)}")
    
    def verify_password(self, password: str, hashed_password: str) -> bool:
        """
        验证密码是否正确
        
        Args:
            password: 明文密码
            hashed_password: 哈希后的密码
            
        Returns:
            bool: 密码是否正确
        """
        if not password or not hashed_password:
            return False
        
        try:
            # 将密码和哈希值编码为字节
            password_bytes = password.encode('utf-8')
            hashed_bytes = hashed_password.encode('utf-8')
            
            # 验证密码
            return bcrypt.checkpw(password_bytes, hashed_bytes)
        
        except Exception as e:
            logger.error(f"密码验证失败: {str(e)}")
            return False
    
    def generate_random_password(self, length: Optional[int] = None) -> str:
        """
        生成随机密码
        
        Args:
            length: 密码长度（可选，默认使用配置中的最小长度）
            
        Returns:
            str: 随机生成的密码
        """
        if length is None:
            length = max(self.settings.password_min_length, 12)
        
        if length < self.settings.password_min_length:
            length = self.settings.password_min_length
        
        if length > self.settings.password_max_length:
            length = self.settings.password_max_length
        
        # 定义字符集
        lowercase = string.ascii_lowercase
        uppercase = string.ascii_uppercase
        digits = string.digits
        special_chars = "!@#$%^&*()_+-=[]{}|;:,.<>?"
        
        # 确保密码包含各种字符类型
        password_chars = []
        
        # 至少包含一个小写字母
        password_chars.append(secrets.choice(lowercase))
        
        # 至少包含一个大写字母
        password_chars.append(secrets.choice(uppercase))
        
        # 至少包含一个数字
        password_chars.append(secrets.choice(digits))
        
        # 至少包含一个特殊字符
        password_chars.append(secrets.choice(special_chars))
        
        # 填充剩余长度
        all_chars = lowercase + uppercase + digits + special_chars
        for _ in range(length - 4):
            password_chars.append(secrets.choice(all_chars))
        
        # 打乱字符顺序
        secrets.SystemRandom().shuffle(password_chars)
        
        return ''.join(password_chars)
    
    def validate_password_strength(self, password: str) -> dict:
        """
        验证密码强度
        
        Args:
            password: 要验证的密码
            
        Returns:
            dict: 包含验证结果的字典
        """
        result = {
            "is_valid": True,
            "score": 0,
            "errors": [],
            "suggestions": []
        }
        
        if not password:
            result["is_valid"] = False
            result["errors"].append("密码不能为空")
            return result
        
        # 检查长度
        if len(password) < self.settings.password_min_length:
            result["is_valid"] = False
            result["errors"].append(f"密码长度不能少于 {self.settings.password_min_length} 位")
        elif len(password) >= self.settings.password_min_length:
            result["score"] += 1
        
        if len(password) > self.settings.password_max_length:
            result["is_valid"] = False
            result["errors"].append(f"密码长度不能超过 {self.settings.password_max_length} 位")
        
        # 检查字符类型
        has_lowercase = any(c.islower() for c in password)
        has_uppercase = any(c.isupper() for c in password)
        has_digit = any(c.isdigit() for c in password)
        has_special = any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in password)
        
        if has_lowercase:
            result["score"] += 1
        else:
            result["suggestions"].append("建议包含小写字母")
        
        if has_uppercase:
            result["score"] += 1
        else:
            result["suggestions"].append("建议包含大写字母")
        
        if has_digit:
            result["score"] += 1
        else:
            result["suggestions"].append("建议包含数字")
        
        if has_special:
            result["score"] += 1
        else:
            result["suggestions"].append("建议包含特殊字符")
        
        # 检查常见弱密码
        weak_passwords = [
            "123456", "password", "123456789", "12345678", "12345",
            "1234567", "1234567890", "qwerty", "abc123", "111111",
            "123123", "admin", "letmein", "welcome", "monkey"
        ]
        
        if password.lower() in weak_passwords:
            result["is_valid"] = False
            result["errors"].append("密码过于简单，请使用更复杂的密码")
            result["score"] = 0
        
        # 检查重复字符
        if len(set(password)) < len(password) * 0.6:
            result["suggestions"].append("密码中重复字符过多，建议增加字符多样性")
            result["score"] = max(0, result["score"] - 1)
        
        # 检查连续字符
        consecutive_count = 0
        for i in range(len(password) - 1):
            if ord(password[i+1]) == ord(password[i]) + 1:
                consecutive_count += 1
        
        if consecutive_count > 2:
            result["suggestions"].append("避免使用连续字符")
            result["score"] = max(0, result["score"] - 1)
        
        # 设置强度等级
        if result["score"] >= 4:
            result["strength"] = "强"
        elif result["score"] >= 3:
            result["strength"] = "中等"
        elif result["score"] >= 2:
            result["strength"] = "弱"
        else:
            result["strength"] = "很弱"
        
        return result
    
    def needs_rehash(self, hashed_password: str) -> bool:
        """
        检查密码哈希是否需要重新哈希
        
        Args:
            hashed_password: 哈希后的密码
            
        Returns:
            bool: 是否需要重新哈希
        """
        try:
            # 检查哈希轮数是否与当前配置匹配
            hashed_bytes = hashed_password.encode('utf-8')
            
            # 提取当前哈希的轮数
            # bcrypt哈希格式: $2b$rounds$salt+hash
            parts = hashed_password.split('$')
            if len(parts) >= 3:
                current_rounds = int(parts[2])
                return current_rounds != self.rounds
            
            return True  # 如果无法解析，建议重新哈希
        
        except Exception as e:
            logger.error(f"检查密码哈希失败: {str(e)}")
            return True  # 出错时建议重新哈希


class PasswordResetHandler:
    """
    密码重置处理器
    
    负责密码重置令牌的生成和验证
    """
    
    def __init__(self):
        self.settings = get_settings()
    
    def generate_reset_token(self) -> str:
        """
        生成密码重置令牌
        
        Returns:
            str: 重置令牌
        """
        # 生成32字节的随机令牌
        token_bytes = secrets.token_bytes(32)
        return token_bytes.hex()
    
    def generate_verification_code(self, length: int = 6) -> str:
        """
        生成验证码
        
        Args:
            length: 验证码长度
            
        Returns:
            str: 验证码
        """
        digits = string.digits
        return ''.join(secrets.choice(digits) for _ in range(length))
    
    def is_token_format_valid(self, token: str) -> bool:
        """
        验证令牌格式是否有效
        
        Args:
            token: 要验证的令牌
            
        Returns:
            bool: 令牌格式是否有效
        """
        if not token:
            return False
        
        # 检查长度（32字节的十六进制字符串应该是64个字符）
        if len(token) != 64:
            return False
        
        # 检查是否只包含十六进制字符
        try:
            int(token, 16)
            return True
        except ValueError:
            return False


# 全局密码处理器实例
_password_handler: Optional[PasswordHandler] = None
_password_reset_handler: Optional[PasswordResetHandler] = None


def get_password_handler() -> PasswordHandler:
    """
    获取密码处理器实例（单例模式）
    
    Returns:
        PasswordHandler: 密码处理器实例
    """
    global _password_handler
    if _password_handler is None:
        _password_handler = PasswordHandler()
    return _password_handler


def get_password_reset_handler() -> PasswordResetHandler:
    """
    获取密码重置处理器实例（单例模式）
    
    Returns:
        PasswordResetHandler: 密码重置处理器实例
    """
    global _password_reset_handler
    if _password_reset_handler is None:
        _password_reset_handler = PasswordResetHandler()
    return _password_reset_handler