# -*- coding: utf-8 -*-
"""
内存模型

定义内存相关的数据模型
"""

from datetime import datetime
from typing import Optional, Dict, Any
from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean, ForeignKey, JSON
from sqlalchemy.orm import relationship

from .base import BaseModel


class Memory(BaseModel):
    """
    内存模型
    
    存储智能体的内存信息
    """
    
    __tablename__ = "memories"
    
    # 基本信息
    memory_id = Column(String(255), unique=True, nullable=False, index=True, comment="内存ID")
    user_id = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"), nullable=False, comment="用户ID")
    owner_id = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"), nullable=False, comment="拥有者ID")
    
    # 内存属性
    name = Column(String(255), nullable=False, comment="内存名称")
    memory_type = Column(String(50), nullable=False, default="general", comment="内存类型")
    status = Column(String(20), nullable=False, default="active", comment="状态")
    
    # 统计信息
    entry_count = Column(Integer, default=0, comment="条目数量")
    last_access = Column(DateTime, comment="最后访问时间")
    
    # 元数据
    meta_data = Column(JSON, comment="元数据")
    
    # 关联关系
    user = relationship("User", foreign_keys=[user_id], back_populates="memories")
    owner = relationship("User", foreign_keys=[owner_id])
    entries = relationship("MemoryEntry", back_populates="memory", cascade="all, delete-orphan")
    
    @classmethod
    async def get_by_memory_id(cls, memory_id: str) -> Optional["Memory"]:
        """
        根据内存ID获取内存
        
        Args:
            memory_id: 内存ID
            
        Returns:
            Memory: 内存实例
        """
        from app.core.database import get_db
        
        async for db in get_db():
            result = await db.execute(
                cls.__table__.select().where(cls.memory_id == memory_id)
            )
            row = result.fetchone()
            if row:
                return cls(**dict(row._mapping))
            return None
    
    @classmethod
    async def get_by_user_id(
        cls, 
        user_id: int, 
        memory_type: Optional[str] = None,
        limit: int = 100,
        offset: int = 0
    ) -> list["Memory"]:
        """
        根据用户ID获取内存列表
        
        Args:
            user_id: 用户ID
            memory_type: 内存类型过滤
            limit: 限制数量
            offset: 偏移量
            
        Returns:
            list[Memory]: 内存列表
        """
        from app.core.database import get_db
        
        async for db in get_db():
            query = cls.__table__.select().where(cls.user_id == user_id)
            
            if memory_type:
                query = query.where(cls.memory_type == memory_type)
                
            query = query.limit(limit).offset(offset)
            
            result = await db.execute(query)
            rows = result.fetchall()
            return [cls(**dict(row._mapping)) for row in rows]


class MemoryEntry(BaseModel):
    """
    内存条目模型
    
    存储内存中的具体条目
    """
    
    __tablename__ = "memory_entries"
    
    # 基本信息
    memory_id = Column(Integer, ForeignKey("memories.id", ondelete="CASCADE"), nullable=False, comment="内存ID")
    key = Column(String(255), nullable=False, comment="条目键")
    value = Column(Text, comment="条目值")
    
    # 条目属性
    entry_type = Column(String(50), nullable=False, default="text", comment="条目类型")
    priority = Column(Integer, default=0, comment="优先级")
    
    # 时间信息
    expires_at = Column(DateTime, comment="过期时间")
    last_access = Column(DateTime, comment="最后访问时间")
    
    # 元数据
    meta_data = Column(JSON, comment="元数据")
    
    # 关联关系
    memory = relationship("Memory", back_populates="entries")
    
    @classmethod
    async def get_by_memory_id(cls, memory_id: int) -> list["MemoryEntry"]:
        """
        根据内存ID获取条目列表
        
        Args:
            memory_id: 内存ID
            
        Returns:
            list[MemoryEntry]: 条目列表
        """
        from app.core.database import get_db
        
        async for db in get_db():
            result = await db.execute(
                cls.__table__.select().where(cls.memory_id == memory_id)
            )
            rows = result.fetchall()
            return [cls(**dict(row._mapping)) for row in rows]
    
    @classmethod
    async def delete_by_memory_id(cls, memory_id: int) -> None:
        """
        根据内存ID删除所有条目
        
        Args:
            memory_id: 内存ID
        """
        from app.core.database import get_db
        
        async for db in get_db():
            await db.execute(
                cls.__table__.delete().where(cls.memory_id == memory_id)
            )
            await db.commit()
    
    @classmethod
    async def get_expired_entries(cls) -> list["MemoryEntry"]:
        """
        获取过期的条目
        
        Returns:
            list[MemoryEntry]: 过期条目列表
        """
        from app.core.database import get_db
        
        async for db in get_db():
            now = datetime.now()
            result = await db.execute(
                cls.__table__.select().where(
                    cls.expires_at.isnot(None),
                    cls.expires_at <= now
                )
            )
            rows = result.fetchall()
            return [cls(**dict(row._mapping)) for row in rows]