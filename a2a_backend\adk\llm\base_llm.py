#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A2A多智能体系统 基础LLM类

为所有LLM实现提供通用接口和功能
"""

import asyncio
import logging
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any, AsyncIterator, Union
from datetime import datetime
from dataclasses import dataclass

from google.genai.types import Content, GenerateContentResponse

from app.services.llm_service import LLMConfig, LLMService


@dataclass
class StreamingResponse:
    """
    流式响应数据类
    """
    content: str
    is_complete: bool = False
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


class BaseLLM(ABC):
    """
    基础LLM抽象类
    
    定义所有LLM实现的通用接口和功能
    """
    
    def __init__(self, config: LLMConfig, user_id: int):
        """
        初始化基础LLM
        
        Args:
            config: LLM配置
            user_id: 用户ID
        """
        self.config = config
        self.user_id = user_id
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 初始化LLM服务
        self._llm_service = LLMService()
        
        # 统计信息
        self._total_requests = 0
        self._total_tokens = 0
        self._total_cost = 0.0
        self._failed_requests = 0
        
        # 重试配置
        self._max_retries = config.retry_count
        self._retry_delay = config.retry_delay
        
        self.logger.info(f"基础LLM初始化完成，用户: {user_id}, 提供商: {config.provider}")
    
    @abstractmethod
    async def generate_content(
        self,
        messages: List[Content],
        stream: bool = False,
        **kwargs
    ) -> Union[GenerateContentResponse, AsyncIterator[GenerateContentResponse]]:
        """
        生成内容（抽象方法）
        
        Args:
            messages: 消息列表
            stream: 是否流式输出
            **kwargs: 其他参数
        
        Returns:
            Union[GenerateContentResponse, AsyncIterator[GenerateContentResponse]]: 生成的内容
        """
        pass
    
    @abstractmethod
    async def get_model_info(self) -> Dict[str, Any]:
        """
        获取模型信息（抽象方法）
        
        Returns:
            Dict[str, Any]: 模型信息
        """
        pass
    
    @abstractmethod
    async def validate_config(self) -> bool:
        """
        验证配置（抽象方法）
        
        Returns:
            bool: 配置是否有效
        """
        pass
    
    @abstractmethod
    async def cleanup(self) -> None:
        """
        清理资源（抽象方法）
        """
        pass
    
    async def generate_content_with_retry(
        self,
        messages: List[Content],
        stream: bool = False,
        **kwargs
    ) -> Union[GenerateContentResponse, AsyncIterator[GenerateContentResponse]]:
        """
        带重试的内容生成
        
        Args:
            messages: 消息列表
            stream: 是否流式输出
            **kwargs: 其他参数
        
        Returns:
            Union[GenerateContentResponse, AsyncIterator[GenerateContentResponse]]: 生成的内容
        """
        last_exception = None
        
        for attempt in range(self._max_retries + 1):
            try:
                self.logger.debug(f"尝试生成内容，第 {attempt + 1} 次")
                
                result = await self.generate_content(messages, stream, **kwargs)
                
                # 如果是流式响应，包装为重试流
                if stream:
                    return self._wrap_stream_with_retry(result, messages, **kwargs)
                else:
                    return result
                    
            except Exception as e:
                last_exception = e
                self.logger.warning(f"生成内容失败，第 {attempt + 1} 次尝试: {e}")
                
                # 如果不是最后一次尝试，等待后重试
                if attempt < self._max_retries:
                    await asyncio.sleep(self._retry_delay * (2 ** attempt))  # 指数退避
                else:
                    self._failed_requests += 1
                    break
        
        # 所有重试都失败
        self.logger.error(f"生成内容失败，已重试 {self._max_retries} 次: {last_exception}")
        raise last_exception
    
    async def _wrap_stream_with_retry(
        self,
        stream: AsyncIterator[GenerateContentResponse],
        messages: List[Content],
        **kwargs
    ) -> AsyncIterator[GenerateContentResponse]:
        """
        包装流式响应以支持重试
        
        Args:
            stream: 原始流
            messages: 消息列表
            **kwargs: 其他参数
        
        Yields:
            GenerateContentResponse: 响应片段
        """
        try:
            async for chunk in stream:
                yield chunk
        except Exception as e:
            self.logger.warning(f"流式响应中断: {e}")
            # 这里可以实现流式响应的重试逻辑
            # 由于流式响应的特殊性，重试可能需要重新开始
            raise
    
    async def _check_rate_limit(self) -> bool:
        """
        检查速率限制
        
        Returns:
            bool: 是否在限制范围内
        """
        try:
            return await self._llm_service.check_rate_limit(
                self.user_id,
                self.config.provider,
                self.config
            )
        except Exception as e:
            self.logger.error(f"检查速率限制失败: {e}")
            return True  # 出错时允许请求
    
    async def _record_usage(
        self,
        tokens_used: int,
        cost: float,
        success: bool
    ) -> None:
        """
        记录使用情况
        
        Args:
            tokens_used: 使用的token数
            cost: 成本
            success: 是否成功
        """
        try:
            # 更新本地统计
            self._total_requests += 1
            if success:
                self._total_tokens += tokens_used
                self._total_cost += cost
            else:
                self._failed_requests += 1
            
            # 记录到LLM服务
            await self._llm_service.record_llm_usage(
                self.user_id,
                self.config.provider,
                self.config.model_name,
                tokens_used,
                cost,
                success
            )
            
        except Exception as e:
            self.logger.error(f"记录使用情况失败: {e}")
    
    def get_usage_stats(self) -> Dict[str, Any]:
        """
        获取使用统计
        
        Returns:
            Dict[str, Any]: 使用统计
        """
        return {
            "total_requests": self._total_requests,
            "total_tokens": self._total_tokens,
            "total_cost": self._total_cost,
            "failed_requests": self._failed_requests,
            "success_rate": (
                (self._total_requests - self._failed_requests) / self._total_requests
                if self._total_requests > 0 else 0.0
            )
        }
    
    async def health_check(self) -> bool:
        """
        健康检查
        
        Returns:
            bool: 是否健康
        """
        try:
            # 获取健康状态
            health_status = await self._llm_service.get_llm_health_status(
                self.config.provider,
                self.config.model_name
            )
            
            if health_status:
                return health_status.status.value == "healthy"
            
            # 如果没有缓存的健康状态，进行实时检查
            return await self.validate_config()
            
        except Exception as e:
            self.logger.error(f"健康检查失败: {e}")
            return False
    
    def supports_streaming(self) -> bool:
        """
        是否支持流式输出
        
        Returns:
            bool: 是否支持流式输出
        """
        return True  # 默认支持流式输出
    
    def supports_function_calling(self) -> bool:
        """
        是否支持函数调用
        
        Returns:
            bool: 是否支持函数调用
        """
        return False  # 默认不支持函数调用
    
    def supports_vision(self) -> bool:
        """
        是否支持视觉输入
        
        Returns:
            bool: 是否支持视觉输入
        """
        return False  # 默认不支持视觉输入
    
    def get_context_window(self) -> int:
        """
        获取上下文窗口大小
        
        Returns:
            int: 上下文窗口大小
        """
        return self.config.extra_params.get("context_window", 4096)
    
    def get_max_output_tokens(self) -> int:
        """
        获取最大输出token数
        
        Returns:
            int: 最大输出token数
        """
        return self.config.max_tokens
    
    async def estimate_tokens(self, text: str) -> int:
        """
        估算文本的token数量
        
        Args:
            text: 文本
        
        Returns:
            int: 估算的token数量
        """
        try:
            # 简单估算：1个token约等于4个字符（英文）或1.5个字符（中文）
            # 这里使用一个通用的估算方法
            chinese_chars = sum(1 for char in text if '\u4e00' <= char <= '\u9fff')
            other_chars = len(text) - chinese_chars
            
            estimated_tokens = int(chinese_chars / 1.5 + other_chars / 4)
            return max(estimated_tokens, 1)
            
        except Exception as e:
            self.logger.error(f"估算token数量失败: {e}")
            return len(text) // 4  # 回退到简单估算
    
    def format_error_message(self, error: Exception) -> str:
        """
        格式化错误消息
        
        Args:
            error: 异常
        
        Returns:
            str: 格式化的错误消息
        """
        try:
            error_type = type(error).__name__
            error_message = str(error)
            
            # 根据错误类型提供更友好的消息
            if "rate limit" in error_message.lower():
                return "请求频率过高，请稍后再试"
            elif "api key" in error_message.lower():
                return "API密钥无效或已过期"
            elif "quota" in error_message.lower():
                return "API配额已用完"
            elif "timeout" in error_message.lower():
                return "请求超时，请稍后再试"
            else:
                return f"{error_type}: {error_message}"
                
        except Exception:
            return "未知错误"
    
    def __repr__(self) -> str:
        return (
            f"<{self.__class__.__name__}("
            f"provider={self.config.provider}, "
            f"model={self.config.model_name}, "
            f"user_id={self.user_id}"
            f")>"
        )