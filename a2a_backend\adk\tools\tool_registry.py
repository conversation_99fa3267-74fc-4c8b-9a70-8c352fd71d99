#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A2A多智能体系统 工具注册器

管理所有工具，支持用户权限过滤和动态注册
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional, Type, Set, Callable
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime
import json
import inspect
from abc import ABC, abstractmethod

# Google ADK imports
from google.ai.generativelanguage import FunctionDeclaration

from .base_tool import (
    BaseTool, ToolConfig, ToolResult, ToolError, ToolStatus,
    ToolExecutionContext, ToolPermission
)
from .mcp_tool import MCPTool, MCPToolConfig
from .database_tool import DatabaseTool, DatabaseToolConfig
from .file_tool import FileTool, FileToolConfig
from .web_tool import WebTool, WebToolConfig
from .calculation_tool import CalculationTool, CalculationConfig


class ToolCategory(Enum):
    """工具分类枚举"""
    DATABASE = "database"
    FILE_SYSTEM = "file_system"
    NETWORK = "network"
    CALCULATION = "calculation"
    MCP = "mcp"
    CUSTOM = "custom"
    SYSTEM = "system"
    AI = "ai"
    UTILITY = "utility"


class RegistrationStatus(Enum):
    """注册状态枚举"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    DEPRECATED = "deprecated"
    MAINTENANCE = "maintenance"
    ERROR = "error"


@dataclass
class ToolRegistration:
    """工具注册信息"""
    tool_class: Type[BaseTool]
    config: ToolConfig
    category: ToolCategory
    version: str
    description: str
    author: str
    created_at: datetime
    updated_at: datetime
    status: RegistrationStatus = RegistrationStatus.ACTIVE
    dependencies: List[str] = field(default_factory=list)
    permissions: Set[ToolPermission] = field(default_factory=set)
    user_permissions: Dict[str, Set[ToolPermission]] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)
    instance: Optional[BaseTool] = None


@dataclass
class ToolFilter:
    """工具过滤器"""
    categories: Optional[List[ToolCategory]] = None
    permissions: Optional[List[ToolPermission]] = None
    status: Optional[List[RegistrationStatus]] = None
    user_id: Optional[str] = None
    search_query: Optional[str] = None
    include_deprecated: bool = False
    include_inactive: bool = False


@dataclass
class ToolStats:
    """工具统计信息"""
    total_tools: int = 0
    active_tools: int = 0
    inactive_tools: int = 0
    deprecated_tools: int = 0
    categories: Dict[str, int] = field(default_factory=dict)
    most_used_tools: List[tuple] = field(default_factory=list)
    error_rates: Dict[str, float] = field(default_factory=dict)
    average_execution_time: Dict[str, float] = field(default_factory=dict)


class ToolRegistryError(Exception):
    """工具注册器异常"""
    pass


class IToolValidator(ABC):
    """工具验证器接口"""
    
    @abstractmethod
    async def validate_tool(self, tool_class: Type[BaseTool], config: ToolConfig) -> List[str]:
        """
        验证工具
        
        Args:
            tool_class: 工具类
            config: 工具配置
        
        Returns:
            List[str]: 验证错误列表
        """
        pass


class DefaultToolValidator(IToolValidator):
    """默认工具验证器"""
    
    async def validate_tool(self, tool_class: Type[BaseTool], config: ToolConfig) -> List[str]:
        """
        验证工具
        
        Args:
            tool_class: 工具类
            config: 工具配置
        
        Returns:
            List[str]: 验证错误列表
        """
        errors = []
        
        try:
            # 检查是否继承自BaseTool
            if not issubclass(tool_class, BaseTool):
                errors.append("工具类必须继承自BaseTool")
            
            # 检查必需方法
            required_methods = ['execute', 'get_function_declaration']
            for method_name in required_methods:
                if not hasattr(tool_class, method_name):
                    errors.append(f"工具类缺少必需方法: {method_name}")
                elif not callable(getattr(tool_class, method_name)):
                    errors.append(f"工具类方法不可调用: {method_name}")
            
            # 检查配置类型
            if not isinstance(config, ToolConfig):
                errors.append("配置必须是ToolConfig的实例")
            
            # 尝试创建实例
            try:
                instance = tool_class(config)
                if not isinstance(instance, BaseTool):
                    errors.append("工具实例不是BaseTool类型")
            except Exception as e:
                errors.append(f"无法创建工具实例: {str(e)}")
            
        except Exception as e:
            errors.append(f"工具验证异常: {str(e)}")
        
        return errors


class ToolRegistry:
    """工具注册器"""
    
    def __init__(self, validator: Optional[IToolValidator] = None):
        """
        初始化工具注册器
        
        Args:
            validator: 工具验证器
        """
        self.logger = logging.getLogger(self.__class__.__name__)
        self._tools: Dict[str, ToolRegistration] = {}
        self._validator = validator or DefaultToolValidator()
        self._execution_stats: Dict[str, Dict[str, Any]] = {}
        self._permission_cache: Dict[str, Dict[str, Set[ToolPermission]]] = {}
        self._hooks: Dict[str, List[Callable]] = {
            'before_register': [],
            'after_register': [],
            'before_unregister': [],
            'after_unregister': [],
            'before_execute': [],
            'after_execute': []
        }
    
    async def register_tool(
        self,
        name: str,
        tool_class: Type[BaseTool],
        config: ToolConfig,
        category: ToolCategory,
        version: str = "1.0.0",
        description: str = "",
        author: str = "",
        dependencies: Optional[List[str]] = None,
        permissions: Optional[Set[ToolPermission]] = None,
        metadata: Optional[Dict[str, Any]] = None,
        force: bool = False
    ) -> bool:
        """
        注册工具
        
        Args:
            name: 工具名称
            tool_class: 工具类
            config: 工具配置
            category: 工具分类
            version: 版本号
            description: 描述
            author: 作者
            dependencies: 依赖列表
            permissions: 权限集合
            metadata: 元数据
            force: 强制注册（覆盖已存在的工具）
        
        Returns:
            bool: 注册是否成功
        """
        try:
            # 执行注册前钩子
            await self._execute_hooks('before_register', name, tool_class, config)
            
            # 检查工具是否已存在
            if name in self._tools and not force:
                raise ToolRegistryError(f"工具 {name} 已存在，使用 force=True 强制覆盖")
            
            # 验证工具
            validation_errors = await self._validator.validate_tool(tool_class, config)
            if validation_errors:
                raise ToolRegistryError(f"工具验证失败: {', '.join(validation_errors)}")
            
            # 检查依赖
            if dependencies:
                missing_deps = await self._check_dependencies(dependencies)
                if missing_deps:
                    raise ToolRegistryError(f"缺少依赖工具: {', '.join(missing_deps)}")
            
            # 创建工具实例
            try:
                instance = tool_class(config)
                instance.name = name
                instance.description = description or config.description
            except Exception as e:
                raise ToolRegistryError(f"创建工具实例失败: {str(e)}")
            
            # 创建注册信息
            now = datetime.now()
            registration = ToolRegistration(
                tool_class=tool_class,
                config=config,
                category=category,
                version=version,
                description=description,
                author=author,
                created_at=now,
                updated_at=now,
                dependencies=dependencies or [],
                permissions=permissions or set(),
                metadata=metadata or {},
                instance=instance
            )
            
            # 注册工具
            self._tools[name] = registration
            
            # 初始化执行统计
            self._execution_stats[name] = {
                'total_executions': 0,
                'successful_executions': 0,
                'failed_executions': 0,
                'total_execution_time': 0.0,
                'average_execution_time': 0.0,
                'last_execution': None,
                'error_rate': 0.0
            }
            
            self.logger.info(f"工具 {name} 注册成功")
            
            # 执行注册后钩子
            await self._execute_hooks('after_register', name, registration)
            
            return True
            
        except Exception as e:
            self.logger.error(f"工具 {name} 注册失败: {e}")
            return False
    
    async def unregister_tool(self, name: str) -> bool:
        """
        注销工具
        
        Args:
            name: 工具名称
        
        Returns:
            bool: 注销是否成功
        """
        try:
            if name not in self._tools:
                raise ToolRegistryError(f"工具 {name} 不存在")
            
            registration = self._tools[name]
            
            # 执行注销前钩子
            await self._execute_hooks('before_unregister', name, registration)
            
            # 清理工具实例
            if registration.instance and hasattr(registration.instance, 'cleanup'):
                try:
                    await registration.instance.cleanup()
                except Exception as e:
                    self.logger.warning(f"工具 {name} 清理失败: {e}")
            
            # 移除工具
            del self._tools[name]
            
            # 清理统计信息
            if name in self._execution_stats:
                del self._execution_stats[name]
            
            # 清理权限缓存
            for user_cache in self._permission_cache.values():
                if name in user_cache:
                    del user_cache[name]
            
            self.logger.info(f"工具 {name} 注销成功")
            
            # 执行注销后钩子
            await self._execute_hooks('after_unregister', name)
            
            return True
            
        except Exception as e:
            self.logger.error(f"工具 {name} 注销失败: {e}")
            return False
    
    async def get_tool(self, name: str) -> Optional[BaseTool]:
        """
        获取工具实例
        
        Args:
            name: 工具名称
        
        Returns:
            Optional[BaseTool]: 工具实例
        """
        registration = self._tools.get(name)
        if registration and registration.status == RegistrationStatus.ACTIVE:
            return registration.instance
        return None
    
    async def get_tools_for_user(
        self,
        user_id: str,
        tool_filter: Optional[ToolFilter] = None
    ) -> List[BaseTool]:
        """
        获取用户可用的工具列表
        
        Args:
            user_id: 用户ID
            tool_filter: 工具过滤器
        
        Returns:
            List[BaseTool]: 工具列表
        """
        tools = []
        
        for name, registration in self._tools.items():
            # 检查工具状态
            if not self._check_tool_status(registration, tool_filter):
                continue
            
            # 检查用户权限
            if not await self._check_user_permissions(user_id, name, registration):
                continue
            
            # 应用过滤器
            if tool_filter and not self._apply_filter(registration, tool_filter):
                continue
            
            if registration.instance:
                tools.append(registration.instance)
        
        return tools
    
    async def get_tool_declarations_for_user(
        self,
        user_id: str,
        tool_filter: Optional[ToolFilter] = None
    ) -> List[FunctionDeclaration]:
        """
        获取用户可用的工具函数声明
        
        Args:
            user_id: 用户ID
            tool_filter: 工具过滤器
        
        Returns:
            List[FunctionDeclaration]: 函数声明列表
        """
        declarations = []
        tools = await self.get_tools_for_user(user_id, tool_filter)
        
        for tool in tools:
            try:
                declaration = tool.get_function_declaration()
                declarations.append(declaration)
            except Exception as e:
                self.logger.warning(f"获取工具 {tool.name} 函数声明失败: {e}")
        
        return declarations
    
    async def execute_tool(
        self,
        name: str,
        context: ToolExecutionContext,
        **kwargs
    ) -> ToolResult:
        """
        执行工具
        
        Args:
            name: 工具名称
            context: 执行上下文
            **kwargs: 额外参数
        
        Returns:
            ToolResult: 执行结果
        """
        start_time = datetime.now()
        
        try:
            # 获取工具
            tool = await self.get_tool(name)
            if not tool:
                return ToolResult(
                    tool_name=name,
                    execution_id=context.execution_id,
                    status=ToolStatus.FAILED,
                    error=ToolError(
                        code="TOOL_NOT_FOUND",
                        message=f"工具 {name} 不存在或不可用"
                    ),
                    user_id=context.user_id
                )
            
            # 检查用户权限
            registration = self._tools[name]
            if not await self._check_user_permissions(context.user_id, name, registration):
                return ToolResult(
                    tool_name=name,
                    execution_id=context.execution_id,
                    status=ToolStatus.FAILED,
                    error=ToolError(
                        code="PERMISSION_DENIED",
                        message=f"用户 {context.user_id} 无权限执行工具 {name}"
                    ),
                    user_id=context.user_id
                )
            
            # 执行前钩子
            await self._execute_hooks('before_execute', name, context)
            
            # 执行工具
            result = await tool.execute(context, **kwargs)
            
            # 更新执行统计
            await self._update_execution_stats(name, result, start_time)
            
            # 执行后钩子
            await self._execute_hooks('after_execute', name, context, result)
            
            return result
            
        except Exception as e:
            self.logger.error(f"工具 {name} 执行异常: {e}")
            
            # 更新错误统计
            await self._update_error_stats(name, start_time)
            
            return ToolResult(
                tool_name=name,
                execution_id=context.execution_id,
                status=ToolStatus.FAILED,
                error=ToolError(
                    code="EXECUTION_ERROR",
                    message=f"工具执行异常: {str(e)}"
                ),
                user_id=context.user_id
            )
    
    async def set_user_permissions(
        self,
        user_id: str,
        tool_name: str,
        permissions: Set[ToolPermission]
    ) -> bool:
        """
        设置用户工具权限
        
        Args:
            user_id: 用户ID
            tool_name: 工具名称
            permissions: 权限集合
        
        Returns:
            bool: 设置是否成功
        """
        try:
            if tool_name not in self._tools:
                raise ToolRegistryError(f"工具 {tool_name} 不存在")
            
            registration = self._tools[tool_name]
            registration.user_permissions[user_id] = permissions
            
            # 清理权限缓存
            if user_id in self._permission_cache:
                if tool_name in self._permission_cache[user_id]:
                    del self._permission_cache[user_id][tool_name]
            
            self.logger.info(f"用户 {user_id} 工具 {tool_name} 权限设置成功")
            return True
            
        except Exception as e:
            self.logger.error(f"设置用户权限失败: {e}")
            return False
    
    async def get_user_permissions(
        self,
        user_id: str,
        tool_name: str
    ) -> Set[ToolPermission]:
        """
        获取用户工具权限
        
        Args:
            user_id: 用户ID
            tool_name: 工具名称
        
        Returns:
            Set[ToolPermission]: 权限集合
        """
        # 检查缓存
        if user_id in self._permission_cache:
            if tool_name in self._permission_cache[user_id]:
                return self._permission_cache[user_id][tool_name]
        
        # 获取权限
        permissions = set()
        if tool_name in self._tools:
            registration = self._tools[tool_name]
            
            # 用户特定权限
            if user_id in registration.user_permissions:
                permissions = registration.user_permissions[user_id]
            else:
                # 默认权限
                permissions = registration.permissions
        
        # 缓存权限
        if user_id not in self._permission_cache:
            self._permission_cache[user_id] = {}
        self._permission_cache[user_id][tool_name] = permissions
        
        return permissions
    
    def get_tool_info(self, name: str) -> Optional[Dict[str, Any]]:
        """
        获取工具信息
        
        Args:
            name: 工具名称
        
        Returns:
            Optional[Dict[str, Any]]: 工具信息
        """
        registration = self._tools.get(name)
        if not registration:
            return None
        
        return {
            'name': name,
            'category': registration.category.value,
            'version': registration.version,
            'description': registration.description,
            'author': registration.author,
            'created_at': registration.created_at.isoformat(),
            'updated_at': registration.updated_at.isoformat(),
            'status': registration.status.value,
            'dependencies': registration.dependencies,
            'permissions': [p.value for p in registration.permissions],
            'metadata': registration.metadata,
            'stats': self._execution_stats.get(name, {})
        }
    
    def list_tools(
        self,
        tool_filter: Optional[ToolFilter] = None
    ) -> List[Dict[str, Any]]:
        """
        列出工具
        
        Args:
            tool_filter: 工具过滤器
        
        Returns:
            List[Dict[str, Any]]: 工具信息列表
        """
        tools = []
        
        for name, registration in self._tools.items():
            # 检查工具状态
            if not self._check_tool_status(registration, tool_filter):
                continue
            
            # 应用过滤器
            if tool_filter and not self._apply_filter(registration, tool_filter):
                continue
            
            tool_info = self.get_tool_info(name)
            if tool_info:
                tools.append(tool_info)
        
        return tools
    
    def get_stats(self) -> ToolStats:
        """
        获取工具统计信息
        
        Returns:
            ToolStats: 统计信息
        """
        stats = ToolStats()
        
        # 基础统计
        stats.total_tools = len(self._tools)
        
        for registration in self._tools.values():
            if registration.status == RegistrationStatus.ACTIVE:
                stats.active_tools += 1
            elif registration.status == RegistrationStatus.INACTIVE:
                stats.inactive_tools += 1
            elif registration.status == RegistrationStatus.DEPRECATED:
                stats.deprecated_tools += 1
            
            # 分类统计
            category = registration.category.value
            if category not in stats.categories:
                stats.categories[category] = 0
            stats.categories[category] += 1
        
        # 使用统计
        tool_usage = []
        for name, exec_stats in self._execution_stats.items():
            tool_usage.append((name, exec_stats['total_executions']))
            
            # 错误率
            if exec_stats['total_executions'] > 0:
                error_rate = exec_stats['failed_executions'] / exec_stats['total_executions']
                stats.error_rates[name] = error_rate
            
            # 平均执行时间
            stats.average_execution_time[name] = exec_stats['average_execution_time']
        
        # 最常用工具
        stats.most_used_tools = sorted(tool_usage, key=lambda x: x[1], reverse=True)[:10]
        
        return stats
    
    async def register_builtin_tools(self) -> None:
        """
        注册内置工具
        """
        builtin_tools = [
            {
                'name': 'database_tool',
                'tool_class': DatabaseTool,
                'config': DatabaseToolConfig(),
                'category': ToolCategory.DATABASE,
                'description': '数据库操作工具',
                'permissions': {ToolPermission.READ, ToolPermission.WRITE}
            },
            {
                'name': 'file_tool',
                'tool_class': FileTool,
                'config': FileToolConfig(),
                'category': ToolCategory.FILE_SYSTEM,
                'description': '文件系统操作工具',
                'permissions': {ToolPermission.READ, ToolPermission.WRITE}
            },
            {
                'name': 'web_tool',
                'tool_class': WebTool,
                'config': WebToolConfig(),
                'category': ToolCategory.NETWORK,
                'description': '网络请求工具',
                'permissions': {ToolPermission.READ}
            },
            {
                'name': 'calculation_tool',
                'tool_class': CalculationTool,
                'config': CalculationConfig(),
                'category': ToolCategory.CALCULATION,
                'description': '数学计算工具',
                'permissions': {ToolPermission.EXECUTE}
            }
        ]
        
        for tool_info in builtin_tools:
            success = await self.register_tool(
                name=tool_info['name'],
                tool_class=tool_info['tool_class'],
                config=tool_info['config'],
                category=tool_info['category'],
                description=tool_info['description'],
                author='A2A System',
                permissions=tool_info['permissions']
            )
            
            if success:
                self.logger.info(f"内置工具 {tool_info['name']} 注册成功")
            else:
                self.logger.error(f"内置工具 {tool_info['name']} 注册失败")
    
    def add_hook(self, event: str, callback: Callable) -> None:
        """
        添加钩子函数
        
        Args:
            event: 事件名称
            callback: 回调函数
        """
        if event in self._hooks:
            self._hooks[event].append(callback)
    
    def remove_hook(self, event: str, callback: Callable) -> None:
        """
        移除钩子函数
        
        Args:
            event: 事件名称
            callback: 回调函数
        """
        if event in self._hooks and callback in self._hooks[event]:
            self._hooks[event].remove(callback)
    
    async def _execute_hooks(self, event: str, *args, **kwargs) -> None:
        """
        执行钩子函数
        
        Args:
            event: 事件名称
            *args: 位置参数
            **kwargs: 关键字参数
        """
        if event in self._hooks:
            for callback in self._hooks[event]:
                try:
                    if asyncio.iscoroutinefunction(callback):
                        await callback(*args, **kwargs)
                    else:
                        callback(*args, **kwargs)
                except Exception as e:
                    self.logger.warning(f"钩子函数执行失败: {e}")
    
    async def _check_dependencies(self, dependencies: List[str]) -> List[str]:
        """
        检查依赖工具
        
        Args:
            dependencies: 依赖列表
        
        Returns:
            List[str]: 缺少的依赖
        """
        missing = []
        for dep in dependencies:
            if dep not in self._tools:
                missing.append(dep)
            elif self._tools[dep].status != RegistrationStatus.ACTIVE:
                missing.append(f"{dep} (inactive)")
        return missing
    
    def _check_tool_status(
        self,
        registration: ToolRegistration,
        tool_filter: Optional[ToolFilter]
    ) -> bool:
        """
        检查工具状态
        
        Args:
            registration: 工具注册信息
            tool_filter: 工具过滤器
        
        Returns:
            bool: 是否通过状态检查
        """
        if registration.status == RegistrationStatus.ACTIVE:
            return True
        
        if tool_filter:
            if registration.status == RegistrationStatus.INACTIVE and tool_filter.include_inactive:
                return True
            if registration.status == RegistrationStatus.DEPRECATED and tool_filter.include_deprecated:
                return True
        
        return False
    
    async def _check_user_permissions(
        self,
        user_id: str,
        tool_name: str,
        registration: ToolRegistration
    ) -> bool:
        """
        检查用户权限
        
        Args:
            user_id: 用户ID
            tool_name: 工具名称
            registration: 工具注册信息
        
        Returns:
            bool: 是否有权限
        """
        user_permissions = await self.get_user_permissions(user_id, tool_name)
        
        # 如果没有设置权限要求，允许所有用户使用
        if not registration.permissions:
            return True
        
        # 检查用户是否有任何必需权限
        return bool(user_permissions.intersection(registration.permissions))
    
    def _apply_filter(
        self,
        registration: ToolRegistration,
        tool_filter: ToolFilter
    ) -> bool:
        """
        应用工具过滤器
        
        Args:
            registration: 工具注册信息
            tool_filter: 工具过滤器
        
        Returns:
            bool: 是否通过过滤
        """
        # 分类过滤
        if tool_filter.categories and registration.category not in tool_filter.categories:
            return False
        
        # 权限过滤
        if tool_filter.permissions:
            required_perms = set(tool_filter.permissions)
            if not registration.permissions.intersection(required_perms):
                return False
        
        # 状态过滤
        if tool_filter.status and registration.status not in tool_filter.status:
            return False
        
        # 搜索查询过滤
        if tool_filter.search_query:
            query = tool_filter.search_query.lower()
            if (query not in registration.description.lower() and
                query not in registration.metadata.get('tags', [])):
                return False
        
        return True
    
    async def _update_execution_stats(
        self,
        tool_name: str,
        result: ToolResult,
        start_time: datetime
    ) -> None:
        """
        更新执行统计
        
        Args:
            tool_name: 工具名称
            result: 执行结果
            start_time: 开始时间
        """
        if tool_name not in self._execution_stats:
            return
        
        stats = self._execution_stats[tool_name]
        execution_time = (datetime.now() - start_time).total_seconds()
        
        stats['total_executions'] += 1
        stats['total_execution_time'] += execution_time
        stats['last_execution'] = datetime.now()
        
        if result.status == ToolStatus.SUCCESS:
            stats['successful_executions'] += 1
        else:
            stats['failed_executions'] += 1
        
        # 更新平均执行时间
        stats['average_execution_time'] = stats['total_execution_time'] / stats['total_executions']
        
        # 更新错误率
        if stats['total_executions'] > 0:
            stats['error_rate'] = stats['failed_executions'] / stats['total_executions']
    
    async def _update_error_stats(self, tool_name: str, start_time: datetime) -> None:
        """
        更新错误统计
        
        Args:
            tool_name: 工具名称
            start_time: 开始时间
        """
        if tool_name not in self._execution_stats:
            return
        
        stats = self._execution_stats[tool_name]
        execution_time = (datetime.now() - start_time).total_seconds()
        
        stats['total_executions'] += 1
        stats['failed_executions'] += 1
        stats['total_execution_time'] += execution_time
        stats['last_execution'] = datetime.now()
        
        # 更新平均执行时间
        stats['average_execution_time'] = stats['total_execution_time'] / stats['total_executions']
        
        # 更新错误率
        stats['error_rate'] = stats['failed_executions'] / stats['total_executions']
    
    async def cleanup(self) -> None:
        """
        清理资源
        """
        for name, registration in self._tools.items():
            if registration.instance and hasattr(registration.instance, 'cleanup'):
                try:
                    await registration.instance.cleanup()
                except Exception as e:
                    self.logger.warning(f"工具 {name} 清理失败: {e}")
        
        self._tools.clear()
        self._execution_stats.clear()
        self._permission_cache.clear()


# 全局工具注册器实例
_global_registry: Optional[ToolRegistry] = None


def get_global_registry() -> ToolRegistry:
    """
    获取全局工具注册器实例
    
    Returns:
        ToolRegistry: 全局注册器实例
    """
    global _global_registry
    if _global_registry is None:
        _global_registry = ToolRegistry()
    return _global_registry


def set_global_registry(registry: ToolRegistry) -> None:
    """
    设置全局工具注册器实例
    
    Args:
        registry: 注册器实例
    """
    global _global_registry
    _global_registry = registry