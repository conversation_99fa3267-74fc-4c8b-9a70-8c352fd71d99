#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A2A多智能体系统工件相关Pydantic模式

定义工件相关的请求和响应模式
"""

from typing import Dict, List, Optional, Any
from datetime import datetime
from pydantic import Field, validator
from enum import Enum

from .base import BaseSchema


class ArtifactType(str, Enum):
    """工件类型枚举"""
    TEXT = "text"
    CODE = "code"
    IMAGE = "image"
    DOCUMENT = "document"
    DATA = "data"
    MODEL = "model"
    CONFIG = "config"
    REPORT = "report"
    OTHER = "other"


class ArtifactStatus(str, Enum):
    """工件状态枚举"""
    DRAFT = "draft"
    ACTIVE = "active"
    ARCHIVED = "archived"
    DELETED = "deleted"
    PROCESSING = "processing"
    ERROR = "error"


class SharePermission(str, Enum):
    """分享权限枚举"""
    VIEW = "view"
    DOWNLOAD = "download"
    EDIT = "edit"
    FULL = "full"


class StorageType(str, Enum):
    """存储类型枚举"""
    LOCAL = "local"
    CLOUD = "cloud"
    DATABASE = "database"
    EXTERNAL = "external"


class ArtifactCreate(BaseSchema):
    """
    工件创建请求模式
    """
    
    name: str = Field(
        min_length=1,
        max_length=200,
        description="工件名称"
    )
    
    description: Optional[str] = Field(
        default=None,
        max_length=1000,
        description="工件描述"
    )
    
    type: ArtifactType = Field(
        description="工件类型"
    )
    
    content: Optional[str] = Field(
        default=None,
        description="工件内容（文本类型）"
    )
    
    file_name: Optional[str] = Field(
        default=None,
        max_length=255,
        description="文件名"
    )
    
    file_extension: Optional[str] = Field(
        default=None,
        max_length=10,
        description="文件扩展名"
    )
    
    mime_type: Optional[str] = Field(
        default=None,
        max_length=100,
        description="MIME类型"
    )
    
    storage_type: StorageType = Field(
        default=StorageType.DATABASE,
        description="存储类型"
    )
    
    storage_path: Optional[str] = Field(
        default=None,
        max_length=500,
        description="存储路径"
    )
    
    metadata: Optional[Dict[str, Any]] = Field(
        default_factory=dict,
        description="元数据"
    )
    
    tags: List[str] = Field(
        default_factory=list,
        description="标签列表"
    )
    
    is_public: bool = Field(
        default=False,
        description="是否公开"
    )
    
    expires_at: Optional[datetime] = Field(
        default=None,
        description="过期时间"
    )
    
    session_id: Optional[str] = Field(
        default=None,
        description="关联会话ID"
    )
    
    message_id: Optional[str] = Field(
        default=None,
        description="关联消息ID"
    )
    
    agent_id: Optional[str] = Field(
        default=None,
        description="关联智能体ID"
    )


class ArtifactUpdate(BaseSchema):
    """
    工件更新请求模式
    """
    
    name: Optional[str] = Field(
        default=None,
        min_length=1,
        max_length=200,
        description="工件名称"
    )
    
    description: Optional[str] = Field(
        default=None,
        max_length=1000,
        description="工件描述"
    )
    
    content: Optional[str] = Field(
        default=None,
        description="工件内容（文本类型）"
    )
    
    status: Optional[ArtifactStatus] = Field(
        default=None,
        description="工件状态"
    )
    
    metadata: Optional[Dict[str, Any]] = Field(
        default=None,
        description="元数据"
    )
    
    tags: Optional[List[str]] = Field(
        default=None,
        description="标签列表"
    )
    
    is_public: Optional[bool] = Field(
        default=None,
        description="是否公开"
    )
    
    expires_at: Optional[datetime] = Field(
        default=None,
        description="过期时间"
    )


class ArtifactResponse(BaseSchema):
    """
    工件响应模式
    """
    
    id: str = Field(
        description="工件ID"
    )
    
    artifact_id: str = Field(
        description="工件标识"
    )
    
    name: str = Field(
        description="工件名称"
    )
    
    description: Optional[str] = Field(
        description="工件描述"
    )
    
    type: ArtifactType = Field(
        description="工件类型"
    )
    
    content: Optional[str] = Field(
        description="工件内容（文本类型）"
    )
    
    file_name: Optional[str] = Field(
        description="文件名"
    )
    
    file_extension: Optional[str] = Field(
        description="文件扩展名"
    )
    
    file_size: Optional[int] = Field(
        description="文件大小（字节）"
    )
    
    file_hash: Optional[str] = Field(
        description="文件哈希"
    )
    
    mime_type: Optional[str] = Field(
        description="MIME类型"
    )
    
    storage_type: StorageType = Field(
        description="存储类型"
    )
    
    storage_path: Optional[str] = Field(
        description="存储路径"
    )
    
    storage_info: Dict[str, Any] = Field(
        description="存储信息"
    )
    
    version: int = Field(
        description="版本号"
    )
    
    status: ArtifactStatus = Field(
        description="工件状态"
    )
    
    metadata: Dict[str, Any] = Field(
        description="元数据"
    )
    
    tags: List[str] = Field(
        description="标签列表"
    )
    
    is_public: bool = Field(
        description="是否公开"
    )
    
    user_id: str = Field(
        description="用户ID"
    )
    
    agent_id: Optional[str] = Field(
        description="智能体ID"
    )
    
    session_id: Optional[str] = Field(
        description="会话ID"
    )
    
    message_id: Optional[str] = Field(
        description="消息ID"
    )
    
    view_count: int = Field(
        description="查看次数"
    )
    
    download_count: int = Field(
        description="下载次数"
    )
    
    expires_at: Optional[datetime] = Field(
        description="过期时间"
    )
    
    created_at: datetime = Field(
        description="创建时间"
    )
    
    updated_at: datetime = Field(
        description="更新时间"
    )


class ArtifactShareCreate(BaseSchema):
    """
    工件分享创建请求模式
    """
    
    permission: SharePermission = Field(
        description="分享权限"
    )
    
    expires_at: Optional[datetime] = Field(
        default=None,
        description="过期时间"
    )
    
    max_views: Optional[int] = Field(
        default=None,
        ge=1,
        description="最大查看次数"
    )
    
    max_downloads: Optional[int] = Field(
        default=None,
        ge=1,
        description="最大下载次数"
    )
    
    password: Optional[str] = Field(
        default=None,
        min_length=4,
        max_length=50,
        description="访问密码"
    )
    
    allowed_ips: List[str] = Field(
        default_factory=list,
        description="允许的IP地址列表"
    )
    
    allowed_domains: List[str] = Field(
        default_factory=list,
        description="允许的域名列表"
    )
    
    require_login: bool = Field(
        default=False,
        description="是否需要登录"
    )
    
    description: Optional[str] = Field(
        default=None,
        max_length=500,
        description="分享描述"
    )


class ArtifactShareResponse(BaseSchema):
    """
    工件分享响应模式
    """
    
    id: str = Field(
        description="分享ID"
    )
    
    artifact_id: str = Field(
        description="工件ID"
    )
    
    share_token: str = Field(
        description="分享令牌"
    )
    
    share_url: str = Field(
        description="分享URL"
    )
    
    permission: SharePermission = Field(
        description="分享权限"
    )
    
    expires_at: Optional[datetime] = Field(
        description="过期时间"
    )
    
    max_views: Optional[int] = Field(
        description="最大查看次数"
    )
    
    max_downloads: Optional[int] = Field(
        description="最大下载次数"
    )
    
    current_views: int = Field(
        description="当前查看次数"
    )
    
    current_downloads: int = Field(
        description="当前下载次数"
    )
    
    has_password: bool = Field(
        description="是否设置密码"
    )
    
    allowed_ips: List[str] = Field(
        description="允许的IP地址列表"
    )
    
    allowed_domains: List[str] = Field(
        description="允许的域名列表"
    )
    
    require_login: bool = Field(
        description="是否需要登录"
    )
    
    description: Optional[str] = Field(
        description="分享描述"
    )
    
    is_active: bool = Field(
        description="是否激活"
    )
    
    created_by: str = Field(
        description="创建者ID"
    )
    
    created_at: datetime = Field(
        description="创建时间"
    )
    
    updated_at: datetime = Field(
        description="更新时间"
    )


class ArtifactVersionResponse(BaseSchema):
    """
    工件版本响应模式
    """
    
    id: str = Field(
        description="版本ID"
    )
    
    artifact_id: str = Field(
        description="工件ID"
    )
    
    version_number: int = Field(
        description="版本号"
    )
    
    changelog: Optional[str] = Field(
        description="变更日志"
    )
    
    content_snapshot: Optional[str] = Field(
        description="内容快照"
    )
    
    file_hash: Optional[str] = Field(
        description="文件哈希"
    )
    
    file_size: Optional[int] = Field(
        description="文件大小（字节）"
    )
    
    metadata_snapshot: Dict[str, Any] = Field(
        description="元数据快照"
    )
    
    changed_by: str = Field(
        description="变更者ID"
    )
    
    created_at: datetime = Field(
        description="创建时间"
    )


class ArtifactStatsResponse(BaseSchema):
    """
    工件统计信息响应模式
    """
    
    total_artifacts: int = Field(
        description="总工件数"
    )
    
    artifacts_by_type: Dict[str, int] = Field(
        description="按类型分组的工件数"
    )
    
    artifacts_by_status: Dict[str, int] = Field(
        description="按状态分组的工件数"
    )
    
    total_size: int = Field(
        description="总大小（字节）"
    )
    
    total_views: int = Field(
        description="总查看次数"
    )
    
    total_downloads: int = Field(
        description="总下载次数"
    )
    
    total_shares: int = Field(
        description="总分享数"
    )
    
    active_shares: int = Field(
        description="活跃分享数"
    )
    
    popular_artifacts: List[Dict[str, Any]] = Field(
        description="热门工件列表"
    )
    
    recent_artifacts: List[Dict[str, Any]] = Field(
        description="最近工件列表"
    )


class ArtifactSearchRequest(BaseSchema):
    """
    工件搜索请求模式
    """
    
    query: Optional[str] = Field(
        default=None,
        max_length=200,
        description="搜索查询"
    )
    
    type: Optional[ArtifactType] = Field(
        default=None,
        description="工件类型过滤"
    )
    
    status: Optional[ArtifactStatus] = Field(
        default=None,
        description="状态过滤"
    )
    
    tags: List[str] = Field(
        default_factory=list,
        description="标签过滤"
    )
    
    is_public: Optional[bool] = Field(
        default=None,
        description="是否公开过滤"
    )
    
    creator_id: Optional[str] = Field(
        default=None,
        description="创建者过滤"
    )
    
    agent_id: Optional[str] = Field(
        default=None,
        description="智能体过滤"
    )
    
    session_id: Optional[str] = Field(
        default=None,
        description="会话过滤"
    )
    
    date_from: Optional[datetime] = Field(
        default=None,
        description="开始日期"
    )
    
    date_to: Optional[datetime] = Field(
        default=None,
        description="结束日期"
    )
    
    sort_by: str = Field(
        default="created_at",
        description="排序字段"
    )
    
    sort_order: str = Field(
        default="desc",
        description="排序方向（asc/desc）"
    )


class ArtifactAccessRequest(BaseSchema):
    """
    工件访问请求模式
    """
    
    share_token: Optional[str] = Field(
        default=None,
        description="分享令牌"
    )
    
    password: Optional[str] = Field(
        default=None,
        description="访问密码"
    )
    
    action: str = Field(
        description="访问动作（view/download）"
    )


class ArtifactBatchRequest(BaseSchema):
    """
    工件批量操作请求模式
    """
    
    artifact_ids: List[str] = Field(
        min_items=1,
        max_items=100,
        description="工件ID列表"
    )
    
    action: str = Field(
        description="批量操作类型（delete/archive/activate/tag）"
    )
    
    parameters: Optional[Dict[str, Any]] = Field(
        default_factory=dict,
        description="操作参数"
    )