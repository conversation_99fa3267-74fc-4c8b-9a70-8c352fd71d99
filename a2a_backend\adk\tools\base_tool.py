#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A2A多智能体系统 基础工具类

扩展Google ADK BaseTool，集成用户权限验证、执行监控和结果缓存
"""

import asyncio
import logging
import time
import uuid
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Union, Callable, Type
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime, timedelta
import json
import traceback
from contextlib import asynccontextmanager

# Google ADK imports
from google.ai.generativelanguage import Tool as ADKTool
from google.ai.generativelanguage import FunctionDeclaration


class ToolStatus(Enum):
    """工具执行状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    SUCCESS = "success"
    FAILED = "failed"
    TIMEOUT = "timeout"
    CANCELLED = "cancelled"
    PERMISSION_DENIED = "permission_denied"


class ToolPermission(Enum):
    """工具权限枚举"""
    READ = "read"
    WRITE = "write"
    EXECUTE = "execute"
    ADMIN = "admin"
    DELETE = "delete"


@dataclass
class ToolError:
    """工具执行错误信息"""
    code: str
    message: str
    details: Optional[Dict[str, Any]] = None
    timestamp: datetime = field(default_factory=datetime.now)
    traceback: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "code": self.code,
            "message": self.message,
            "details": self.details,
            "timestamp": self.timestamp.isoformat(),
            "traceback": self.traceback
        }


@dataclass
class ToolResult:
    """工具执行结果"""
    tool_name: str
    execution_id: str
    status: ToolStatus
    result: Optional[Any] = None
    error: Optional[ToolError] = None
    start_time: datetime = field(default_factory=datetime.now)
    end_time: Optional[datetime] = None
    execution_time: float = 0.0
    user_id: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    @property
    def is_success(self) -> bool:
        """检查执行是否成功"""
        return self.status == ToolStatus.SUCCESS
    
    @property
    def is_failed(self) -> bool:
        """检查执行是否失败"""
        return self.status in [ToolStatus.FAILED, ToolStatus.TIMEOUT, ToolStatus.PERMISSION_DENIED]
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        result_dict = {
            "tool_name": self.tool_name,
            "execution_id": self.execution_id,
            "status": self.status.value,
            "result": self.result,
            "start_time": self.start_time.isoformat(),
            "end_time": self.end_time.isoformat() if self.end_time else None,
            "execution_time": self.execution_time,
            "user_id": self.user_id,
            "metadata": self.metadata
        }
        
        if self.error:
            result_dict["error"] = self.error.to_dict()
        
        return result_dict


@dataclass
class ToolConfig:
    """工具配置"""
    name: str
    description: str
    version: str = "1.0.0"
    enabled: bool = True
    timeout: float = 30.0
    max_retries: int = 3
    required_permissions: List[ToolPermission] = field(default_factory=list)
    user_specific: bool = False
    cache_enabled: bool = True
    cache_ttl: int = 3600  # 缓存时间（秒）
    rate_limit: Optional[int] = None  # 每分钟最大调用次数
    config_data: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "name": self.name,
            "description": self.description,
            "version": self.version,
            "enabled": self.enabled,
            "timeout": self.timeout,
            "max_retries": self.max_retries,
            "required_permissions": [p.value for p in self.required_permissions],
            "user_specific": self.user_specific,
            "cache_enabled": self.cache_enabled,
            "cache_ttl": self.cache_ttl,
            "rate_limit": self.rate_limit,
            "config_data": self.config_data
        }


@dataclass
class ToolExecutionContext:
    """工具执行上下文"""
    user_id: str
    execution_id: str
    tool_name: str
    parameters: Dict[str, Any]
    permissions: List[ToolPermission]
    config: ToolConfig
    start_time: datetime = field(default_factory=datetime.now)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def has_permission(self, permission: ToolPermission) -> bool:
        """检查是否具有指定权限"""
        return permission in self.permissions or ToolPermission.ADMIN in self.permissions


class BaseTool(ABC):
    """基础工具类，扩展Google ADK BaseTool"""
    
    def __init__(self, config: ToolConfig):
        """
        初始化基础工具
        
        Args:
            config: 工具配置
        """
        self.config = config
        self.logger = logging.getLogger(f"{__name__}.{config.name}")
        self._execution_count = 0
        self._last_execution_time = None
        self._rate_limit_tracker: Dict[str, List[datetime]] = {}
        
        # 初始化ADK工具声明
        self._adk_tool = self._create_adk_tool()
        
        self.logger.info(f"工具 {config.name} 初始化完成")
    
    @property
    def name(self) -> str:
        """获取工具名称"""
        return self.config.name
    
    @property
    def description(self) -> str:
        """获取工具描述"""
        return self.config.description
    
    @property
    def version(self) -> str:
        """获取工具版本"""
        return self.config.version
    
    @property
    def adk_tool(self) -> ADKTool:
        """获取ADK工具声明"""
        return self._adk_tool
    
    @abstractmethod
    def get_function_declaration(self) -> FunctionDeclaration:
        """
        获取工具的函数声明
        
        Returns:
            FunctionDeclaration: ADK函数声明
        """
        pass
    
    @abstractmethod
    async def execute(
        self,
        context: ToolExecutionContext,
        **kwargs
    ) -> ToolResult:
        """
        执行工具逻辑
        
        Args:
            context: 执行上下文
            **kwargs: 额外参数
        
        Returns:
            ToolResult: 执行结果
        """
        pass
    
    async def execute_with_validation(
        self,
        user_id: str,
        parameters: Dict[str, Any],
        user_permissions: List[ToolPermission],
        **kwargs
    ) -> ToolResult:
        """
        带验证的工具执行
        
        Args:
            user_id: 用户ID
            parameters: 执行参数
            user_permissions: 用户权限
            **kwargs: 额外参数
        
        Returns:
            ToolResult: 执行结果
        """
        execution_id = str(uuid.uuid4())
        start_time = datetime.now()
        
        try:
            # 创建执行上下文
            context = ToolExecutionContext(
                user_id=user_id,
                execution_id=execution_id,
                tool_name=self.name,
                parameters=parameters,
                permissions=user_permissions,
                config=self.config,
                start_time=start_time
            )
            
            # 权限验证
            if not self._validate_permissions(context):
                return ToolResult(
                    tool_name=self.name,
                    execution_id=execution_id,
                    status=ToolStatus.PERMISSION_DENIED,
                    error=ToolError(
                        code="PERMISSION_DENIED",
                        message=f"用户 {user_id} 没有执行工具 {self.name} 的权限"
                    ),
                    start_time=start_time,
                    end_time=datetime.now(),
                    user_id=user_id
                )
            
            # 速率限制检查
            if not self._check_rate_limit(user_id):
                return ToolResult(
                    tool_name=self.name,
                    execution_id=execution_id,
                    status=ToolStatus.FAILED,
                    error=ToolError(
                        code="RATE_LIMIT_EXCEEDED",
                        message=f"用户 {user_id} 超过了工具 {self.name} 的调用频率限制"
                    ),
                    start_time=start_time,
                    end_time=datetime.now(),
                    user_id=user_id
                )
            
            # 参数验证
            validation_error = await self._validate_parameters(parameters)
            if validation_error:
                return ToolResult(
                    tool_name=self.name,
                    execution_id=execution_id,
                    status=ToolStatus.FAILED,
                    error=validation_error,
                    start_time=start_time,
                    end_time=datetime.now(),
                    user_id=user_id
                )
            
            # 执行工具
            result = await self._execute_with_timeout(context, **kwargs)
            
            # 更新执行统计
            self._execution_count += 1
            self._last_execution_time = datetime.now()
            
            return result
            
        except asyncio.TimeoutError:
            return ToolResult(
                tool_name=self.name,
                execution_id=execution_id,
                status=ToolStatus.TIMEOUT,
                error=ToolError(
                    code="TIMEOUT",
                    message=f"工具 {self.name} 执行超时"
                ),
                start_time=start_time,
                end_time=datetime.now(),
                user_id=user_id
            )
        except Exception as e:
            self.logger.error(f"工具 {self.name} 执行异常: {e}", exc_info=True)
            
            return ToolResult(
                tool_name=self.name,
                execution_id=execution_id,
                status=ToolStatus.FAILED,
                error=ToolError(
                    code="EXECUTION_ERROR",
                    message=f"工具执行异常: {str(e)}",
                    traceback=traceback.format_exc()
                ),
                start_time=start_time,
                end_time=datetime.now(),
                user_id=user_id
            )
    
    async def validate_config(self) -> List[str]:
        """
        验证工具配置
        
        Returns:
            List[str]: 配置错误列表
        """
        errors = []
        
        if not self.config.name:
            errors.append("工具名称不能为空")
        
        if not self.config.description:
            errors.append("工具描述不能为空")
        
        if self.config.timeout <= 0:
            errors.append("超时时间必须大于0")
        
        if self.config.max_retries < 0:
            errors.append("最大重试次数不能小于0")
        
        if self.config.cache_ttl < 0:
            errors.append("缓存TTL不能小于0")
        
        if self.config.rate_limit is not None and self.config.rate_limit <= 0:
            errors.append("速率限制必须大于0")
        
        return errors
    
    def get_execution_stats(self) -> Dict[str, Any]:
        """
        获取执行统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        return {
            "tool_name": self.name,
            "execution_count": self._execution_count,
            "last_execution_time": self._last_execution_time.isoformat() if self._last_execution_time else None,
            "config": self.config.to_dict()
        }
    
    def _create_adk_tool(self) -> ADKTool:
        """
        创建ADK工具声明
        
        Returns:
            ADKTool: ADK工具对象
        """
        function_declaration = self.get_function_declaration()
        
        return ADKTool(
            function_declarations=[function_declaration]
        )
    
    def _validate_permissions(self, context: ToolExecutionContext) -> bool:
        """
        验证用户权限
        
        Args:
            context: 执行上下文
        
        Returns:
            bool: 是否有权限
        """
        # 检查是否启用
        if not self.config.enabled:
            return False
        
        # 检查必需权限
        for required_permission in self.config.required_permissions:
            if not context.has_permission(required_permission):
                return False
        
        return True
    
    def _check_rate_limit(self, user_id: str) -> bool:
        """
        检查速率限制
        
        Args:
            user_id: 用户ID
        
        Returns:
            bool: 是否在限制范围内
        """
        if self.config.rate_limit is None:
            return True
        
        now = datetime.now()
        minute_ago = now - timedelta(minutes=1)
        
        # 获取用户的调用记录
        if user_id not in self._rate_limit_tracker:
            self._rate_limit_tracker[user_id] = []
        
        user_calls = self._rate_limit_tracker[user_id]
        
        # 清理过期记录
        user_calls[:] = [call_time for call_time in user_calls if call_time > minute_ago]
        
        # 检查是否超过限制
        if len(user_calls) >= self.config.rate_limit:
            return False
        
        # 记录本次调用
        user_calls.append(now)
        
        return True
    
    async def _validate_parameters(self, parameters: Dict[str, Any]) -> Optional[ToolError]:
        """
        验证参数
        
        Args:
            parameters: 参数字典
        
        Returns:
            Optional[ToolError]: 验证错误，如果没有错误则返回None
        """
        try:
            # 基础参数验证
            if not isinstance(parameters, dict):
                return ToolError(
                    code="INVALID_PARAMETERS",
                    message="参数必须是字典类型"
                )
            
            # 子类可以重写此方法进行具体验证
            return await self._validate_specific_parameters(parameters)
            
        except Exception as e:
            return ToolError(
                code="PARAMETER_VALIDATION_ERROR",
                message=f"参数验证异常: {str(e)}"
            )
    
    async def _validate_specific_parameters(self, parameters: Dict[str, Any]) -> Optional[ToolError]:
        """
        验证具体参数（子类重写）
        
        Args:
            parameters: 参数字典
        
        Returns:
            Optional[ToolError]: 验证错误
        """
        return None
    
    async def _execute_with_timeout(self, context: ToolExecutionContext, **kwargs) -> ToolResult:
        """
        带超时的执行
        
        Args:
            context: 执行上下文
            **kwargs: 额外参数
        
        Returns:
            ToolResult: 执行结果
        """
        try:
            # 使用asyncio.wait_for实现超时控制
            result = await asyncio.wait_for(
                self.execute(context, **kwargs),
                timeout=self.config.timeout
            )
            
            # 设置结束时间和执行时间
            end_time = datetime.now()
            result.end_time = end_time
            result.execution_time = (end_time - context.start_time).total_seconds()
            
            return result
            
        except asyncio.TimeoutError:
            raise
        except Exception as e:
            # 重新抛出异常，让上层处理
            raise
    
    @asynccontextmanager
    async def _execution_context(self, context: ToolExecutionContext):
        """
        执行上下文管理器
        
        Args:
            context: 执行上下文
        """
        self.logger.info(
            f"开始执行工具 {self.name}，用户: {context.user_id}，执行ID: {context.execution_id}"
        )
        
        try:
            yield
        finally:
            self.logger.info(
                f"完成执行工具 {self.name}，用户: {context.user_id}，执行ID: {context.execution_id}"
            )
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"Tool({self.name}, v{self.version})"
    
    def __repr__(self) -> str:
        """详细字符串表示"""
        return f"Tool(name='{self.name}', version='{self.version}', enabled={self.config.enabled})"