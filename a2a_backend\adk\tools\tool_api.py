#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A2A多智能体系统 工具API接口

提供RESTful API和WebSocket接口，供外部系统调用工具
"""

import asyncio
import logging
import json
import time
from typing import Dict, List, Any, Optional, Callable, Union, Set
from dataclasses import dataclass, field, asdict
from enum import Enum
from datetime import datetime, timed<PERSON><PERSON>
from pathlib import Path
import uuid

# FastAPI imports
from fastapi import FastAPI, HTTPException, Depends, WebSocket, WebSocketDisconnect
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field
import uvicorn

# Google ADK imports
from google.ai.generativelanguage import FunctionDeclaration, Schema, Type as SchemaType

from .base_tool import (
    BaseTool, ToolConfig, ToolResult, ToolError, <PERSON><PERSON><PERSON><PERSON>us,
    ToolExecutionContext, ToolPermission
)
from .tool_registry import Too<PERSON><PERSON><PERSON><PERSON><PERSON>, ToolFilter, get_global_registry
from .tool_executor import (
    ToolExecutor, ExecutorConfig, ExecutionRequest, ExecutionResult,
    ExecutionMode, ExecutionPriority, ExecutionState, get_global_executor
)


class APIResponseStatus(Enum):
    """API响应状态枚举"""
    SUCCESS = "success"
    ERROR = "error"
    PENDING = "pending"
    PROCESSING = "processing"


# Pydantic模型定义
class ToolExecuteRequest(BaseModel):
    """工具执行请求模型"""
    tool_name: str = Field(..., description="工具名称")
    arguments: Dict[str, Any] = Field(default_factory=dict, description="工具参数")
    mode: str = Field(default="sync", description="执行模式: sync, async, background")
    priority: str = Field(default="normal", description="执行优先级: low, normal, high, urgent")
    timeout: Optional[int] = Field(None, description="超时时间（秒）")
    retry_attempts: int = Field(default=0, description="重试次数")
    retry_delay: float = Field(default=1.0, description="重试延迟（秒）")
    enable_cache: bool = Field(default=True, description="是否启用缓存")
    cache_ttl: Optional[int] = Field(None, description="缓存TTL（秒）")
    session_id: Optional[str] = Field(None, description="会话ID")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="元数据")


class ToolExecuteResponse(BaseModel):
    """工具执行响应模型"""
    status: str = Field(..., description="响应状态")
    request_id: str = Field(..., description="请求ID")
    tool_name: str = Field(..., description="工具名称")
    state: str = Field(..., description="执行状态")
    result: Optional[Dict[str, Any]] = Field(None, description="执行结果")
    error: Optional[str] = Field(None, description="错误信息")
    duration: Optional[float] = Field(None, description="执行时长（秒）")
    from_cache: bool = Field(default=False, description="是否来自缓存")
    attempt_count: int = Field(default=1, description="尝试次数")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    completed_at: Optional[datetime] = Field(None, description="完成时间")


class ToolListResponse(BaseModel):
    """工具列表响应模型"""
    status: str = Field(..., description="响应状态")
    tools: List[Dict[str, Any]] = Field(..., description="工具列表")
    total: int = Field(..., description="工具总数")
    page: int = Field(default=1, description="页码")
    page_size: int = Field(default=50, description="页大小")


class ToolInfoResponse(BaseModel):
    """工具信息响应模型"""
    status: str = Field(..., description="响应状态")
    tool: Dict[str, Any] = Field(..., description="工具信息")


class ExecutionStatusResponse(BaseModel):
    """执行状态响应模型"""
    status: str = Field(..., description="响应状态")
    execution: Dict[str, Any] = Field(..., description="执行信息")


class BatchExecuteRequest(BaseModel):
    """批量执行请求模型"""
    requests: List[ToolExecuteRequest] = Field(..., description="执行请求列表")
    max_concurrent: int = Field(default=10, description="最大并发数")
    fail_fast: bool = Field(default=False, description="是否快速失败")


class BatchExecuteResponse(BaseModel):
    """批量执行响应模型"""
    status: str = Field(..., description="响应状态")
    batch_id: str = Field(..., description="批次ID")
    results: List[ToolExecuteResponse] = Field(..., description="执行结果列表")
    total: int = Field(..., description="总请求数")
    successful: int = Field(..., description="成功数")
    failed: int = Field(..., description="失败数")
    duration: float = Field(..., description="总耗时（秒）")


class StatsResponse(BaseModel):
    """统计信息响应模型"""
    status: str = Field(..., description="响应状态")
    stats: Dict[str, Any] = Field(..., description="统计信息")


class WebSocketMessage(BaseModel):
    """WebSocket消息模型"""
    type: str = Field(..., description="消息类型")
    data: Dict[str, Any] = Field(..., description="消息数据")
    timestamp: datetime = Field(default_factory=datetime.now, description="时间戳")
    message_id: str = Field(default_factory=lambda: str(uuid.uuid4()), description="消息ID")


@dataclass
class APIConfig:
    """API配置"""
    # 服务器配置
    host: str = "0.0.0.0"
    port: int = 8000
    title: str = "A2A工具API"
    description: str = "A2A多智能体系统工具API接口"
    version: str = "1.0.0"
    
    # 安全配置
    enable_auth: bool = True
    auth_tokens: Set[str] = field(default_factory=set)
    allowed_origins: List[str] = field(default_factory=lambda: ["*"])
    
    # API配置
    api_prefix: str = "/api/v1"
    enable_docs: bool = True
    enable_websocket: bool = True
    
    # 限流配置
    enable_rate_limiting: bool = True
    rate_limit_per_minute: int = 100
    
    # 日志配置
    log_level: str = "INFO"
    log_requests: bool = True
    log_responses: bool = False
    
    # 工具配置
    tool_registry: Optional[ToolRegistry] = None
    tool_executor: Optional[ToolExecutor] = None


class ToolAPIError(Exception):
    """工具API异常"""
    pass


class ToolAPI:
    """工具API服务
    
    提供RESTful API和WebSocket接口，供外部系统调用工具
    """
    
    def __init__(self, config: APIConfig):
        """
        初始化工具API
        
        Args:
            config: API配置
        """
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
        self.logger.setLevel(getattr(logging, config.log_level.upper()))
        
        # 工具组件
        self.registry = config.tool_registry or get_global_registry()
        self.executor = config.tool_executor or get_global_executor()
        
        if not self.registry:
            raise ToolAPIError("工具注册器未配置")
        if not self.executor:
            raise ToolAPIError("工具执行器未配置")
        
        # FastAPI应用
        self.app = FastAPI(
            title=config.title,
            description=config.description,
            version=config.version,
            docs_url="/docs" if config.enable_docs else None,
            redoc_url="/redoc" if config.enable_docs else None
        )
        
        # 安全组件
        self.security = HTTPBearer() if config.enable_auth else None
        
        # WebSocket连接管理
        self.websocket_connections: Dict[str, WebSocket] = {}
        
        # 限流记录
        self.rate_limits: Dict[str, List[datetime]] = {}
        
        # 批量执行记录
        self.batch_executions: Dict[str, Dict[str, Any]] = {}
        
        # 配置中间件
        self._setup_middleware()
        
        # 配置路由
        self._setup_routes()
    
    def _setup_middleware(self) -> None:
        """
        配置中间件
        """
        # CORS中间件
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=self.config.allowed_origins,
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"]
        )
        
        # 请求日志中间件
        if self.config.log_requests:
            @self.app.middleware("http")
            async def log_requests(request, call_next):
                start_time = time.time()
                response = await call_next(request)
                process_time = time.time() - start_time
                
                self.logger.info(
                    f"{request.method} {request.url.path} "
                    f"状态={response.status_code} "
                    f"耗时={process_time:.3f}s"
                )
                
                return response
    
    def _setup_routes(self) -> None:
        """
        配置路由
        """
        prefix = self.config.api_prefix
        
        # 工具相关路由
        self.app.get(f"{prefix}/tools", response_model=ToolListResponse)(self.list_tools)
        self.app.get(f"{prefix}/tools/{{tool_name}}", response_model=ToolInfoResponse)(self.get_tool_info)
        self.app.post(f"{prefix}/tools/{{tool_name}}/execute", response_model=ToolExecuteResponse)(self.execute_tool)
        self.app.post(f"{prefix}/tools/execute/batch", response_model=BatchExecuteResponse)(self.execute_batch)
        
        # 执行状态路由
        self.app.get(f"{prefix}/executions/{{request_id}}", response_model=ExecutionStatusResponse)(self.get_execution_status)
        self.app.delete(f"{prefix}/executions/{{request_id}}")(self.cancel_execution)
        self.app.get(f"{prefix}/executions")(self.list_executions)
        
        # 统计信息路由
        self.app.get(f"{prefix}/stats", response_model=StatsResponse)(self.get_stats)
        self.app.get(f"{prefix}/stats/tools")(self.get_tool_stats)
        self.app.get(f"{prefix}/stats/executions")(self.get_execution_stats)
        
        # 健康检查路由
        self.app.get(f"{prefix}/health")(self.health_check)
        
        # WebSocket路由
        if self.config.enable_websocket:
            self.app.websocket(f"{prefix}/ws")(self.websocket_endpoint)
    
    async def _authenticate(self, credentials: HTTPAuthorizationCredentials = Depends(HTTPBearer())) -> str:
        """
        身份验证
        
        Args:
            credentials: 认证凭据
        
        Returns:
            str: 用户ID
        """
        if not self.config.enable_auth:
            return "anonymous"
        
        if not credentials or credentials.credentials not in self.config.auth_tokens:
            raise HTTPException(status_code=401, detail="无效的认证令牌")
        
        # 这里可以根据token解析用户ID
        return "user_from_token"
    
    async def _check_rate_limit(self, user_id: str) -> None:
        """
        检查限流
        
        Args:
            user_id: 用户ID
        """
        if not self.config.enable_rate_limiting:
            return
        
        now = datetime.now()
        minute_ago = now - timedelta(minutes=1)
        
        # 清理过期记录
        if user_id not in self.rate_limits:
            self.rate_limits[user_id] = []
        
        self.rate_limits[user_id] = [
            ts for ts in self.rate_limits[user_id]
            if ts > minute_ago
        ]
        
        # 检查限制
        if len(self.rate_limits[user_id]) >= self.config.rate_limit_per_minute:
            raise HTTPException(status_code=429, detail="请求频率超限")
        
        self.rate_limits[user_id].append(now)
    
    async def list_tools(
        self,
        page: int = 1,
        page_size: int = 50,
        category: Optional[str] = None,
        user_id: str = Depends(_authenticate)
    ) -> ToolListResponse:
        """
        列出工具
        
        Args:
            page: 页码
            page_size: 页大小
            category: 工具分类
            user_id: 用户ID
        
        Returns:
            ToolListResponse: 工具列表响应
        """
        try:
            await self._check_rate_limit(user_id)
            
            # 构建过滤器
            tool_filter = ToolFilter(
                user_id=user_id,
                category=category
            )
            
            # 获取工具列表
            tools = self.registry.list_tools(tool_filter)
            
            # 分页
            start = (page - 1) * page_size
            end = start + page_size
            page_tools = tools[start:end]
            
            # 转换为字典格式
            tool_dicts = []
            for tool in page_tools:
                tool_dict = {
                    'name': tool.name,
                    'description': tool.description,
                    'category': tool.category.value if tool.category else None,
                    'version': tool.version,
                    'author': tool.author,
                    'tags': tool.tags,
                    'permissions': [p.value for p in tool.permissions],
                    'config': asdict(tool.config) if tool.config else None,
                    'function_declaration': tool.get_function_declaration().to_dict() if hasattr(tool.get_function_declaration(), 'to_dict') else None
                }
                tool_dicts.append(tool_dict)
            
            return ToolListResponse(
                status=APIResponseStatus.SUCCESS.value,
                tools=tool_dicts,
                total=len(tools),
                page=page,
                page_size=page_size
            )
            
        except HTTPException:
            raise
        except Exception as e:
            self.logger.error(f"列出工具失败: {e}")
            raise HTTPException(status_code=500, detail=f"列出工具失败: {str(e)}")
    
    async def get_tool_info(
        self,
        tool_name: str,
        user_id: str = Depends(_authenticate)
    ) -> ToolInfoResponse:
        """
        获取工具信息
        
        Args:
            tool_name: 工具名称
            user_id: 用户ID
        
        Returns:
            ToolInfoResponse: 工具信息响应
        """
        try:
            await self._check_rate_limit(user_id)
            
            # 获取工具
            tool = self.registry.get_tool(tool_name)
            if not tool:
                raise HTTPException(status_code=404, detail=f"工具不存在: {tool_name}")
            
            # 检查权限
            if not self.registry.check_user_permission(user_id, tool_name):
                raise HTTPException(status_code=403, detail=f"无权限访问工具: {tool_name}")
            
            # 获取工具统计
            stats = self.registry.get_tool_stats(tool_name)
            
            # 构建工具信息
            tool_info = {
                'name': tool.name,
                'description': tool.description,
                'category': tool.category.value if tool.category else None,
                'version': tool.version,
                'author': tool.author,
                'tags': tool.tags,
                'permissions': [p.value for p in tool.permissions],
                'config': asdict(tool.config) if tool.config else None,
                'function_declaration': tool.get_function_declaration().to_dict() if hasattr(tool.get_function_declaration(), 'to_dict') else None,
                'stats': asdict(stats) if stats else None
            }
            
            return ToolInfoResponse(
                status=APIResponseStatus.SUCCESS.value,
                tool=tool_info
            )
            
        except HTTPException:
            raise
        except Exception as e:
            self.logger.error(f"获取工具信息失败 {tool_name}: {e}")
            raise HTTPException(status_code=500, detail=f"获取工具信息失败: {str(e)}")
    
    async def execute_tool(
        self,
        tool_name: str,
        request: ToolExecuteRequest,
        user_id: str = Depends(_authenticate)
    ) -> ToolExecuteResponse:
        """
        执行工具
        
        Args:
            tool_name: 工具名称
            request: 执行请求
            user_id: 用户ID
        
        Returns:
            ToolExecuteResponse: 执行响应
        """
        try:
            await self._check_rate_limit(user_id)
            
            # 验证工具名称
            if request.tool_name != tool_name:
                raise HTTPException(status_code=400, detail="工具名称不匹配")
            
            # 检查工具是否存在
            tool = self.registry.get_tool(tool_name)
            if not tool:
                raise HTTPException(status_code=404, detail=f"工具不存在: {tool_name}")
            
            # 检查权限
            if not self.registry.check_user_permission(user_id, tool_name):
                raise HTTPException(status_code=403, detail=f"无权限使用工具: {tool_name}")
            
            # 创建执行请求
            execution_request = ExecutionRequest(
                request_id=str(uuid.uuid4()),
                tool_name=tool_name,
                arguments=request.arguments,
                mode=ExecutionMode(request.mode),
                priority=ExecutionPriority[request.priority.upper()],
                timeout=request.timeout,
                retry_attempts=request.retry_attempts,
                retry_delay=request.retry_delay,
                user_id=user_id,
                session_id=request.session_id,
                enable_cache=request.enable_cache,
                cache_ttl=request.cache_ttl,
                metadata=request.metadata
            )
            
            # 执行工具
            result = await self.executor.execute_request(execution_request)
            
            # 构建响应
            response = ToolExecuteResponse(
                status=APIResponseStatus.SUCCESS.value,
                request_id=result.request_id,
                tool_name=result.tool_name,
                state=result.state.value,
                result=asdict(result.result) if result.result else None,
                error=str(result.error) if result.error else None,
                duration=result.duration,
                from_cache=result.from_cache,
                attempt_count=result.attempt_count,
                completed_at=result.completed_at
            )
            
            # 发送WebSocket通知
            if self.config.enable_websocket:
                await self._broadcast_websocket({
                    'type': 'tool_executed',
                    'data': response.dict()
                })
            
            return response
            
        except HTTPException:
            raise
        except Exception as e:
            self.logger.error(f"执行工具失败 {tool_name}: {e}")
            raise HTTPException(status_code=500, detail=f"执行工具失败: {str(e)}")
    
    async def execute_batch(
        self,
        request: BatchExecuteRequest,
        user_id: str = Depends(_authenticate)
    ) -> BatchExecuteResponse:
        """
        批量执行工具
        
        Args:
            request: 批量执行请求
            user_id: 用户ID
        
        Returns:
            BatchExecuteResponse: 批量执行响应
        """
        try:
            await self._check_rate_limit(user_id)
            
            batch_id = str(uuid.uuid4())
            start_time = time.time()
            
            # 创建执行请求列表
            execution_requests = []
            for req in request.requests:
                execution_request = ExecutionRequest(
                    request_id=str(uuid.uuid4()),
                    tool_name=req.tool_name,
                    arguments=req.arguments,
                    mode=ExecutionMode(req.mode),
                    priority=ExecutionPriority[req.priority.upper()],
                    timeout=req.timeout,
                    retry_attempts=req.retry_attempts,
                    retry_delay=req.retry_delay,
                    user_id=user_id,
                    session_id=req.session_id,
                    enable_cache=req.enable_cache,
                    cache_ttl=req.cache_ttl,
                    metadata=req.metadata
                )
                execution_requests.append(execution_request)
            
            # 批量执行
            results = await self.executor.execute_batch(execution_requests)
            
            # 统计结果
            successful = sum(1 for r in results if r.state == ExecutionState.COMPLETED)
            failed = len(results) - successful
            duration = time.time() - start_time
            
            # 构建响应
            response_results = []
            for result in results:
                response_result = ToolExecuteResponse(
                    status=APIResponseStatus.SUCCESS.value,
                    request_id=result.request_id,
                    tool_name=result.tool_name,
                    state=result.state.value,
                    result=asdict(result.result) if result.result else None,
                    error=str(result.error) if result.error else None,
                    duration=result.duration,
                    from_cache=result.from_cache,
                    attempt_count=result.attempt_count,
                    completed_at=result.completed_at
                )
                response_results.append(response_result)
            
            # 记录批量执行
            self.batch_executions[batch_id] = {
                'batch_id': batch_id,
                'total': len(results),
                'successful': successful,
                'failed': failed,
                'duration': duration,
                'created_at': datetime.now()
            }
            
            response = BatchExecuteResponse(
                status=APIResponseStatus.SUCCESS.value,
                batch_id=batch_id,
                results=response_results,
                total=len(results),
                successful=successful,
                failed=failed,
                duration=duration
            )
            
            # 发送WebSocket通知
            if self.config.enable_websocket:
                await self._broadcast_websocket({
                    'type': 'batch_executed',
                    'data': response.dict()
                })
            
            return response
            
        except HTTPException:
            raise
        except Exception as e:
            self.logger.error(f"批量执行工具失败: {e}")
            raise HTTPException(status_code=500, detail=f"批量执行工具失败: {str(e)}")
    
    async def get_execution_status(
        self,
        request_id: str,
        user_id: str = Depends(_authenticate)
    ) -> ExecutionStatusResponse:
        """
        获取执行状态
        
        Args:
            request_id: 请求ID
            user_id: 用户ID
        
        Returns:
            ExecutionStatusResponse: 执行状态响应
        """
        try:
            await self._check_rate_limit(user_id)
            
            # 获取执行状态
            result = self.executor.get_execution_status(request_id)
            if not result:
                raise HTTPException(status_code=404, detail=f"执行记录不存在: {request_id}")
            
            # 构建执行信息
            execution_info = {
                'request_id': result.request_id,
                'tool_name': result.tool_name,
                'state': result.state.value,
                'result': asdict(result.result) if result.result else None,
                'error': str(result.error) if result.error else None,
                'started_at': result.started_at,
                'completed_at': result.completed_at,
                'duration': result.duration,
                'attempt_count': result.attempt_count,
                'retry_errors': result.retry_errors,
                'from_cache': result.from_cache,
                'stats': result.stats
            }
            
            return ExecutionStatusResponse(
                status=APIResponseStatus.SUCCESS.value,
                execution=execution_info
            )
            
        except HTTPException:
            raise
        except Exception as e:
            self.logger.error(f"获取执行状态失败 {request_id}: {e}")
            raise HTTPException(status_code=500, detail=f"获取执行状态失败: {str(e)}")
    
    async def cancel_execution(
        self,
        request_id: str,
        user_id: str = Depends(_authenticate)
    ) -> Dict[str, Any]:
        """
        取消执行
        
        Args:
            request_id: 请求ID
            user_id: 用户ID
        
        Returns:
            Dict[str, Any]: 取消结果
        """
        try:
            await self._check_rate_limit(user_id)
            
            # 取消执行
            success = await self.executor.cancel_execution(request_id)
            
            if success:
                # 发送WebSocket通知
                if self.config.enable_websocket:
                    await self._broadcast_websocket({
                        'type': 'execution_cancelled',
                        'data': {'request_id': request_id}
                    })
                
                return {
                    'status': APIResponseStatus.SUCCESS.value,
                    'message': f'执行已取消: {request_id}'
                }
            else:
                raise HTTPException(status_code=404, detail=f"执行记录不存在或无法取消: {request_id}")
            
        except HTTPException:
            raise
        except Exception as e:
            self.logger.error(f"取消执行失败 {request_id}: {e}")
            raise HTTPException(status_code=500, detail=f"取消执行失败: {str(e)}")
    
    async def list_executions(
        self,
        page: int = 1,
        page_size: int = 50,
        state: Optional[str] = None,
        user_id: str = Depends(_authenticate)
    ) -> Dict[str, Any]:
        """
        列出执行记录
        
        Args:
            page: 页码
            page_size: 页大小
            state: 执行状态过滤
            user_id: 用户ID
        
        Returns:
            Dict[str, Any]: 执行记录列表
        """
        try:
            await self._check_rate_limit(user_id)
            
            # 获取活跃执行
            executions = self.executor.list_active_executions()
            
            # 状态过滤
            if state:
                executions = [
                    exec for exec in executions
                    if exec.state.value == state
                ]
            
            # 分页
            start = (page - 1) * page_size
            end = start + page_size
            page_executions = executions[start:end]
            
            # 转换为字典格式
            execution_dicts = []
            for execution in page_executions:
                execution_dict = {
                    'request_id': execution.request_id,
                    'tool_name': execution.tool_name,
                    'state': execution.state.value,
                    'started_at': execution.started_at,
                    'completed_at': execution.completed_at,
                    'duration': execution.duration,
                    'attempt_count': execution.attempt_count,
                    'from_cache': execution.from_cache
                }
                execution_dicts.append(execution_dict)
            
            return {
                'status': APIResponseStatus.SUCCESS.value,
                'executions': execution_dicts,
                'total': len(executions),
                'page': page,
                'page_size': page_size
            }
            
        except HTTPException:
            raise
        except Exception as e:
            self.logger.error(f"列出执行记录失败: {e}")
            raise HTTPException(status_code=500, detail=f"列出执行记录失败: {str(e)}")
    
    async def get_stats(
        self,
        user_id: str = Depends(_authenticate)
    ) -> StatsResponse:
        """
        获取统计信息
        
        Args:
            user_id: 用户ID
        
        Returns:
            StatsResponse: 统计信息响应
        """
        try:
            await self._check_rate_limit(user_id)
            
            # 获取执行器统计
            executor_stats = self.executor.get_stats()
            
            # 获取注册器统计
            registry_stats = self.registry.get_stats()
            
            # 合并统计信息
            stats = {
                'executor': executor_stats,
                'registry': asdict(registry_stats),
                'api': {
                    'websocket_connections': len(self.websocket_connections),
                    'batch_executions': len(self.batch_executions)
                }
            }
            
            return StatsResponse(
                status=APIResponseStatus.SUCCESS.value,
                stats=stats
            )
            
        except HTTPException:
            raise
        except Exception as e:
            self.logger.error(f"获取统计信息失败: {e}")
            raise HTTPException(status_code=500, detail=f"获取统计信息失败: {str(e)}")
    
    async def get_tool_stats(
        self,
        tool_name: Optional[str] = None,
        user_id: str = Depends(_authenticate)
    ) -> Dict[str, Any]:
        """
        获取工具统计信息
        
        Args:
            tool_name: 工具名称（可选）
            user_id: 用户ID
        
        Returns:
            Dict[str, Any]: 工具统计信息
        """
        try:
            await self._check_rate_limit(user_id)
            
            if tool_name:
                # 获取单个工具统计
                stats = self.registry.get_tool_stats(tool_name)
                if not stats:
                    raise HTTPException(status_code=404, detail=f"工具统计不存在: {tool_name}")
                
                return {
                    'status': APIResponseStatus.SUCCESS.value,
                    'tool_name': tool_name,
                    'stats': asdict(stats)
                }
            else:
                # 获取所有工具统计
                all_stats = {}
                tools = self.registry.list_tools()
                for tool in tools:
                    stats = self.registry.get_tool_stats(tool.name)
                    if stats:
                        all_stats[tool.name] = asdict(stats)
                
                return {
                    'status': APIResponseStatus.SUCCESS.value,
                    'stats': all_stats
                }
            
        except HTTPException:
            raise
        except Exception as e:
            self.logger.error(f"获取工具统计失败: {e}")
            raise HTTPException(status_code=500, detail=f"获取工具统计失败: {str(e)}")
    
    async def get_execution_stats(
        self,
        user_id: str = Depends(_authenticate)
    ) -> Dict[str, Any]:
        """
        获取执行统计信息
        
        Args:
            user_id: 用户ID
        
        Returns:
            Dict[str, Any]: 执行统计信息
        """
        try:
            await self._check_rate_limit(user_id)
            
            # 获取执行统计
            stats = self.executor.get_stats()
            
            return {
                'status': APIResponseStatus.SUCCESS.value,
                'stats': stats
            }
            
        except HTTPException:
            raise
        except Exception as e:
            self.logger.error(f"获取执行统计失败: {e}")
            raise HTTPException(status_code=500, detail=f"获取执行统计失败: {str(e)}")
    
    async def health_check(self) -> Dict[str, Any]:
        """
        健康检查
        
        Returns:
            Dict[str, Any]: 健康状态
        """
        try:
            # 检查组件状态
            registry_healthy = self.registry is not None
            executor_healthy = self.executor is not None
            
            # 获取基本统计
            stats = self.executor.get_stats() if executor_healthy else {}
            
            return {
                'status': 'healthy' if registry_healthy and executor_healthy else 'unhealthy',
                'timestamp': datetime.now().isoformat(),
                'components': {
                    'registry': 'healthy' if registry_healthy else 'unhealthy',
                    'executor': 'healthy' if executor_healthy else 'unhealthy'
                },
                'stats': {
                    'total_executions': stats.get('total_executions', 0),
                    'active_executions': stats.get('active_executions', 0),
                    'queue_size': stats.get('queue_size', 0)
                }
            }
            
        except Exception as e:
            self.logger.error(f"健康检查失败: {e}")
            return {
                'status': 'unhealthy',
                'timestamp': datetime.now().isoformat(),
                'error': str(e)
            }
    
    async def websocket_endpoint(self, websocket: WebSocket) -> None:
        """
        WebSocket端点
        
        Args:
            websocket: WebSocket连接
        """
        connection_id = str(uuid.uuid4())
        
        try:
            await websocket.accept()
            self.websocket_connections[connection_id] = websocket
            
            self.logger.info(f"WebSocket连接已建立: {connection_id}")
            
            # 发送欢迎消息
            welcome_message = WebSocketMessage(
                type="welcome",
                data={
                    'connection_id': connection_id,
                    'message': '欢迎使用A2A工具API WebSocket接口'
                }
            )
            await websocket.send_text(welcome_message.json())
            
            # 监听消息
            while True:
                try:
                    data = await websocket.receive_text()
                    message = json.loads(data)
                    
                    # 处理消息
                    await self._handle_websocket_message(connection_id, message)
                    
                except WebSocketDisconnect:
                    break
                except json.JSONDecodeError:
                    error_message = WebSocketMessage(
                        type="error",
                        data={'error': '无效的JSON格式'}
                    )
                    await websocket.send_text(error_message.json())
                except Exception as e:
                    error_message = WebSocketMessage(
                        type="error",
                        data={'error': str(e)}
                    )
                    await websocket.send_text(error_message.json())
                    
        except WebSocketDisconnect:
            pass
        except Exception as e:
            self.logger.error(f"WebSocket异常 {connection_id}: {e}")
        finally:
            # 清理连接
            if connection_id in self.websocket_connections:
                del self.websocket_connections[connection_id]
            self.logger.info(f"WebSocket连接已断开: {connection_id}")
    
    async def _handle_websocket_message(self, connection_id: str, message: Dict[str, Any]) -> None:
        """
        处理WebSocket消息
        
        Args:
            connection_id: 连接ID
            message: 消息内容
        """
        websocket = self.websocket_connections.get(connection_id)
        if not websocket:
            return
        
        message_type = message.get('type')
        
        if message_type == 'ping':
            # 心跳响应
            pong_message = WebSocketMessage(
                type="pong",
                data={'timestamp': datetime.now().isoformat()}
            )
            await websocket.send_text(pong_message.json())
            
        elif message_type == 'subscribe':
            # 订阅事件（这里可以实现事件订阅逻辑）
            response_message = WebSocketMessage(
                type="subscribed",
                data={'events': message.get('events', [])}
            )
            await websocket.send_text(response_message.json())
            
        else:
            # 未知消息类型
            error_message = WebSocketMessage(
                type="error",
                data={'error': f'未知消息类型: {message_type}'}
            )
            await websocket.send_text(error_message.json())
    
    async def _broadcast_websocket(self, data: Dict[str, Any]) -> None:
        """
        广播WebSocket消息
        
        Args:
            data: 消息数据
        """
        if not self.websocket_connections:
            return
        
        message = WebSocketMessage(**data)
        message_text = message.json()
        
        # 发送给所有连接
        disconnected = []
        for connection_id, websocket in self.websocket_connections.items():
            try:
                await websocket.send_text(message_text)
            except Exception as e:
                self.logger.warning(f"发送WebSocket消息失败 {connection_id}: {e}")
                disconnected.append(connection_id)
        
        # 清理断开的连接
        for connection_id in disconnected:
            if connection_id in self.websocket_connections:
                del self.websocket_connections[connection_id]
    
    async def start_server(self) -> None:
        """
        启动API服务器
        """
        config = uvicorn.Config(
            app=self.app,
            host=self.config.host,
            port=self.config.port,
            log_level=self.config.log_level.lower()
        )
        server = uvicorn.Server(config)
        
        self.logger.info(f"启动工具API服务器: {self.config.host}:{self.config.port}")
        await server.serve()
    
    def run(self) -> None:
        """
        运行API服务器（同步方式）
        """
        uvicorn.run(
            app=self.app,
            host=self.config.host,
            port=self.config.port,
            log_level=self.config.log_level.lower()
        )