# A2A多智能体系统简化开发计划

## 1. 项目概述

A2A (Agent to Agent) 是一个基于 Google Agent Development Kit (ADK) 和 FastAPI 框架的多智能体协作系统。系统采用极简架构，仅使用数据库作为存储层，所有配置、缓存、工件等数据均存储在数据库中。

### 1.1 核心特性
- 基于 Google ADK 的 Runner-Agent 架构
- 强制流式输出和事件驱动机制
- 多智能体协作和工作流编排
- 数据库统一存储（配置、会话、工件等）
- 工具系统集成（MCP、自定义工具）
- 系统监控和日志记录

### 1.2 架构原则
- **极简存储**：仅使用数据库，无Redis、GCS等外部组件
- **配置集中**：所有配置存储在数据库配置表中
- **工件本地化**：文件和工件存储在数据库BLOB字段或本地文件系统
- **无容器化**：直接部署，无Docker要求
- **专注核心**：无单元测试要求，专注功能实现

## 2. 技术栈

### 2.1 核心框架
- **Python**: 3.12+
- **Google ADK**: 最新版本（核心智能体框架）
- **FastAPI**: 0.104.1（Web API框架）
- **SQLAlchemy**: 2.0.23（ORM）
- **MySQL**: 8.0+（唯一存储组件）

### 2.2 LLM集成
- **Google Gemini**: 通过 google-genai
- **OpenAI**: 通过 openai 客户端
- **Anthropic**: 通过 anthropic 客户端
- **阿里云千问**: 通过 dashscope
- **智谱AI**: 通过 zhipuai

### 2.3 工具和监控
- **MCP工具**: 基于ADK的MCP集成
- **自定义工具**: 基于ADK工具基类
- **日志系统**: loguru
- **监控指标**: prometheus-client
- **流式输出**: sse-starlette

## 3. 系统架构

```
┌─────────────────────────────────────────────────────────────────┐
│                    A2A多智能体系统架构                            │
│                   （极简数据库存储）                              │
└─────────────────────────────────────────────────────────────────┘

┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端界面       │    │   FastAPI       │    │   负载均衡       │
│  Vue 3 + TS     │◄──►│   RESTful API   │◄──►│   Nginx        │
│  Element Plus   │    │   WebSocket     │    │                │
│                │    │   SSE流式输出    │    │                │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────────┐
│                        ADK Runner 层                            │
├─────────────────┬─────────────────┬─────────────────┬───────────┤
│  Runner管理     │   事件处理       │   会话管理       │ 状态管理   │
│                │                │                │          │
│ • Runner创建    │ • 事件分发       │ • 会话生命周期   │ • 状态同步 │
│ • 生命周期管理   │ • 消息路由       │ • 上下文管理     │ • 状态持久化│
│ • 资源管理       │ • 异常处理       │ • 会话恢复       │ • 状态查询 │
└─────────────────┴─────────────────┴─────────────────┴───────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────────┐
│                        ADK Agent 层                             │
├─────────────────┬─────────────────┬─────────────────┬───────────┤
│  LLM Agent      │   Sequential    │   Parallel      │ Loop Agent│
│                │                │                │          │
│ • 流式LLM调用   │ • 顺序执行       │ • 并行执行       │ • 循环控制 │
│ • 上下文管理     │ • 步骤协调       │ • 结果聚合       │ • 条件判断 │
│ • 工具调用       │ • 错误处理       │ • 异常处理       │ • 状态管理 │
│ • 智能体转移     │ • 状态传递       │ • 资源管理       │ • 中断控制 │
└─────────────────┴─────────────────┴─────────────────┴───────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────────┐
│                        业务服务层                                │
├─────────────────┬─────────────────┬─────────────────┬───────────┤
│  智能体服务      │   工作流服务     │   工具服务       │ 监控服务   │
│                │                │                │          │
│ • 智能体管理     │ • 工作流编排     │ • MCP工具       │ • 性能监控 │
│ • 配置管理       │ • 流程控制       │ • 自定义工具     │ • 日志记录 │
│ • 状态跟踪       │ • 异常处理       │ • 工具注册       │ • 指标收集 │
│ • 生命周期       │ • 结果聚合       │ • 权限控制       │ • 告警通知 │
└─────────────────┴─────────────────┴─────────────────┴───────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────────┐
│                        MySQL数据库                              │
├─────────────────┬─────────────────┬─────────────────┬───────────┤
│  核心数据表      │   配置数据表     │   工件数据表     │ 监控数据表 │
│                │                │                │          │
│ • 智能体配置     │ • 系统配置       │ • 文件存储       │ • 性能指标 │
│ • 工作流定义     │ • LLM配置       │ • 工件版本       │ • 日志记录 │
│ • 会话记录       │ • 工具配置       │ • 访问记录       │ • 错误追踪 │
│ • 消息历史       │ • 用户配置       │ • 权限控制       │ • 统计数据 │
│ • 任务执行       │ • 环境变量       │ • 备份恢复       │ • 告警记录 │
└─────────────────┴─────────────────┴─────────────────┴───────────┘
```

## 4. 数据库设计

### 4.1 用户管理表

```sql
-- 用户表
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id VARCHAR(255) UNIQUE NOT NULL COMMENT '用户ID',
    username VARCHAR(100) UNIQUE NOT NULL COMMENT '用户名',
    email VARCHAR(255) UNIQUE NOT NULL COMMENT '邮箱',
    password_hash VARCHAR(255) NOT NULL COMMENT '密码哈希',
    full_name VARCHAR(255) COMMENT '全名',
    avatar_url VARCHAR(500) COMMENT '头像URL',
    phone VARCHAR(20) COMMENT '手机号',
    status ENUM('active', 'inactive', 'suspended', 'deleted') DEFAULT 'active' COMMENT '用户状态',
    role ENUM('admin', 'user', 'guest') DEFAULT 'user' COMMENT '用户角色',
    preferences JSON COMMENT '用户偏好设置',
    metadata JSON COMMENT '用户元数据',
    last_login_at TIMESTAMP NULL COMMENT '最后登录时间',
    email_verified_at TIMESTAMP NULL COMMENT '邮箱验证时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_status (status),
    INDEX idx_role (role)
) COMMENT='用户表';

-- 用户会话令牌表
CREATE TABLE user_tokens (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    token_id VARCHAR(255) UNIQUE NOT NULL COMMENT '令牌ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    token_type ENUM('access', 'refresh', 'reset', 'verify') NOT NULL COMMENT '令牌类型',
    token_hash VARCHAR(255) NOT NULL COMMENT '令牌哈希',
    expires_at TIMESTAMP NOT NULL COMMENT '过期时间',
    used_at TIMESTAMP NULL COMMENT '使用时间',
    revoked_at TIMESTAMP NULL COMMENT '撤销时间',
    metadata JSON COMMENT '令牌元数据',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_token_id (token_id),
    INDEX idx_user_id (user_id),
    INDEX idx_token_type (token_type),
    INDEX idx_expires_at (expires_at)
) COMMENT='用户令牌表';

-- 用户权限表
CREATE TABLE user_permissions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    resource_type VARCHAR(100) NOT NULL COMMENT '资源类型',
    resource_id VARCHAR(255) COMMENT '资源ID',
    permission VARCHAR(100) NOT NULL COMMENT '权限名称',
    granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '授权时间',
    expires_at TIMESTAMP NULL COMMENT '过期时间',
    granted_by BIGINT COMMENT '授权人ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (granted_by) REFERENCES users(id) ON DELETE SET NULL,
    UNIQUE KEY uk_user_resource_permission (user_id, resource_type, resource_id, permission),
    INDEX idx_user_id (user_id),
    INDEX idx_resource (resource_type, resource_id),
    INDEX idx_permission (permission)
) COMMENT='用户权限表';

-- 用户操作日志表
CREATE TABLE user_activity_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT COMMENT '用户ID',
    action VARCHAR(100) NOT NULL COMMENT '操作类型',
    resource_type VARCHAR(100) COMMENT '资源类型',
    resource_id VARCHAR(255) COMMENT '资源ID',
    details JSON COMMENT '操作详情',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    status ENUM('success', 'failed', 'error') DEFAULT 'success' COMMENT '操作状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_action (action),
    INDEX idx_resource (resource_type, resource_id),
    INDEX idx_created_at (created_at)
) COMMENT='用户操作日志表';
```

### 4.2 核心业务表

```sql
-- 智能体表
CREATE TABLE agents (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL COMMENT '智能体名称',
    type ENUM('sequential', 'parallel', 'loop', 'custom') NOT NULL COMMENT '智能体类型',
    description TEXT COMMENT '智能体描述',
    config JSON COMMENT '智能体配置',
    status ENUM('active', 'inactive', 'error') DEFAULT 'active' COMMENT '状态',
    owner_id BIGINT COMMENT '所有者用户ID',
    is_public BOOLEAN DEFAULT FALSE COMMENT '是否公开',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (owner_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_name (name),
    INDEX idx_type (type),
    INDEX idx_status (status),
    INDEX idx_owner_id (owner_id)
) COMMENT='智能体表';

-- 会话表
CREATE TABLE sessions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    session_id VARCHAR(255) UNIQUE NOT NULL COMMENT '会话ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    agent_id BIGINT COMMENT '关联智能体ID',
    status ENUM('active', 'completed', 'error', 'timeout') DEFAULT 'active' COMMENT '会话状态',
    context JSON COMMENT '会话上下文',
    metadata JSON COMMENT '会话元数据',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (agent_id) REFERENCES agents(id) ON DELETE SET NULL,
    INDEX idx_session_id (session_id),
    INDEX idx_user_id (user_id),
    INDEX idx_agent_id (agent_id),
    INDEX idx_status (status)
) COMMENT='会话表';

-- 消息表
CREATE TABLE messages (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    session_id BIGINT NOT NULL COMMENT '会话ID',
    message_id VARCHAR(255) UNIQUE NOT NULL COMMENT '消息ID',
    role ENUM('user', 'assistant', 'system', 'tool') NOT NULL COMMENT '消息角色',
    content TEXT COMMENT '消息内容',
    metadata JSON COMMENT '消息元数据',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (session_id) REFERENCES sessions(id) ON DELETE CASCADE,
    INDEX idx_session_id (session_id),
    INDEX idx_message_id (message_id),
    INDEX idx_role (role),
    INDEX idx_created_at (created_at)
) COMMENT='消息表';

-- 任务表
CREATE TABLE tasks (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    task_id VARCHAR(255) UNIQUE NOT NULL COMMENT '任务ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    session_id BIGINT COMMENT '关联会话ID',
    agent_id BIGINT COMMENT '执行智能体ID',
    name VARCHAR(255) NOT NULL COMMENT '任务名称',
    description TEXT COMMENT '任务描述',
    status ENUM('pending', 'running', 'completed', 'failed', 'cancelled') DEFAULT 'pending' COMMENT '任务状态',
    priority INT DEFAULT 0 COMMENT '任务优先级',
    input_data JSON COMMENT '输入数据',
    output_data JSON COMMENT '输出数据',
    error_info JSON COMMENT '错误信息',
    started_at TIMESTAMP NULL COMMENT '开始时间',
    completed_at TIMESTAMP NULL COMMENT '完成时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (session_id) REFERENCES sessions(id) ON DELETE SET NULL,
    FOREIGN KEY (agent_id) REFERENCES agents(id) ON DELETE SET NULL,
    INDEX idx_task_id (task_id),
    INDEX idx_user_id (user_id),
    INDEX idx_session_id (session_id),
    INDEX idx_agent_id (agent_id),
    INDEX idx_status (status),
    INDEX idx_priority (priority)
) COMMENT='任务表';

-- 工作流表
CREATE TABLE workflows (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    workflow_id VARCHAR(255) UNIQUE NOT NULL COMMENT '工作流ID',
    name VARCHAR(255) NOT NULL COMMENT '工作流名称',
    description TEXT COMMENT '工作流描述',
    definition JSON NOT NULL COMMENT '工作流定义',
    status ENUM('active', 'inactive', 'deprecated') DEFAULT 'active' COMMENT '状态',
    version VARCHAR(50) DEFAULT '1.0.0' COMMENT '版本号',
    owner_id BIGINT COMMENT '所有者用户ID',
    is_public BOOLEAN DEFAULT FALSE COMMENT '是否公开',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (owner_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_workflow_id (workflow_id),
    INDEX idx_name (name),
    INDEX idx_status (status),
    INDEX idx_owner_id (owner_id)
) COMMENT='工作流表';

-- 工作流实例表
CREATE TABLE workflow_instances (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    instance_id VARCHAR(255) UNIQUE NOT NULL COMMENT '实例ID',
    workflow_id BIGINT NOT NULL COMMENT '工作流ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    session_id BIGINT COMMENT '关联会话ID',
    status ENUM('pending', 'running', 'completed', 'failed', 'cancelled') DEFAULT 'pending' COMMENT '执行状态',
    current_step VARCHAR(255) COMMENT '当前步骤',
    context JSON COMMENT '执行上下文',
    result JSON COMMENT '执行结果',
    error_info JSON COMMENT '错误信息',
    started_at TIMESTAMP NULL COMMENT '开始时间',
    completed_at TIMESTAMP NULL COMMENT '完成时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (workflow_id) REFERENCES workflows(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (session_id) REFERENCES sessions(id) ON DELETE SET NULL,
    INDEX idx_instance_id (instance_id),
    INDEX idx_workflow_id (workflow_id),
    INDEX idx_user_id (user_id),
    INDEX idx_session_id (session_id),
    INDEX idx_status (status)
) COMMENT='工作流实例表';

-- 工具表
CREATE TABLE tools (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    tool_id VARCHAR(255) UNIQUE NOT NULL COMMENT '工具ID',
    name VARCHAR(255) NOT NULL COMMENT '工具名称',
    description TEXT COMMENT '工具描述',
    type ENUM('builtin', 'mcp', 'custom') NOT NULL COMMENT '工具类型',
    config JSON COMMENT '工具配置',
    tool_schema JSON COMMENT '工具Schema',
    status ENUM('active', 'inactive', 'error') DEFAULT 'active' COMMENT '状态',
    version VARCHAR(50) DEFAULT '1.0.0' COMMENT '版本号',
    owner_id BIGINT COMMENT '所有者用户ID',
    is_public BOOLEAN DEFAULT TRUE COMMENT '是否公开',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (owner_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_tool_id (tool_id),
    INDEX idx_name (name),
    INDEX idx_type (type),
    INDEX idx_status (status),
    INDEX idx_owner_id (owner_id)
) COMMENT='工具表';

-- 工具执行记录表
CREATE TABLE tool_executions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    execution_id VARCHAR(255) UNIQUE NOT NULL COMMENT '执行ID',
    tool_id BIGINT NOT NULL COMMENT '工具ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    session_id BIGINT COMMENT '会话ID',
    task_id BIGINT COMMENT '任务ID',
    input_data JSON COMMENT '输入数据',
    output_data JSON COMMENT '输出数据',
    status ENUM('pending', 'running', 'completed', 'failed') DEFAULT 'pending' COMMENT '执行状态',
    error_info JSON COMMENT '错误信息',
    duration_ms INT COMMENT '执行时长(毫秒)',
    started_at TIMESTAMP NULL COMMENT '开始时间',
    completed_at TIMESTAMP NULL COMMENT '完成时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (tool_id) REFERENCES tools(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (session_id) REFERENCES sessions(id) ON DELETE SET NULL,
    FOREIGN KEY (task_id) REFERENCES tasks(id) ON DELETE SET NULL,
    INDEX idx_execution_id (execution_id),
    INDEX idx_tool_id (tool_id),
    INDEX idx_user_id (user_id),
    INDEX idx_session_id (session_id),
    INDEX idx_task_id (task_id),
    INDEX idx_status (status)
) COMMENT='工具执行记录表';
```

### 4.3 配置管理表

```sql
-- 系统配置表
CREATE TABLE system_configs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    config_key VARCHAR(255) UNIQUE NOT NULL COMMENT '配置键',
    config_value JSON COMMENT '配置值',
    description TEXT COMMENT '配置描述',
    category VARCHAR(100) COMMENT '配置分类',
    is_encrypted BOOLEAN DEFAULT FALSE COMMENT '是否加密',
    is_public BOOLEAN DEFAULT FALSE COMMENT '是否公开',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_config_key (config_key),
    INDEX idx_category (category)
) COMMENT='系统配置表';

-- LLM配置表
CREATE TABLE llm_configs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    provider VARCHAR(100) NOT NULL COMMENT 'LLM提供商',
    model_name VARCHAR(255) NOT NULL COMMENT '模型名称',
    config JSON NOT NULL COMMENT 'LLM配置',
    api_key_encrypted TEXT COMMENT '加密的API密钥',
    endpoint_url VARCHAR(500) COMMENT '端点URL',
    status ENUM('active', 'inactive', 'error') DEFAULT 'active' COMMENT '状态',
    owner_id BIGINT COMMENT '所有者用户ID',
    is_public BOOLEAN DEFAULT FALSE COMMENT '是否公开',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (owner_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_provider (provider),
    INDEX idx_model_name (model_name),
    INDEX idx_status (status),
    INDEX idx_owner_id (owner_id)
) COMMENT='LLM配置表';

-- 工具配置表
CREATE TABLE tool_configs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    tool_id BIGINT NOT NULL COMMENT '工具ID',
    config_key VARCHAR(255) NOT NULL COMMENT '配置键',
    config_value JSON COMMENT '配置值',
    description TEXT COMMENT '配置描述',
    is_encrypted BOOLEAN DEFAULT FALSE COMMENT '是否加密',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (tool_id) REFERENCES tools(id) ON DELETE CASCADE,
    UNIQUE KEY uk_tool_config (tool_id, config_key),
    INDEX idx_tool_id (tool_id),
    INDEX idx_config_key (config_key)
) COMMENT='工具配置表';

-- 用户配置表
CREATE TABLE user_configs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    config_key VARCHAR(255) NOT NULL COMMENT '配置键',
    config_value JSON COMMENT '配置值',
    description TEXT COMMENT '配置描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY uk_user_config (user_id, config_key),
    INDEX idx_user_id (user_id),
    INDEX idx_config_key (config_key)
) COMMENT='用户配置表';
```

### 4.4 工件存储表

```sql
-- 工件表
CREATE TABLE artifacts (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    artifact_id VARCHAR(255) UNIQUE NOT NULL COMMENT '工件ID',
    name VARCHAR(255) NOT NULL COMMENT '工件名称',
    description TEXT COMMENT '工件描述',
    type VARCHAR(100) NOT NULL COMMENT '工件类型',
    mime_type VARCHAR(255) COMMENT 'MIME类型',
    file_size BIGINT COMMENT '文件大小',
    file_hash VARCHAR(255) COMMENT '文件哈希',
    storage_type ENUM('database', 'filesystem') DEFAULT 'database' COMMENT '存储类型',
    storage_path VARCHAR(1000) COMMENT '存储路径',
    content LONGBLOB COMMENT '文件内容',
    metadata JSON COMMENT '工件元数据',
    owner_id BIGINT COMMENT '所有者用户ID',
    is_public BOOLEAN DEFAULT FALSE COMMENT '是否公开',
    status ENUM('active', 'archived', 'deleted') DEFAULT 'active' COMMENT '状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (owner_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_artifact_id (artifact_id),
    INDEX idx_name (name),
    INDEX idx_type (type),
    INDEX idx_owner_id (owner_id),
    INDEX idx_status (status)
) COMMENT='工件表';

-- 工件版本表
CREATE TABLE artifact_versions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    artifact_id BIGINT NOT NULL COMMENT '工件ID',
    version VARCHAR(50) NOT NULL COMMENT '版本号',
    description TEXT COMMENT '版本描述',
    file_size BIGINT COMMENT '文件大小',
    file_hash VARCHAR(255) COMMENT '文件哈希',
    storage_path VARCHAR(1000) COMMENT '存储路径',
    content LONGBLOB COMMENT '文件内容',
    metadata JSON COMMENT '版本元数据',
    created_by BIGINT COMMENT '创建者用户ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (artifact_id) REFERENCES artifacts(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    UNIQUE KEY uk_artifact_version (artifact_id, version),
    INDEX idx_artifact_id (artifact_id),
    INDEX idx_version (version),
    INDEX idx_created_by (created_by)
) COMMENT='工件版本表';

-- 工件访问记录表
CREATE TABLE artifact_access_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    artifact_id BIGINT NOT NULL COMMENT '工件ID',
    user_id BIGINT COMMENT '用户ID',
    action VARCHAR(100) NOT NULL COMMENT '操作类型',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    metadata JSON COMMENT '访问元数据',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (artifact_id) REFERENCES artifacts(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_artifact_id (artifact_id),
    INDEX idx_user_id (user_id),
    INDEX idx_action (action),
    INDEX idx_created_at (created_at)
) COMMENT='工件访问记录表';
```

### 4.5 监控日志表

```sql
-- 性能指标表
CREATE TABLE performance_metrics (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    metric_name VARCHAR(255) NOT NULL COMMENT '指标名称',
    metric_value DECIMAL(20,6) NOT NULL COMMENT '指标值',
    metric_unit VARCHAR(50) COMMENT '指标单位',
    labels JSON COMMENT '指标标签',
    resource_type VARCHAR(100) COMMENT '资源类型',
    resource_id VARCHAR(255) COMMENT '资源ID',
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '时间戳',
    INDEX idx_metric_name (metric_name),
    INDEX idx_resource (resource_type, resource_id),
    INDEX idx_timestamp (timestamp)
) COMMENT='性能指标表';

-- 系统日志表
CREATE TABLE system_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    level ENUM('DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL') NOT NULL COMMENT '日志级别',
    logger_name VARCHAR(255) COMMENT '日志器名称',
    message TEXT NOT NULL COMMENT '日志消息',
    module VARCHAR(255) COMMENT '模块名称',
    function VARCHAR(255) COMMENT '函数名称',
    line_number INT COMMENT '行号',
    extra_data JSON COMMENT '额外数据',
    user_id BIGINT COMMENT '用户ID',
    session_id VARCHAR(255) COMMENT '会话ID',
    request_id VARCHAR(255) COMMENT '请求ID',
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '时间戳',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_level (level),
    INDEX idx_logger_name (logger_name),
    INDEX idx_module (module),
    INDEX idx_user_id (user_id),
    INDEX idx_session_id (session_id),
    INDEX idx_request_id (request_id),
    INDEX idx_timestamp (timestamp)
) COMMENT='系统日志表';

-- 错误日志表
CREATE TABLE error_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    error_type VARCHAR(255) NOT NULL COMMENT '错误类型',
    error_message TEXT NOT NULL COMMENT '错误消息',
    stack_trace TEXT COMMENT '堆栈跟踪',
    module VARCHAR(255) COMMENT '模块名称',
    function VARCHAR(255) COMMENT '函数名称',
    line_number INT COMMENT '行号',
    context JSON COMMENT '错误上下文',
    user_id BIGINT COMMENT '用户ID',
    session_id VARCHAR(255) COMMENT '会话ID',
    request_id VARCHAR(255) COMMENT '请求ID',
    resolved_at TIMESTAMP NULL COMMENT '解决时间',
    resolved_by BIGINT COMMENT '解决人ID',
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '时间戳',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (resolved_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_error_type (error_type),
    INDEX idx_module (module),
    INDEX idx_user_id (user_id),
    INDEX idx_session_id (session_id),
    INDEX idx_request_id (request_id),
    INDEX idx_timestamp (timestamp)
) COMMENT='错误日志表';

-- 审计日志表
CREATE TABLE audit_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT COMMENT '用户ID',
    action VARCHAR(100) NOT NULL COMMENT '操作类型',
    resource_type VARCHAR(100) NOT NULL COMMENT '资源类型',
    resource_id VARCHAR(255) COMMENT '资源ID',
    old_values JSON COMMENT '旧值',
    new_values JSON COMMENT '新值',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    session_id VARCHAR(255) COMMENT '会话ID',
    request_id VARCHAR(255) COMMENT '请求ID',
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '时间戳',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_action (action),
    INDEX idx_resource (resource_type, resource_id),
    INDEX idx_timestamp (timestamp)
) COMMENT='审计日志表';
```

## 5. 目录结构

```
a2a_backend/
├── app/
│   ├── __init__.py
│   ├── main.py                     # FastAPI应用入口
│   ├── config/
│   │   ├── __init__.py
│   │   ├── settings.py             # 配置管理
│   │   ├── database.py             # 数据库配置
│   │   └── logging.py              # 日志配置
│   ├── auth/
│   │   ├── __init__.py
│   │   ├── authentication.py       # 认证逻辑
│   │   ├── authorization.py         # 授权逻辑
│   │   ├── jwt_handler.py           # JWT处理
│   │   ├── password_handler.py      # 密码处理
│   │   └── permissions.py           # 权限管理
│   ├── adk/
│   │   ├── __init__.py
│   │   ├── runners/                # ADK Runner封装
│   │   ├── agents/                 # ADK Agent扩展
│   │   ├── tools/                  # ADK工具扩展
│   │   ├── services/               # ADK服务扩展
│   │   └── converters/             # 数据转换器
│   ├── core/
│   │   ├── __init__.py
│   │   ├── security.py             # 安全认证
│   │   ├── dependencies.py         # 依赖注入
│   │   ├── middleware.py           # 中间件
│   │   └── exceptions.py           # 异常处理
│   ├── models/
│   │   ├── __init__.py
│   │   ├── base.py                 # 基础模型
│   │   ├── user.py                 # 用户模型
│   │   ├── agent.py                # 智能体模型
│   │   ├── workflow.py             # 工作流模型
│   │   ├── session.py              # 会话模型
│   │   ├── message.py              # 消息模型
│   │   ├── task.py                 # 任务模型
│   │   ├── config.py               # 配置模型
│   │   ├── artifact.py             # 工件模型
│   │   └── monitoring.py           # 监控模型
│   ├── schemas/
│   │   ├── __init__.py
│   │   ├── user.py                 # 用户Schema
│   │   ├── auth.py                 # 认证Schema
│   │   ├── agent.py                # 智能体Schema
│   │   ├── workflow.py             # 工作流Schema
│   │   ├── session.py              # 会话Schema
│   │   ├── message.py              # 消息Schema
│   │   ├── task.py                 # 任务Schema
│   │   ├── config.py               # 配置Schema
│   │   ├── artifact.py             # 工件Schema
│   │   ├── monitoring.py           # 监控Schema
│   │   └── common.py               # 通用Schema
│   ├── api/
│   │   ├── __init__.py
│   │   ├── deps.py                 # API依赖
│   │   └── v1/
│   │       ├── __init__.py
│   │       ├── auth.py             # 认证API
│   │       ├── users.py            # 用户管理API
│   │       ├── agents.py           # 智能体API
│   │       ├── workflows.py        # 工作流API
│   │       ├── sessions.py         # 会话API
│   │       ├── messages.py         # 消息API
│   │       ├── tasks.py            # 任务API
│   │       ├── configs.py          # 配置API
│   │       ├── artifacts.py        # 工件API
│   │       ├── monitoring.py       # 监控API
│   │       └── stream.py           # 流式输出API
│   ├── services/
│   │   ├── __init__.py
│   │   ├── user_service.py         # 用户服务
│   │   ├── auth_service.py         # 认证服务
│   │   ├── agent_service.py        # 智能体服务
│   │   ├── workflow_service.py     # 工作流服务
│   │   ├── session_service.py      # 会话服务
│   │   ├── message_service.py      # 消息服务
│   │   ├── task_service.py         # 任务服务
│   │   ├── config_service.py       # 配置服务
│   │   ├── artifact_service.py     # 工件服务
│   │   ├── monitoring_service.py   # 监控服务
│   │   └── stream_service.py       # 流式输出服务
│   ├── utils/
│   │   ├── __init__.py
│   │   ├── logger.py               # 日志工具
│   │   ├── crypto.py               # 加密工具
│   │   ├── validators.py           # 验证工具
│   │   ├── helpers.py              # 辅助函数
│   │   └── constants.py            # 常量定义
│   └── db/
│       ├── __init__.py
│       ├── session.py              # 数据库会话
│       ├── base.py                 # 数据库基础类
│       └── migrations/             # 数据库迁移
├── scripts/
│   ├── init_db.py                  # 数据库初始化
│   ├── create_admin.py             # 创建管理员
│   └── migrate.py                  # 数据迁移
├── docs/
│   ├── api.md                      # API文档
│   └── deployment.md               # 部署文档
├── requirements.txt                # 依赖列表
├── alembic.ini                     # Alembic配置
└── README.md                       # 项目说明
```

## 6. 开发计划（10次迭代）

### 第1次迭代：项目基础架构搭建

**提示词**:
```
你是一个专业的Python后端开发工程师，请帮我创建一个基于Google ADK和FastAPI的A2A多智能体系统后端项目。

请完成以下任务：
1. 创建完整的FastAPI项目目录结构，包括app/、config/、core/、models/、schemas/、api/、auth/等目录
2. 实现配置管理系统，支持环境变量和数据库配置存储，使用Pydantic Settings
3. 设置MySQL数据库连接，使用SQLAlchemy 2.0异步模式
4. 配置loguru日志系统，支持结构化日志和数据库日志存储
5. 实现基础中间件：CORS、请求日志记录、全局异常处理、用户认证中间件
6. 创建用户认证基础架构：JWT处理、密码加密、权限管理
7. 创建main.py入口文件，配置FastAPI应用
8. 生成requirements.txt文件，包含Google ADK、认证相关和其他必要依赖
9. 修复conda环境配置文件，移除asyncio等内置模块

技术要求：
- 使用Python 3.12+、Google ADK最新版、FastAPI 0.104.1、SQLAlchemy 2.0.23
- 集成JWT认证、bcrypt密码加密、权限控制系统
- 所有配置存储在数据库中，无外部配置文件依赖
- 所有代码必须支持异步操作
- 遵循RESTful API设计规范
- 实现用户认证和授权的完整流程
- 添加完整的类型注解和中文文档字符串

请提供完整的代码实现和目录结构。
```

### 第2次迭代：数据模型和Schema定义

**提示词**:
```
你是一个专业的Python后端开发工程师，请帮我为A2A多智能体系统创建完整的数据模型和Schema定义。

请完成以下任务：
1. 创建SQLAlchemy模型文件（models/目录）：
   - base.py: 基础模型类，包含通用字段（id、创建时间、更新时间等）
   - user.py: 用户表、用户会话令牌表、用户权限表、用户操作日志表
   - agent.py: 智能体配置表、智能体层次关系表（添加user_id和owner_id字段）
   - workflow.py: 工作流定义表、工作流步骤表（添加user_id和owner_id字段）
   - session.py: 会话表、会话状态表（添加user_id字段）
   - message.py: 消息表、消息附件表（添加user_id字段）
   - task.py: 任务表、任务步骤执行表（添加user_id和owner_id字段）
   - config.py: 系统配置表、LLM配置表、工具配置表、用户配置表（添加user_id字段）
   - artifact.py: 工件表、工件版本表、文件存储表（添加user_id和owner_id字段）
   - monitoring.py: 性能指标表、系统日志表、错误日志表、审计日志表（添加user_id字段）

2. 创建Pydantic Schema文件（schemas/目录）：
   - user.py: 用户注册、登录、更新、响应Schema，权限Schema
   - auth.py: 认证相关Schema（登录请求、令牌响应、权限验证等）
   - 为每个模型创建Create、Update、Response schema（包含用户权限验证）
   - 支持嵌套关系和复杂JSON字段验证
   - 实现流式输出相关的Schema
   - 添加通用的分页和响应Schema（包含用户权限过滤）

3. 创建数据库迁移文件：
   - 使用Alembic创建初始迁移
   - 添加索引和外键约束
   - 支持数据库版本管理

技术要求：
- 使用SQLAlchemy 2.0异步模式和声明式映射
- 所有表支持软删除和审计字段
- JSON字段用于存储复杂配置和元数据
- 外键关系正确设置，支持级联操作
- Schema支持数据验证和序列化
- 添加完整的类型注解和中文文档字符串

请提供完整的模型定义和Schema实现。
```

### 第3次迭代：Google ADK集成层实现

**提示词**:
```
你是一个专业的Python后端开发工程师，请帮我实现Google ADK的集成层，为A2A多智能体系统提供ADK功能封装。

请完成以下任务：
1. 创建ADK Runner封装（adk/runners/目录）：
   - base_runner.py: 基础Runner类，封装ADK Runner功能，集成用户权限验证
   - agent_runner.py: 智能体Runner，支持单智能体执行，包含用户权限检查
   - workflow_runner.py: 工作流Runner，支持多智能体协作，包含用户权限控制
   - runner_manager.py: Runner管理器，负责Runner生命周期，支持用户隔离
   - runner_factory.py: Runner工厂，根据配置和用户权限创建不同类型的Runner

2. 创建ADK Agent扩展（adk/agents/目录）：
   - custom_llm_agent.py: 自定义LLM智能体，支持多种LLM提供商，包含用户权限验证
   - workflow_agent.py: 工作流智能体，支持复杂工作流编排，包含用户权限控制
   - tool_agent.py: 工具智能体，专门处理工具调用，包含工具权限验证
   - agent_registry.py: 智能体注册器，管理智能体类型，支持用户权限过滤
   - agent_factory.py: 智能体工厂，根据配置和用户权限创建智能体实例

3. 创建ADK服务扩展（adk/services/目录）：
   - database_session_service.py: 数据库会话服务，替代ADK内存会话，支持用户隔离
   - database_memory_service.py: 数据库内存服务，替代ADK内存存储，支持用户数据隔离
   - database_artifact_service.py: 数据库工件服务，替代GCS存储，包含用户权限控制
   - service_factory.py: 服务工厂，创建自定义服务实例，集成用户权限验证

4. 创建数据转换器（adk/converters/目录）：
   - event_converter.py: ADK事件与数据库模型转换
   - message_converter.py: ADK消息与数据库模型转换
   - response_converter.py: ADK响应与API响应转换

技术要求：
- 完全基于Google ADK架构，不自定义智能体框架
- 所有ADK服务使用数据库存储，不依赖外部组件
- 支持流式输出和事件驱动机制
- 实现ADK与数据库模型的双向转换
- 支持多种LLM提供商的统一接口
- 添加完整的错误处理和日志记录
- 添加完整的类型注解和中文文档字符串

请提供完整的ADK集成层实现。
```

### 第4次迭代：LLM客户端集成

**提示词**:
```
你是一个专业的Python后端开发工程师，请帮我实现多种LLM提供商的客户端集成，基于Google ADK的LLM框架。

请完成以下任务：
1. 创建LLM配置管理（services/llm_service.py）：
   - 从数据库读取LLM配置（API密钥、端点、参数等），支持用户级别配置
   - 支持多个LLM提供商的配置管理，包含用户权限验证
   - 实现配置的动态更新和缓存，支持用户隔离
   - 支持LLM健康检查和故障转移，记录用户使用情况

2. 扩展ADK LLM集成（adk/llm/目录）：
   - google_llm.py: Google Gemini集成，基于ADK GoogleLLM
   - openai_llm.py: OpenAI GPT集成，扩展ADK LiteLLM
   - anthropic_llm.py: Anthropic Claude集成，扩展ADK LiteLLM
   - dashscope_llm.py: 阿里云千问集成，自定义ADK LLM实现
   - zhipu_llm.py: 智谱AI集成，自定义ADK LLM实现
   - llm_factory.py: LLM工厂，根据配置创建LLM实例

3. 实现流式输出支持：
   - 所有LLM客户端必须支持流式输出
   - 实现统一的流式响应格式
   - 支持流式输出的错误处理和重试
   - 实现流式输出的性能监控

4. 创建LLM工具集成：
   - function_calling.py: 函数调用工具，支持所有LLM的工具调用
   - context_manager.py: 上下文管理，智能管理token限制
   - prompt_template.py: 提示词模板管理
   - response_parser.py: 响应解析和格式化

技术要求：
- 基于Google ADK的LLM架构，不自定义LLM框架
- 所有LLM配置存储在数据库中
- 支持异步调用和并发处理
- 实现统一的错误处理和重试机制
- 支持流式输出和实时响应
- 实现LLM调用的性能监控和日志记录
- 添加完整的类型注解和中文文档字符串

请提供完整的LLM客户端集成实现。
```

### 第5次迭代：工具系统实现

**提示词**:
```
你是一个专业的Python后端开发工程师，请帮我实现基于Google ADK的工具系统，支持MCP工具和自定义工具。

请完成以下任务：
1. 创建ADK工具扩展（adk/tools/目录）：
   - base_tool.py: 基础工具类，扩展ADK BaseTool，集成用户权限验证
   - mcp_tool.py: MCP工具集成，支持MCP协议，包含用户权限控制
   - database_tool.py: 数据库操作工具，支持用户数据隔离
   - file_tool.py: 文件操作工具，包含用户文件权限验证
   - web_tool.py: 网络请求工具，支持用户配置和权限控制
   - calculation_tool.py: 计算工具，记录用户使用情况
   - tool_registry.py: 工具注册器，管理所有工具，支持用户权限过滤

2. 实现工具配置管理（services/tool_service.py）：
   - 从数据库读取工具配置，支持用户级别配置
   - 支持工具的动态注册和注销，包含用户权限验证
   - 实现工具权限控制和访问限制，支持细粒度权限管理
   - 支持工具的版本管理和更新，记录用户操作历史

3. 创建MCP服务集成：
   - mcp_client.py: MCP客户端，连接外部MCP服务
   - mcp_server.py: MCP服务器，提供内部工具服务
   - mcp_protocol.py: MCP协议实现
   - mcp_manager.py: MCP服务管理器

4. 实现工具执行引擎：
   - tool_executor.py: 工具执行器，支持同步和异步执行
   - tool_validator.py: 工具参数验证器
   - tool_monitor.py: 工具执行监控
   - tool_cache.py: 工具结果缓存（存储在数据库中）

5. 创建工具API接口（api/v1/tools.py）：
   - 工具列表查询接口
   - 工具执行接口
   - 工具配置管理接口
   - 工具执行历史查询接口

技术要求：
- 基于Google ADK的工具架构
- 所有工具配置和执行记录存储在数据库中
- 支持工具的异步执行和并发控制
- 实现工具的权限控制和安全验证
- 支持MCP协议的完整实现
- 实现工具执行的监控和日志记录
- 添加完整的类型注解和中文文档字符串

请提供完整的工具系统实现。
```

### 第6次迭代：智能体核心服务

**提示词**:
```
你是一个专业的Python后端开发工程师，请帮我实现智能体核心服务，基于Google ADK的智能体架构。

请完成以下任务：
1. 实现用户管理服务（services/user_service.py）：
   - 用户注册、登录、更新、删除功能
   - 用户权限管理和角色分配
   - 用户会话管理和令牌验证
   - 用户操作日志记录和审计

2. 实现认证服务（services/auth_service.py）：
   - JWT令牌生成、验证和刷新
   - 密码加密和验证
   - 权限检查和访问控制
   - 登录状态管理和会话控制

3. 实现智能体服务（services/agent_service.py）：
   - 智能体的创建、更新、删除、查询（包含用户权限验证）
   - 智能体配置的管理和验证（支持用户级别配置）
   - 智能体状态的跟踪和管理（支持用户隔离）
   - 智能体层次关系的管理（包含所有者权限控制）

4. 实现会话服务（services/session_service.py）：
   - 会话的创建、管理和销毁（包含用户权限验证）
   - 会话状态的持久化和恢复（支持用户隔离）
   - 会话上下文的管理和优化（包含用户数据隔离）
   - 会话历史的查询和分析（支持用户权限过滤）

5. 实现消息服务（services/message_service.py）：
   - 消息的发送、接收和存储（包含用户权限验证）
   - 消息格式的验证和转换（支持用户级别配置）
   - 消息历史的管理和查询（支持用户权限过滤）
   - 消息的流式处理和实时推送（包含用户权限控制）

6. 实现任务服务（services/task_service.py）：
   - 任务的创建、执行和监控（包含用户权限验证）
   - 任务步骤的管理和跟踪（支持用户隔离）
   - 任务结果的收集和分析（包含所有者权限控制）
   - 任务的并发执行和资源管理（支持用户配额限制）

7. 创建认证API接口（api/v1/auth.py）：
   - 用户注册接口
   - 用户登录接口
   - 令牌刷新接口
   - 用户登出接口
   - 密码重置接口

8. 创建用户管理API接口（api/v1/users.py）：
   - 用户信息查询接口
   - 用户信息更新接口
   - 用户权限管理接口
   - 用户操作日志接口

9. 创建智能体API接口（api/v1/agents.py）：
   - 智能体CRUD接口（包含用户权限验证）
   - 智能体执行接口（包含用户权限检查）
   - 智能体状态查询接口（支持用户权限过滤）
   - 智能体性能统计接口（支持用户数据隔离）

10. 创建会话API接口（api/v1/sessions.py）：
    - 会话管理接口（包含用户权限验证）
    - 会话消息接口（支持用户权限过滤）
    - 会话状态接口（包含用户权限检查）
    - 会话历史接口（支持用户数据隔离）

技术要求：
- 完全基于Google ADK的智能体架构
- 所有数据存储在数据库中，支持事务处理
- 支持智能体的异步执行和并发处理
- 实现智能体的生命周期管理
- 支持会话的状态恢复和容错处理
- 实现消息的流式处理和实时推送
- 添加完整的错误处理和日志记录
- 添加完整的类型注解和中文文档字符串

请提供完整的智能体核心服务实现。
```

### 第7次迭代：工作流引擎实现

**提示词**:
```
你是一个专业的Python后端开发工程师，请帮我实现基于Google ADK的工作流引擎，支持复杂的多智能体协作。

请完成以下任务：
1. 实现工作流服务（services/workflow_service.py）：
   - 工作流的创建、更新、删除、查询（包含用户权限验证）
   - 工作流定义的验证和解析（支持用户级别配置）
   - 工作流实例的管理和执行（包含所有者权限控制）
   - 工作流状态的跟踪和持久化（支持用户数据隔离）

2. 创建工作流引擎（workflow/engine.py）：
   - 基于ADK的SequentialAgent、ParallelAgent、LoopAgent
   - 工作流的解析和执行计划生成
   - 工作流步骤的调度和执行
   - 工作流异常的处理和恢复

3. 实现工作流执行器（workflow/executor.py）：
   - 顺序执行器：按步骤顺序执行智能体
   - 并行执行器：支持多智能体并行执行
   - 循环执行器：支持条件循环和迭代
   - 分支执行器：支持条件分支和决策

4. 创建工作流调度器（workflow/scheduler.py）：
   - 工作流任务的调度和分配
   - 资源的管理和优化
   - 优先级的处理和排队
   - 负载均衡和性能优化

5. 实现上下文管理器（workflow/context_manager.py）：
   - 工作流上下文的传递和管理
   - 智能体间数据的共享和隔离
   - 上下文的持久化和恢复
   - 上下文的版本管理和回滚

6. 创建工作流API接口（api/v1/workflows.py）：
   - 工作流定义管理接口
   - 工作流执行接口
   - 工作流状态查询接口
   - 工作流性能分析接口

技术要求：
- 基于Google ADK的工作流智能体架构
- 支持复杂的工作流编排和控制流
- 所有工作流数据存储在数据库中
- 支持工作流的异步执行和并发处理
- 实现工作流的容错和恢复机制
- 支持工作流的动态修改和热更新
- 实现工作流的性能监控和优化
- 添加完整的类型注解和中文文档字符串

请提供完整的工作流引擎实现。
```

### 第8次迭代：流式输出和实时通信

**提示词**:
```
你是一个专业的Python后端开发工程师，请帮我实现基于Google ADK的流式输出和实时通信系统。

请完成以下任务：
1. 实现流式输出服务（services/stream_service.py）：
   - 基于ADK的流式LLM连接（包含用户权限验证）
   - 流式数据的缓冲和管理（支持用户隔离）
   - 流式输出的格式化和转换（支持用户级别配置）
   - 流式连接的管理和监控（包含用户会话管理）

2. 创建SSE流式接口（api/v1/stream.py）：
   - SSE连接的建立和管理（包含用户认证验证）
   - 流式数据的实时推送（支持用户权限过滤）
   - 连接状态的监控和维护（包含用户会话管理）
   - 多客户端的并发支持（支持用户隔离）

3. 实现WebSocket支持：
   - WebSocket连接的建立和管理（包含用户认证验证）
   - 双向实时通信（支持用户权限控制）
   - 消息的路由和分发（包含用户权限过滤）
   - 连接的认证和权限控制（支持细粒度权限管理）

4. 创建事件系统：
   - 基于ADK的事件驱动架构
   - 事件的发布和订阅
   - 事件的持久化和重放
   - 事件的过滤和路由

5. 实现实时监控：
   - 系统性能的实时监控
   - 智能体状态的实时跟踪
   - 工作流进度的实时更新
   - 异常和告警的实时通知

6. 创建流式数据处理：
   - 流式数据的解析和验证
   - 流式数据的聚合和分析
   - 流式数据的存储和查询
   - 流式数据的压缩和优化

技术要求：
- 基于Google ADK的流式架构
- 支持SSE和WebSocket两种实时通信方式
- 所有流式数据可选择性存储在数据库中
- 支持高并发的流式连接
- 实现流式数据的容错和重连机制
- 支持流式数据的压缩和优化
- 实现流式连接的性能监控
- 添加完整的类型注解和中文文档字符串

请提供完整的流式输出和实时通信实现。
```

### 第9次迭代：配置管理和工件存储

**提示词**:
```
你是一个专业的Python后端开发工程师，请帮我实现基于数据库的配置管理和工件存储系统。

请完成以下任务：
1. 实现配置服务（services/config_service.py）：
   - 系统配置的读取、更新和缓存（包含管理员权限验证）
   - 用户配置的个性化管理（支持用户级别配置隔离）
   - LLM配置的动态管理（包含用户权限验证和配额控制）
   - 工具配置的版本控制（支持用户权限管理）
   - 配置的验证和格式化（包含用户权限检查）
   - 配置的备份和恢复（支持用户数据隔离）

2. 实现工件服务（services/artifact_service.py）：
   - 文件的上传、下载和存储（包含用户权限验证）
   - 工件的版本管理和历史记录（支持所有者权限控制）
   - 工件的权限控制和访问管理（支持细粒度权限管理）
   - 工件的元数据管理和搜索（包含用户权限过滤）
   - 工件的压缩和优化（支持用户配额管理）
   - 工件的备份和恢复（支持用户数据隔离）

3. 创建配置API接口（api/v1/configs.py）：
   - 系统配置管理接口（包含管理员权限验证）
   - 用户配置管理接口（支持用户级别配置管理）
   - LLM配置管理接口（包含用户权限验证和配额控制）
   - 工具配置管理接口（支持用户权限管理）
   - 配置导入导出接口（包含用户权限检查）
   - 配置历史查询接口（支持用户权限过滤）

4. 创建工件API接口（api/v1/artifacts.py）：
   - 文件上传下载接口（包含用户权限验证）
   - 工件管理接口（支持所有者权限控制）
   - 工件版本接口（包含用户权限检查）
   - 工件搜索接口（支持用户权限过滤）
   - 工件权限接口（支持细粒度权限管理）
   - 工件统计接口（支持用户数据隔离）

5. 实现数据库存储优化：
   - 大文件的分块存储
   - 文件内容的压缩算法
   - 数据库连接池的优化
   - 查询性能的优化
   - 存储空间的管理和清理
   - 数据的备份和恢复策略

6. 创建缓存机制：
   - 配置的内存缓存
   - 工件的本地缓存
   - 缓存的失效和更新
   - 缓存的性能监控

技术要求：
- 所有配置和工件数据存储在数据库中
- 支持大文件的高效存储和传输
- 实现配置的热更新和动态加载
- 支持工件的版本管理和回滚
- 实现数据的压缩和优化
- 支持数据的备份和恢复
- 实现访问权限的精细控制
- 添加完整的类型注解和中文文档字符串

请提供完整的配置管理和工件存储实现。
```

### 第10次迭代：系统监控和性能优化

**提示词**:
```
你是一个专业的Python后端开发工程师，请帮我实现系统监控和性能优化功能，完成A2A多智能体系统的最终集成。

请完成以下任务：
1. 实现监控服务（services/monitoring_service.py）：
   - 系统性能指标的收集和分析（包含用户级别统计）
   - 智能体执行状态的监控（支持用户权限过滤）
   - 数据库性能的监控和优化（包含用户操作统计）
   - 内存使用情况的跟踪（支持用户资源监控）
   - API响应时间的统计（包含用户访问统计）
   - 错误率和异常的监控（支持用户权限过滤）

2. 创建性能分析工具：
   - 智能体执行性能分析
   - 工作流执行效率分析
   - 数据库查询性能分析
   - 内存使用优化建议
   - 系统瓶颈识别和诊断
   - 性能趋势分析和预测

3. 实现日志管理系统：
   - 结构化日志的收集和存储
   - 日志的分级和过滤
   - 日志的搜索和分析
   - 日志的归档和清理
   - 异常日志的告警和通知
   - 日志的可视化和报表

4. 创建监控API接口（api/v1/monitoring.py）：
   - 系统状态查询接口（包含管理员权限验证）
   - 性能指标查询接口（支持用户权限过滤）
   - 监控数据导出接口（包含用户权限检查）
   - 告警配置接口（支持用户级别告警配置）
   - 性能报表接口（支持用户数据隔离）
   - 系统诊断接口（包含管理员权限验证）

5. 实现告警系统：
   - 告警规则的配置和管理
   - 告警的触发和通知
   - 告警的升级和处理
   - 告警历史的记录和分析
   - 告警的自动恢复和确认

6. 系统集成和优化：
   - 完整的系统启动和初始化
   - 数据库连接池的优化
   - 异步任务的性能优化
   - 内存使用的优化
   - API响应速度的优化
   - 系统稳定性的提升

7. 创建部署脚本：
   - 数据库初始化脚本（包含用户管理表结构）
   - 系统配置初始化（包含用户权限配置）
   - 管理员账户创建（包含默认权限分配）
   - 用户权限系统初始化
   - 系统健康检查（包含用户认证系统检查）
   - 性能基准测试（包含用户并发测试）

技术要求：
- 所有监控数据存储在数据库中（包含用户级别数据隔离）
- 支持实时监控和历史数据分析（包含用户权限过滤）
- 实现高效的性能数据收集（包含用户操作指标）
- 支持自定义监控指标和告警规则（支持用户级别配置）
- 实现系统的自动化运维（包含用户权限系统维护）
- 提供完整的性能优化建议（包含用户资源使用优化）
- 确保系统的高可用性和稳定性（包含用户认证系统稳定性）
- 添加完整的类型注解和中文文档字符串（包含用户管理模块文档）

请提供完整的监控系统实现和最终的系统集成。
```

## 7. 修复环境配置文件

需要修复 `scripts/a2a_environment.yml` 文件中的问题：

```yaml
# A2A多智能体系统 Conda环境配置文件（修复版）
name: a2a_backend
channels:
  - conda-forge
  - defaults
dependencies:
  # Python版本
  - python=3.12
  
  # 核心框架
  - pip
  
  # 数据库相关
  - mysql
  
  # Web相关
  - requests
  - urllib3
  
  # 安全认证
  - cryptography
  
  # 监控日志
  - prometheus_client
  
  # 通用工具
  - click
  - rich
  - python-dateutil
  - pytz
  
  # 数据处理
  - orjson
  
  # 开发工具
  - black
  - isort
  
  # pip安装的包
  - pip:
    # Google Agent Development Kit
    - google-adk>=1.4.2
    
    # FastAPI框架
    - fastapi==0.104.1
    - uvicorn==0.24.0
    - pydantic==2.5.0
    
    # 数据库
    - sqlalchemy==2.0.23
    - alembic==1.13.0
    - pymysql==1.1.0
    - mysql-connector-python==8.2.0
    
    # LLM客户端
    - openai==1.3.7
    - anthropic==0.7.8
    - google-generativeai==0.3.2
    - dashscope==1.17.0
    - zhipuai==2.0.1
    
    # 异步支持
    - aiofiles==23.2.0
    - aiohttp==3.9.1
    
    # 流式输出
    - sse-starlette==1.8.2
    - websockets==12.0
    
    # 认证安全
    - python-multipart==0.0.6
    - python-jose==3.3.0
    - passlib==1.7.4
    - bcrypt==4.1.2
    
    # 日志监控
    - loguru==0.7.2
    - structlog==23.2.0
    
    # 工具
    - tenacity
    - cachetools
    - python-slugify
    - email-validator
    - phonenumbers
    - pillow
    - python-magic
```

## 8. 总结

这个简化的开发计划专注于核心功能实现，去除了不必要的复杂性：

1. **存储简化**：仅使用MySQL数据库，所有数据统一存储
2. **架构简化**：基于Google ADK，减少自定义实现
3. **部署简化**：无Docker要求，直接部署
4. **测试简化**：无单元测试要求，专注功能实现
5. **配置简化**：所有配置存储在数据库中

通过10次迭代，逐步构建完整的A2A多智能体系统，每次迭代都有明确的目标和详细的实现要求。