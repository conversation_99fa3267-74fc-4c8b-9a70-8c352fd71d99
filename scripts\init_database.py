#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A2A多智能体系统数据库初始化脚本

功能：
1. 创建数据库表结构
2. 初始化基础数据
3. 创建默认管理员用户
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from loguru import logger
from app.core.database import get_database_manager, init_database
from app.models.base import Base
from app.services.user_service import UserService
from app.core.config import get_settings


async def create_tables():
    """
    创建数据库表结构
    """
    logger.info("开始创建数据库表结构...")
    
    try:
        # 获取数据库管理器
        db_manager = get_database_manager()
        
        # 创建所有表
        async with db_manager.engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        
        logger.success("数据库表结构创建成功")
        return True
        
    except Exception as e:
        logger.error(f"创建数据库表结构失败: {str(e)}")
        return False


async def create_admin_user():
    """
    创建默认管理员用户
    """
    logger.info("开始创建默认管理员用户...")
    
    try:
        user_service = UserService()
        
        # 检查是否已存在管理员用户
        existing_admin = await user_service.get_user_by_username("admin")
        if existing_admin:
            logger.warning("管理员用户已存在，跳过创建")
            return True
        
        # 创建管理员用户数据
        admin_data = {
            "username": "admin",
            "email": "<EMAIL>",
            "password": "Admin123!@#",
            "full_name": "系统管理员",
            "role": "super_admin",
            "is_verified": True,
            "is_active": True
        }
        
        # 创建用户
        result = await user_service.create_user(admin_data)
        
        if result.get("success"):
            logger.success(f"管理员用户创建成功，用户ID: {result['data']['user_id']}")
            logger.info("默认管理员账户信息:")
            logger.info("  用户名: admin")
            logger.info("  密码: Admin123!@#")
            logger.info("  邮箱: <EMAIL>")
            logger.warning("请在首次登录后立即修改默认密码！")
            return True
        else:
            logger.error(f"管理员用户创建失败: {result.get('message')}")
            return False
            
    except Exception as e:
        logger.error(f"创建管理员用户失败: {str(e)}")
        return False


async def init_basic_data():
    """
    初始化基础数据
    """
    logger.info("开始初始化基础数据...")
    
    try:
        # 这里可以添加初始化基础数据的逻辑
        # 例如：默认配置、系统角色、权限等
        
        logger.success("基础数据初始化成功")
        return True
        
    except Exception as e:
        logger.error(f"初始化基础数据失败: {str(e)}")
        return False


async def check_database_connection():
    """
    检查数据库连接
    """
    logger.info("检查数据库连接...")
    
    try:
        db_manager = get_database_manager()
        
        # 测试连接
        async with db_manager.get_session() as session:
            await session.execute("SELECT 1")
        
        logger.success("数据库连接正常")
        return True
        
    except Exception as e:
        logger.error(f"数据库连接失败: {str(e)}")
        logger.error("请检查数据库配置和服务状态")
        return False


async def main():
    """
    主函数
    """
    logger.info("=" * 50)
    logger.info("A2A多智能体系统数据库初始化")
    logger.info("=" * 50)
    
    # 加载配置
    settings = get_settings()
    logger.info(f"数据库URL: {settings.database_url}")
    
    # 检查数据库连接
    if not await check_database_connection():
        logger.error("数据库连接失败，初始化终止")
        return False
    
    # 初始化数据库连接
    await init_database()
    
    # 创建表结构
    if not await create_tables():
        logger.error("创建表结构失败，初始化终止")
        return False
    
    # 初始化基础数据
    if not await init_basic_data():
        logger.error("初始化基础数据失败，但可以继续")
    
    # 创建管理员用户
    if not await create_admin_user():
        logger.error("创建管理员用户失败，但可以继续")
    
    logger.success("=" * 50)
    logger.success("数据库初始化完成！")
    logger.success("=" * 50)
    logger.info("下一步:")
    logger.info("1. 启动应用: python main.py")
    logger.info("2. 访问API文档: http://localhost:8000/docs")
    logger.info("3. 使用管理员账户登录测试系统")
    
    return True


if __name__ == "__main__":
    try:
        # 运行初始化
        success = asyncio.run(main())
        
        if success:
            logger.success("数据库初始化成功完成")
            sys.exit(0)
        else:
            logger.error("数据库初始化失败")
            sys.exit(1)
            
    except KeyboardInterrupt:
        logger.warning("用户中断了初始化过程")
        sys.exit(1)
    except Exception as e:
        logger.error(f"初始化过程中发生未预期的错误: {str(e)}")
        sys.exit(1)
