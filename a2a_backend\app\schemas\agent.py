#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A2A多智能体系统智能体相关Pydantic模式

定义智能体相关的请求和响应模式
"""

from typing import Dict, List, Optional, Any
from datetime import datetime
from pydantic import Field, validator
from enum import Enum

from .base import BaseSchema


class AgentType(str, Enum):
    """智能体类型枚举"""
    CHAT = "chat"
    TASK = "task"
    WORKFLOW = "workflow"
    TOOL = "tool"
    CUSTOM = "custom"


class AgentStatus(str, Enum):
    """智能体状态枚举"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    TRAINING = "training"
    MAINTENANCE = "maintenance"
    DEPRECATED = "deprecated"


class AgentVisibility(str, Enum):
    """智能体可见性枚举"""
    PUBLIC = "public"
    PRIVATE = "private"
    SHARED = "shared"


class AgentCreate(BaseSchema):
    """
    智能体创建请求模式
    """
    
    name: str = Field(
        min_length=1,
        max_length=100,
        description="智能体名称"
    )
    
    description: Optional[str] = Field(
        default=None,
        max_length=1000,
        description="智能体描述"
    )
    
    type: AgentType = Field(
        description="智能体类型"
    )
    
    model_name: str = Field(
        description="使用的模型名称"
    )
    
    system_prompt: Optional[str] = Field(
        default=None,
        max_length=10000,
        description="系统提示词"
    )
    
    instructions: Optional[str] = Field(
        default=None,
        max_length=10000,
        description="智能体指令"
    )
    
    capabilities: List[str] = Field(
        default_factory=list,
        description="智能体能力列表"
    )
    
    tools: List[str] = Field(
        default_factory=list,
        description="可用工具ID列表"
    )
    
    config: Optional[Dict[str, Any]] = Field(
        default_factory=dict,
        description="智能体配置"
    )
    
    avatar_url: Optional[str] = Field(
        default=None,
        max_length=500,
        description="头像URL"
    )
    
    tags: List[str] = Field(
        default_factory=list,
        description="标签列表"
    )
    
    visibility: AgentVisibility = Field(
        default=AgentVisibility.PRIVATE,
        description="可见性"
    )
    
    max_tokens: Optional[int] = Field(
        default=None,
        ge=1,
        le=100000,
        description="最大令牌数"
    )
    
    temperature: Optional[float] = Field(
        default=None,
        ge=0.0,
        le=2.0,
        description="温度参数"
    )
    
    top_p: Optional[float] = Field(
        default=None,
        ge=0.0,
        le=1.0,
        description="Top-p参数"
    )


class AgentUpdate(BaseSchema):
    """
    智能体更新请求模式
    """
    
    name: Optional[str] = Field(
        default=None,
        min_length=1,
        max_length=100,
        description="智能体名称"
    )
    
    description: Optional[str] = Field(
        default=None,
        max_length=1000,
        description="智能体描述"
    )
    
    system_prompt: Optional[str] = Field(
        default=None,
        max_length=10000,
        description="系统提示词"
    )
    
    instructions: Optional[str] = Field(
        default=None,
        max_length=10000,
        description="智能体指令"
    )
    
    capabilities: Optional[List[str]] = Field(
        default=None,
        description="智能体能力列表"
    )
    
    tools: Optional[List[str]] = Field(
        default=None,
        description="可用工具ID列表"
    )
    
    config: Optional[Dict[str, Any]] = Field(
        default=None,
        description="智能体配置"
    )
    
    avatar_url: Optional[str] = Field(
        default=None,
        max_length=500,
        description="头像URL"
    )
    
    tags: Optional[List[str]] = Field(
        default=None,
        description="标签列表"
    )
    
    visibility: Optional[AgentVisibility] = Field(
        default=None,
        description="可见性"
    )
    
    status: Optional[AgentStatus] = Field(
        default=None,
        description="状态"
    )
    
    max_tokens: Optional[int] = Field(
        default=None,
        ge=1,
        le=100000,
        description="最大令牌数"
    )
    
    temperature: Optional[float] = Field(
        default=None,
        ge=0.0,
        le=2.0,
        description="温度参数"
    )
    
    top_p: Optional[float] = Field(
        default=None,
        ge=0.0,
        le=1.0,
        description="Top-p参数"
    )


class AgentResponse(BaseSchema):
    """
    智能体响应模式
    """
    
    id: str = Field(
        description="智能体ID"
    )
    
    name: str = Field(
        description="智能体名称"
    )
    
    description: Optional[str] = Field(
        description="智能体描述"
    )
    
    type: AgentType = Field(
        description="智能体类型"
    )
    
    model_name: str = Field(
        description="使用的模型名称"
    )
    
    system_prompt: Optional[str] = Field(
        description="系统提示词"
    )
    
    instructions: Optional[str] = Field(
        description="智能体指令"
    )
    
    capabilities: List[str] = Field(
        description="智能体能力列表"
    )
    
    tools: List[str] = Field(
        description="可用工具ID列表"
    )
    
    config: Dict[str, Any] = Field(
        description="智能体配置"
    )
    
    avatar_url: Optional[str] = Field(
        description="头像URL"
    )
    
    tags: List[str] = Field(
        description="标签列表"
    )
    
    visibility: AgentVisibility = Field(
        description="可见性"
    )
    
    status: AgentStatus = Field(
        description="状态"
    )
    
    creator_id: str = Field(
        description="创建者ID"
    )
    
    version: str = Field(
        description="版本号"
    )
    
    max_tokens: Optional[int] = Field(
        description="最大令牌数"
    )
    
    temperature: Optional[float] = Field(
        description="温度参数"
    )
    
    top_p: Optional[float] = Field(
        description="Top-p参数"
    )
    
    usage_count: int = Field(
        description="使用次数"
    )
    
    rating: Optional[float] = Field(
        description="评分"
    )
    
    created_at: datetime = Field(
        description="创建时间"
    )
    
    updated_at: datetime = Field(
        description="更新时间"
    )


class AgentTemplateResponse(BaseSchema):
    """
    智能体模板响应模式
    """
    
    id: str = Field(
        description="模板ID"
    )
    
    name: str = Field(
        description="模板名称"
    )
    
    description: Optional[str] = Field(
        description="模板描述"
    )
    
    category: str = Field(
        description="模板分类"
    )
    
    template_data: Dict[str, Any] = Field(
        description="模板数据"
    )
    
    preview_url: Optional[str] = Field(
        description="预览URL"
    )
    
    tags: List[str] = Field(
        description="标签列表"
    )
    
    is_official: bool = Field(
        description="是否官方模板"
    )
    
    usage_count: int = Field(
        description="使用次数"
    )
    
    rating: Optional[float] = Field(
        description="评分"
    )
    
    creator_id: str = Field(
        description="创建者ID"
    )
    
    created_at: datetime = Field(
        description="创建时间"
    )
    
    updated_at: datetime = Field(
        description="更新时间"
    )


class AgentCollaborationResponse(BaseSchema):
    """
    智能体协作响应模式
    """
    
    id: str = Field(
        description="协作ID"
    )
    
    primary_agent_id: str = Field(
        description="主智能体ID"
    )
    
    secondary_agent_id: str = Field(
        description="协作智能体ID"
    )
    
    collaboration_type: str = Field(
        description="协作类型"
    )
    
    config: Dict[str, Any] = Field(
        description="协作配置"
    )
    
    status: str = Field(
        description="协作状态"
    )
    
    created_at: datetime = Field(
        description="创建时间"
    )
    
    updated_at: datetime = Field(
        description="更新时间"
    )


class AgentStatsResponse(BaseSchema):
    """
    智能体统计信息响应模式
    """
    
    total_sessions: int = Field(
        description="总会话数"
    )
    
    total_messages: int = Field(
        description="总消息数"
    )
    
    total_tasks: int = Field(
        description="总任务数"
    )
    
    avg_response_time: float = Field(
        description="平均响应时间（秒）"
    )
    
    success_rate: float = Field(
        description="成功率"
    )
    
    total_tokens_used: int = Field(
        description="总使用令牌数"
    )
    
    total_cost: float = Field(
        description="总成本"
    )
    
    last_used_at: Optional[datetime] = Field(
        description="最后使用时间"
    )


class AgentCloneRequest(BaseSchema):
    """
    智能体克隆请求模式
    """
    
    name: str = Field(
        min_length=1,
        max_length=100,
        description="新智能体名称"
    )
    
    description: Optional[str] = Field(
        default=None,
        max_length=1000,
        description="新智能体描述"
    )
    
    visibility: AgentVisibility = Field(
        default=AgentVisibility.PRIVATE,
        description="可见性"
    )