#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A2A多智能体系统 事件转换器

ADK事件与数据库模型转换
"""

import logging
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
import json
import uuid

from google.ai.generativelanguage import GenerateContentRequest, GenerateContentResponse
from google.ai.generativelanguage import Content, Part

from app.models.agent import Agent
from app.models.workflow import Workflow, WorkflowExecution, WorkflowStep
from app.models.task import Task, TaskExecution
from app.models.message import Message
from app.models.session import Session
from app.core.logging import get_logger

class EventConverter:
    """
    事件转换器，ADK事件与数据库模型转换
    
    提供以下功能：
    1. ADK事件到数据库模型的转换
    2. 数据库模型到ADK事件的转换
    3. 事件类型识别和验证
    4. 事件数据格式化和标准化
    5. 事件元数据处理
    6. 批量事件转换
    7. 事件转换历史记录
    8. 错误处理和日志记录
    """
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        """
        初始化事件转换器
        
        Args:
            logger: 日志记录器
        """
        self.logger = logger or get_logger("event_converter")
        
        # 事件类型映射
        self.event_type_mapping = {
            "agent_start": "agent.execution.start",
            "agent_complete": "agent.execution.complete",
            "agent_error": "agent.execution.error",
            "workflow_start": "workflow.execution.start",
            "workflow_complete": "workflow.execution.complete",
            "workflow_error": "workflow.execution.error",
            "node_start": "workflow.node.start",
            "node_complete": "workflow.node.complete",
            "node_error": "workflow.node.error",
            "message_received": "session.message.received",
        "message_sent": "session.message.sent",
            "tool_call": "agent.tool.call",
            "tool_response": "agent.tool.response"
        }
        
        # 转换统计
        self.conversion_stats = {
            "total_conversions": 0,
            "successful_conversions": 0,
            "failed_conversions": 0,
            "event_types_processed": set(),
            "conversion_history": [],
            "last_conversion": None
        }
        
        self.logger.info("EventConverter已初始化")
    
    def _generate_event_id(self) -> str:
        """
        生成事件ID
        
        Returns:
            str: 事件ID
        """
        return str(uuid.uuid4())
    
    def _format_timestamp(self, timestamp: Optional[datetime] = None) -> str:
        """
        格式化时间戳
        
        Args:
            timestamp: 时间戳
            
        Returns:
            str: 格式化的时间戳
        """
        if timestamp is None:
            timestamp = datetime.now()
        
        return timestamp.isoformat()
    
    def _validate_event_data(self, event_data: Dict[str, Any]) -> bool:
        """
        验证事件数据
        
        Args:
            event_data: 事件数据
            
        Returns:
            bool: 是否有效
        """
        try:
            # 检查必需字段
            required_fields = ["event_type", "timestamp"]
            for field in required_fields:
                if field not in event_data:
                    self.logger.error(f"事件数据缺少必需字段: {field}")
                    return False
            
            # 检查事件类型
            event_type = event_data.get("event_type")
            if event_type not in self.event_type_mapping.values():
                self.logger.warning(f"未知的事件类型: {event_type}")
            
            return True
        except Exception as e:
            self.logger.error(f"验证事件数据错误: {str(e)}")
            return False
    
    def agent_execution_to_event(
        self,
        task_execution: TaskExecution,
        event_type: str = "agent_start"
    ) -> Dict[str, Any]:
        """
        将智能体执行转换为事件
        
        Args:
            task_execution: 任务执行记录
            event_type: 事件类型
            
        Returns:
            Dict[str, Any]: 事件数据
        """
        try:
            event_data = {
                "event_id": self._generate_event_id(),
                "event_type": self.event_type_mapping.get(event_type, event_type),
                "timestamp": self._format_timestamp(task_execution.created_at),
                "source": "agent_runner",
                "data": {
                    "execution_id": task_execution.id,
                    "task_id": task_execution.task_id,
                    "agent_id": task_execution.task.agent_id if task_execution.task else None,
                    "user_id": task_execution.user_id,
                    "status": task_execution.status,
                    "input_data": task_execution.input_data,
                    "output_data": task_execution.output_data,
                    "error_message": task_execution.error_message,
                    "metrics": task_execution.metrics,
                    "started_at": self._format_timestamp(task_execution.started_at) if task_execution.started_at else None,
                    "completed_at": self._format_timestamp(task_execution.completed_at) if task_execution.completed_at else None
                },
                "metadata": {
                    "execution_duration": task_execution.execution_time,
                    "resource_usage": task_execution.resource_usage,
                    "conversion_timestamp": self._format_timestamp()
                }
            }
            
            # 添加智能体信息
            if task_execution.task and task_execution.task.agent:
                agent = task_execution.task.agent
                event_data["data"]["agent_info"] = {
                    "name": agent.name,
                    "type": agent.type,
                    "version": agent.version,
                    "config": agent.config
                }
            
            return event_data
        except Exception as e:
            self.logger.error(f"转换智能体执行事件错误: {str(e)}")
            raise e
    
    def workflow_execution_to_event(
        self,
        workflow_execution: WorkflowExecution,
        event_type: str = "workflow_start"
    ) -> Dict[str, Any]:
        """
        将工作流执行转换为事件
        
        Args:
            workflow_execution: 工作流执行记录
            event_type: 事件类型
            
        Returns:
            Dict[str, Any]: 事件数据
        """
        try:
            event_data = {
                "event_id": self._generate_event_id(),
                "event_type": self.event_type_mapping.get(event_type, event_type),
                "timestamp": self._format_timestamp(workflow_execution.created_at),
                "source": "workflow_runner",
                "data": {
                    "execution_id": workflow_execution.id,
                    "workflow_id": workflow_execution.workflow_id,
                    "user_id": workflow_execution.user_id,
                    "status": workflow_execution.status,
                    "input_data": workflow_execution.input_data,
                    "output_data": workflow_execution.output_data,
                    "current_node_id": workflow_execution.current_node_id,
                    "error_message": workflow_execution.error_message,
                    "metrics": workflow_execution.metrics,
                    "started_at": self._format_timestamp(workflow_execution.started_at) if workflow_execution.started_at else None,
                    "completed_at": self._format_timestamp(workflow_execution.completed_at) if workflow_execution.completed_at else None
                },
                "metadata": {
                    "execution_duration": workflow_execution.execution_time,
                    "nodes_executed": len(workflow_execution.execution_log) if workflow_execution.execution_log else 0,
                    "conversion_timestamp": self._format_timestamp()
                }
            }
            
            # 添加工作流信息
            if workflow_execution.workflow:
                workflow = workflow_execution.workflow
                event_data["data"]["workflow_info"] = {
                    "name": workflow.name,
                    "version": workflow.version,
                    "description": workflow.description,
                    "config": workflow.config
                }
            
            return event_data
        except Exception as e:
            self.logger.error(f"转换工作流执行事件错误: {str(e)}")
            raise e
    
    def message_to_event(
        self,
        message: Message,
        event_type: str = "message_received"
    ) -> Dict[str, Any]:
        """
        将消息转换为事件
        
        Args:
            message: 消息记录
            event_type: 事件类型
            
        Returns:
            Dict[str, Any]: 事件数据
        """
        try:
            event_data = {
                "event_id": self._generate_event_id(),
                "event_type": self.event_type_mapping.get(event_type, event_type),
                "timestamp": self._format_timestamp(message.created_at),
                "source": "session_manager",
                "data": {
                    "message_id": message.id,
                    "session_id": message.session_id,
                    "user_id": message.user_id,
                    "role": message.role,
                    "content": message.content,
                    "message_type": message.message_type,
                    "metadata": message.metadata
                },
                "metadata": {
                    "content_length": len(message.content) if message.content else 0,
                    "has_attachments": bool(message.metadata and message.metadata.get("attachments")),
                    "conversion_timestamp": self._format_timestamp()
                }
            }
            
            # 添加会话信息
            if message.session:
                session = message.session
                event_data["data"]["session_info"] = {
                    "title": session.title,
                    "status": session.status,
                    "agent_id": session.agent_id,
                    "workflow_id": session.workflow_id
                }
            
            return event_data
        except Exception as e:
            self.logger.error(f"转换消息事件错误: {str(e)}")
            raise e
    
    def event_to_task_execution(
        self,
        event_data: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """
        将事件转换为任务执行数据
        
        Args:
            event_data: 事件数据
            
        Returns:
            Optional[Dict[str, Any]]: 任务执行数据
        """
        try:
            if not self._validate_event_data(event_data):
                return None
            
            event_type = event_data.get("event_type")
            data = event_data.get("data", {})
            
            # 检查是否为智能体执行事件
            if not event_type.startswith("agent.execution"):
                return None
            
            task_execution_data = {
                "task_id": data.get("task_id"),
                "user_id": data.get("user_id"),
                "status": data.get("status", "pending"),
                "input_data": data.get("input_data"),
                "output_data": data.get("output_data"),
                "error_message": data.get("error_message"),
                "metrics": data.get("metrics"),
                "execution_time": event_data.get("metadata", {}).get("execution_duration")
            }
            
            # 处理时间戳
            if data.get("started_at"):
                task_execution_data["started_at"] = datetime.fromisoformat(data["started_at"])
            
            if data.get("completed_at"):
                task_execution_data["completed_at"] = datetime.fromisoformat(data["completed_at"])
            
            return task_execution_data
        except Exception as e:
            self.logger.error(f"转换事件到任务执行错误: {str(e)}")
            return None
    
    def event_to_workflow_execution(
        self,
        event_data: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """
        将事件转换为工作流执行数据
        
        Args:
            event_data: 事件数据
            
        Returns:
            Optional[Dict[str, Any]]: 工作流执行数据
        """
        try:
            if not self._validate_event_data(event_data):
                return None
            
            event_type = event_data.get("event_type")
            data = event_data.get("data", {})
            
            # 检查是否为工作流执行事件
            if not event_type.startswith("workflow.execution"):
                return None
            
            workflow_execution_data = {
                "workflow_id": data.get("workflow_id"),
                "user_id": data.get("user_id"),
                "status": data.get("status", "pending"),
                "input_data": data.get("input_data"),
                "output_data": data.get("output_data"),
                "current_node_id": data.get("current_node_id"),
                "error_message": data.get("error_message"),
                "metrics": data.get("metrics"),
                "execution_time": event_data.get("metadata", {}).get("execution_duration")
            }
            
            # 处理时间戳
            if data.get("started_at"):
                workflow_execution_data["started_at"] = datetime.fromisoformat(data["started_at"])
            
            if data.get("completed_at"):
                workflow_execution_data["completed_at"] = datetime.fromisoformat(data["completed_at"])
            
            return workflow_execution_data
        except Exception as e:
            self.logger.error(f"转换事件到工作流执行错误: {str(e)}")
            return None
    
    def event_to_message(
        self,
        event_data: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """
        将事件转换为消息数据
        
        Args:
            event_data: 事件数据
            
        Returns:
            Optional[Dict[str, Any]]: 消息数据
        """
        try:
            if not self._validate_event_data(event_data):
                return None
            
            event_type = event_data.get("event_type")
            data = event_data.get("data", {})
            
            # 检查是否为消息事件
            if not event_type.startswith("session.message"):
                return None
            
            return {
                "session_id": data.get("session_id"),
                "user_id": data.get("user_id"),
                "role": data.get("role", "user"),
                "content": data.get("content"),
                "message_type": data.get("message_type", "text"),
                "metadata": data.get("metadata")
            }
            
            return message_data
        except Exception as e:
            self.logger.error(f"转换事件到消息错误: {str(e)}")
            return None
    
    def batch_convert_to_events(
        self,
        records: List[Any],
        record_type: str
    ) -> List[Dict[str, Any]]:
        """
        批量转换记录为事件
        
        Args:
            records: 记录列表
            record_type: 记录类型 (task_execution, workflow_execution, message)
            
        Returns:
            List[Dict[str, Any]]: 事件列表
        """
        try:
            events = []
            
            for record in records:
                try:
                    if record_type == "task_execution":
                        event = self.agent_execution_to_event(record)
                    elif record_type == "workflow_execution":
                        event = self.workflow_execution_to_event(record)
                    elif record_type == "message":
                        event = self.message_to_event(record)
                    else:
                        self.logger.warning(f"不支持的记录类型: {record_type}")
                        continue
                    
                    events.append(event)
                    
                    # 更新统计
                    self.conversion_stats["successful_conversions"] += 1
                    self.conversion_stats["event_types_processed"].add(event["event_type"])
                except Exception as e:
                    self.logger.error(f"批量转换记录错误: {str(e)}")
                    self.conversion_stats["failed_conversions"] += 1
            
            # 更新总体统计
            self.conversion_stats["total_conversions"] += len(records)
            self.conversion_stats["last_conversion"] = self._format_timestamp()
            
            return events
        except Exception as e:
            self.logger.error(f"批量转换事件错误: {str(e)}")
            return []
    
    def batch_convert_from_events(
        self,
        events: List[Dict[str, Any]],
        target_type: str
    ) -> List[Dict[str, Any]]:
        """
        批量转换事件为记录
        
        Args:
            events: 事件列表
            target_type: 目标类型 (task_execution, workflow_execution, message)
            
        Returns:
            List[Dict[str, Any]]: 记录列表
        """
        try:
            records = []
            
            for event in events:
                try:
                    if target_type == "task_execution":
                        record = self.event_to_task_execution(event)
                    elif target_type == "workflow_execution":
                        record = self.event_to_workflow_execution(event)
                    elif target_type == "message":
                        record = self.event_to_message(event)
                    else:
                        self.logger.warning(f"不支持的目标类型: {target_type}")
                        continue
                    
                    if record:
                        records.append(record)
                        self.conversion_stats["successful_conversions"] += 1
                except Exception as e:
                    self.logger.error(f"批量转换事件错误: {str(e)}")
                    self.conversion_stats["failed_conversions"] += 1
            
            # 更新总体统计
            self.conversion_stats["total_conversions"] += len(events)
            self.conversion_stats["last_conversion"] = self._format_timestamp()
            
            return records
        except Exception as e:
            self.logger.error(f"批量转换记录错误: {str(e)}")
            return []
    
    def create_custom_event(
        self,
        event_type: str,
        data: Dict[str, Any],
        source: str = "custom",
        metadata: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        创建自定义事件
        
        Args:
            event_type: 事件类型
            data: 事件数据
            source: 事件源
            metadata: 元数据
            
        Returns:
            Dict[str, Any]: 事件数据
        """
        try:
            event_data = {
                "event_id": self._generate_event_id(),
                "event_type": event_type,
                "timestamp": self._format_timestamp(),
                "source": source,
                "data": data,
                "metadata": metadata or {}
            }
            
            # 添加转换时间戳
            event_data["metadata"]["conversion_timestamp"] = self._format_timestamp()
            
            # 记录转换历史
            conversion_record = {
                "event_id": event_data["event_id"],
                "event_type": event_type,
                "source": source,
                "created_at": event_data["timestamp"]
            }
            
            self.conversion_stats["conversion_history"].append(conversion_record)
            
            # 限制历史记录数量
            if len(self.conversion_stats["conversion_history"]) > 1000:
                self.conversion_stats["conversion_history"] = self.conversion_stats["conversion_history"][-500:]
            
            return event_data
        except Exception as e:
            self.logger.error(f"创建自定义事件错误: {str(e)}")
            raise e
    
    def get_conversion_stats(self) -> Dict[str, Any]:
        """
        获取转换统计
        
        Returns:
            Dict[str, Any]: 转换统计信息
        """
        stats = self.conversion_stats.copy()
        stats["event_types_processed"] = list(stats["event_types_processed"])
        stats["success_rate"] = (
            stats["successful_conversions"] / stats["total_conversions"]
            if stats["total_conversions"] > 0 else 0
        )
        return stats
    
    def reset_stats(self) -> None:
        """
        重置转换统计
        """
        self.conversion_stats = {
            "total_conversions": 0,
            "successful_conversions": 0,
            "failed_conversions": 0,
            "event_types_processed": set(),
            "conversion_history": [],
            "last_conversion": None
        }
        
        self.logger.info("转换统计已重置")